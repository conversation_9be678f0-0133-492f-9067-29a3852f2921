<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.4.2</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent> 
	
    <groupId>com.zkr</groupId>
	<artifactId>booster-yearbook-biz</artifactId>
	<packaging>jar</packaging> 
	<description>booster cloud gen</description>
	<dependencies>
		<!-- Spring系列 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>



		<!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>2.1.3</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>


		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.0.M2</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.5.3</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.75</version>
		</dependency>

		<!-- <dependency> -->
		<!-- <groupId>io.swagger</groupId> -->
		<!-- <artifactId>swagger-models</artifactId> -->
		<!-- <version>1.5.24</version> -->
		<!-- </dependency> -->
		<!-- <dependency> -->
		<!-- <groupId>io.swagger</groupId> -->
		<!-- <artifactId>swagger-annotations</artifactId> -->
		<!-- <version>1.5.24</version> -->
		<!-- </dependency> -->

		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-boot-starter</artifactId>
			<version>2.0.7</version>
		</dependency>
		<!-- commons工具 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.11</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>30.0-jre</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>4.4</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.12</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>4.1.2</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/jakarta.validation/jakarta.validation-api -->
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
			<version>2.0.2</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.13.3</version>
		</dependency>

		<!-- pdf转图片 -->
		<dependency>
			<groupId>org.icepdf.os</groupId>
			<artifactId>icepdf-core</artifactId>
			<version>6.1.2</version>
			<exclusions>
				<exclusion>
					<groupId>javax.media</groupId>
					<artifactId>jai-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.aspose</groupId>
			<artifactId>aspose-words</artifactId>
			<version>15.8.0</version>
		</dependency>
		<!-- excel-spring-boot-starter https://github.com/pig-mesh/excel-spring-boot-starter -->
		<dependency>
			<groupId>com.pig4cloud.excel</groupId>
			<artifactId>excel-spring-boot-starter</artifactId>
			<version>0.5.0</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.11.3</version>
		</dependency>


		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
			<version>8.1.1.193</version>
		</dependency>

		<!-- 引入easy-es最新版本的依赖 -->
		<dependency>
			<groupId>cn.easy-es</groupId>
			<artifactId>easy-es-boot-starter</artifactId>
			<version>2.0.0-beta1</version>
		</dependency>


		<!-- 排除springboot中内置的es依赖,以防和easy-es中的依赖冲突 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.elasticsearch.client</groupId>
					<artifactId>elasticsearch-rest-high-level-client</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.elasticsearch</groupId>
					<artifactId>elasticsearch</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>7.14.0</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>7.14.0</version>
		</dependency>

		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
			<version>2.5.1</version>
		</dependency>

		<!-- <dependency> <groupId>com.google.guava</groupId> <artifactId>guava</artifactId> 
			<version>31.1-jre</version> </dependency> -->

		<!-- <dependency> <groupId>org.springframework.boot</groupId> <artifactId>spring-boot-starter-actuator</artifactId> 
			</dependency> -->

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
			<!-- maven 打包时跳过测试 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
		<!-- 		<configuration>
					<mainClass>com.sinosoft.ie.booster.yearbook.DemoApplication</mainClass>
					<layout>ZIP</layout>
					<includes>
						设置没有jar包
						<include>
							<groupId>nothing</groupId>
							<artifactId>nothing</artifactId>
						</include>
						<include>
							<groupId>org.springframework</groupId>
							<artifactId>spring-core</artifactId>
						</include>
					</includes>
				</configuration> -->
			</plugin>

			<plugin>
				<!-- 1. 加密后,方法体被清空,保留方法参数、注解等信息.主要兼容swagger文档注解扫描 
				    2. 方法体被清空后,反编译只能看到方法名和注解,看不到方法体的具体内容 
					3. 加密后的项目需要设置javaagent来启动,启动过程中解密class,完全内存解密,不留下任何解密后的文件 
					4. 启动加密后的jar,生成xxx-encrypted.jar,这个就是加密后的jar文件,加密后不可直接执行 
					5. 无密码启动方式,java -javaagent:xxx-encrypted.jar -jar xxx-encrypted.jar 
					6. 有密码启动方式,java 
					-javaagent:xxx-encrypted.jar='-pwd= 密码' -jar xxx-encrypted.jar -->
				<groupId>net.roseboy</groupId>
				<artifactId>classfinal-maven-plugin</artifactId>
				<version>1.2.1</version>
				<configuration>
					<password>#</password><!-- #表示启动时不需要密码,事实上对于代码混淆来说,这个密码没什么用,它只是一个启动密码 -->
					<excludes>org,org.springframework.core.io.ClassPathResource</excludes>
					<packages>com</packages>
					<cfgfiles>application.properties,application-dev.properties,application-dm.properties,application-prd.properties</cfgfiles><!-- 加密的配置文件,多个包用逗号分开 -->
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>classFinal</goal>
						</goals>
					</execution>
				</executions>
				</plugin>
		</plugins>
	</build>
</project>
