from win32com.client import constants,gencache
import glob
import os
import pythoncom
import win32
import sys

word_path = sys.argv[1]
pdf_path = sys.argv[2]



def Word_to_Pdf(Word_path,Pdf_path):
    word = gencache.EnsureDispatch('Word.Application')
    doc = word.Documents.Open(Word_path, ReadOnly=1)
    # 转换方法
    doc.ExportAsFixedFormat(Pdf_path, constants.wdExportFormatPDF)

if __name__ == '__main__':
    Word_to_Pdf(word_path,pdf_path)
