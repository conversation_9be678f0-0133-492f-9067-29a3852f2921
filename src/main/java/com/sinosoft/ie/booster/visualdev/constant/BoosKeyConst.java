package com.sinosoft.ie.booster.visualdev.constant;


/**
 * VisualKey类型
 *
 * <AUTHOR>
 * @create 2018/1/9
 */

public class BoosKeyConst {
	/**
	 * 单选
	 */
	public static final String RADIO = "radio";

	/**
	 * 开关
	 */
	public static final String SWITCH = "switch";

	/**
	 * 下拉框
	 */
	public static final String SELECT = "select";

	/**
	 * 多选框
	 */
	public static final String CHECKBOX = "checkbox";
	/**
	 * 公司
	 */
	public static final String COMSELECT = "comSelect";
	/**
	 * 部门
	 */
	public static final String DEPSELECT = "depSelect";
	/**
	 * 數據字典
	 */
	public static final String DICSELECT = "dicSelect";
	/**
	 * 岗位
	 */
	public static final String POSSELECT = "posSelect";
	/**
	 * 用户
	 */
	public static final String USERSELECT = "userSelect";
	/**
	 * 行政区划
	 */
	public static final String ADDRESS = "address";
	/**
	 * 时间范围
	 */
	public static final String TIMERANGE = "timeRange";
	/**
	 * 日期范围
	 */
	public static final String DATERANGE = "dateRange";
	/**
	 * 日期选择
	 */
	public static final String DATE = "date";
	/**
	 * 附件
	 */
	public static final String UPLOADFZ = "uploadFz";
	/**
	 * 图片
	 */
	public static final String UPLOADIMG = "uploadImg";
	/**
	 * 滑块
	 */
	public static final String SLIDER = "slider";
	/**
	 * 所属公司
	 */
	public static final String CURRORGANIZE = "currOrganize";
	/**
	 * 所属部门
	 */
	public static final String CURRDEPT = "currDept";
	/**
	 * 创建用户
	 */
	public static final String CREATEUSER = "createUser";
	/**
	 * 创建时间
	 */
	public static final String CREATETIME = "createTime";
	/**
	 * 修改用户
	 */
	public static final String MODIFYUSER = "modifyUser";
	/**
	 * 修改时间
	 */
	public static final String MODIFYTIME = "modifyTime";
	/**
	 * 所属岗位
	 */
	public static final String CURRPOSITION = "currPosition";
	/**
	 * 单据规则
	 */
	public static final String BILLRULE = "billRule";

	/**
	 * 功能关联表单
	 */
	public static final String RELATIONFORM = "relationForm";
	/**
	 * 工作流关联表单
	 */
	public static final String RELATIONFLOW = "relationFlow";

	/**
	 * 树形选择
	 */
	public static final String TREESELECT = "treeSelect";

	/**
	 * 级联选择
	 */
	public static final String CASCADER = "cascader";

	/**
	 * 子表boosKey
	 */
	public static final String CHILD_TABLE = "table";


}
