package com.sinosoft.ie.booster.common.core.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;

/**
 * 线程池配置
 * <AUTHOR>
 *
 */
@EnableAsync
@Configuration
public class ThreadPoolConfig {

	@Autowired
	private ProjectConfig projectConfig;
	
	@Bean("taskExecutor")
	public Executor asyncServiceExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		// 设置核心线程数等于系统核数--8核
		int availableProcessors = Runtime.getRuntime().availableProcessors();
		executor.setCorePoolSize(projectConfig.getThread_size());
		// 设置最大线程数
		executor.setMaxPoolSize(projectConfig.getThread_max());
		// 配置队列大小
		//executor.setQueueCapacity(2000); 使用默认的Integer.MAX_VALUE;
		// 设置线程活跃时间（秒）
		executor.setKeepAliveSeconds(10);
		// 线程满了之后由调用者所在的线程来执行
		// 拒绝策略：CALLER_RUNS：不在新线程中执行任务，而是由调用者所在的线程来执行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
		// 设置默认线程名称
		executor.setThreadNamePrefix("extract-");
		// 等待所有任务结束后再关闭线程池
		executor.setWaitForTasksToCompleteOnShutdown(true);
		// 执行初始化
		executor.initialize();
		return executor;
	}
}
