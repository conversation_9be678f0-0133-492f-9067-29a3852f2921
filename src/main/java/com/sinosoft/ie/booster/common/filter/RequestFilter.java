package com.sinosoft.ie.booster.common.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.filter.OncePerRequestFilter;
/**
 * 处理xss攻击
 * <AUTHOR>
 *
 */
@WebFilter("/*")
public class RequestFilter extends OncePerRequestFilter{

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,  FilterChain filterChain)
			throws ServletException, IOException {
		 // 将request通过自定义的装饰类进行装饰
		XssRequestWrapper xssRequest = new XssRequestWrapper((HttpServletRequest) request);     
//		if (request.getRequestURI().contains("actuator")) {
//			filterChain.doFilter(request, response);
//		}else {
//			filterChain.doFilter(xssRequest, response);
//		}
		filterChain.doFilter(xssRequest, response);
	}

}
