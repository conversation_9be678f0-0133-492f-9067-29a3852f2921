package com.sinosoft.ie.booster.common.feign.handle;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.R;

import cn.hutool.json.JSONException;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 全局异常处理器结合sentinel 全局异常处理器不能作用在 oauth server
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-29
 */

@Slf4j
@RestControllerAdvice
public class GlobalBizExceptionHandler {

	@Resource
	HttpServletResponse response;
	/**
	 * 全局异常.
	 * @param e the e
	 * @return R
	 */
	
	@ExceptionHandler(DataException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public R handleGlobalException(DataException e) {
		log.error("全局异常信息 ex={}", e.getMessage());
		return R.failed(e.getLocalizedMessage());
	}

	@ExceptionHandler(JSONException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public R jsonFileGlobalException(JSONException e) {
		log.error("全局异常信息 ex={}", e.getMessage());
		return R.failed("JSON文件格式错误!");
	}
	/**
	 * 统一处理请求参数校验(json)
	 *
	 * @param e ConstraintViolationException
	 * @return FebsResponse
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	public R handlerMethodArgumentNotValidException(MethodArgumentNotValidException e) {
		StringBuilder message = new StringBuilder();
		for (FieldError error : e.getBindingResult().getFieldErrors()) {
			message.append(error.getField()).append(error.getDefaultMessage());
		}
		message = new StringBuilder(message.substring(0, message.length()));
		log.error(message.toString(), e);
		return R.failed(message.toString());
	}
	
	@ExceptionHandler(org.springframework.web.method.annotation.MethodArgumentTypeMismatchException.class)
	public R handlerMethodArgumentNotValidException(org.springframework.web.method.annotation.MethodArgumentTypeMismatchException e) {
		log.error("参数类型错误=>{}", e);
//		try {
//			response.sendError(404, "File not found!");
//		} catch (IOException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
		return R.failed("参数类型错误");
	}

	
}
