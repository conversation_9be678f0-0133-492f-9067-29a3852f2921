package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-09 14:49:54
 */
@Data
@TableName("mark_sentence")
@ApiModel(value = "")
public class MarkSentenceEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文档id
     */
    @ApiModelProperty(value = "文档id")
    private Long fid;

    /**
     * 在文档中的顺序
     */
    @ApiModelProperty(value = "在文档中的顺序")
    private Integer fsort;

    /**
     * 标题
     */
    @ApiModelProperty(value = "一句话")
    private String sentence;

    /**
     * 句子详情
     */
    @ApiModelProperty(value = "修改后的句子")
    private String sentenceExc;

    /**
     * 抽取的数据
     */
    @ApiModelProperty(value = "抽取的数据")
    private String executeData;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 状态：默认1，未处理,就绪；2处理中；3智能标注已完成；4已完成；5报错
     */
    @ApiModelProperty(value = "1未抽取；2已抽取；3抽取报错")
    private Integer status;

    /**
     * 语料id
     */
    @ApiModelProperty(value = "语料id")
    private Long corpusId;
    /**
     * 语料版本
     */
    @ApiModelProperty(value = "语料版本")
    private Integer corpusVersion;
}
