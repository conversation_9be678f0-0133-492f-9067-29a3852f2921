package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibConceptMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

import javax.annotation.Resource;

/**
 *
 * 本体概念类
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-30 09:58:21
 */
@Service
public class LibConceptServiceImpl extends ServiceImpl<LibConceptMapper, LibConceptEntity> implements LibConceptService {

	@Resource
	private LibConceptMapper libConceptMapper;
	
	
    @Override
    public List<LibConceptEntity> getList(LibConceptPagination libConceptPagination){
        QueryWrapper<LibConceptEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(libConceptPagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(LibConceptEntity::getId,libConceptPagination.getId()));
        }

        if(!"null".equals(String.valueOf(libConceptPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getName,libConceptPagination.getName()));
        }

        if(!"null".equals(String.valueOf(libConceptPagination.getPid()))){
            queryWrapper.lambda().and(t->t.eq(LibConceptEntity::getPid,libConceptPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(libConceptPagination.getScore()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getScore,libConceptPagination.getScore()));
        }
        if(!"null".equals(String.valueOf(libConceptPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getSchemaId,libConceptPagination.getSchemaId()));
        }
        //排序
        if(StrUtil.isEmpty(libConceptPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(LibConceptEntity::getId);
        }else{
            queryWrapper="asc".equals(libConceptPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(libConceptPagination.getSidx()):queryWrapper.orderByDesc(libConceptPagination.getSidx());
        }
        if(libConceptPagination.getCurrentPage()==0) {
        	List<LibConceptEntity> list  = this.list(queryWrapper);
        	return list;
        }
        Page<LibConceptEntity> page=new Page<>(libConceptPagination.getCurrentPage(), libConceptPagination.getPageSize());
        IPage<LibConceptEntity> userIPage=this.page(page,queryWrapper);
        return libConceptPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<LibConceptEntity> getTypeList(LibConceptPagination libConceptPagination,String dataType){
        QueryWrapper<LibConceptEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(libConceptPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getId,libConceptPagination.getId()));
        }

        if(!"null".equals(String.valueOf(libConceptPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getName,libConceptPagination.getName()));
        }

        if(!"null".equals(String.valueOf(libConceptPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getPid,libConceptPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(libConceptPagination.getScore()))){
            queryWrapper.lambda().and(t->t.like(LibConceptEntity::getScore,libConceptPagination.getScore()));
        }

        //排序
        if(StrUtil.isEmpty(libConceptPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(LibConceptEntity::getId);
        }else{
            queryWrapper="asc".equals(libConceptPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(libConceptPagination.getSidx()):queryWrapper.orderByDesc(libConceptPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<LibConceptEntity> page=new Page<>(libConceptPagination.getCurrentPage(), libConceptPagination.getPageSize());
            IPage<LibConceptEntity> userIPage=this.page(page,queryWrapper);
            return libConceptPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public LibConceptEntity getInfo(Long id){
        QueryWrapper<LibConceptEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(LibConceptEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(LibConceptEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, LibConceptEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(LibConceptEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
    
    /**
     * 根据id查询子节点
     */
	@Override
	public List<LibConceptEntity> getAllSubNode(Long id) {
		LibConceptEntity ent1 = this.getById(id);
		if(ent1==null) {
			return null;
		}
		List<LibConceptEntity> allNode = new  ArrayList<>();
		List<LibConceptEntity> seNode = new  ArrayList<>();
		seNode.add(ent1);
		allNode.add(ent1);
		while(seNode!=null  && seNode.size()!=0) {
			StringBuffer  stb = new StringBuffer();
			for (int i = 0; i < seNode.size(); i++) {
				stb.append(seNode.get(i).getId()).append(",");
			}
			String ids = stb.toString().substring(0,stb.length()-1);
			seNode =  libConceptMapper.getListByIds(ids);
			allNode.addAll(seNode);
		}
		return allNode;
	}

    @Override
    public void deleteChild(Long id){
        QueryWrapper<LibConceptEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().or(t->t.eq(LibConceptEntity::getId,id));
        queryWrapper.lambda().or(t->t.eq(LibConceptEntity::getPid,id));
        libConceptMapper.delete(queryWrapper);
    }
}