package com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * DomainThesaurus模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-14 15:44:58
 */
@Data
@ApiModel(value = "DomainThesaurus模型")
public class DomainThesaurusPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 词汇名称
     */
    @ApiModelProperty(value = "词汇名称")
    private String name;

    /**
     * 类别(1.军语)
     */
    @ApiModelProperty(value = "类别(1.军语)")
    private String category;

    /**
     * 词性(1.名词 2.动词)
     */
    @ApiModelProperty(value = "词性(1.名词 2.动词)")
    private String pos;

    /**
     * 同义词(分号分割)
     */
    @ApiModelProperty(value = "同义词(分号分割)")
    private String synonym;

    /**
     * 审核状态 (0.未审核 1.审核成功）
     */
    @ApiModelProperty(value = "审核状态 (0.未审核 1.审核成功）")
    private String status;

    /**
     * 删除标识 (1.删除 0.正常)
     */
    @ApiModelProperty(value = "删除标识 (1.删除 0.正常)")
    private String delFlag;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long updateTime;
}