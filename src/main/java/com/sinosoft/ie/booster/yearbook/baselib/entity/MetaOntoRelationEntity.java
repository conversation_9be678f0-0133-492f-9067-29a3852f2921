package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 本体关系表
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:37:39
 */
@Data
@TableName("meta_onto_relation")
@ApiModel(value = "本体关系表")
public class MetaOntoRelationEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 本体1的id
     */
    @ApiModelProperty(value = "本体1的id")
    private Long onto1Id;

    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的id")
    private Long onto2Id;

    /**
     * 关系名称
     */
    @ApiModelProperty(value = "关系名称")
    private String name;

    /**
     * 关系的父id
     */
    @ApiModelProperty(value = "关系的父id")
    private Long pid;
    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;
}
