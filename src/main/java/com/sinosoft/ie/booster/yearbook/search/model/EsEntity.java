package com.sinosoft.ie.booster.yearbook.search.model;

import cn.easyes.annotation.IndexId;
import cn.easyes.annotation.IndexName;
import cn.easyes.annotation.rely.IdType;
import lombok.Data;

@Data
@IndexName("entity")
public class EsEntity {
	
	
	@IndexId(type=IdType.CUSTOMIZE)
    private String id;
	
	private String onto_id;
	
	private String name;
	
	private String data;
	
	private String portrait;
	
	private String org_rank;
	
	private String es_show;
}
