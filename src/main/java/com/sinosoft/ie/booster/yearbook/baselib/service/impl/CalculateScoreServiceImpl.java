package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.CalculateScoreService;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.HotInformationEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.HotInformationMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CalculateScoreServiceImpl implements CalculateScoreService {

    @Resource
    private HotInformationMapper hotInformationMapper;

    @Resource
    private EventMapper eventMapper;

    @Resource
    private ResearchTagMapper researchTagMapper;


    /**
     * 计算热点信息权重
     */
    @Override
    public void CalculateInfoScore(){
        QueryWrapper<HotInformationEntity> queryInfo=new QueryWrapper<>();
        List<HotInformationEntity> hotInformationEntities = hotInformationMapper.selectList(queryInfo);
        Map<String,Integer> map = new HashMap<>();
        for (HotInformationEntity info:hotInformationEntities) {
            int weight = 0;
            if (map.containsKey(info.getTheme()+info.getYear())){
                weight = map.get(info.getTheme()+info.getYear());
            }else {
                QueryWrapper<EventEntity> queryEvent = new QueryWrapper<>();
                queryEvent.select("IFNULL(sum( weight ),0)weight");
                queryEvent.lambda().and(t->t.eq(EventEntity::getTheme,info.getTheme()));
                queryEvent.lambda().and(t->t.like(EventEntity::getStartTime,info.getYear()));
                List<EventEntity> eventEntities = eventMapper.selectList(queryEvent);
                weight = eventEntities.get(0).getWeight();
                map.put(info.getTheme()+info.getYear(),weight);
            }
            if (weight == 0 && (info.getNumber()==null || info.getNumber()==0)){
                continue;
            }
            if (weight == info.getNumber()){
                continue;
            }
            info.setNumber(weight);
            hotInformationMapper.updateById(info);
        }
    }

    @Override
    public void CalculateTagScore(){
//        Map<String,String> tagMap = new HashMap<>();
//        tagMap.put("政治工作","political_work");
//        tagMap.put("管理保障","security_manage");
//        tagMap.put("综合协调","comprehensive_coordination");
//        tagMap.put("科研管理","scientific_manage");
//        tagMap.put("领导决策","decision_making");
//        tagMap.put("科研力量","scientific_strength");
//        tagMap.put("科研组织","scientific_organization");
//        tagMap.put("科研课题","scientific_subject");
//        tagMap.put("科研成果","scientific_result");
//        tagMap.put("科研经费","scientific_funding");

        QueryWrapper<ResearchTagEntity> queryTag=new QueryWrapper<>();
        List<ResearchTagEntity> tagEntities = researchTagMapper.selectList(queryTag);
        for (ResearchTagEntity tag:tagEntities) {
            int weight = 0;
            QueryWrapper<EventEntity> queryEvent = new QueryWrapper<>();
            queryEvent.select("IFNULL(sum( weight ),0)weight");
            queryEvent.lambda().and(t->t.like(EventEntity::getTag,tag.getName()));
            List<EventEntity> eventEntities = eventMapper.selectList(queryEvent);
            weight = eventEntities.get(0).getWeight();
            if (weight == 0 && (tag.getZonelevel()==null || tag.getZonelevel()==0)){
                continue;
            }
            if (weight == tag.getZonelevel()){
                continue;
            }
            tag.setZonelevel(weight);
            researchTagMapper.updateById(tag);
        }
    }
}