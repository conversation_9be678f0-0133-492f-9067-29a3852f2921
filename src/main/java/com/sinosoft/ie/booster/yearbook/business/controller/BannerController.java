package com.sinosoft.ie.booster.yearbook.business.controller;

import io.swagger.annotations.Api;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerPagination;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerListVO;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerUpForm;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.BannerEntity;
import com.sinosoft.ie.booster.yearbook.business.service.BannerService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
 *
 * banner
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-10 13:51:42
 */
@Slf4j
@RestController
@Api(tags = "banner")
@RequestMapping("/Banner")
public class BannerController {

    @Autowired
    private BannerService bannerservice;

    /**
     * 列表
     *
     * @param bannerPagination
     * @return
     */
    @GetMapping
    public R list(BannerPagination bannerPagination)throws IOException{
        List<BannerEntity> list= bannerservice.getList(bannerPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(BannerEntity entity:list){
            }
        List<BannerListVO> listVO=JsonUtil.getJsonToList(list,BannerListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(bannerPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param bannerCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid BannerCrForm bannerCrForm) throws DataException {
        BannerEntity entity=JsonUtil.getJsonToBean(bannerCrForm, BannerEntity.class);
        bannerservice.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<BannerInfoVO> info(@PathVariable("id") Long id){
        BannerEntity entity= bannerservice.getInfo(id);
        BannerInfoVO vo=JsonUtil.getJsonToBean(entity, BannerInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid BannerUpForm bannerUpForm) throws DataException {
        BannerEntity entity= bannerservice.getInfo(id);
        if(entity!=null){
            bannerservice.delete(entity);
            entity=JsonUtil.getJsonToBean(bannerUpForm, BannerEntity.class);
            entity.setId(id);
            bannerservice.create(entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        BannerEntity entity= bannerservice.getInfo(id);
        if(entity!=null){
            bannerservice.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
