package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.HotInformationEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationPagination;
import java.util.*;

/**
 *
 * hot_information
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-21 17:37:05
 */
public interface HotInformationService extends IService<HotInformationEntity> {

    List<HotInformationEntity> getList(HotInformationPagination hotInformationPagination);

    List<HotInformationEntity> getTypeList(HotInformationPagination hotInformationPagination,String dataType);



    HotInformationEntity getInfo(Long id);

    void delete(HotInformationEntity entity);

    void create(HotInformationEntity entity);

    boolean update( Long id, HotInformationEntity entity);
    
//  子表方法
}
