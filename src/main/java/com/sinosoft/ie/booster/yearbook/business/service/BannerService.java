package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.BannerEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerPagination;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.BannerOut;

import java.util.*;

/**
 *
 * banner
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-10 13:51:42
 */
public interface BannerService extends IService<BannerEntity> {

    List<BannerEntity> getList(BannerPagination bannerPagination);

    List<BannerEntity> getTypeList(BannerPagination bannerPagination,String dataType);

    BannerEntity getInfo(Long id);

    void delete(BannerEntity entity);

    void create(BannerEntity entity);

    boolean update( Long id, BannerEntity entity);

//  子表方法
}
