package com.sinosoft.ie.booster.yearbook.baselib.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportLogEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.FileImportLogService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * file_import_log
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-08 16:35:51
 */
@Slf4j
@RestController
@Api(tags = "file_import_log")
@RequestMapping("/FileImportLog")
public class FileImportLogController {
    @Autowired
    private FileImportLogService fileImportLogService;


    /**
     * 列表
     *
     * @param fileImportLogPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<FileImportLogListVO>> list(FileImportLogPagination fileImportLogPagination)throws IOException{
        List<FileImportLogEntity> list= fileImportLogService.getList(fileImportLogPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(FileImportLogEntity entity:list){
            }
        List<FileImportLogListVO> listVO=JsonUtil.getJsonToList(list,FileImportLogListVO.class);
        PageListVO<FileImportLogListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(fileImportLogPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param fileImportLogCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid FileImportLogCrForm fileImportLogCrForm) throws DataException {
        FileImportLogEntity entity=JsonUtil.getJsonToBean(fileImportLogCrForm, FileImportLogEntity.class);
        fileImportLogService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<FileImportLogInfoVO> info(@PathVariable("id") Long id){
        FileImportLogEntity entity= fileImportLogService.getInfo(id);
        FileImportLogInfoVO vo=JsonUtil.getJsonToBean(entity, FileImportLogInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid FileImportLogUpForm fileImportLogUpForm) throws DataException {
        FileImportLogEntity entity= fileImportLogService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(fileImportLogUpForm, FileImportLogEntity.class);
            fileImportLogService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        FileImportLogEntity entity= fileImportLogService.getInfo(id);
        if(entity!=null){
            fileImportLogService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
