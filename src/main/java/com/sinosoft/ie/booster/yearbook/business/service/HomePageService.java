package com.sinosoft.ie.booster.yearbook.business.service;

import java.util.List;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.BannerOut;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.ImportantNews;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.YearbookCount;


public interface HomePageService {

    List<BannerOut> queryBanner();

//    Map<String,List<ResearchTagListVO>> labelRead(String typeList);

    YearbookCount count();

    ImportantNews getNews();

    Object eventVein();
    
    
    R getEventByTitleYearPage(EventPagination eventPagination);
}
