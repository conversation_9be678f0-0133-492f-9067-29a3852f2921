package com.sinosoft.ie.booster.yearbook.mark.model.marktriplet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * MarkTriplet模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:56
 */
@Data
@ApiModel
public class MarkTripletCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long tag1id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long tag2id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long relationid;
    

    /**
     * 关系名称
     */
    @ApiModelProperty(value = "关系名称")
    private String relationname;

    /**
     * 句子id
     */
    @ApiModelProperty(value = "句子id")
    private Long sentenceid;
}