package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-02 10:13:41
 */
@Data
@TableName("everelation")
@ApiModel(value = "")
public class EverelationEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 事件1id
     */
    @ApiModelProperty(value = "事件1id")
    private String e1id;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id")
    private String e2id;

    /**
     * 关联名称
     */
    @ApiModelProperty(value = "关联名称")
    private String relationName;
}
