package com.sinosoft.ie.booster.yearbook.mark.model.markdataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * MarkDataset模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-11 15:34:34
 */
@Data
@ApiModel
public class MarkDatasetListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    
    /**
     * 数据集名称或文件名称
     */
    @ApiModelProperty(value = "数据集名称或文件名称")
    private String name;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private String pid;
    
    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fid;
    
    /**
     * 状态：默认1，未处理；2已处理；3处理失败
     */
    @ApiModelProperty(value = "状态：默认1，未处理；2已处理；3处理失败")
    private Integer stat;
    
    @ApiModelProperty(value = "开始页码")
    private Integer startPage;

}