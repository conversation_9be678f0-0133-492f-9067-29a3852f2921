package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkCorpusEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkSentenceMapper;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkCorpusService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkEntityService;
import com.sinosoft.ie.booster.yearbook.util.PdfUtil;
import com.sinosoft.ie.booster.yearbook.util.WordUtil;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EnableAsync
@Service
public class ExtractTextDataService2 {
	@Resource
	private MarkSentenceMapper sentenceMapper;
	@Resource
	private MakrSentenceService sentenceService;
	@Resource
	private ProjectConfig projectConfig;
	@Resource
	private LibConceptService libConceptService;
	@Resource
	private MarkEntityService markEntityService;
	@Resource
	private ExtractTripletDataService extractTripletDataService;
	@Resource
	private MarkCorpusService markCorpusService;
	/**
	 * 抽取标注文本语句
	 */
	@Async("taskExecutor")
	@Transactional
	public R  extractText(MarkCorpusEntity entity , UploadfileEntity file  ) {
		//查询语料信息
		MarkCorpusEntity versionEnt = markCorpusService.getById(entity.getPid());
		MarkCorpusEntity corpusEnt = markCorpusService.getById(versionEnt.getPid());;
		//分解文件内容
		String word_path = projectConfig.getUploadPath() + File.separator + file.getPath();
		String pdf_path = projectConfig.getUploadPath() + File.separator+IdUtil.simpleUUID()+".pdf";
		String word_txt = "";
		log.info("解析pdf文件");
		try {
			word_txt = WordUtil.analysisWord1(word_path);
			//java ->  pdf 
            File pdf_file = new File(pdf_path);
            FileOutputStream os = new FileOutputStream(pdf_file);
            Document doc = new Document(word_path);
            doc.save(os, SaveFormat.PDF);
			// python -> pdf
//			String cmd = "python  word_to_pdf.py  "+word_path + " "+pdf_path;
//			Process process = Runtime.getRuntime().exec(cmd);
//			int exitCode = process .waitFor();
			
		} catch (Exception e) {
			log.error("word转pdf失败:{}",e);
			return R.failed("word转pdf失败");
		}
		
        Map<Integer,String>  sedMap =  PdfUtil.readPdfByPage(pdf_path);
		log.info("循环map-->抽取事件文本");
		if (entity != null) {
			log.info("抽取MarkCorpusEntity的id="+entity.getId()+"的文件");
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("content", word_txt);
			HashMap<String, Object> pdfMap = new HashMap<>();
			JSONObject pdf_json = JSONUtil.createObj();
			Integer size = entity.getStartPage();
			for(Integer number:sedMap.keySet()) {
				pdf_json.set(size.toString(), sedMap.get(number));
				size++;
			}             
			paramMap.put("pdf_content", pdf_json.toString());
			String str = JSONUtil.toJsonStr(paramMap);
			log.info("参数--->"+str);
			
			HttpResponse res = HttpRequest.post(projectConfig.getEveUrl()).form(paramMap).execute();
			String result_body = res.body();
			log.info("抽取返回值--->"+result_body);
			JSONObject jsonObject = JSONUtil.parseObj(result_body);
			Integer code = jsonObject.getInt("code");
			//返回数据格式--->example.json
			// 状态码正确200；否则500
			if (!code.equals(200)) {
				entity.setStat(YearbookConstant.MARK_STATS_ERROR);
				log.error(jsonObject.toString());
			} else {
				extractTriplet(jsonObject,entity,corpusEnt);
			}
		}
		return R.ok();
	}
	
	/**
	 * 
	 * @param jsonObject   抽取的数据
	 * @param entity       当前上传文件所在对象
	 * @param corpusEnt    当前语料对象
	 */
//	@Async("taskExecutor")
	public  void    extractTriplet(JSONObject jsonObject,MarkCorpusEntity entity,MarkCorpusEntity corpusEnt) {
		JSONArray array = jsonObject.getJSONArray("result");
		List<MarkSentenceEntity>  entlist = new ArrayList<MarkSentenceEntity>();
		for (int i = 0; i < array.size(); i++) {
			JSONObject  sen = (JSONObject)array.get(i);
			String title = sen.getStr("title");
			String text1 = sen.getStr("text");
			String page  = sen.getStr("page");
			
			MarkSentenceEntity  sentEnt = new MarkSentenceEntity();
			sentEnt.setFid(entity.getFid());
			sentEnt.setFsort(Integer.parseInt(page));
			sentEnt.setSentence(title);
			sentEnt.setSentenceExc(text1);
			sentEnt.setStatus(YearbookConstant.MARK_STATS_WAIT);
			entlist.add(sentEnt);
			sentEnt.setCorpusId(corpusEnt.getId());
			sentEnt.setCorpusVersion(entity.getVersion());
			sentEnt.setCreateTime(LocalDateTime.now());
			extractTripletDataService.extractTriplet(sentEnt);
		}
		sentenceService.saveBatch(entlist);
		entity.setStat(2);
		markCorpusService.updateById(entity);
	}
	
	
	
//	
//	@Transactional
//	@Async("taskExecutor")
//	public void extractText1(MarkCorpusEntity entity , UploadfileEntity file  ) {
//		//查询语料信息
//		MarkCorpusEntity versionEnt = markCorpusService.getById(entity.getPid());
//		MarkCorpusEntity corpusEnt = markCorpusService.getById(versionEnt.getPid());;
//		//分解文件内容
//		String word_path = projectConfig.getUploadPath() + File.separator + file.getPath();
//		log.info("解析pdf文件");
//		try {
//			String word_txt = WordUtil.analysisWord1(word_path);
//            // 把word转为pdf
//			String pdf_path = projectConfig.getUploadPath() + File.separator+IdUtil.simpleUUID()+".pdf";
//			try {
//				String cmd = "";
//				Process process = Runtime.getRuntime().exec(cmd);
//				int exitCode = process .waitFor();
//				
//			} catch (Exception e) {
//				return R.failed("word转pdf失败");
//			}
//			
//			
//			
//			
//			File pdf_file = new File(pdf_path);
//            FileOutputStream os = new FileOutputStream(pdf_file);
//            Document doc = new Document(word_path);
//            doc.save(os, SaveFormat.PDF);
//            Map<Integer,String>  sedMap =  PdfUtil.readPdfByPage(pdf_path);
//			log.info("循环map-->抽取事件文本");
//			if (entity != null) {
//				log.info("抽取MarkCorpusEntity的id="+entity.getId()+"的文件");
//				HashMap<String, Object> paramMap = new HashMap<>();
//				paramMap.put("content", word_txt);
//				HashMap<String, Object> pdfMap = new HashMap<>();
//				JSONObject pdf_json = JSONUtil.createObj();
//				Integer size = entity.getStartPage();
//				for(Integer number:sedMap.keySet()) {
//					pdf_json.set(size.toString(), sedMap.get(number));
//					size++;
//				}             
//				paramMap.put("pdf_content", pdf_json.toString());
//				String str = JSONUtil.toJsonStr(paramMap);
//				log.info("参数--->"+str);
//				
//				HttpResponse res = HttpRequest.post(projectConfig.getEveUrl()).form(paramMap).execute();
//				String result_body = res.body();
//				log.info("抽取返回值--->"+result_body);
//				JSONObject jsonObject = JSONUtil.parseObj(result_body);
//				Integer code = jsonObject.getInt("code");
//				//返回数据格式--->example.json
//				// 状态码正确200；否则500
//				if (!code.equals(200)) {
//					entity.setStat(YearbookConstant.MARK_STATS_ERROR);
//					log.error(jsonObject.toString());
//				} else {
//					JSONArray array = jsonObject.getJSONArray("result");
//					List<MarkSentenceEntity>  entlist = new ArrayList<MarkSentenceEntity>();
//					for (int i = 0; i < array.size(); i++) {
//						JSONObject  sen = (JSONObject)array.get(i);
//						String title = sen.getStr("title");
//						String text1 = sen.getStr("text");
//						String page  = sen.getStr("page");
//						
//						
//						MarkSentenceEntity  sentEnt = new MarkSentenceEntity();
//						sentEnt.setFid(entity.getFid());
//						sentEnt.setFsort(Integer.parseInt(page));
//						sentEnt.setSentence(title);
//						sentEnt.setSentenceExc(text1);
//						sentEnt.setStatus(YearbookConstant.MARK_STATS_WAIT);
//						entlist.add(sentEnt);
//						sentEnt.setCorpusId(corpusEnt.getId());
//						sentEnt.setCorpusVersion(entity.getVersion());
//						sentEnt.setCreateTime(LocalDateTime.now());
//						log.info("抽取三元组--->");
//						extractTripletDataService.extractTriplet(sentEnt);
//					}
//					sentenceService.saveBatch(entlist);
//					entity.setStat(2);
//				}
//				markCorpusService.updateById(entity);
//			}
//		} catch (IOException e) {
//			log.error("解析word文件出错",e);
//		} catch (Exception e) {
//			log.error("抽取三元组数据出错",e);
//			entity.setStat(2);
//			markCorpusService.updateById(entity);
//		}
//	}

	

	/**
	 * 将json格式数据转成对象
	 * @param entObj
	 */
	public MarkEntityEntity    convertJson(JSONObject entObj) {
		// "实体"或"属性"
		String tagname = entObj.getStr("tagname");
		// 本体名称
		String tagonto = entObj.getStr("tagonto");
		// 抽取的文本词
		String lab_text = entObj.getStr("text");
		// 保存抽取的数据
		MarkEntityEntity markent = new MarkEntityEntity();
		markent.setTagname(tagname);
		markent.setText(lab_text);
		markent.setOntoname(tagonto);
		//查询并保存本体id
		
		if(tagonto.equals("UNKNOWN")) {
			return markent;
		}
		// 从缓存读取本体数据
		LibConceptEntity ontoEntity = OntoCache.getOntoCache(tagonto);
		// 如果没有则查询
		if (ontoEntity == null) {
			QueryWrapper<LibConceptEntity> query = new QueryWrapper<LibConceptEntity>();
			query.eq("ontoName", tagonto);
			List<LibConceptEntity> onlist = libConceptService.list(query);
			if (onlist.size() != 0) {
				markent.setTagId(ontoEntity.getId());
			}
		}
		return markent;
	}
	
	/**
	 * 判断实体列表中是否包含当前的实体
	 * @param entlist
	 * @param ent
	 * @return
	 */
	public Long   getMarkEntId(List<MarkEntityEntity>  entlist , MarkEntityEntity ent) {
		Long id = null;
		//循环实体集合
		for (int i = 0; i < entlist.size(); i++) {
			MarkEntityEntity item = entlist.get(i);
			if(item.comparison(ent)) {
				id =  item.getId();
				break;
			}
		}
		//如果集合中没有该实体则保存至数据库和集合
		if(id==null) {
			markEntityService.save(ent);
			entlist.add(ent);
			id = ent.getId();
		}
		return id;
	}
	

}
