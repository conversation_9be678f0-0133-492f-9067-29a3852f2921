package com.sinosoft.ie.booster.yearbook.baselib.model.libentity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * LibEntity模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-16 15:45:57
 */
@Data
@ApiModel
public class LibEntityCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @NotEmpty
    @Length(max = 200)
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 所属本体
     */
    @NotNull
    @ApiModelProperty(value = "所属本体")
    private Long ontoId;

    /**
     * 属性数据
     */
    @ApiModelProperty(value = "属性数据")
    private String data;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer state;
    /**
     * 语料id
     */
    @ApiModelProperty(value = "语料id")
    private Long corpusId;
    
    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;
    
    /**
     * 消歧项
     */
    @ApiModelProperty(value = "消歧项")
    private String disambiguation;

    /**
     * 同义词
     */
    @ApiModelProperty(value = "同义词")
    private String synonymWord;
}