package com.sinosoft.ie.booster.yearbook.mark.model.everelation;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Everelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-02 10:13:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EverelationPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String id;

    /**
     * 事件1id
     */
    @ApiModelProperty(value = "事件1id")
    private String e1id;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id")
    private String e2id;

    /**
     * 关联名称
     */
    @ApiModelProperty(value = "关联名称")
    private String relationName;
}