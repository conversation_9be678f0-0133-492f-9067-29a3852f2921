package com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * GraphThemeEvent模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 16:14:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class GraphThemeEventUpForm extends GraphThemeEventCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}