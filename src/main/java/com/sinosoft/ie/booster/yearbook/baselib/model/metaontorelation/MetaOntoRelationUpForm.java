package com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MetaOntoRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:37:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MetaOntoRelationUpForm extends MetaOntoRelationCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}