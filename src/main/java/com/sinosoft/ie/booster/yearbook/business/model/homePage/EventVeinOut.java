package com.sinosoft.ie.booster.yearbook.business.model.homePage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：EventVeinOut.java
 * @description：事件脉络查询结果返回类
 * @author：peas
 * @data：2022/11/11 16:30
 **/
@Data
public class EventVeinOut {

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String year;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String tag;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String theme;
}
