package com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 把属性和属性值放到同一个类中
 * <AUTHOR>
 *
 */
@Data
public class MateAttributeAndValueVo {
	
	

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String nameEn;

    /**
     * 中文
     */
    @ApiModelProperty(value = "中文")
    private String nameCn;

    /**
     * 所属本体
     */
    @ApiModelProperty(value = "所属本体")
    private Long onto;
    /**
     * 表单类型
     */
    private Integer fromType;
  
    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    private Long relationId;
    
    private Long pid;
    /**
     * 是该属性的值还是子属性
     * 0 属性值
     * 1 子属性
     * 
     */
    private  int  isVal;

    /**
     * 指向：1：正向；2：反向,3:双向
     */
    @ApiModelProperty(value = "指向：1：正向；2：反向,3:双向")
    private Integer towards;
}
