package com.sinosoft.ie.booster.yearbook.mark.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkCorpusEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusPagination;

/**
 *
 * mark_corpus
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-24 17:45:08
 */
public interface MarkCorpusService extends IService<MarkCorpusEntity> {

    List<MarkCorpusEntity> getList(MarkCorpusPagination markCorpusPagination);

    List<MarkCorpusEntity> getTypeList(MarkCorpusPagination markCorpusPagination,String dataType);



    MarkCorpusEntity getInfo(Long id);

    void delete(MarkCorpusEntity entity);

    void create(MarkCorpusEntity entity);

    boolean update( Long id, MarkCorpusEntity entity);

    PageListVO<MarkCorpusListVO> getAllList(MarkCorpusPagination markCorpusPagination);

    void deleteChild(Long id);

    void deleteCorpusVersion(MarkCorpusCrForm markCorpusCrForm);

//  子表方法

//    void deleteMarkBySentence(MarkCorpusCrForm markCorpusCrForm);

    void deleteMarkBySentenceIds(String ids);

    R importCorpus(MarkCorpusEntity entity);

    void inheritVersion(MarkCorpusEntity corpus);
}
