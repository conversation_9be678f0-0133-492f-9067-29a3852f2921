package com.sinosoft.ie.booster.yearbook.database.controller;

import com.sinosoft.ie.booster.yearbook.database.entity.DataConnectionEntity;
import com.sinosoft.ie.booster.yearbook.database.model.dataconnection.*;
import com.sinosoft.ie.booster.yearbook.database.service.DataConnectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * data_connection
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-06 15:01:22
 */
@Slf4j
@RestController
@Api(tags = "data_connection")
@RequestMapping("/DataConnection")
public class DataConnectionController {
    @Autowired
    private DataConnectionService dataConnectionService;


    /**
     * 列表
     *
     * @param dataConnectionPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<DataConnectionListVO>> list(DataConnectionPagination dataConnectionPagination)throws IOException{
        List<DataConnectionEntity> list= dataConnectionService.getList(dataConnectionPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(DataConnectionEntity entity:list){
            }
        List<DataConnectionListVO> listVO=JsonUtil.getJsonToList(list,DataConnectionListVO.class);
        PageListVO<DataConnectionListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(dataConnectionPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param dataConnectionCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid DataConnectionCrForm dataConnectionCrForm) throws DataException {
        DataConnectionEntity entity=JsonUtil.getJsonToBean(dataConnectionCrForm, DataConnectionEntity.class);
        dataConnectionService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<DataConnectionInfoVO> info(@PathVariable("id") Long id){
        DataConnectionEntity entity= dataConnectionService.getInfo(id);
        DataConnectionInfoVO vo=JsonUtil.getJsonToBean(entity, DataConnectionInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid DataConnectionUpForm dataConnectionUpForm) throws DataException {
        DataConnectionEntity entity= dataConnectionService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(dataConnectionUpForm, DataConnectionEntity.class);
            dataConnectionService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        DataConnectionEntity entity= dataConnectionService.getInfo(id);
        if(entity!=null){
            dataConnectionService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
