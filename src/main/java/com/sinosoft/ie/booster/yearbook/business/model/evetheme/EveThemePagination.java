package com.sinosoft.ie.booster.yearbook.business.model.evetheme;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * EveTheme模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-06 13:45:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EveThemePagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String name;

    /**
     * 常态主题还是动态主题
     */
    @ApiModelProperty(value = "常态主题还是动态主题")
    private Integer category1;

    /**
     * 二级分类
     */
    @ApiModelProperty(value = "科研类还是管理类")
    private Long category2;

    /**
     * 是否默认：0，不；1，默认选中
     */
    @ApiModelProperty(value = "是否默认：0，不；1，默认选中")
    private Integer isdef;
}