package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-11 15:34:34
 */
@Data
@TableName("mark_dataset")
@ApiModel(value = "")
public class MarkDatasetEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 数据集名称或文件名称
     */
    @ApiModelProperty(value = "数据集名称或文件名称")
    private String name;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private Long pid;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Long fid;

    /**
     * 状态：默认1，未处理；2已处理；3处理失败
     */
    @ApiModelProperty(value = "状态：默认1，未处理；2已处理；3处理失败")
    private Integer stat;
    
    @ApiModelProperty(value = "开始页码")
    private Integer startPage;
}
