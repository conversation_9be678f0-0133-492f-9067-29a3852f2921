package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.entity.PageConfigEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.PageConfigMapper;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigPagination;
import com.sinosoft.ie.booster.yearbook.business.service.PageConfigService;

import cn.hutool.core.util.StrUtil;

/**
 *
 * page_config
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-22 17:46:32
 */
@Service
public class PageConfigServiceImpl extends ServiceImpl<PageConfigMapper, PageConfigEntity> implements PageConfigService {


    @Override
    public List<PageConfigEntity> getList(PageConfigPagination pageConfigPagination){
        QueryWrapper<PageConfigEntity> queryWrapper=new QueryWrapper<>();
        //排序
        if(StrUtil.isEmpty(pageConfigPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(PageConfigEntity::getId);
        }else{
            queryWrapper="asc".equals(pageConfigPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(pageConfigPagination.getSidx()):queryWrapper.orderByDesc(pageConfigPagination.getSidx());
        }
        Page<PageConfigEntity> page=new Page<>(pageConfigPagination.getCurrentPage(), pageConfigPagination.getPageSize());
        IPage<PageConfigEntity> userIPage=this.page(page,queryWrapper);
        return pageConfigPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<PageConfigEntity> getTypeList(PageConfigPagination pageConfigPagination,String dataType){
        QueryWrapper<PageConfigEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(pageConfigPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(PageConfigEntity::getId,pageConfigPagination.getId()));
        }

        if(!"null".equals(String.valueOf(pageConfigPagination.getTagTotal()))){
            queryWrapper.lambda().and(t->t.like(PageConfigEntity::getTagTotal,pageConfigPagination.getTagTotal()));
        }

        if(!"null".equals(String.valueOf(pageConfigPagination.getTagSinglecate()))){
            queryWrapper.lambda().and(t->t.like(PageConfigEntity::getTagSinglecate,pageConfigPagination.getTagSinglecate()));
        }

        if(!"null".equals(String.valueOf(pageConfigPagination.getTagAverage()))){
            queryWrapper.lambda().and(t->t.like(PageConfigEntity::getTagAverage,pageConfigPagination.getTagAverage()));
        }

        if(!"null".equals(String.valueOf(pageConfigPagination.getRotationSpeed()))){
            queryWrapper.lambda().and(t->t.like(PageConfigEntity::getRotationSpeed,pageConfigPagination.getRotationSpeed()));
        }

        //排序
        if(StrUtil.isEmpty(pageConfigPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(PageConfigEntity::getId);
        }else{
            queryWrapper="asc".equals(pageConfigPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(pageConfigPagination.getSidx()):queryWrapper.orderByDesc(pageConfigPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<PageConfigEntity> page=new Page<>(pageConfigPagination.getCurrentPage(), pageConfigPagination.getPageSize());
            IPage<PageConfigEntity> userIPage=this.page(page,queryWrapper);
            return pageConfigPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public PageConfigEntity getInfo(Long id){
        QueryWrapper<PageConfigEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(PageConfigEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(PageConfigEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, PageConfigEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(PageConfigEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}