package com.sinosoft.ie.booster.yearbook.baselib.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaEntityRelationService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * meta_entity_relation
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:34:43
 */
@Slf4j
@RestController
@Api(tags = "meta_entity_relation")
@RequestMapping("/MetaEntityRelation")
public class MetaEntityRelationController {
    @Autowired
    private MetaEntityRelationService metaEntityRelationService;


    /**
     * 列表
     *
     * @param metaEntityRelationPagination
     * @return
     */
    @GetMapping
    public R list(MetaEntityRelationPagination metaEntityRelationPagination)throws IOException{
        List<MetaEntityRelationEntity> list= metaEntityRelationService.getList(metaEntityRelationPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MetaEntityRelationEntity entity:list){
            }
        List<MetaEntityRelationListVO> listVO=JsonUtil.getJsonToList(list,MetaEntityRelationListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(metaEntityRelationPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param metaEntityRelationCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid MetaEntityRelationCrForm metaEntityRelationCrForm) throws DataException {
        MetaEntityRelationEntity entity=JsonUtil.getJsonToBean(metaEntityRelationCrForm, MetaEntityRelationEntity.class);
        metaEntityRelationService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MetaEntityRelationInfoVO> info(@PathVariable("id") Long id){
        MetaEntityRelationEntity entity= metaEntityRelationService.getInfo(id);
        MetaEntityRelationInfoVO vo=JsonUtil.getJsonToBean(entity, MetaEntityRelationInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid MetaEntityRelationUpForm metaEntityRelationUpForm) throws DataException {
        MetaEntityRelationEntity entity= metaEntityRelationService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(metaEntityRelationUpForm, MetaEntityRelationEntity.class);
            metaEntityRelationService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        MetaEntityRelationEntity entity= metaEntityRelationService.getInfo(id);
        if(entity!=null){
            metaEntityRelationService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

//    /**
//     * 列表
//     *
//     * @param metaEntityRelationPagination
//     * @return
//     */
//    @GetMapping("/getByORId")
//    public R getByORId(MetaEntityRelationPagination metaEntityRelationPagination)throws IOException{
//        List<MetaEntityRelationListVO> listVO = metaEntityRelationService.getByORId(metaEntityRelationPagination);
//        //处理id字段转名称，若无需转或者为空可删除
//        PageListVO vo=new PageListVO();
//        vo.setList(listVO);
//        PaginationVO page=JsonUtil.getJsonToBean(metaEntityRelationPagination,PaginationVO.class);
//        vo.setPagination(page);
//        return R.ok(vo);
//    }
//
//    /**
//     * @methodName：getListByIds
//     * @description： 根据本体id和实体id查询实体关系列表
//     * @author：peas
//     * @date：2022/11/16 15:59
//     * @param：[onto1Id, entity1Id]
//     * @return：com.sinosoft.ie.booster.common.core.util.R
//     **/
//    @GetMapping("/getListByIds")
//    public R getListByIds(String onto1Id, String entity1Id)throws IOException{
//        List<MetaEntityRelationOut> listVO = metaEntityRelationService.getListByIds(onto1Id, entity1Id);
//        return R.ok(listVO);
//    }

    /**
     * 创建-校验数据重复
     */
    @PostMapping("/createUnduplicate")
    @Transactional
    public R createUnduplicate(@RequestBody @Valid MetaEntityRelationCrForm metaEntityRelationCrForm) throws DataException {
        MetaEntityRelationEntity entity=JsonUtil.getJsonToBean(metaEntityRelationCrForm, MetaEntityRelationEntity.class);
        QueryWrapper<MetaEntityRelationEntity> queryWrapper=new QueryWrapper<>();

        if(!"null".equals(String.valueOf(entity.getOnto1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto1Id,entity.getOnto1Id()));
        }

        if(!"null".equals(String.valueOf(entity.getOnto2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto2Id,entity.getOnto2Id()));
        }

        if(!"null".equals(String.valueOf(entity.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getName,entity.getName()));
        }

        if(!"null".equals(String.valueOf(entity.getEntity1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity1Id,entity.getEntity1Id()));
        }

        if(!"null".equals(String.valueOf(entity.getEntity2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity2Id,entity.getEntity2Id()));
        }

        if(!"null".equals(String.valueOf(entity.getOntoRelId()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOntoRelId,entity.getOntoRelId()));
        }
        if (metaEntityRelationService.count(queryWrapper) > 0){
            return R.failed(null, "数据重复");
        }
        metaEntityRelationService.create(entity);
        //libentity添加data值
        metaEntityRelationService.createEntity(entity,1);
        return R.ok(null, "新建成功");
    }

    /**
     * 更新-修改实体中的data
     */
    @PutMapping("/updateWithEntityData/{id}")
    @Transactional
    public R updateWithEntityData(@PathVariable("id") Long id,@RequestBody @Valid MetaEntityRelationUpForm metaEntityRelationUpForm) throws DataException {
        MetaEntityRelationEntity entity= metaEntityRelationService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(metaEntityRelationUpForm, MetaEntityRelationEntity.class);
            metaEntityRelationService.update(id, entity);
            //libentity添加data值
            metaEntityRelationService.createEntity(entity,1);
            return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

    /**
     * 删除-修改实体中的data
     */
    @DeleteMapping("/updateWithEntityData/{id}")
    @Transactional
    public R updateWithEntityData(@PathVariable("id") Long id){
        MetaEntityRelationEntity entity= metaEntityRelationService.getInfo(id);
        if(entity!=null){
            metaEntityRelationService.delete(entity);
            //libentity添加data值
            metaEntityRelationService.createEntity(entity,2);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 图谱查询-实体名称-实体关系
     */
    @GetMapping("/getGraph")
    public  R getGraph(String name, Long relationId) {

        return metaEntityRelationService.getGraph(name,relationId);
    }

    /**
     * 图谱查询-实体名称-实体关系
     */
    @GetMapping("/getGraphOnto")
    public R getGraphOnto(Long ontoId) {

        return metaEntityRelationService.getGraphOnto(ontoId);
    }
}
