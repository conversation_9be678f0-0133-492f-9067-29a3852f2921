package com.sinosoft.ie.booster.yearbook.database.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.constant.enums.DataSetPieEnum;
import com.sinosoft.ie.booster.yearbook.database.entity.DataDatasetEntity;
import com.sinosoft.ie.booster.yearbook.database.mapper.DataDatasetMapper;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetPagination;
import com.sinosoft.ie.booster.yearbook.database.service.DataDatasetService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * data_dataset
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-06 15:00:08
 */
@Service
public class DataDatasetServiceImpl extends ServiceImpl<DataDatasetMapper, DataDatasetEntity> implements DataDatasetService {

    @Resource
    private DataDatasetMapper dataDatasetMapper;

    @Override
    public List<DataDatasetEntity> getList(DataDatasetPagination dataDatasetPagination){
        QueryWrapper<DataDatasetEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(dataDatasetPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getId,dataDatasetPagination.getId()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getName,dataDatasetPagination.getName()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getTableName()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getTableName,dataDatasetPagination.getTableName()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getSource()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getSource,dataDatasetPagination.getSource()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getSchemaId,dataDatasetPagination.getSchemaId()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getDataConnectId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getDataConnectId,dataDatasetPagination.getDataConnectId()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getDataId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getDataId,dataDatasetPagination.getDataId()));
        }

        //排序
        if(StrUtil.isEmpty(dataDatasetPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(DataDatasetEntity::getId);
        }else{
            queryWrapper="asc".equals(dataDatasetPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(dataDatasetPagination.getSidx()):queryWrapper.orderByDesc(dataDatasetPagination.getSidx());
        }
        Page<DataDatasetEntity> page=new Page<>(dataDatasetPagination.getCurrentPage(), dataDatasetPagination.getPageSize());
        IPage<DataDatasetEntity> userIPage=this.page(page,queryWrapper);
        return dataDatasetPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<DataDatasetEntity> getTypeList(DataDatasetPagination dataDatasetPagination,String dataType){
        QueryWrapper<DataDatasetEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(dataDatasetPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getId,dataDatasetPagination.getId()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getName,dataDatasetPagination.getName()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getTableName()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getTableName,dataDatasetPagination.getTableName()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getSource()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getSource,dataDatasetPagination.getSource()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getSchemaId,dataDatasetPagination.getSchemaId()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getDataConnectId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getDataConnectId,dataDatasetPagination.getDataConnectId()));
        }

        if(!"null".equals(String.valueOf(dataDatasetPagination.getDataId()))){
            queryWrapper.lambda().and(t->t.like(DataDatasetEntity::getDataId,dataDatasetPagination.getDataId()));
        }

        //排序
        if(StrUtil.isEmpty(dataDatasetPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(DataDatasetEntity::getId);
        }else{
            queryWrapper="asc".equals(dataDatasetPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(dataDatasetPagination.getSidx()):queryWrapper.orderByDesc(dataDatasetPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<DataDatasetEntity> page=new Page<>(dataDatasetPagination.getCurrentPage(), dataDatasetPagination.getPageSize());
            IPage<DataDatasetEntity> userIPage=this.page(page,queryWrapper);
            return dataDatasetPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public DataDatasetEntity getInfo(Long id){
        QueryWrapper<DataDatasetEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(DataDatasetEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataDatasetEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, DataDatasetEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(DataDatasetEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

    @Override
    public R chartPie(Long id){
        List<Map<String,Object>> list = new ArrayList<>();
        for (int i = 1; i < DataSetPieEnum.values().length+1; i++) {
            Map<String,Object> map = new HashMap<>();
            int finalI = i;
            QueryWrapper<DataDatasetEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda().and(t->t.eq(DataDatasetEntity::getDataId,id));
            queryWrapper.lambda().and(t->t.eq(DataDatasetEntity::getSource, finalI));
            long bdcount = this.count(queryWrapper);
            map.put("name", DataSetPieEnum.getName(finalI));
            map.put("value", bdcount);
            list.add(map);
        }
        return R.ok(list);
    }

    @Override
    public R tableCount(Long id){
        Map<String,Object> map = new HashMap<>();
        //库里存在的表
        List<String> yearbooksTableName = dataDatasetMapper.getTableName();
        //数据集对应的表
        QueryWrapper<DataDatasetEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.select("table_name");
        queryWrapper.lambda().and(t->t.eq(DataDatasetEntity::getDataId,id));
        List<DataDatasetEntity> datasetEntities = this.list(queryWrapper);
        //数据集对应的表并且在库中存在
        List<String> tableName = new ArrayList<>();
        for (DataDatasetEntity dataSet:datasetEntities) {
            if (dataSet.getTableName() == null || dataSet.getTableName().isEmpty()){
                continue;
            }
            if (yearbooksTableName.contains(dataSet.getTableName())){
                tableName.add(dataSet.getTableName());
            }
        }
        map.put("tableName",tableName);
        List<Integer> tableNumList = new ArrayList<>();
        for (String table:tableName) {
            int tableNum = dataDatasetMapper.getTableNum(table);
            tableNumList.add(tableNum);
        }
        map.put("tableNum",tableNumList);
        return R.ok(map);
    }
}