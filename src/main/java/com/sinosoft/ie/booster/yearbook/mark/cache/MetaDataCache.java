package com.sinosoft.ie.booster.yearbook.mark.cache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.map.MultiKeyMap;

import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;

public class MetaDataCache {
	
	
	//本体map   <本体id,本体对象>
	private static Map<Long ,List<LibConceptEntity>>  ontoBySchemaIdGroupMap =  new HashMap<>();
	//本体map   <本体名称,本体对象>
	private static Map<String ,LibConceptEntity>  ontoMap =  new HashMap<>();
	
	//双key   <模式id,本体name,本体对象>
	private static MultiKeyMap<Object, Object> ontoMultiKey = new MultiKeyMap<>();
	
	public  static void   saveOnto(List<LibConceptEntity> list) {
		for (int i = 0; i < list.size(); i++) {
			LibConceptEntity ent = list.get(i);
			if(ent.getSchemaId()==null || ent.getName()==null) {
				continue;
			}
			ontoMultiKey.put(ent.getSchemaId(),ent.getName(),ent);
		}
		
		
		ontoBySchemaIdGroupMap  = 	list.stream().collect(Collectors.groupingBy(LibConceptEntity::getSchemaId));
		for (Long  sid : ontoBySchemaIdGroupMap.keySet()) {
			List<LibConceptEntity> ontolist = ontoBySchemaIdGroupMap.get(sid);
			Map<String,LibConceptEntity> map = ontolist.stream().collect(Collectors.toMap(LibConceptEntity::getName,LibConceptEntity->LibConceptEntity));
		}
	
	}
	//通过模式id,本体名称查询本体
	public static LibConceptEntity  getOntoEntity(Long sid,String name) {
		return (LibConceptEntity) ontoMultiKey.get(sid,name);
	}
	

	
}
