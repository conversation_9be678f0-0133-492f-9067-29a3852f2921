package com.sinosoft.ie.booster.yearbook.util;

import org.icepdf.core.pobjects.Document;
import org.icepdf.core.util.GraphicsRenderingHints;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.*;

/**
 * @version 1.0
 * @projectName：yearbooks
 * @className：PdfUtil.java
 * @description：pdf工具类
 * @author：peas
 * @data：2022/9/14 17:42
 **/
public class Pdf2PicUtil {

	public static void main(String [] args) throws Exception {
		int pageCount = pdf2Pic("D:\\file\\1.pdf","D:\\file\\","fileName");
		System.out.println("pageCount:"+pageCount);
	}

	public static int pdf2Pic(String pdfPath, String path, String fileName) throws Exception {
		Document document = new Document();
		document.setFile(pdfPath);
		//缩放比例
		float scale = 2.5f;
		//旋转角度
		float rotation = 0f;
		for (int i = 0; i < document.getNumberOfPages(); i++) {
			BufferedImage image = (BufferedImage)
					document.getPageImage(i, GraphicsRenderingHints.SCREEN, org.icepdf.core.pobjects.Page.BOUNDARY_CROPBOX, rotation, scale);
			RenderedImage rendImage = image;
			try {
				File fileDir = new File(path);
				if (!fileDir.exists()) {
					fileDir.mkdirs();
				}
				String imgName = fileName + i + ".png";
				System.out.println(path);
				System.out.println(imgName);
				File file = new File(path + imgName);
				ImageIO.write(rendImage, "png", file);
			} catch (IOException e) {
				e.printStackTrace();
			}
			image.flush();
		}
		document.dispose();
		return document.getNumberOfPages();
	}
	
	
	
	public static int pdf2Pic(String pdfPath, String out) throws Exception {
		Document document = new Document();
		document.setFile(pdfPath);
		//缩放比例
		float scale = 1f;
		//旋转角度
		float rotation = 0f;
		for (int i = 0; i < document.getNumberOfPages(); i++) {
			BufferedImage image = (BufferedImage)
					document.getPageImage(i, GraphicsRenderingHints.SCREEN, org.icepdf.core.pobjects.Page.BOUNDARY_CROPBOX, rotation, scale);
			RenderedImage rendImage = image;
			try {
				File file = new File(out);
				ImageIO.write(rendImage, "png", file);
			} catch (IOException e) {
				e.printStackTrace();
			}
			image.flush();
		}
		document.dispose();
		return document.getNumberOfPages();
	}
	
}
