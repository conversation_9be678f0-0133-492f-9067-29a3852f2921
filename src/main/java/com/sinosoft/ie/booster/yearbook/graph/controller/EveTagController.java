package com.sinosoft.ie.booster.yearbook.graph.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagPagination;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagCrForm;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagInfoVO;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagListVO;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagUpForm;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.graph.entity.EveTagEntity;
import com.sinosoft.ie.booster.yearbook.graph.service.EveTagService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
 *
 * eve_tag
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-07-21 10:53:07
 */
@Slf4j
@RestController
@Api(tags = "eve_tag")
@RequestMapping("/EveTag")
public class EveTagController {
    @Autowired
    private EveTagService eveTagService;


    /**
     * 列表
     *
     * @param eveTagPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<EveTagListVO>> list(EveTagPagination eveTagPagination)throws IOException{
        List<EveTagEntity> list= eveTagService.getList(eveTagPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(EveTagEntity entity:list){
            }
        List<EveTagListVO> listVO=JsonUtil.getJsonToList(list,EveTagListVO.class);
        PageListVO<EveTagListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(eveTagPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param eveTagCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid EveTagCrForm eveTagCrForm) throws DataException {
        EveTagEntity entity=JsonUtil.getJsonToBean(eveTagCrForm, EveTagEntity.class);
        eveTagService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<EveTagInfoVO> info(@PathVariable("id") Long id){
        EveTagEntity entity= eveTagService.getInfo(id);
        EveTagInfoVO vo=JsonUtil.getJsonToBean(entity, EveTagInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid EveTagUpForm eveTagUpForm) throws DataException {
        EveTagEntity entity= eveTagService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(eveTagUpForm, EveTagEntity.class);
            eveTagService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        EveTagEntity entity= eveTagService.getInfo(id);
        if(entity!=null){
            eveTagService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
