package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRules2Entity;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2CrForm;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2InfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2ListVO;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2Pagination;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2UpForm;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRules2Service;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * conver_rules2
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-20 16:32:03
 */
@Slf4j
@RestController
@Api(tags = "conver_rules2")
@RequestMapping("/ConverRules2")
public class ConverRules2Controller {
    @Autowired
    private ConverRules2Service converRules2Service;


    /**
     * 列表
     *
     * @param converRules2Pagination
     * @return
     */
    @GetMapping
    public R<PageListVO<ConverRules2ListVO>> list(ConverRules2Pagination converRules2Pagination)throws IOException{
        List<ConverRules2Entity> list= converRules2Service.getList(converRules2Pagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(ConverRules2Entity entity:list){
            }
        List<ConverRules2ListVO> listVO=JsonUtil.getJsonToList(list,ConverRules2ListVO.class);
        PageListVO<ConverRules2ListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(converRules2Pagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param converRules2CrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid ConverRules2CrForm converRules2CrForm) throws DataException {
        ConverRules2Entity entity=JsonUtil.getJsonToBean(converRules2CrForm, ConverRules2Entity.class);
        converRules2Service.create(entity);
        return R.ok(null, "新建成功");
    }
    
    /**
     * 批量添加
     * @param converRules2CrForm
     * @return
     * @throws DataException
     */
    @PostMapping("/savabatch")
    @Transactional
    public R<String> create2(@RequestBody @Valid List<ConverRules2CrForm> list) throws DataException {
        List<ConverRules2Entity> entlist=JsonUtil.getJsonToList(list, ConverRules2Entity.class);
        converRules2Service.create2(entlist);
        return R.ok(null, "新建成功");
    }

    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<ConverRules2InfoVO> info(@PathVariable("id") Long id){
        ConverRules2Entity entity= converRules2Service.getInfo(id);
        ConverRules2InfoVO vo=JsonUtil.getJsonToBean(entity, ConverRules2InfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid ConverRules2UpForm converRules2UpForm) throws DataException {
        ConverRules2Entity entity= converRules2Service.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(converRules2UpForm, ConverRules2Entity.class);
            converRules2Service.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        ConverRules2Entity entity= converRules2Service.getInfo(id);
        if(entity!=null){
            converRules2Service.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
