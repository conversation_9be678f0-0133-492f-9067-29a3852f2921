package com.sinosoft.ie.booster.yearbook.mark.service;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletPagination;
import java.util.*;

/**
 *
 * mark_triplet
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-08 16:58:56
 */
public interface MarkTripletService extends IService<MarkTripletEntity> {

    List<MarkTripletEntity> getList(MarkTripletPagination markTripletPagination);

    List<MarkTripletEntity> getTypeList(MarkTripletPagination markTripletPagination,String dataType);



    MarkTripletEntity getInfo(Long id);

    void delete(MarkTripletEntity entity);

    void create(MarkTripletEntity entity);

    boolean update( Long id, MarkTripletEntity entity);
    
//  子表方法
}
