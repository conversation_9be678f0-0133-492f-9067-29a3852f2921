package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateRelationAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateRelationAttributeValueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValuePagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * mate_relation_attribute_value
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-01 14:35:03
 */
@Service
public class MateRelationAttributeValueServiceImpl extends ServiceImpl<MateRelationAttributeValueMapper, MateRelationAttributeValueEntity> implements MateRelationAttributeValueService {


    @Override
    public List<MateRelationAttributeValueEntity> getList(MateRelationAttributeValuePagination mateRelationAttributeValuePagination){
        QueryWrapper<MateRelationAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getId,mateRelationAttributeValuePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getAttributeId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getAttributeId,mateRelationAttributeValuePagination.getAttributeId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getValName()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getValName,mateRelationAttributeValuePagination.getValName()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getPid,mateRelationAttributeValuePagination.getPid()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getOntoId,mateRelationAttributeValuePagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getRelationId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getRelationId,mateRelationAttributeValuePagination.getRelationId()));
        }

        //排序
        if(StrUtil.isEmpty(mateRelationAttributeValuePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateRelationAttributeValueEntity::getId);
        }else{
            queryWrapper="asc".equals(mateRelationAttributeValuePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateRelationAttributeValuePagination.getSidx()):queryWrapper.orderByDesc(mateRelationAttributeValuePagination.getSidx());
        }
        Page<MateRelationAttributeValueEntity> page=new Page<>(mateRelationAttributeValuePagination.getCurrentPage(), mateRelationAttributeValuePagination.getPageSize());
        IPage<MateRelationAttributeValueEntity> userIPage=this.page(page,queryWrapper);
        return mateRelationAttributeValuePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MateRelationAttributeValueEntity> getTypeList(MateRelationAttributeValuePagination mateRelationAttributeValuePagination,String dataType){
        QueryWrapper<MateRelationAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getId,mateRelationAttributeValuePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getAttributeId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getAttributeId,mateRelationAttributeValuePagination.getAttributeId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getValName()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getValName,mateRelationAttributeValuePagination.getValName()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getPid,mateRelationAttributeValuePagination.getPid()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getOntoId,mateRelationAttributeValuePagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributeValuePagination.getRelationId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeValueEntity::getRelationId,mateRelationAttributeValuePagination.getRelationId()));
        }

        //排序
        if(StrUtil.isEmpty(mateRelationAttributeValuePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateRelationAttributeValueEntity::getId);
        }else{
            queryWrapper="asc".equals(mateRelationAttributeValuePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateRelationAttributeValuePagination.getSidx()):queryWrapper.orderByDesc(mateRelationAttributeValuePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MateRelationAttributeValueEntity> page=new Page<>(mateRelationAttributeValuePagination.getCurrentPage(), mateRelationAttributeValuePagination.getPageSize());
            IPage<MateRelationAttributeValueEntity> userIPage=this.page(page,queryWrapper);
            return mateRelationAttributeValuePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MateRelationAttributeValueEntity getInfo(Long id){
        QueryWrapper<MateRelationAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MateRelationAttributeValueEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MateRelationAttributeValueEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MateRelationAttributeValueEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MateRelationAttributeValueEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}