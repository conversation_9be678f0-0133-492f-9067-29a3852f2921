package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.sinosoft.ie.booster.visualdev.util.StringUtil;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.HotInformationEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.HotInformationMapper;
import com.sinosoft.ie.booster.yearbook.business.service.HotInformationService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import org.apache.commons.collections4.Get;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.business.entity.EveTheme2Entity;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveThemeMapper;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemePagination;
import com.sinosoft.ie.booster.yearbook.business.service.EveTheme2Service;
import com.sinosoft.ie.booster.yearbook.business.service.EveThemeService;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * eve_theme
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-06 13:45:32
 */
@Service
public class EveThemeServiceImpl extends ServiceImpl<EveThemeMapper, EveThemeEntity> implements EveThemeService {


    @Resource
    private MateAttributeMapper mateAttributeMapper;
    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;
    @Resource
    private LibEntityService libEntityService;
    @Resource
    private EveTheme2Service eveTheme2Service;
    @Resource
    private EventMapper eventMapper;
    @Resource
    private HotInformationMapper hotInformationMapper;
    @Resource
    private HotInformationService hotInformationService;
    @Resource
    private LibEntityMapper libEntityMapper;

    @Resource
    private ProjectConfig projectConfig;

    @Override
    public List<EveThemeEntity> getList(EveThemePagination eveThemePagination){
        QueryWrapper<EveThemeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(eveThemePagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(EveThemeEntity::getId,eveThemePagination.getId()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getName()))){
            queryWrapper.lambda().and(t->t.like(EveThemeEntity::getName,eveThemePagination.getName()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getCategory1()))){
            queryWrapper.lambda().and(t->t.eq(EveThemeEntity::getCategory1,eveThemePagination.getCategory1()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getCategory2()))){
            queryWrapper.lambda().and(t->t.eq(EveThemeEntity::getCategory2,eveThemePagination.getCategory2()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getIsdef()))){
            queryWrapper.lambda().and(t->t.eq(EveThemeEntity::getIsdef,eveThemePagination.getIsdef()));
        }

        //排序
        if(StrUtil.isEmpty(eveThemePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EveThemeEntity::getId);
        }else{
            queryWrapper="asc".equals(eveThemePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(eveThemePagination.getSidx()):queryWrapper.orderByDesc(eveThemePagination.getSidx());
        }
        Page<EveThemeEntity> page=new Page<>(eveThemePagination.getCurrentPage(), eveThemePagination.getPageSize());
        IPage<EveThemeEntity> userIPage=this.page(page,queryWrapper);
        return eveThemePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<EveThemeEntity> getTypeList(EveThemePagination eveThemePagination,String dataType){
        QueryWrapper<EveThemeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(eveThemePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EveThemeEntity::getId,eveThemePagination.getId()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getName()))){
            queryWrapper.lambda().and(t->t.like(EveThemeEntity::getName,eveThemePagination.getName()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getCategory1()))){
            queryWrapper.lambda().and(t->t.like(EveThemeEntity::getCategory1,eveThemePagination.getCategory1()));
        }

        if(!"null".equals(String.valueOf(eveThemePagination.getCategory2()))){
            queryWrapper.lambda().and(t->t.like(EveThemeEntity::getCategory2,eveThemePagination.getCategory2()));
        }

        //排序
        if(StrUtil.isEmpty(eveThemePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EveThemeEntity::getId);
        }else{
            queryWrapper="asc".equals(eveThemePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(eveThemePagination.getSidx()):queryWrapper.orderByDesc(eveThemePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<EveThemeEntity> page=new Page<>(eveThemePagination.getCurrentPage(), eveThemePagination.getPageSize());
            IPage<EveThemeEntity> userIPage=this.page(page,queryWrapper);
            return eveThemePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public EveThemeEntity getInfo(Long id){
        QueryWrapper<EveThemeEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(EveThemeEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public R create(EveThemeEntity entity){
        if (StringUtils.isEmpty(entity.getName())){
            return R.failed("主题名称不能为空！");
        }
        QueryWrapper<EveThemeEntity> query = new QueryWrapper<>();
        query.eq("name",entity.getName());
        if (this.count(query) > 0){
            return R.failed("主题名称不能重复！");
        }
        this.save(entity);
        LibEntityEntity libentity = new LibEntityEntity();
        libentity.setName(entity.getName());
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("name",entity.getName());
        libentity.setData(String.valueOf(jsonObject));
        libentity.setOntoId(YearbookConstant.MARK_THEME_ONTO_ID);
        libEntityMapper.insert(libentity);
        return R.ok(null,"保存成功！");
    }

    @Override
    public boolean update(Long id, EveThemeEntity entity){
        entity.setId(id);
        if(entity.getIsdef() == 1){
            QueryWrapper<EveThemeEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda().and(t->t.eq(EveThemeEntity::getIsdef,1));
            EveThemeEntity entity1 = new EveThemeEntity();
            entity1.setIsdef(0);
            this.update(entity1,queryWrapper);
        }
        return this.updateById(entity);
    }
    @Override
    public void delete(EveThemeEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

    @Override
    public R<Map<Integer,List<EveThemeEntity>>> getListGroup(){
        Map<Integer,List<EveThemeEntity>> map = new HashMap<>();
        QueryWrapper<EveThemeEntity> queryWrapper1=new QueryWrapper<>();
        queryWrapper1.lambda().and(t->t.eq(EveThemeEntity::getCategory1,1));
        List<EveThemeEntity> list1 = this.list(queryWrapper1);
        QueryWrapper<EveThemeEntity> queryWrapper2=new QueryWrapper<>();
        queryWrapper2.lambda().and(t->t.eq(EveThemeEntity::getCategory1,2));
        List<EveThemeEntity> list2 = this.list(queryWrapper2);
        map.put(1,list1);
        map.put(2,list2);
        return R.ok(map);
    }

    @Override
    public R<String> themeMate2Eve(){

        Long theme_onto_id =  YearbookConstant.MARK_THEME_ONTO_ID;
        String def_img_id = projectConfig.getDef_img_id();
        List<EveThemeEntity> eveList = this.list();
//        List<LibEntityEntity> entList = new ArrayList<>();
        for (EveThemeEntity evetheme:eveList) {
            LibEntityEntity ent = new LibEntityEntity();
            ent.setName(evetheme.getName());
            ent.setOntoId(theme_onto_id);
            //根据主题name去事件表找图片
            QueryWrapper<EventEntity> eventEntityQuery = new QueryWrapper<>();
            eventEntityQuery.eq("theme",evetheme.getName());
            eventEntityQuery.select("images");
            eventEntityQuery.isNotNull("images");
            List<EventEntity> eventEntityList = eventMapper.selectList(eventEntityQuery);
            Long images = null;
            for (EventEntity event:eventEntityList) {
                if (event.getImages() == null || event.getImages().isEmpty()){
                    continue;
                }
                if (def_img_id.contains(event.getImages())){
                    continue;
                }
                String imag = event.getImages();
                images = Long.parseLong(imag);
                
            }
            if (eventEntityList.size()>0){
                JSONObject json = new JSONObject();
                if(images!=null) {
                	json.set("img",images);	
                }
                json.set("portrait","1");
                ent.setData(String.valueOf(json));
            }
            QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
            libEntityEntityQuery.eq("name",evetheme.getName());
            libEntityEntityQuery.eq("onto_id",theme_onto_id);
            List<LibEntityEntity> libentitylist = libEntityService.list(libEntityEntityQuery);
            if (libentitylist.size() == 0){
                libEntityService.save(ent);
                continue;
            }
            JSONObject data = new JSONObject();
            if(libentitylist.get(0).getData() != null){
                data = JSONUtil.parseObj(libentitylist.get(0).getData());
            }
            if (eventEntityList.size()>0){
                if (libentitylist.get(0).getData() != null){
                	if(images!=null) {
                		data.set("img",images);
                	}
                }
            }
            data.set("portrait","1");
            ent.setData(String.valueOf(data));
            ent.setId(libentitylist.get(0).getId());
            libEntityMapper.updateById(ent);
        }
        //插入实体表
//        //先删除
//        QueryWrapper<LibEntityEntity>  del_ent_query = new QueryWrapper<LibEntityEntity>();
//        del_ent_query.lambda().and(t->t.eq(LibEntityEntity::getOntoId, theme_onto_id));
//        libEntityService.remove(del_ent_query);
//        libEntityService.saveBatch(entList);
//        libEntityService.saveOrUpdateBatch(entList);

        return R.ok("转换成功！");
    }
	@Override
	public R getThemeTree() {
		List<EveTheme2Entity>  theme2List  = eveTheme2Service.list();
		List<EveThemeEntity>  themeList = this.list();
		
		// 过滤掉category2为null的数据
		Map<Long,List<EveThemeEntity>> map = themeList.stream()
		        .filter(x -> x.getCategory2() != null)
		        .collect(Collectors.groupingBy(EveThemeEntity::getCategory2));
		
		JSONArray data = JSONUtil.createArray();
		JSONArray json1 = JSONUtil.createArray();
		JSONArray json2 = JSONUtil.createArray();
		
		theme2List.forEach(x->{
			Long c2 = x.getId();
			List<EveThemeEntity> thelist = map.get(c2);
			JSONObject json = JSONUtil.createObj();
			json.set("children", thelist != null ? thelist : new ArrayList<>());
			json.set("name", x.getCategoryName());
			if(x.getCategory1()==1) {
				json1.add(json);
			}
			if(x.getCategory1()==2) {
				json2.add(json);
			}
		});
		
		JSONObject json_c = JSONUtil.createObj();
		JSONObject json_d = JSONUtil.createObj();
		json_c.set("children", json1);
		json_c.set("name", "常态主题");
		json_d.set("children", json2);
		json_d.set("name", "动态主题");
		
		data.add(json_c);
		data.add(json_d);
		return R.ok(data);
	}

    @Override
    @Transactional(rollbackFor=Exception.class)
    public R insertTheme() {
        //获取年份
        QueryWrapper<EventEntity> eventYear = new QueryWrapper<>();
        eventYear.select("substr(start_time,1,4) as start_time","theme");
        eventYear.isNotNull("start_time");
        eventYear.isNotNull("theme");
        eventYear.groupBy("substr(start_time,1,4),theme");
        List<EventEntity> yearList = eventMapper.selectList(eventYear);

        hotInformationMapper.delete(new QueryWrapper<>());
        //循环年和主题
        for (EventEntity eventYears:yearList) {
            QueryWrapper<EventEntity> eventquery = new QueryWrapper<>();
            eventquery.select("tag","weight");
            eventquery.like("start_time",eventYears.getStartTime());
            eventquery.isNotNull("tag");
            eventquery.isNotNull("weight");
            eventquery.eq("theme",eventYears.getTheme());
            List<EventEntity> eventList = eventMapper.selectList(eventquery);
            //标签和权重
            List<Map<String,Object>> tagList = new ArrayList<>();
            for (EventEntity event:eventList) {
                String[] tagsplit = event.getTag().split(";");
                for (String tag:tagsplit) {
                    Map<String,Object> tagMap = new HashMap<>();
                    tagMap.put("tag",tag);
                    tagMap.put("weight",event.getWeight());
                    tagList.add(tagMap);
                }
            }
            //按照tag分组，并计算权重和
            Map<Object, Integer> map = tagList.stream().collect(
                Collectors.groupingBy(item -> item.get("tag").toString(),
                        Collectors.summingInt(item -> (int) item.get("weight")))
            );
            List<HotInformationEntity> hotInformationEntityList = new ArrayList<>();
            map.forEach((k,v) -> {
                //插入hot_information
                HotInformationEntity hotInformation = new HotInformationEntity();
                hotInformation.setTheme(eventYears.getTheme());
                hotInformation.setYear(eventYears.getStartTime());
                hotInformation.setNumber(v);
                hotInformation.setName((String) k);
                hotInformationEntityList.add(hotInformation);
            });
            hotInformationService.saveBatch(hotInformationEntityList);
        }
        return R.ok();
    }
}