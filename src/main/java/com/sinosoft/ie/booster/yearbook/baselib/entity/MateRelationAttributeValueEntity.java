package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 边属性值
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-01 14:35:03
 */
@Data
@TableName("mate_relation_attribute_value")
@ApiModel(value = "边属性值")
public class MateRelationAttributeValueEntity  {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 属性id
     */
    @ApiModelProperty(value = "属性id")
    private Long attributeId;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String valName;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private Long pid;

    /**
     * 本体id
     */
    @ApiModelProperty(value = "本体id")
    private Long ontoId;

    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    private Long relationId;

    private Long schemaId;
}
