package com.sinosoft.ie.booster.yearbook.business.service.impl;

  import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.service.ElectronicReadService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.FileDirectoryService;
import com.sinosoft.ie.booster.yearbook.business.service.YearbookService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;
import com.sinosoft.ie.booster.yearbook.util.CustomRenderListener;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ElectronicReadServiceImpl  implements ElectronicReadService {

	@Resource
	private EventService eventService;
	@Resource
	private YearbookService yearbookService;
	@Resource
	private ProjectConfig projectConfig;
	@Resource
	private FileDirectoryService fileDirectoryService;
	@Resource
	private LibEntityService libEntityService;
	
	@Override
	public R generateHighlight(EventPagination eventPagination) throws IOException {
		Map<String, Object> map =  new HashedMap<String, Object>();
		//查询所在年鉴
		EventEntity entity = eventService.getById(eventPagination.getId());
		YearbookEntity  bookEntity = 	yearbookService.getById(entity.getFid());
		
		
		//当前事件所在地pdf文件名称
		String pdfname = bookEntity.getNewName().replace(".pdf","")+"_"+ entity.getPageNum()+".pdf";
		//当前事件所在地pdf文件全路径
		String pdfPath = projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + pdfname;
		//高亮后的文件路径
		String hf_name = IdUtil.simpleUUID()+".pdf";
		String h_outpath=projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + hf_name;
		log.info("高亮后的文件路径-->"+h_outpath);
		
		String argv3 =  entity.getTitle();;
		if(argv3.indexOf("\"")==0) {
			argv3 = "\\"+argv3;
		}
		argv3 = argv3.replace(" ", "@@@").replace("　", "===");
		//关键词高亮
		String argv4 = eventPagination.getTag();
		if(StringUtils.isEmpty(argv4)) {
			argv4 = "null_tag";
		}else {
			argv4 = argv4.replace(" ", "@@@").replace("　", "===");
		}
		String  useDir = System.getProperty("user.dir");
		String cmd = "python   "+useDir+File.separator+"Highlight.py  "+pdfPath+" "+h_outpath+" "+argv3+" "+argv4 ;
		log.info(cmd);
		try {
			
			Process process = Runtime.getRuntime().exec(cmd);
			int exitCode = process .waitFor();
			
		} catch (Exception e) {
			return R.failed("文件高亮失败！");
		}
		map.put("pdf", hf_name);
//		Float h = computHigh(h_outpath, entity.getTitle());
//		map.put("height", h);
		return R.ok(map);
	}
	
	
	@Override
	public R highlightContext(Integer year, Integer pageNumber,String name) throws IOException {
		
		Map<String, Object> map =  new HashedMap<>();
		QueryWrapper<YearbookEntity>  query = new QueryWrapper<YearbookEntity>();
		query.eq("book_year", year);
		List<YearbookEntity>  list = yearbookService.list(query);
		if(list.size()==0) {
			throw new DataException("未查询到相关年鉴");
		}
		map.put("book_id", list.get(0).getId());
		map.put("book_page_count", list.get(0).getPageNum());
		
		String[] tags= name.split(";");

		//查询所在年鉴
		YearbookEntity  bookEntity = 	list.get(0);
		//当前事件所在地pdf文件名称
		String pdfname = bookEntity.getNewName().replace(".pdf","")+"_"+ pageNumber+".pdf";
		//当前事件所在地pdf文件全路径
		String pdfPath = projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + pdfname;
		//高亮后的文件路径
		String hf_name = IdUtil.simpleUUID()+".pdf";
		String h_outpath=projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + hf_name;
		log.info("高亮后的文件路径-->"+h_outpath);
		String  useDir = System.getProperty("user.dir");

		StringBuffer sb = new StringBuffer();
		
		if(name.indexOf("\"")==0) {
			sb.append("\\");
		}
		name = name.replace(" ", "@@@").replace("　", "===");
		sb.append(name);
		if(name.length()==2) {
			String a =  name.substring(0,1);
			String b =  name.substring(1,2);
			sb.append(";").append(a).append("@@@").append(b).append(";").append(a).append("@@@@@@").append(b).append(";")
			.append(a).append("===").append(b);
		}
		
		String argv4 = "null_tag";
		String cmd = "python  "+useDir+File.separator+"Highlight.py  "+pdfPath+" "+h_outpath+" "+sb.toString()+" "+argv4 ;
		log.info(cmd);
		try {
			Process process = Runtime.getRuntime().exec(cmd);
			int exitCode = process .waitFor();
			if (exitCode != 0) {  
			    BufferedReader stdInput = new BufferedReader(new InputStreamReader(process.getInputStream()));  
			    BufferedReader stdError = new BufferedReader(new InputStreamReader(process.getErrorStream()));  
			  
			    StringBuffer si =  new StringBuffer();
			    StringBuffer se = new StringBuffer();
			    String s = "";
			    while ((s = stdInput.readLine()) != null) {  
			    	si.append(s);
			    }  
			    while ((s = stdError.readLine()) != null) {  
			       se.append(s);
			    }  
			    log.info("标准输出==>{}",si.toString());
			    log.info("error输出==>{}",se.toString());
			}
			
			
		} catch (Exception e) {
			return R.failed("文件高亮失败！");
		}
		log.debug("结果文件==>{}",hf_name);
		map.put("pdf", hf_name);
//		Float h = computHigh(h_outpath, tags[0]);
//		map.put("height", h);
		return R.ok(map);
	}
	
	
	public Float  computHigh(String paht,String txt) throws IOException {
		PdfReader reader = new PdfReader(paht);
		//新建一个PDF解析对象
		Float  ty =null;
		Float  hi = null;
		PdfReaderContentParser parser = new PdfReaderContentParser(reader);
		for(int i = 1;i <= reader.getNumberOfPages();i++){
			Rectangle rect =  reader .getPageSize(i);
			hi = rect.getHeight();
			//新建一个ImageRenderListener对象，该对象实现了RenderListener接口，作为处理PDF的主要类
			CustomRenderListener listener = new CustomRenderListener();
			listener.setKeyWord(txt);
			//解析PDF，并处理里面的文字
			parser.processContent(i, listener);
			//获取文字的矩形边框
			List<Rectangle2D.Float> rectText = listener.rectText;
			List<String> textList = listener.textList;
			List<Float> listY = listener.listY;			
			
			int list_idx=0;
			int idx = 0;
			for (int j = 0; j < textList.size(); j++) {
				String t = textList.get(j);
				if(t.equals(txt.substring(idx,idx+1))) {
					idx++;
					if(idx==txt.length()) {
						list_idx = j;
						break;
					}
				}else {
					idx =0;
					if(t.equals(txt.substring(idx,idx+1))) {
						idx++;
						if(idx==txt.length()) {
							list_idx = j;
							break;
						}
					}
				}
			}
			ty = listY.get(list_idx);
		}
		return ty/hi;
	}


	@Override
	public R highlightContext(Long id, String names) throws IOException {
		Map<String, Object> map =  new HashedMap<>();
		YearbookEntity  bookEntity = yearbookService.getById(id);
		if(bookEntity==null) {
			throw new DataException("未查询到相关年鉴");
		}
		//当前事件所在地pdf文件全路径
		String pdfPath = projectConfig.getUploadPath()+ bookEntity.getPath();
		//高亮后的文件路径
		String hf_name = IdUtil.simpleUUID()+".pdf";
		String h_outpath=projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + hf_name;
		log.info("高亮后的文件路径-->"+h_outpath);
		
		String  useDir = System.getProperty("user.dir");
		String cmd = "python   "+useDir+File.separator+"Highlight.py  "+pdfPath+" "+h_outpath+" null_tag  "+names ;
		log.info(cmd);
		try {
			Process process = Runtime.getRuntime().exec(cmd);
			int exitCode = process .waitFor();
		} catch (Exception e) {
			return R.failed("文件高亮失败！");
		}
		map.put("pdf", hf_name);
		
		return R.ok(map);
	}


	@Override
	public R esTohighlight(Integer year, Integer pageNumber, Long  entity_id) throws IOException {
		
		Map<String, Object> map =  new HashedMap<>();
		QueryWrapper<YearbookEntity>  query = new QueryWrapper<YearbookEntity>();
		query.eq("book_year", year);
		List<YearbookEntity>  list = yearbookService.list(query);
		if(list.size()==0) {
			throw new DataException("未查询到相关年鉴");
		}
		map.put("book_id", list.get(0).getId());
		map.put("book_page_count", list.get(0).getPageNum());
		//查询所在年鉴
		YearbookEntity  bookEntity = 	list.get(0);
		//当前事件所在地pdf文件名称
		String pdfname = bookEntity.getNewName().replace(".pdf","")+"_"+ pageNumber+".pdf";
		//当前事件所在地pdf文件全路径
		String pdfPath = projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + pdfname;
		//高亮后的文件路径
		String hf_name = IdUtil.simpleUUID()+".pdf";
		String h_outpath=projectConfig.getUploadPath()+File.separator+ProjectConstant.pdf_prefix + hf_name;
		log.info("高亮后的文件路径-->"+h_outpath);
		String  useDir = System.getProperty("user.dir");
		
		LibEntityEntity entity =  libEntityService.getByEntityId(entity_id);
		String argv3 =  entity.getName();
		
		if(argv3.indexOf("\"")==0) {
			argv3 = "\\"+argv3;
		}
		argv3 = argv3.replace(" ", "@@@").replace("　", "===");
		//关键词高亮
		String argv4 = "";
		if(StringUtils.isEmpty(argv4)) {
			argv4 = "null_tag";
		}else {
			argv4 = argv4.replace(" ", "@@@").replace("　", "===");
		}
		String cmd = "python   "+useDir+File.separator+"Highlight.py  "+pdfPath+" "+h_outpath+" "+argv3+" "+argv4 ;
		log.info(cmd);
		try {
			Process process = Runtime.getRuntime().exec(cmd);
			int exitCode = process .waitFor();
		} catch (Exception e) {
			return R.failed("文件高亮失败！");
		}
		map.put("pdf", hf_name);
		return R.ok(map);
		
	}
	

}
