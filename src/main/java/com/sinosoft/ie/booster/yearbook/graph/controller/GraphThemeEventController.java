package com.sinosoft.ie.booster.yearbook.graph.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventPagination;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventCrForm;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventInfoVO;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventListVO;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventUpForm;
import com.sinosoft.ie.booster.yearbook.graph.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphThemeEventService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
 *
 * graph_theme_event
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 16:14:57
 */
@Slf4j
@RestController
@Api(tags = "graph_theme_event")
@RequestMapping("/GraphThemeEvent")
public class GraphThemeEventController {
    @Autowired
    private GraphThemeEventService graphThemeEventService;


    /**
     * 列表
     *
     * @param graphThemeEventPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<GraphThemeEventListVO>> list(GraphThemeEventPagination graphThemeEventPagination)throws IOException{
        List<GraphThemeEventEntity> list= graphThemeEventService.getList(graphThemeEventPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(GraphThemeEventEntity entity:list){
            }
        List<GraphThemeEventListVO> listVO=JsonUtil.getJsonToList(list,GraphThemeEventListVO.class);
        PageListVO<GraphThemeEventListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(graphThemeEventPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param graphThemeEventCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid GraphThemeEventCrForm graphThemeEventCrForm) throws DataException {
        GraphThemeEventEntity entity=JsonUtil.getJsonToBean(graphThemeEventCrForm, GraphThemeEventEntity.class);
        graphThemeEventService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<GraphThemeEventInfoVO> info(@PathVariable("id") Long id){
        GraphThemeEventEntity entity= graphThemeEventService.getInfo(id);
        GraphThemeEventInfoVO vo=JsonUtil.getJsonToBean(entity, GraphThemeEventInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid GraphThemeEventUpForm graphThemeEventUpForm) throws DataException {
        GraphThemeEventEntity entity= graphThemeEventService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(graphThemeEventUpForm, GraphThemeEventEntity.class);
            graphThemeEventService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        GraphThemeEventEntity entity= graphThemeEventService.getInfo(id);
        if(entity!=null){
            graphThemeEventService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
