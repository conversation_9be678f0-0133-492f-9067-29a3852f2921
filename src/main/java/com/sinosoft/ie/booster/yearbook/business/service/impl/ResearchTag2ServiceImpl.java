package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRules2Entity;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTag2Entity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTag2Mapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2CrForm;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2Pagination;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRules2Service;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTag2Service;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 *
 * research_tag2 版本： V1.0.0 作者： booster开发平台组 日期： 2023-02-20 13:38:22
 */
@Service
public class ResearchTag2ServiceImpl extends ServiceImpl<ResearchTag2Mapper, ResearchTag2Entity>
		implements ResearchTag2Service {

	@Autowired
	private EventMapper eventMapper;

	@Autowired
	private ResearchTagMapper researchTagMapper;
	@Resource
	private ResearchTag2Mapper researchTag2Mapper;
	@Resource
	private LibEntityMapper libEntityMapper;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	@Autowired
	private ConverRules2Service converRules2Service;

	@Override
	public List<ResearchTag2Entity> getList(ResearchTag2Pagination researchTag2Pagination) {
		QueryWrapper<ResearchTag2Entity> queryWrapper = new QueryWrapper<>();
		// 关键字（账户、姓名、手机）
		if (!"null".equals(String.valueOf(researchTag2Pagination.getId()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTag2Entity::getId, researchTag2Pagination.getId()));
		}

		if (!"null".equals(String.valueOf(researchTag2Pagination.getName()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTag2Entity::getName, researchTag2Pagination.getName()));
		}

		if (!"null".equals(String.valueOf(researchTag2Pagination.getTagGroup()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTag2Entity::getTagGroup, researchTag2Pagination.getTagGroup()));
		}

		if (!"null".equals(String.valueOf(researchTag2Pagination.getTagType()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTag2Entity::getTagType, researchTag2Pagination.getTagType()));
		}

		// 排序
		if (StrUtil.isEmpty(researchTag2Pagination.getSidx())) {
			queryWrapper.lambda().orderByDesc(ResearchTag2Entity::getId);
		} else {
			queryWrapper = "asc".equals(researchTag2Pagination.getSort().toLowerCase())
					? queryWrapper.orderByAsc(researchTag2Pagination.getSidx())
					: queryWrapper.orderByDesc(researchTag2Pagination.getSidx());
		}
		Page<ResearchTag2Entity> page = new Page<>(researchTag2Pagination.getCurrentPage(),
				researchTag2Pagination.getPageSize());
		IPage<ResearchTag2Entity> userIPage = this.page(page, queryWrapper);
		return researchTag2Pagination.setData(userIPage.getRecords(), userIPage.getTotal());
	}

	@Override
	public List<ResearchTag2Entity> getTypeList(ResearchTag2Pagination researchTag2Pagination, String dataType) {
		QueryWrapper<ResearchTag2Entity> queryWrapper = new QueryWrapper<>();
		// 关键字（账户、姓名、手机）
		if (!"null".equals(String.valueOf(researchTag2Pagination.getId()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTag2Entity::getId, researchTag2Pagination.getId()));
		}

		if (!"null".equals(String.valueOf(researchTag2Pagination.getName()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTag2Entity::getName, researchTag2Pagination.getName()));
		}

		if (!"null".equals(String.valueOf(researchTag2Pagination.getTagGroup()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTag2Entity::getTagGroup, researchTag2Pagination.getTagGroup()));
		}

		if (!"null".equals(String.valueOf(researchTag2Pagination.getTagType()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTag2Entity::getTagType, researchTag2Pagination.getTagType()));
		}

		// 排序
		if (StrUtil.isEmpty(researchTag2Pagination.getSidx())) {
			queryWrapper.lambda().orderByDesc(ResearchTag2Entity::getId);
		} else {
			queryWrapper = "asc".equals(researchTag2Pagination.getSort().toLowerCase())
					? queryWrapper.orderByAsc(researchTag2Pagination.getSidx())
					: queryWrapper.orderByDesc(researchTag2Pagination.getSidx());
		}
		if ("0".equals(dataType)) {
			Page<ResearchTag2Entity> page = new Page<>(researchTag2Pagination.getCurrentPage(),
					researchTag2Pagination.getPageSize());
			IPage<ResearchTag2Entity> userIPage = this.page(page, queryWrapper);
			return researchTag2Pagination.setData(userIPage.getRecords(), userIPage.getTotal());
		} else {
			return this.list(queryWrapper);
		}
	}

	@Override
	public ResearchTag2Entity getInfo(Long id) {
		QueryWrapper<ResearchTag2Entity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ResearchTag2Entity::getId, id);
		return this.getOne(queryWrapper);
	}

	@Override
	public void create(ResearchTag2Entity entity) {
		this.save(entity);
	}

	@Override
	public boolean update(Long id, ResearchTag2Entity entity) {
		entity.setId(id);
		return this.updateById(entity);
	}

	@Override
	public void delete(ResearchTag2Entity entity) {
		if (entity != null) {
			this.removeById(entity.getId());
		}
	}
	// 子表方法

	@Override
	public void createList(ResearchTag2CrForm researchTag2CrForm) {
		if (researchTag2CrForm == null) {
			return;
		}
		List<ResearchTag2Entity> researchTag2EntityList = new ArrayList<>();
		List<MateAttributeValueEntity> attrVal = researchTag2CrForm.getAttrVal();
		for (MateAttributeValueEntity mateAttributeValue : attrVal) {
			ResearchTag2Entity researchTag2 = new ResearchTag2Entity();
			researchTag2.setTagGroup(researchTag2CrForm.getTagGroup());
			researchTag2.setTagType(researchTag2CrForm.getTagType());
			researchTag2.setName(mateAttributeValue.getValName());
			researchTag2.setOntoId(researchTag2CrForm.getOntoId());
			researchTag2.setAttrCategory(researchTag2CrForm.getAttrCategory());
			researchTag2.setAttrValId(mateAttributeValue.getId());
			researchTag2.setTagAttr(researchTag2CrForm.getTagAttr());
			researchTag2EntityList.add(researchTag2);
		}
		this.saveBatch(researchTag2EntityList);
	}

	@Override
	public R<Map<String, Object>> showTree() {
		List<ResearchTag2Entity> allList = this.list();
		Map<String, Object> collectMap = new HashMap<>();

		Map<String, List<ResearchTag2Entity>> collect = allList.stream()
				.collect(Collectors.groupingBy(ResearchTag2Entity::getTagType));
		collect.forEach((k, v) -> {
			collectMap.put(k, v.stream().collect(Collectors.groupingBy(ResearchTag2Entity::getTagGroup)));
		});
		return R.ok(collectMap);
	}

	@Async("taskExecutor")
	@Transactional
	@Override
	public R event2tag2() {
		QueryWrapper<EventEntity> eventQuery = new QueryWrapper<>();
		eventQuery.select("id", "political_work", "security_manage", "comprehensive_coordination", "scientific_manage",
				"decision_making", "scientific_strength", "scientific_organization", "scientific_subject",
				"scientific_result", "scientific_funding");
		List<EventEntity> eventList = eventMapper.selectList(eventQuery);
		for (EventEntity event : eventList) {
			QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
			libEntityWrapper.eq("name", event.getTitle());
			List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityWrapper);
			if (libEntityEntities.size() == 0) {
				continue;
			}
			eventGetTag2_2(event, libEntityEntities.get(0));
			eventMapper.updateById(event);
			log.debug("当前事件的tag2--->" + event.getTag2());
		}
		return R.ok();
	}

	@Override
	public void eventGetTag2(EventEntity event) {
		Set<String> tag2s = new HashSet<>();
		List<ResearchTag2Entity> tag2List = this.list();
		for (ResearchTag2Entity researchTag2 : tag2List) {
			String entitysql = " data->'$." + researchTag2.getAttrCategory() + "' like '%" + researchTag2.getAttrValId()
					+ "%' ";
			QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
			libEntityWrapper.eq("onto_id", researchTag2.getOntoId());
			libEntityWrapper.apply(entitysql);
			List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityWrapper);
			if (libEntityEntities.size() == 0) {
				continue;
			}
			boolean isHave = false;
			for (LibEntityEntity libEntity : libEntityEntities) {
				if (researchTag2.getTagAttr().equals("political_work")
						&& !StringUtils.isEmpty(event.getPoliticalWork())) {
					if ((";" + event.getPoliticalWork().replace("；", ";")).contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("security_manage")
						&& !StringUtils.isEmpty(event.getSecurityManage())) {
					if ((";" + event.getSecurityManage().replace("；", ";")).contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("comprehensive_coordination")
						&& !StringUtils.isEmpty(event.getComprehensiveCoordination())) {
					if ((";" + event.getComprehensiveCoordination().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("scientific_manage")
						&& !StringUtils.isEmpty(event.getScientificManage())) {
					if ((";" + event.getScientificManage().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("decision_making")
						&& !StringUtils.isEmpty(event.getDecisionMaking())) {
					if ((";" + event.getDecisionMaking().replace("；", ";")).contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("scientific_strength")
						&& !StringUtils.isEmpty(event.getScientificStrength())) {
					if ((";" + event.getScientificStrength().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("scientific_organization")
						&& !StringUtils.isEmpty(event.getScientificOrganization())) {
					if ((";" + event.getScientificOrganization().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("scientific_subject")
						&& !StringUtils.isEmpty(event.getScientificSubject())) {
					if ((";" + event.getScientificSubject().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("scientific_result")
						&& !StringUtils.isEmpty(event.getScientificResult())) {
					if ((";" + event.getScientificResult().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
				if (researchTag2.getTagAttr().equals("scientific_funding")
						&& !StringUtils.isEmpty(event.getScientificFunding())) {
					if ((";" + event.getScientificFunding().replace("；", ";"))
							.contains(";" + libEntity.getName() + ";")) {
						isHave = true;
					}
				}
			}
			if (isHave) {
				tag2s.add(researchTag2.getName());
			}
		}

		// 批示字段
		String inst = event.getInstructions();
		if (!StringUtils.isEmpty(inst)) {
			String[] insts = inst.split(";");
			for (int i = 0; i < insts.length; i++) {
				QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
				libEntityWrapper.eq("name", insts[i]);
				List<LibEntityEntity> entList = libEntityMapper.selectList(libEntityWrapper);
				if (entList.size() != 0) {
					String data = entList.get(0).getData();
					if (ObjectUtil.isEmpty(data)) {
						continue;
					}
					Map<String, Object> dataMap = JsonUtil.stringToMap(data);
					Object value = dataMap.get("category_id");
					if (ObjectUtil.isEmpty(value)) {
						continue;
					}
					if (value instanceof Collection) {
						for (Object obj : (JSONArray) value) {
							Long iid = null;
							if (obj instanceof Long) {
								iid = (Long) obj;
							}
							MateAttributeValueEntity valEntity = mateAttributeValueService.getById(iid);
							if (valEntity != null) {
								tag2s.add(valEntity.getValName());
							}
						}
					} else {
						Long iid = (Long) value;
						MateAttributeValueEntity valEntity = mateAttributeValueService.getById(iid);
						if (valEntity != null) {
							tag2s.add(valEntity.getValName());
						}
					}

				}
			}
		}

		String tag2 = joinSet(tag2s);
		event.setTag2(tag2);
	}

	public String joinSet(Set<String> names) {
		StringBuffer sb = new StringBuffer();
		for (String string : names) {
			sb.append(string).append(";");
		}
		return sb.toString();
	}

	@Override
	public void eventGetTag2_2(EventEntity event, LibEntityEntity libEntityEntity) {
		Set<String> tag2s = new HashSet<>();
		List<ResearchTag2Entity> tag2List = this.list();
		for (ResearchTag2Entity researchTag2 : tag2List) {
			String entitysql = " data->'$." + researchTag2.getAttrCategory() + "' like '%" + researchTag2.getAttrValId()
					+ "%' ";
			QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
			libEntityWrapper.eq("onto_id", researchTag2.getOntoId());
			libEntityWrapper.apply(entitysql);
			boolean isHave = false;

			if (researchTag2.getTagAttr().equals("political_work") && !StringUtils.isEmpty(event.getPoliticalWork())) {
				if (event.getPoliticalWork().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("security_manage")
					&& !StringUtils.isEmpty(event.getSecurityManage())) {
				if (event.getSecurityManage().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("comprehensive_coordination")
					&& !StringUtils.isEmpty(event.getComprehensiveCoordination())) {
				if (event.getComprehensiveCoordination().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("scientific_manage")
					&& !StringUtils.isEmpty(event.getScientificManage())) {
				if (event.getScientificManage().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("decision_making")
					&& !StringUtils.isEmpty(event.getDecisionMaking())) {
				if (event.getDecisionMaking().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("scientific_strength")
					&& !StringUtils.isEmpty(event.getScientificStrength())) {
				if (event.getScientificStrength().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("scientific_organization")
					&& !StringUtils.isEmpty(event.getScientificOrganization())) {
				if (event.getScientificOrganization().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("scientific_subject")
					&& !StringUtils.isEmpty(event.getScientificSubject())) {
				if (event.getScientificSubject().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("scientific_result")
					&& !StringUtils.isEmpty(event.getScientificResult())) {
				if (event.getScientificResult().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (researchTag2.getTagAttr().equals("scientific_funding")
					&& !StringUtils.isEmpty(event.getScientificFunding())) {
				if (event.getScientificFunding().contains(researchTag2.getName())) {
					isHave = true;
				}
			}
			if (isHave == true) {
				tag2s.add(researchTag2.getName());
			}
		}
		// 批示字段
		String inst = event.getInstructions();
		if (!StringUtils.isEmpty(inst)) {
			String[] insts = inst.split(";");
			for (int i = 0; i < insts.length; i++) {
				QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
				libEntityWrapper.eq("name", insts[i]);
				List<LibEntityEntity> entList = libEntityMapper.selectList(libEntityWrapper);
				if (entList.size() != 0) {
					String data = entList.get(0).getData();
					if (ObjectUtil.isEmpty(data)) {
						continue;
					}
					Map<String, Object> dataMap = JsonUtil.stringToMap(data);
					Object value = dataMap.get("category_id");
					if (ObjectUtil.isEmpty(value)) {
						continue;
					}
					if (value instanceof Collection) {
						for (Object obj : (JSONArray) value) {
							Long iid = null;
							if (obj instanceof Long) {
								iid = (Long) obj;
							}
							MateAttributeValueEntity valEntity = mateAttributeValueService.getById(iid);
							if (valEntity != null) {
								tag2s.add(valEntity.getValName());
							}
						}
					} else {
						Long iid = (Long) value;
						MateAttributeValueEntity valEntity = mateAttributeValueService.getById(iid);
						if (valEntity != null) {
							tag2s.add(valEntity.getValName());
						}
					}

				}
			}
		}

		String tag2 = joinSet(tag2s);
		event.setTag2(tag2);
	}

	Map<String, List<ConverRules2Entity>> rulesMap2 = new HashedMap();
	Map<String, List<ResearchTag2Entity>> tag2Map2 = new HashedMap();
	List<ResearchTag2Entity> tag2List = new ArrayList(); 
	
	public Set<String> event_tag2_3( String attrName, Map dataMap) {
		Set<String> tag2s = new HashSet<>();
		if (rulesMap2.size() == 0) {
			List<ConverRules2Entity> rulesList2 = converRules2Service.list();
			rulesMap2 = rulesList2.stream().collect(Collectors.groupingBy(t -> t.getType()));
		}
		if(tag2Map2.size()==0) {
			tag2List = this.list();
			tag2Map2 = tag2List.stream().collect(Collectors.groupingBy(t -> t.getTagAttr()));
		}
		
		//当前【要素】的tag2标签
		List<ResearchTag2Entity>   attrTagList = tag2Map2.get(attrName);
		if(attrTagList==null ) {
			return  tag2s;
		}
		List<MateAttributeEntity> list = OntoCache.getAttListByOntoId(YearbookConstant.MARK_EVENT_ONTO_ID);
		Map<String, MateAttributeEntity> map = list.stream()
				.collect(Collectors.toMap(MateAttributeEntity::getNameEn, MateAttributeEntity -> MateAttributeEntity));
		List<ConverRules2Entity> rulelist = rulesMap2.get(attrName);
		if (rulelist == null) {
			return  tag2s;
		}
		for (int i = 0; i < rulelist.size(); i++) {
			String attr_name = rulelist.get(i).getAttrName();
			Object obj = dataMap.get(attr_name);
			if (obj == null) {
				continue;
			}
			MateAttributeEntity attr = map.get(attr_name);
			if (attr.getVals() == 1) {
				JSONArray jsarry = new JSONArray();
				jsarry.add(obj);
				obj = jsarry;
			}
			if (attr.getVals() == 2) {
				if (!(obj instanceof Collection)) {
					throw new DataException("【" + attr.getNameEn() + "】数据类型不对:" + obj);
				}
			}
			for (Object ob : (JSONArray) obj) {
				// 查询 该实体对象
				if (attr.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_SELECT)) {
					Long entId = (Long) ob;
					LibEntityEntity libEntityEntity = libEntityMapper.selectById(entId);
					if(libEntityEntity.getData()==null) {
						continue;
					}
					Map<String, Object> objectMap = JsonUtil.stringToMap(libEntityEntity.getData());
					for (int j = 0; j < attrTagList.size(); j++) {
						ResearchTag2Entity tag2ent =  attrTagList.get(j);
						//当前实体获取属性值
						Object  entVal =  objectMap.get(tag2ent.getAttrCategory());
						if(!ObjectUtil.isEmpty(entVal)) {
							//如果值是数组
							if(entVal instanceof Collection) {
								//如果数组中包含当前标签的id,则当前标签放入tag2中
								if(((Collection) entVal).contains(tag2ent.getAttrValId())) {
									tag2s.add(tag2ent.getName());
								}else {
									continue;
								}
							}else {
								//非数组
								Long entValId = (Long)entVal;
								if(entValId.equals(tag2ent.getAttrValId())) {
									tag2s.add(tag2ent.getName());
								}else {
									continue;
								}
							}
						}
					}
				}
				if (attr.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT)) {
					Long entId = (Long) ob;
					
					for (int j = 0; j < attrTagList.size(); j++) {
						ResearchTag2Entity tag2ent =  attrTagList.get(j);
						if(tag2ent.getAttrValId().equals(entId)) {
							tag2s.add(tag2ent.getName());
						}
					}
				}
//				if (attr.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT)) {
//				}
			}
		}
		return tag2s;
	}

}