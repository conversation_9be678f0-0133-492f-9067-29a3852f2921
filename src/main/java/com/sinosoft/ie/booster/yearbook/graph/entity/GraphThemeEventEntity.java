package com.sinosoft.ie.booster.yearbook.graph.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 16:14:57
 */
@Data
@TableName("graph_theme_event")
@ApiModel(value = "")
public class GraphThemeEventEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String theme;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id")
    private Long eventId;

    /**
     * 实体id
     */
    @ApiModelProperty(value = "实体id")
    private Long entityId;

    /**
     * 实体名称
     */
    @ApiModelProperty(value = "实体名称")
    private String entityName;

    /**
     * 本体id
     */
    @ApiModelProperty(value = "本体id")
    private Long ontoId;

    /**
     * 本体名称
     */
    @ApiModelProperty(value = "本体名称")
    private String ontoName;
}
