package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.StringUtils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.mark.cache.EventCache;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;

import cn.hutool.core.util.ObjectUtil;

/**
 * 用excel导入数据，将数据转为三元组数据的工具
 * <AUTHOR>
 *
 */
public class EventUtil {
	
	List<MarkEntityEntity>  markEntityList;
	List<MarkTripletEntity>  markTripletList;
	Map<String , MarkEntityEntity >   entityMap =  new HashMap<>();
	Long fid;
	Long sentId;
	String content;
	//当前事件的三元组实体
	MarkEntityEntity   currentEvent;
	
	public void addEvent(MarkEntityEntity ent) {
		this.currentEvent = ent;
	}
	
	/**
	 * 	/**
	 * 
	 * @param markEntityList	实体集合
	 * @param markTripletList	关系集合
	 * @param fid				文件id
	 * @param sentId			所在句子的id
	 * @param content			完整句子文本
	 * 
	 */
	public  EventUtil(List<MarkEntityEntity>  markEntityList,List<MarkTripletEntity>  markTripletList,Long fid,Long sentId,String content) {
		this.markEntityList=markEntityList;
		this.markTripletList = markTripletList;
		this.fid=fid;
		this.sentId=sentId;
		this.content=content;
	}
	
	//添加一般实体或属性
	public  void addEntityToList(Object obj,String attr_name) {
		String text = getObjectVal(obj);
		String cate ,labname,relname;
		
		
		if(StringUtils.isEmpty(text)) {
			return ;
		}
		List<String>  valList = new ArrayList<>();
		text = text.replace("：", ":");
		String []  tags =  text.split(";");
		for (int i = 0; i < tags.length; i++) {
			if(tags[i].contains(":")) {
				String [] ta = tags[i].split(":");
				valList.add(ta[1]);
			}else {
				valList.add(tags[i]);
			}
				
		}
		MateAttributeEntity attrr = EventCache.getAttrByName(attr_name);
		if(attrr.getVals()==2) {
			for (int i = 0; i < valList.size(); i++) {
				String val = valList.get(i);
				MarkEntityEntity entity = getEntity(val);
				entity.setId(IdWorker.getId());
				if(attrr.getType()==2) {
					entity.setLabType(1);
					entity.setTagname("实体");
					entity.setTagId(attrr.getOnto());
				}else  {
					entity.setLabType(2);
					entity.setTagname("属性");
					entity.setTagId(attrr.getId());
				}
				entity.setText(val);
				entity.setSentenceid(sentId);
				Integer start_idx = content.indexOf(val);
				entity.setStartindex(start_idx);
				entity.setEndindex(start_idx+val.length());
				markEntityList.add(entity);
				MarkTripletEntity markTripletEntity = new MarkTripletEntity();
				markTripletEntity.setTag1id(currentEvent.getId());
				markTripletEntity.setTag2id(entity.getId());
				markTripletEntity.setSentenceid(sentId);
				if(attrr.getType()==2) {
					//实体和实体 用【关系】表示
					MetaOntoRelationEntity relation = 	EventCache.getRelationById(attrr.getRelationId());
					markTripletEntity.setRelationid(relation.getId());
					markTripletEntity.setRelationname(relation.getName());
				}else  {
					markTripletEntity.setRelationid(attrr.getId());
					markTripletEntity.setRelationname(attrr.getNameCn());
				}
				markTripletList.add(markTripletEntity);
			}
		}
	}
	//添加 事件内容
	public  void addEntityContentToList() {
		MateAttributeEntity attrr = EventCache.getAttrByName("content");
		String tit = "【"+currentEvent.getText()+"】";
		String content_str =  content.replace( tit , "");
		MarkEntityEntity entity_content1 = new MarkEntityEntity();
		entity_content1.setId(IdWorker.getId());
		entity_content1.setLabType(2);
		entity_content1.setTagname("属性");
		entity_content1.setTagId(attrr.getId());
		entity_content1.setText(content_str.substring(0, 1));
		entity_content1.setSentenceid(sentId);
		Integer entity_content1_idx = tit.length();
		entity_content1.setStartindex(entity_content1_idx);
		entity_content1.setEndindex(entity_content1_idx+1);
		
		MarkEntityEntity entity_content2 = new MarkEntityEntity();
		entity_content2.setId(IdWorker.getId());
		entity_content2.setLabType(2);
		entity_content2.setTagname("属性");
		entity_content2.setTagId(attrr.getId());
		entity_content2.setSentenceid(sentId);
		Integer entity_content2_idx = content.lastIndexOf("。");
		entity_content2.setStartindex(entity_content2_idx);
		entity_content2.setEndindex(entity_content2_idx);
		entity_content2.setText(content.substring(entity_content2_idx,entity_content2_idx+1));
		markEntityList.add(entity_content1);
		markEntityList.add(entity_content2);
		
		MarkTripletEntity markTripletEntity1 = new MarkTripletEntity();
		markTripletEntity1.setTag1id(currentEvent.getId());
		markTripletEntity1.setTag2id(entity_content1.getId());
		markTripletEntity1.setSentenceid(sentId);
		markTripletEntity1.setRelationid(attrr.getId());
		markTripletEntity1.setRelationname(attrr.getNameCn());
		markTripletList.add(markTripletEntity1);
		MarkTripletEntity markTripletEntity2 = new MarkTripletEntity();
		markTripletEntity2.setTag1id(currentEvent.getId());
		markTripletEntity2.setTag2id(entity_content1.getId());
		markTripletEntity2.setSentenceid(sentId);
		markTripletEntity2.setRelationid(attrr.getId());
		markTripletEntity2.setRelationname(attrr.getNameCn());
		markTripletList.add(markTripletEntity2);
	}
	/**
	 * 添加属性值数据
	 */
	public  void addValEntityToList(String vals,String attr_name) {
		if(ObjectUtil.isEmpty(vals)) {
			return ;
		}
		MateAttributeEntity attrr = EventCache.getAttrByName(attr_name);
		List<MateAttributeValueEntity> valList = OntoCache.getAttrValListByAttrId(attrr.getId());
		String[] str_arry = vals.split(";");
		for (int i = 0; i < str_arry.length; i++) {
			for (int j = 0; j < valList.size(); j++) {
				if( valList.get(j).getValName().equals(str_arry[i])) {
					MarkEntityEntity entity = getEntity(str_arry[i]);
					entity.setId(IdWorker.getId());
					entity.setLabType(3);
					entity.setTagname("属性");
					entity.setTagId(attrr.getId());
					entity.setTagPid(valList.get(j).getId());
					entity.setText(str_arry[i]);
					entity.setSentenceid(sentId);
					entity.setStartindex(currentEvent.getStartindex());
					entity.setEndindex(currentEvent.getEndindex());
					markEntityList.add(entity);
					
					MarkTripletEntity markTripletEntity1 = new MarkTripletEntity();
					markTripletEntity1.setTag1id(currentEvent.getId());
					markTripletEntity1.setTag2id(entity.getId());
					markTripletEntity1.setSentenceid(sentId);
					markTripletEntity1.setRelationid(attrr.getId());
					markTripletEntity1.setRelationname(attrr.getNameCn());
					markTripletList.add(markTripletEntity1);
				}
			}
			
			
		}
	}
	
	public MarkEntityEntity  getEntity(String val) {
		MarkEntityEntity entity  =  entityMap.get(val);
		if(entity ==null) {
		  entity = new MarkEntityEntity();
		}
		return entity;
	}
	
	
	public List<MarkEntityEntity>  getEntitleList(){
		return this.markEntityList;
	}
	public List<MarkTripletEntity>  getRelation(){
		return this.markTripletList;
	}
	
	public String getObjectVal(Object obj) {
		String str = (String) obj;
		if (!StringUtils.isEmpty(str)) {
			str = str.replace("；", ";");
		} else {
			str = "";
		}
		return str;
	}
	
	/**********************实体关系*****************************************************/
	
	//当前事件实体的id
	Long eveEnt_id;
	//本体关系双键map
	MultiKeyMap<Object, Object> multiKey = new MultiKeyMap<>();
	
	List<MetaEntityRelationEntity>   entRelationList =  new ArrayList<MetaEntityRelationEntity>();
	public void putEveEnt_id(Long id ) {
		this.eveEnt_id = id;
	}
	public void  impOntoRelations(List<MetaOntoRelationEntity>  ontoRelationList ) {
		for (int i = 0; i < ontoRelationList.size(); i++) {
			MetaOntoRelationEntity rel = ontoRelationList.get(i);
			multiKey.put(rel.getOnto1Id(), rel.getOnto2Id(),rel);
		}
	}
	//添加实体关系
	public MetaEntityRelationEntity  createRelation(LibEntityEntity entity) {
		MetaEntityRelationEntity relation = new MetaEntityRelationEntity();
		relation.setOnto1Id(YearbookConstant.MARK_EVENT_ONTO_ID);
		relation.setOnto2Id(entity.getOntoId());
		relation.setEntity1Id(eveEnt_id);
		relation.setEntity2Id(entity.getId());
		relation.setEventid(eveEnt_id);
		MetaOntoRelationEntity ontoRelation = (MetaOntoRelationEntity)multiKey.get(YearbookConstant.MARK_EVENT_ONTO_ID,entity.getOntoId());
		if(ontoRelation==null) {
			return null;
		}
		relation.setOntoRelId(relation.getId());
		relation.setName(ontoRelation.getName());
		relation.setState(2);
		return relation;
	}
	
	public List<MetaEntityRelationEntity> getRelationList() {
		return entRelationList;
	}
	
}
