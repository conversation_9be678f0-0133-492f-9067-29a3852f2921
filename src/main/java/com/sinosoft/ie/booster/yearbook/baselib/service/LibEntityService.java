package com.sinosoft.ie.booster.yearbook.baselib.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibEntityListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibEntityPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationOut;

/**
 *
 * lib_entity
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 15:45:57
 */
public interface LibEntityService extends IService<LibEntityEntity> {

    List<LibEntityEntity> getList(LibEntityPagination libEntityPagination);

    List<LibEntityEntity> getTypeList(LibEntityPagination libEntityPagination,String dataType);



    LibEntityEntity getInfo(Long id);

    void delete(LibEntityEntity entity);

    void create(LibEntityEntity entity);

    boolean update( Long id, LibEntityEntity entity);

    void deleteids(String ids);

    //子表方法
    PageListVO<LibEntityListVO> getEntityByonto(LibEntityPagination libEntityPagination);

    List<MetaEntityRelationOut> queryEntityRelationByEntityId(Long entityId,Long ontoId);

    List<LibEntityEntity> getEntityByOntoIdANDName(LibEntityPagination libEntityPagination);

    LibEntityEntity getByEntityId(Long id);

    LibEntityEntity createEntityS2L(LibEntityEntity entity);

    LibEntityEntity getListId2Name(LibEntityEntity one);
    
    //根据本体id查询 实体name，id,不查询data
    List<LibEntityEntity>  getList(Long ontoId);

    boolean checkEntity(LibEntityEntity entity);
    
    void updataEntityAndRealation(LibEntityEntity entity);

    void mergeEntity(Long ontoId, Long corpusId);
}
