package com.sinosoft.ie.booster.yearbook.business.model.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * Event模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-21 18:11:42
 */
@Data
@ApiModel
public class EventListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;


    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 地点
     */
    @ApiModelProperty(value = "地点")
    private String venue;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 人物
     */
    @ApiModelProperty(value = "人物")
    private String persons;

    /**
     * 物品
     */
    @ApiModelProperty(value = "物品")
    private String articles;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String organisation;

    /**
     * 触发词
     */
    @ApiModelProperty(value = "触发词")
    private String triggerss;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String multiRelations;

    /**
     * 次数
     */
    @ApiModelProperty(value = "次数")
    private String frequency;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String images;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String category;

    /**
     * 事件文本
     */
    @ApiModelProperty(value = "事件文本")
    private String content;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String note;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgid;
    
    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String tag;

    /**
     * 成果物
     */
    @ApiModelProperty(value = "成果物")
    private String achievements;

    /**
     * 会议
     */
    @ApiModelProperty(value = "会议")
    private String meeting;

    /**
     * 年鉴id
     */
    @ApiModelProperty(value = "年鉴id")
    private String fid;
    
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String theme;

	/**
	 * 政治工作
	 */
	@ApiModelProperty(value = "政治工作")
	private String politicalWork;

	/**
	 * 管理保障
	 */
	@ApiModelProperty(value = "管理保障")
	private String securityManage;

	/**
	 * 综合协调
	 */
	@ApiModelProperty(value = "综合协调")
	private String comprehensiveCoordination;

	/**
	 * 科研管理
	 */
	@ApiModelProperty(value = "科研管理")
	private String scientificManage;

	/**
	 * 领导决策
	 */
	@ApiModelProperty(value = "领导决策")
	private String decisionMaking;

	/**
	 * 科研力量
	 */
	@ApiModelProperty(value = "科研力量")
	private String scientificStrength;

	/**
	 * 科研组织
	 */
	@ApiModelProperty(value = "科研组织")
	private String scientificOrganization;

	/**
	 * 科研课题
	 */
	@ApiModelProperty(value = "科研课题")
	private String scientificSubject;

	/**
	 * 科研成果
	 */
	@ApiModelProperty(value = "科研成果")
	private String scientificResult;

	/**
	 * 权重
	 */
	@ApiModelProperty(value = "权重")
	private Integer weight;

    /**
     * 科研成果
     */
    @ApiModelProperty(value = "科研经费")
    private String scientificFunding;
    
	/**
	 * 奖项
	 */
	@ApiModelProperty(value = "奖项")
	private String prize	;
}