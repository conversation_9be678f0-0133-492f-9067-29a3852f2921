package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 知识运维-导入导出-任务表
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-08 15:13:22
 */
@Data
@TableName("file_import_job")
@ApiModel(value = "知识运维-导入导出-任务表")
public class FileImportJobEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    /**
     * 任务类型：1.实体导入；2实体关系导入；3同义导入
     */
    @ApiModelProperty(value = "任务类型：1.实体导入；2实体关系导入；3同义导入")
    private Integer jobType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 任务状态：1.正在导入；2导入成功；3导入失败
     */
    @ApiModelProperty(value = "任务状态：1.正在导入；2导入成功；3导入失败")
    private Integer jobState;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createUser;

    /**
     * 新文件名称
     */
    @ApiModelProperty(value = "新文件名称")
    private String fileNewName;
}
