package com.sinosoft.ie.booster.yearbook.business.controller;

import io.swagger.annotations.Api;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2Pagination;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2CrForm;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2InfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2ListVO;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2UpForm;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTag2Entity;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTag2Service;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
 *
 * research_tag2
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-20 13:38:22
 */
@Slf4j
@RestController
@Api(tags = "research_tag2")
@RequestMapping("/ResearchTag2")
public class ResearchTag2Controller {
    @Autowired
    private ResearchTag2Service researchTag2Service;


    /**
     * 列表
     *
     * @param researchTag2Pagination
     * @return
     */
    @GetMapping
    public R<PageListVO<ResearchTag2ListVO>> list(ResearchTag2Pagination researchTag2Pagination)throws IOException{
        List<ResearchTag2Entity> list= researchTag2Service.getList(researchTag2Pagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(ResearchTag2Entity entity:list){
            }
        List<ResearchTag2ListVO> listVO=JsonUtil.getJsonToList(list,ResearchTag2ListVO.class);
        PageListVO<ResearchTag2ListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(researchTag2Pagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param researchTag2CrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid ResearchTag2CrForm researchTag2CrForm) throws DataException {
        ResearchTag2Entity entity=JsonUtil.getJsonToBean(researchTag2CrForm, ResearchTag2Entity.class);
        researchTag2Service.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<ResearchTag2InfoVO> info(@PathVariable("id") Long id){
        ResearchTag2Entity entity= researchTag2Service.getInfo(id);
        ResearchTag2InfoVO vo=JsonUtil.getJsonToBean(entity, ResearchTag2InfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid ResearchTag2UpForm researchTag2UpForm) throws DataException {
        ResearchTag2Entity entity= researchTag2Service.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(researchTag2UpForm, ResearchTag2Entity.class);
            researchTag2Service.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        ResearchTag2Entity entity= researchTag2Service.getInfo(id);
        if(entity!=null){
            researchTag2Service.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 从页面创建-多个枚举值
     */
    @PostMapping("/createList")
    @Transactional
    public R<String> createList(@RequestBody @Valid ResearchTag2CrForm researchTag2CrForm) throws DataException {
        researchTag2Service.createList(researchTag2CrForm);
        return R.ok(null, "新建成功");
    }

    /**
     * 页面展示
     */
    @GetMapping("/showTree")
    public R<Map<String, Object>> showTree(){
        return researchTag2Service.showTree();
    }

    /**
     * 标签管理-tag2插入event
     */
    @GetMapping("/event2tag2")
    public R event2tag2(){
        return researchTag2Service.event2tag2();
    }
}
