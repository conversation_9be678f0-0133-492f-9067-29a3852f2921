package com.sinosoft.ie.booster.yearbook.business.model.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Event模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-21 18:11:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EventUpForm extends EventCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}