package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.GraphEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.GraphMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.GraphService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * graph
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-31 13:39:56
 */
@Service
public class GraphServiceImpl extends ServiceImpl<GraphMapper, GraphEntity> implements GraphService {


    @Override
    public List<GraphEntity> getList(GraphPagination graphPagination){
        QueryWrapper<GraphEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(graphPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getId,graphPagination.getId()));
        }

        if(!"null".equals(String.valueOf(graphPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getName,graphPagination.getName()));
        }

        if(!"null".equals(String.valueOf(graphPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getSchemaId,graphPagination.getSchemaId()));
        }

        if(!"null".equals(String.valueOf(graphPagination.getStatus()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getStatus,graphPagination.getStatus()));
        }

        //排序
        if(StrUtil.isEmpty(graphPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(GraphEntity::getId);
        }else{
            queryWrapper="asc".equals(graphPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(graphPagination.getSidx()):queryWrapper.orderByDesc(graphPagination.getSidx());
        }
        Page<GraphEntity> page=new Page<>(graphPagination.getCurrentPage(), graphPagination.getPageSize());
        IPage<GraphEntity> userIPage=this.page(page,queryWrapper);
        return graphPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<GraphEntity> getTypeList(GraphPagination graphPagination,String dataType){
        QueryWrapper<GraphEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(graphPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getId,graphPagination.getId()));
        }

        if(!"null".equals(String.valueOf(graphPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getName,graphPagination.getName()));
        }

        if(!"null".equals(String.valueOf(graphPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getSchemaId,graphPagination.getSchemaId()));
        }

        if(!"null".equals(String.valueOf(graphPagination.getStatus()))){
            queryWrapper.lambda().and(t->t.like(GraphEntity::getStatus,graphPagination.getStatus()));
        }

        //排序
        if(StrUtil.isEmpty(graphPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(GraphEntity::getId);
        }else{
            queryWrapper="asc".equals(graphPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(graphPagination.getSidx()):queryWrapper.orderByDesc(graphPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<GraphEntity> page=new Page<>(graphPagination.getCurrentPage(), graphPagination.getPageSize());
            IPage<GraphEntity> userIPage=this.page(page,queryWrapper);
            return graphPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public GraphEntity getInfo(Long id){
        QueryWrapper<GraphEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(GraphEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(GraphEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, GraphEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(GraphEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}