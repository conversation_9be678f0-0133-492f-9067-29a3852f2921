package com.sinosoft.ie.booster.yearbook.mark.cache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;

/**
 * 通过excel导入事件信息转成标注数据时用到的缓存
 * 
 * <AUTHOR>
 *
 */
public class EventCache {
	
	
	public static Map<String,MateAttributeEntity>  map = new HashMap<String,MateAttributeEntity>();
	public static Map<Long,MetaOntoRelationEntity>  relationMap = new HashMap<Long,MetaOntoRelationEntity>();
	
	public static  void setData(List<MateAttributeEntity> list ,List<MetaOntoRelationEntity> rlist) {
		map = list.stream().collect(Collectors.toMap(MateAttributeEntity::getNameEn, MateAttributeEntity->MateAttributeEntity));
		relationMap = rlist.stream().collect(Collectors.toMap(MetaOntoRelationEntity::getId, MetaOntoRelationEntity->MetaOntoRelationEntity));
	}

	
	public static MateAttributeEntity getAttrByName(String name) {
		return  map.get(name);
	}
	public static MetaOntoRelationEntity getRelationById(Long id) {
		return relationMap.get(id);
	}
	
}
