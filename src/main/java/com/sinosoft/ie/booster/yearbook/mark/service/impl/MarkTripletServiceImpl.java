package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkTripletMapper;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkTripletService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * mark_triplet
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-08 16:58:56
 */
@Service
public class MarkTripletServiceImpl extends ServiceImpl<MarkTripletMapper, MarkTripletEntity> implements MarkTripletService {


    @Override
    public List<MarkTripletEntity> getList(MarkTripletPagination markTripletPagination){
        QueryWrapper<MarkTripletEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markTripletPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getId,markTripletPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markTripletPagination.getTag1id()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getTag1id,markTripletPagination.getTag1id()));
        }

        if(!"null".equals(String.valueOf(markTripletPagination.getTag2id()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getTag2id,markTripletPagination.getTag2id()));
        }

        if(!"null".equals(String.valueOf(markTripletPagination.getRelationid()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getRelationid,markTripletPagination.getRelationid()));
        }

        //排序
        if(StrUtil.isEmpty(markTripletPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkTripletEntity::getId);
        }else{
            queryWrapper="asc".equals(markTripletPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markTripletPagination.getSidx()):queryWrapper.orderByDesc(markTripletPagination.getSidx());
        }
        Page<MarkTripletEntity> page=new Page<>(markTripletPagination.getCurrentPage(), markTripletPagination.getPageSize());
        IPage<MarkTripletEntity> userIPage=this.page(page,queryWrapper);
        return markTripletPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MarkTripletEntity> getTypeList(MarkTripletPagination markTripletPagination,String dataType){
        QueryWrapper<MarkTripletEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markTripletPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getId,markTripletPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markTripletPagination.getTag1id()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getTag1id,markTripletPagination.getTag1id()));
        }

        if(!"null".equals(String.valueOf(markTripletPagination.getTag2id()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getTag2id,markTripletPagination.getTag2id()));
        }

        if(!"null".equals(String.valueOf(markTripletPagination.getRelationid()))){
            queryWrapper.lambda().and(t->t.like(MarkTripletEntity::getRelationid,markTripletPagination.getRelationid()));
        }

        //排序
        if(StrUtil.isEmpty(markTripletPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkTripletEntity::getId);
        }else{
            queryWrapper="asc".equals(markTripletPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markTripletPagination.getSidx()):queryWrapper.orderByDesc(markTripletPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MarkTripletEntity> page=new Page<>(markTripletPagination.getCurrentPage(), markTripletPagination.getPageSize());
            IPage<MarkTripletEntity> userIPage=this.page(page,queryWrapper);
            return markTripletPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MarkTripletEntity getInfo(Long id){
        QueryWrapper<MarkTripletEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MarkTripletEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MarkTripletEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MarkTripletEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MarkTripletEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}