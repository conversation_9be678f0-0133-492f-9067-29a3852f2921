package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 实体类关系表	导入图数据库逻辑：	
 * 先处理state=3的，完成后处理state=1的数据	
 * 默认标注后插入的数据state=1	导入图数据库后state修改为2	如果state=3,则删除图数据库中的该记录		标注系统逻辑：	标注好的数据入库时先查询是否有该记录（ent1和ent2查询），如果没有则插入；	如果有判断关系名称是否一致，一致则不插入，不一致则修改查询出的数据state为3，插入当前数据
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-15 10:50:41
 */
@Data
@TableName("meta_entity_relation")
@ApiModel(value = "实体关系")
public class MetaEntityRelationEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 本体1的id
     */
    @ApiModelProperty(value = "本体1的id")
    private Long onto1Id;

    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的id")
    private Long onto2Id;

    /**
     * 关系中文名称
     */
    @ApiModelProperty(value = "关系中文名称")
    private String name;

    /**
     * 实体1
     */
    @ApiModelProperty(value = "实体1")
    private Long entity1Id;

    /**
     * 实体2
     */
    @ApiModelProperty(value = "实体2")
    private Long entity2Id;

    /**
     * 本体关系id
     */
    @ApiModelProperty(value = "本体关系id")
    private Long ontoRelId;

    /**
     * 1未导入2已导入3删除
     */
    @ApiModelProperty(value = "1未导入2已导入3删除")
    private Integer state;

    /**
     * 置信度
     */
    @ApiModelProperty(value = "置信度")
    private Integer confidence;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private String startTime;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "事件id")
    private Long eventid;
}
