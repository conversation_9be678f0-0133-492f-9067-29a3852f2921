package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 10:45:23
 */
@Data
@TableName("eve_theme2")
@ApiModel(value = "")
public class EveTheme2Entity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 1常态主题;2动态主题
     */
    @ApiModelProperty(value = "1常态主题;2动态主题")
    private Integer category1;

    /**
     * 分类2的名称
     */
    @ApiModelProperty(value = "分类2的名称")
    private String categoryName;
}
