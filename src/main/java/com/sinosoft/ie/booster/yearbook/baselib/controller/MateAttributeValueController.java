package com.sinosoft.ie.booster.yearbook.baselib.controller;

import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValuePaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValuePagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValueCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValueInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValueListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValueUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mate_attribute_value
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-16 17:52:49
 */
@Slf4j
@RestController
@Api(tags = "mate_attribute_value")
@RequestMapping("/MateAttributeValue")
public class MateAttributeValueController {
    @Autowired
    private MateAttributeValueService mateAttributeValueService;


    /**
     * 列表
     *
     * @param mateAttributeValuePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MateAttributeValueListVO>> list(MateAttributeValuePagination mateAttributeValuePagination)throws IOException{
        List<MateAttributeValueEntity> list= mateAttributeValueService.getList(mateAttributeValuePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MateAttributeValueEntity entity:list){
            }
        List<MateAttributeValueListVO> listVO=JsonUtil.getJsonToList(list,MateAttributeValueListVO.class);
        PageListVO<MateAttributeValueListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(mateAttributeValuePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param mateAttributeValueCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MateAttributeValueCrForm mateAttributeValueCrForm) throws DataException {
        MateAttributeValueEntity entity=JsonUtil.getJsonToBean(mateAttributeValueCrForm, MateAttributeValueEntity.class);
        mateAttributeValueService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MateAttributeValueInfoVO> info(@PathVariable("id") Long id){
        MateAttributeValueEntity entity= mateAttributeValueService.getInfo(id);
        MateAttributeValueInfoVO vo=JsonUtil.getJsonToBean(entity, MateAttributeValueInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MateAttributeValueUpForm mateAttributeValueUpForm) throws DataException {
        MateAttributeValueEntity entity= mateAttributeValueService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(mateAttributeValueUpForm, MateAttributeValueEntity.class);
            mateAttributeValueService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MateAttributeValueEntity entity= mateAttributeValueService.getInfo(id);
        if(entity!=null){
            mateAttributeValueService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }


    /**
     * 根据属性id查询数据
     * @param mateAttributeValuePagination
     * @return
     * @throws IOException
     */
    @GetMapping("/getListByAttrId")
    public R<PageListVO<MateAttributeValueListVO>> getListByAttrId(Long  attrId)throws IOException{
        List<MateAttributeValueEntity> list= mateAttributeValueService.getListByAttrId(attrId);
        //处理id字段转名称，若无需转或者为空可删除
        List<MateAttributeValueListVO> listVO=JsonUtil.getJsonToList(list,MateAttributeValueListVO.class);
        PageListVO<MateAttributeValueListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        return R.ok(vo);
    }

    @GetMapping("/getEntityInsertValue")
    public R<List<MateAttributeListVO>> getEntityInsertValue(Long ontoId){
        List<MateAttributeListVO> mateAttributeListVOS = mateAttributeValueService.getEntityInsertValue(ontoId);
        return R.ok(mateAttributeListVOS);
    }

    /**
     * 根据属性id查询数据
     */
    @GetMapping("/getListByAttrIdAndName")
    public R<PageListVO<MateAttributeValueListVO>> getListByAttrIdAndName(Long  attrId,String name){
        List<MateAttributeValueEntity> list= mateAttributeValueService.getListByAttrIdAndName(attrId,name);
        //处理id字段转名称，若无需转或者为空可删除
        List<MateAttributeValueListVO> listVO=JsonUtil.getJsonToList(list,MateAttributeValueListVO.class);
        PageListVO<MateAttributeValueListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        return R.ok(vo);
    }
}
