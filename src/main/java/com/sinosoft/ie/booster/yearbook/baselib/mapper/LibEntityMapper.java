package com.sinosoft.ie.booster.yearbook.baselib.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibEntityPaginationExportModel;

/**
 * 实体表数据通用查询
 * <AUTHOR>
 *
 */
@Mapper
public interface LibEntityMapper extends BaseMapper<LibEntityEntity> {

	@Select(value="select  * from lib_concept t where t.pid in (${ids})")
	public List<Object>  queryByWapper(String ids);

	@Select(value = "SELECT e.*,c.name ontoName FROM lib_entity e " +
			"LEFT JOIN lib_concept c ON c.id = e.onto_id ${ew.customSqlSegment}")
	IPage<LibEntityPaginationExportModel>   getEntityByonto(IPage page, @Param(Constants.WRAPPER) Wrapper<LibEntityEntity> queryWrapper);

	List<LibEntityEntity> getMergeEntity();

	List<LibEntityEntity> getSynonymLike(String synonym);
	
	List<LibEntityEntity>  getByNameOrSynonymLike(LibEntityEntity ent);

	List<LibEntityEntity>  orgGraphszs(Map map);

	List<Map> orgPersonList(Map map);
}
