package com.sinosoft.ie.booster.yearbook.database.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.database.entity.DataDatasetEntity;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetPagination;

import java.util.*;

/**
 *
 * data_dataset
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-06 15:00:08
 */
public interface DataDatasetService extends IService<DataDatasetEntity> {

    List<DataDatasetEntity> getList(DataDatasetPagination dataDatasetPagination);

    List<DataDatasetEntity> getTypeList(DataDatasetPagination dataDatasetPagination,String dataType);



    DataDatasetEntity getInfo(Long id);

    void delete(DataDatasetEntity entity);

    void create(DataDatasetEntity entity);

    boolean update( Long id, DataDatasetEntity entity);

    R chartPie(Long id);

    R tableCount(Long id);

//  子表方法
}
