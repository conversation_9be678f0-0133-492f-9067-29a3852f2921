package com.sinosoft.ie.booster.yearbook.business.model.evetheme2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * EveTheme2模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 10:45:23
 */
@Data
@ApiModel
public class EveTheme2CrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 1常态主题;2动态主题
     */
    @ApiModelProperty(value = "1常态主题;2动态主题")
    private Integer category1;

    /**
     * 分类2的名称
     */
    @ApiModelProperty(value = "分类2的名称")
    private String categoryName;
}