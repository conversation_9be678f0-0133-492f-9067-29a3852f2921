package com.sinosoft.ie.booster.yearbook.mark.model.markentity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MarkEntity模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-26 14:30:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkEntityUpForm extends MarkEntityCrForm{

}