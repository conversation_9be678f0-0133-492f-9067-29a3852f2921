package com.sinosoft.ie.booster.yearbook.database.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetPagination;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetCrForm;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetInfoVO;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetListVO;
import com.sinosoft.ie.booster.yearbook.database.model.datadataset.DataDatasetUpForm;
import com.sinosoft.ie.booster.yearbook.database.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.database.entity.DataDatasetEntity;
import com.sinosoft.ie.booster.yearbook.database.service.DataDatasetService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * data_dataset
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-06 15:00:08
 */
@Slf4j
@RestController
@Api(tags = "data_dataset")
@RequestMapping("/DataDataset")
public class DataDatasetController {
    @Autowired
    private DataDatasetService dataDatasetService;


    /**
     * 列表
     *
     * @param dataDatasetPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<DataDatasetListVO>> list(DataDatasetPagination dataDatasetPagination)throws IOException{
        List<DataDatasetEntity> list= dataDatasetService.getList(dataDatasetPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(DataDatasetEntity entity:list){
            }
        List<DataDatasetListVO> listVO=JsonUtil.getJsonToList(list,DataDatasetListVO.class);
        PageListVO<DataDatasetListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(dataDatasetPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param dataDatasetCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid DataDatasetCrForm dataDatasetCrForm) throws DataException {
        DataDatasetEntity entity=JsonUtil.getJsonToBean(dataDatasetCrForm, DataDatasetEntity.class);
        dataDatasetService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<DataDatasetInfoVO> info(@PathVariable("id") Long id){
        DataDatasetEntity entity= dataDatasetService.getInfo(id);
        DataDatasetInfoVO vo=JsonUtil.getJsonToBean(entity, DataDatasetInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid DataDatasetUpForm dataDatasetUpForm) throws DataException {
        DataDatasetEntity entity= dataDatasetService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(dataDatasetUpForm, DataDatasetEntity.class);
            dataDatasetService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        DataDatasetEntity entity= dataDatasetService.getInfo(id);
        if(entity!=null){
            dataDatasetService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 数据类型分布-饼图
     */
    @GetMapping("/chartPie")
    public R<Map<String,Long>> chartPie(Long id)throws IOException{
        return dataDatasetService.chartPie(id);
    }

    /**
     * 数据量统计-柱状图
     */
    @GetMapping("/tableCount")
    public R<Map<String,Object>> tableCount(Long id)throws IOException{
        return dataDatasetService.tableCount(id);
    }
}
