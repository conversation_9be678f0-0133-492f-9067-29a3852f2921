package com.sinosoft.ie.booster.yearbook.business.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;
import io.swagger.annotations.Api;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationPagination;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationListVO;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationUpForm;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import com.sinosoft.ie.booster.yearbook.business.service.OrganizationService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletException;
import javax.validation.Valid;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * organization
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:08:32
 */
@Slf4j
@RestController
@Api(tags = "organization")
@RequestMapping("/Organization")
public class OrganizationController {
    @Autowired
    private OrganizationService organizationService;
	@Autowired
	private ProjectConfig projectConfig;

    /**
     * 列表
     *
     * @param organizationPagination
     * @return
     */
    @GetMapping
    public R list(OrganizationPagination organizationPagination)throws IOException{
        System.out.println("执行了Organization");
        List<OrganizationEntity> list= organizationService.getList(organizationPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(OrganizationEntity entity:list){
            }
        List<OrganizationListVO> listVO=JsonUtil.getJsonToList(list,OrganizationListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(organizationPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

	/**
	 * 分级列表(tree)
	 *
	 * @param organizationPagination
	 * @return
	 */
	@GetMapping("/showOrgTree")
	public R showOrgTree(OrganizationPagination organizationPagination)throws IOException{
		List<OrganizationEntity> list= organizationService.showOrgTree(organizationPagination);
		//处理id字段转名称，若无需转或者为空可删除

		List<OrganizationListVO> listVO=JsonUtil.getJsonToList(list,OrganizationListVO.class);
		return R.ok(listVO);
	}

    /**
     * 创建
     *
     * @param organizationCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid OrganizationCrForm organizationCrForm) throws DataException {
        OrganizationEntity entity=JsonUtil.getJsonToBean(organizationCrForm, OrganizationEntity.class);
        organizationService.create(entity);
        return R.ok(null, "新建成功");
	}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<OrganizationInfoVO> info(@PathVariable("id") Long id){
        OrganizationEntity entity= organizationService.getInfo(id);
        OrganizationInfoVO vo=JsonUtil.getJsonToBean(entity, OrganizationInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid OrganizationUpForm organizationUpForm) throws DataException {
        OrganizationEntity entity= organizationService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(organizationUpForm, OrganizationEntity.class);
            organizationService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        OrganizationEntity entity= organizationService.getInfo(id);
        if(entity!=null){
            organizationService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    @GetMapping("/queryRotationPage")
    public R  queryRotationPage() {
    	List list = organizationService.queryRotationPage();
    	return R.ok(list);
    }
    
    @GetMapping("/queryTagPage")
    public R  queryTagPage() {
    	List list = organizationService.queryTagPage();
    	return R.ok(list);
    }

}
