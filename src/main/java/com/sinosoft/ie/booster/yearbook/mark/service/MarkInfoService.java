package com.sinosoft.ie.booster.yearbook.mark.service;

import java.util.List;
import java.util.Map;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeAndValueVo;

import cn.hutool.json.JSONObject;

public interface MarkInfoService {
	
	
	public  List<MateAttributeAndValueVo>  getPropertsList(Long id);
	
	Map<String,Object> getTagInfo(Long id);

	R insertTagInfo(Map<String,Object> map) throws DataException;
}
