package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookListVO;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookPagination;

import java.io.IOException;
import java.util.*;

/**
 *
 * yearbook
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-16 13:39:57
 */
public interface YearbookService extends IService<YearbookEntity> {

    List<YearbookEntity> getList(YearbookPagination yearbookPagination);

    List<YearbookEntity> getTypeList(YearbookPagination yearbookPagination,String dataType);



    YearbookEntity getInfo(Long id);

    void delete(YearbookEntity entity);

    void create(YearbookEntity entity) throws Exception;

    boolean update( Long id, YearbookEntity entity);
    
//  子表方法
	void previewPdf(String name) throws IOException;
	
	
	void  getCurrentPageTagAndTheme(YearbookListVO listVO);

    YearbookEntity getInfoFid(Long fid);
}
