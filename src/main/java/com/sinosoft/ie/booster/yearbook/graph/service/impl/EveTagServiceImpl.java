package com.sinosoft.ie.booster.yearbook.graph.service.impl;

import com.sinosoft.ie.booster.yearbook.graph.entity.EveTagEntity;
import com.sinosoft.ie.booster.yearbook.graph.mapper.EveTagMapper;
import com.sinosoft.ie.booster.yearbook.graph.service.EveTagService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * eve_tag
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-07-21 10:53:07
 */
@Service
public class EveTagServiceImpl extends ServiceImpl<EveTagMapper, EveTagEntity> implements EveTagService {


    @Override
    public List<EveTagEntity> getList(EveTagPagination eveTagPagination){
        QueryWrapper<EveTagEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(eveTagPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getId,eveTagPagination.getId()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getName,eveTagPagination.getName()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getCategory1()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getCategory1,eveTagPagination.getCategory1()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getCategory2()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getCategory2,eveTagPagination.getCategory2()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getIsdef()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getIsdef,eveTagPagination.getIsdef()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getPname()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getPname,eveTagPagination.getPname()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getSynopsis()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getSynopsis,eveTagPagination.getSynopsis()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getIslab()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getIslab,eveTagPagination.getIslab()));
        }

        //排序
        if(StrUtil.isEmpty(eveTagPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EveTagEntity::getId);
        }else{
            queryWrapper="asc".equals(eveTagPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(eveTagPagination.getSidx()):queryWrapper.orderByDesc(eveTagPagination.getSidx());
        }
        Page<EveTagEntity> page=new Page<>(eveTagPagination.getCurrentPage(), eveTagPagination.getPageSize());
        IPage<EveTagEntity> userIPage=this.page(page,queryWrapper);
        return eveTagPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<EveTagEntity> getTypeList(EveTagPagination eveTagPagination,String dataType){
        QueryWrapper<EveTagEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(eveTagPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getId,eveTagPagination.getId()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getName,eveTagPagination.getName()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getCategory1()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getCategory1,eveTagPagination.getCategory1()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getCategory2()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getCategory2,eveTagPagination.getCategory2()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getIsdef()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getIsdef,eveTagPagination.getIsdef()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getPname()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getPname,eveTagPagination.getPname()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getSynopsis()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getSynopsis,eveTagPagination.getSynopsis()));
        }

        if(!"null".equals(String.valueOf(eveTagPagination.getIslab()))){
            queryWrapper.lambda().and(t->t.like(EveTagEntity::getIslab,eveTagPagination.getIslab()));
        }

        //排序
        if(StrUtil.isEmpty(eveTagPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EveTagEntity::getId);
        }else{
            queryWrapper="asc".equals(eveTagPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(eveTagPagination.getSidx()):queryWrapper.orderByDesc(eveTagPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<EveTagEntity> page=new Page<>(eveTagPagination.getCurrentPage(), eveTagPagination.getPageSize());
            IPage<EveTagEntity> userIPage=this.page(page,queryWrapper);
            return eveTagPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public EveTagEntity getInfo(Long id){
        QueryWrapper<EveTagEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(EveTagEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(EveTagEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, EveTagEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(EveTagEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}