package com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * MateRelationAttributeValue模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-01 14:35:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MateRelationAttributeValueInfoVO extends MateRelationAttributeValueCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}