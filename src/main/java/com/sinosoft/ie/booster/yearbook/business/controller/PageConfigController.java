package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.PageConfigEntity;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigListVO;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigPagination;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigUpForm;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.TagFont;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.WordCloudEntity;
import com.sinosoft.ie.booster.yearbook.business.service.PageConfigService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * page_config @版本： V1.0.0 @作者： booster开发平台组 @日期： 2023-02-22 17:46:32
 */
@Slf4j
@RestController
@Api(tags = "page_config")
@RequestMapping("/PageConfig")
public class PageConfigController {
	@Autowired
	private PageConfigService pageConfigService;

	/**
	 * 列表
	 *
	 * @param pageConfigPagination
	 * @return
	 */
	@GetMapping
	public R<PageListVO<PageConfigListVO>> list(PageConfigPagination pageConfigPagination) throws IOException {
		List<PageConfigEntity> list = pageConfigService.getList(pageConfigPagination);
		// 处理id字段转名称，若无需转或者为空可删除
		List<PageConfigListVO> listVO = JsonUtil.getJsonToList(list, PageConfigListVO.class);
		for (PageConfigListVO entity : listVO) {
			String font_sizes = entity.getTagFontSize();
			String font_counts = entity.getTagFontNumber();
			if(StringUtils.isEmpty(font_sizes) || StringUtils.isEmpty(font_counts) ) {
				continue;
			}
			String[] size_arry = font_sizes.split(";");
			String[] count_arry = font_counts.split(";");
			List<TagFont> flist = new ArrayList<>();
			for (int i = 0; i < size_arry.length; i++) {
				TagFont font = new TagFont();
				font.setFont_size(Integer.parseInt(size_arry[i]));
				font.setFont_count(Integer.parseInt(count_arry[i]));
				flist.add(font);
			}
			List<TagFont>  fontlist = flist.stream().sorted(Comparator.comparing(TagFont::getFont_size).reversed())  
                    .collect(Collectors.toList());  
			entity.setTagFontList(fontlist);
		}
		
		PageListVO<PageConfigListVO> vo = new PageListVO<>();
		vo.setList(listVO);
		PaginationVO page = JsonUtil.getJsonToBean(pageConfigPagination, PaginationVO.class);
		vo.setPagination(page);
		return R.ok(vo);
	}

	/**
	 * 创建
	 *
	 * @param pageConfigCrForm
	 * @return
	 */
	@PostMapping
	@Transactional
	public R<String> create(@RequestBody @Valid PageConfigCrForm pageConfigCrForm) throws DataException {
		PageConfigEntity entity = JsonUtil.getJsonToBean(pageConfigCrForm, PageConfigEntity.class);
		pageConfigService.create(entity);
		return R.ok(null, "新建成功");
	}

	/**
	 * 信息
	 *
	 * @param id
	 * @return
	 */
	@GetMapping("/{id}")
	public R<PageConfigInfoVO> info(@PathVariable("id") Long id) {
		PageConfigEntity entity = pageConfigService.getInfo(id);
		PageConfigInfoVO vo = JsonUtil.getJsonToBean(entity, PageConfigInfoVO.class);
		return R.ok(vo);
	}

	/**
	 * 更新
	 *
	 * @param id
	 * @return
	 */
	@PutMapping("/{id}")
	@Transactional
	public R<String> update(@PathVariable("id") Long id, @RequestBody @Valid PageConfigUpForm pageConfigUpForm)
			throws DataException {
		PageConfigEntity entity = pageConfigService.getInfo(id);
		if (entity != null) {
			entity = JsonUtil.getJsonToBean(pageConfigUpForm, PageConfigEntity.class);
			pageConfigService.update(id, entity);
			return R.ok(null, "更新成功");
		} else {
			return R.failed("更新失败，数据不存在");
		}
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	@DeleteMapping("/{id}")
	@Transactional
	public R<String> delete(@PathVariable("id") Long id) {
		PageConfigEntity entity = pageConfigService.getInfo(id);
		if (entity != null) {
			pageConfigService.delete(entity);
		}
		return R.ok(null, "删除成功");
	}

}
