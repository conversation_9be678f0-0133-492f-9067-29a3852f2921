package com.sinosoft.ie.booster.yearbook.graph.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeMapper;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveThemeMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper;
import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEntity;
import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeEventMapper;
import com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeMapper;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphThemeEventService;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphThemeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.log.SysoCounter;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemePagination;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * graph_theme
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 16:14:43
 */
@Service
public class GraphThemeServiceImpl extends ServiceImpl<GraphThemeMapper, GraphThemeEntity> implements GraphThemeService {

    @Resource
    private EventMapper eventMapper;

    @Resource
    private LibEntityMapper libEntityMapper;

    @Resource
    private EveThemeMapper eveThemeMapper;

    @Resource
    private GraphThemeEventMapper graphThemeEventMapper;

    @Resource
    private GraphThemeEventService graphThemeEventService;

    private String person_arr[] = { "person_ids", "person_main", "person_related", "person_cooperation", "person_exchange",
            "person_achievements" };

    private Map<String, MateAttributeEntity> attr_Map = new HashedMap<>();

    @Resource
    private MateAttributeMapper mateAttributeService;

    @Override
    public List<GraphThemeEntity> getList(GraphThemePagination graphThemePagination){
        QueryWrapper<GraphThemeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(graphThemePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getId,graphThemePagination.getId()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getTheme()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getTheme,graphThemePagination.getTheme()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getPersons()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getPersons,graphThemePagination.getPersons()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getTeam()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getTeam,graphThemePagination.getTeam()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getAchievements()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getAchievements,graphThemePagination.getAchievements()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getArticles()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getArticles,graphThemePagination.getArticles()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getPrize()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getPrize,graphThemePagination.getPrize()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getProject()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getProject,graphThemePagination.getProject()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getFunding()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getFunding,graphThemePagination.getFunding()));
        }

        //排序
        if(StrUtil.isEmpty(graphThemePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(GraphThemeEntity::getId);
        }else{
            queryWrapper="asc".equals(graphThemePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(graphThemePagination.getSidx()):queryWrapper.orderByDesc(graphThemePagination.getSidx());
        }
        Page<GraphThemeEntity> page=new Page<>(graphThemePagination.getCurrentPage(), graphThemePagination.getPageSize());
        IPage<GraphThemeEntity> userIPage=this.page(page,queryWrapper);
        return graphThemePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<GraphThemeEntity> getTypeList(GraphThemePagination graphThemePagination,String dataType){
        QueryWrapper<GraphThemeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(graphThemePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getId,graphThemePagination.getId()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getTheme()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getTheme,graphThemePagination.getTheme()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getPersons()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getPersons,graphThemePagination.getPersons()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getTeam()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getTeam,graphThemePagination.getTeam()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getAchievements()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getAchievements,graphThemePagination.getAchievements()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getArticles()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getArticles,graphThemePagination.getArticles()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getPrize()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getPrize,graphThemePagination.getPrize()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getProject()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getProject,graphThemePagination.getProject()));
        }

        if(!"null".equals(String.valueOf(graphThemePagination.getFunding()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEntity::getFunding,graphThemePagination.getFunding()));
        }

        //排序
        if(StrUtil.isEmpty(graphThemePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(GraphThemeEntity::getId);
        }else{
            queryWrapper="asc".equals(graphThemePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(graphThemePagination.getSidx()):queryWrapper.orderByDesc(graphThemePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<GraphThemeEntity> page=new Page<>(graphThemePagination.getCurrentPage(), graphThemePagination.getPageSize());
            IPage<GraphThemeEntity> userIPage=this.page(page,queryWrapper);
            return graphThemePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public GraphThemeEntity getInfo(Long id){
        QueryWrapper<GraphThemeEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(GraphThemeEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(GraphThemeEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, GraphThemeEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(GraphThemeEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

    public void insertGraphTheme(){
        QueryWrapper<EveThemeEntity> eveThemeEntityQuery = new QueryWrapper<>();
        eveThemeEntityQuery.select("name");
        List<EveThemeEntity> eveThemeList = eveThemeMapper.selectList(eveThemeEntityQuery);
        this.remove(new QueryWrapper<>());
        graphThemeEventMapper.delete(new QueryWrapper<>());
        for (EveThemeEntity themeEntity:eveThemeList) {
//            insertGraphThemeInLoop(themeEntity.getName());
            insertGraphThemeEventInLoop(themeEntity.getName());
        }
    }

    R insertGraphThemeInLoop(String theme){
        GraphThemeEntity graphTheme = new GraphThemeEntity();
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        libEntityEntityQuery.apply("json_value(data,'$.theme')= '"+ theme +"'");
        List<LibEntityEntity> eventEntityList = libEntityMapper.selectList(libEntityEntityQuery);
        Set<Long> perSet = new HashSet<>();
        Set<Long> zuzhiSet = new HashSet<>();
        Set<Long> achievementSet = new HashSet<>();
        Set<Long> prizeSet = new HashSet<>();
        Set<Long> research_projectSet = new HashSet<>();
        Set<Long> fundsSet = new HashSet<>();
        for (LibEntityEntity event:eventEntityList) {
            Map<String, Object> dataMap = JsonUtil.stringToMap(event.getData());
//            String tit = event.getName();
            // 设置人物
            for (int i = 0; i < person_arr.length; i++) {
                String per_attr_name = person_arr[i];
                perSet = returnIds(perSet, per_attr_name, dataMap);
            }
            //组织
            zuzhiSet = returnIds(zuzhiSet, "relation_zuzhi", dataMap);
            //成果
            achievementSet = returnIds(achievementSet, "achievement", dataMap);
            //荣誉
            prizeSet = returnIds(prizeSet, "prize", dataMap);
            //项目
            research_projectSet = returnIds(research_projectSet, "research_project", dataMap);
            //经费
            fundsSet = returnIds(fundsSet, "funds", dataMap);
        }
        //物资
        QueryWrapper<LibEntityEntity> themeEntityQuery = new QueryWrapper<>();
        themeEntityQuery.eq("onto_id",YearbookConstant.MARK_THEME_ONTO_ID);
        themeEntityQuery.eq("name",theme);
        themeEntityQuery.apply("json_value(data,'$.goods') is not null");
        List<LibEntityEntity> themeEntityList = libEntityMapper.selectList(themeEntityQuery);
        if (themeEntityList.size()>0){
            Map<String, Object> dataMap = JsonUtil.stringToMap(themeEntityList.get(0).getData());
            graphTheme.setArticles(dataMap.get("goods").toString());
        }
        graphTheme.setTheme(theme);
        graphTheme.setPersons(joinSet(perSet));
        graphTheme.setTeam(joinSet(zuzhiSet));
        graphTheme.setAchievements(joinSet(achievementSet));
        graphTheme.setPrize(joinSet(prizeSet));
        graphTheme.setProject(joinSet(research_projectSet));
        graphTheme.setFunding(joinSet(fundsSet));
        this.save(graphTheme);
        //graph_theme_event计算

        return null;
    }

    /**
     * 根据属性名称和事件本体id获取属性对象
     *
     * @param attr_name
     */
    public MateAttributeEntity getAttrEntity(String attr_name) {
        if (attr_Map.get(attr_name) != null) {
            return attr_Map.get(attr_name);
        }
        QueryWrapper<MateAttributeEntity> query = new QueryWrapper<MateAttributeEntity>();
        query.lambda().and(t -> t.eq(MateAttributeEntity::getNameEn, attr_name));
        query.lambda().and(t -> t.eq(MateAttributeEntity::getOnto, YearbookConstant.MARK_EVENT_ONTO_ID));
        List<MateAttributeEntity> attrList = mateAttributeService.selectList(query);
        attr_Map.put(attr_name, attrList.get(0));
        return attrList.get(0);
    }

    public String joinSet(Set<Long> ids) {
        if (ids.size() == 0){
            return null;
        }
        StringBuffer sb = new StringBuffer();
        for (Long id : ids) {
            sb.append(id).append(";");
        }
        return sb.toString();
    }
    Set<Long> returnIds(Set<Long> perSet,String per_attr_name,Map<String, Object> dataMap){
        JSONArray perjson = new JSONArray();
        MateAttributeEntity attr = getAttrEntity(per_attr_name);
        Object value = dataMap.get(per_attr_name);
        if (ObjectUtil.isEmpty(value)) {
            return perSet;
        }
        if (attr.getVals() == 1) {
            if ((value instanceof Collection)) {
                return perSet;
            }
            perjson.add(value);
        }
        if (attr.getVals() == 2) {
            if (!(value instanceof Collection)) {
                return perSet;
            }
            perjson = (JSONArray) value;
        }
        for (int j = 0; j < perjson.size(); j++) {
            Long obj = (Long) perjson.get(j);
            perSet.add(obj);
        }
        return perSet;
    }

    R insertGraphThemeEventInLoop(String theme){
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        libEntityEntityQuery.apply("json_value(data,'$.theme')= '"+ theme +"'");
        List<LibEntityEntity> eventEntityList = libEntityMapper.selectList(libEntityEntityQuery);
        int fundsid = 0;
        for (LibEntityEntity event:eventEntityList) {
            List<GraphThemeEventEntity> GraphThemeEventList = new ArrayList<>();
            Map<String, Object> dataMap = JsonUtil.stringToMap(event.getData());
//            String tit = event.getName();
            // 设置人物
            for (int i = 0; i < person_arr.length; i++) {
                String per_attr_name = person_arr[i];
                List<Long> perSet = returnIdList(per_attr_name, dataMap);

                for (Long id:perSet) {
                    GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                    graphThemeEventEntity.setTheme(theme);
                    graphThemeEventEntity.setEntityId(id);
                    graphThemeEventEntity.setEntityName(getEntityName(id));
                    graphThemeEventEntity.setOntoId(YearbookConstant.MARK_PER_ONTO_ID);
                    graphThemeEventEntity.setOntoName("人物");
                    graphThemeEventEntity.setEventId(event.getId());
                    GraphThemeEventList.add(graphThemeEventEntity);
                }
            }
            //组织
            List<Long> zuzhiList = returnIdList("relation_zuzhi", dataMap);
            for (Long id:zuzhiList) {
                GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                graphThemeEventEntity.setTheme(theme);
                graphThemeEventEntity.setEntityId(id);
                graphThemeEventEntity.setEntityName(getEntityName(id));
                graphThemeEventEntity.setOntoId(YearbookConstant.MARK_TEAM_ONTO_ID);
                graphThemeEventEntity.setOntoName("组织");
                graphThemeEventEntity.setEventId(event.getId());
                GraphThemeEventList.add(graphThemeEventEntity);
            }
            //成果
            List<Long> achievementList = returnIdList("achievement", dataMap);
            for (Long id:achievementList) {
                GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                graphThemeEventEntity.setTheme(theme);
                graphThemeEventEntity.setEntityId(id);
                graphThemeEventEntity.setEntityName(getEntityName(id));
                graphThemeEventEntity.setOntoId(YearbookConstant.MARK_RESULT_ONTO_ID);
                graphThemeEventEntity.setOntoName("成果");
                graphThemeEventEntity.setEventId(event.getId());
                GraphThemeEventList.add(graphThemeEventEntity);
            }
            //荣誉
            List<Long> prizeList = returnIdList("prize", dataMap);
            for (Long id:prizeList) {
                GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                graphThemeEventEntity.setTheme(theme);
                graphThemeEventEntity.setEntityId(id);
                graphThemeEventEntity.setEntityName(getEntityName(id));
                graphThemeEventEntity.setOntoId(YearbookConstant.MARK_AWARD_ONTO_ID);
                graphThemeEventEntity.setOntoName("荣誉");
                graphThemeEventEntity.setEventId(event.getId());
                GraphThemeEventList.add(graphThemeEventEntity);
            }
            //项目
            List<Long> research_projectList = returnIdList("research_project", dataMap);
            for (Long id:research_projectList) {
                GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                graphThemeEventEntity.setTheme(theme);
                graphThemeEventEntity.setEntityId(id);
                graphThemeEventEntity.setEntityName(getEntityName(id));
                graphThemeEventEntity.setOntoId(YearbookConstant.MARK_PROJECT_ONTO_ID);
                graphThemeEventEntity.setOntoName("项目");
                graphThemeEventEntity.setEventId(event.getId());
                GraphThemeEventList.add(graphThemeEventEntity);
            }
            //经费
            Object value = dataMap.get("funds");
            if (!ObjectUtil.isEmpty(value)) {
                if (value.getClass().getName().equals("java.lang.String")){
                    GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                    graphThemeEventEntity.setTheme(theme);
                    graphThemeEventEntity.setEntityId(Long.valueOf(YearbookConstant.MARK_FUNDS_ONTO_ID.toString().substring(5)+fundsid));
                    fundsid ++;
                    graphThemeEventEntity.setEntityName(getNum(value));
                    graphThemeEventEntity.setOntoId(YearbookConstant.MARK_FUNDS_ONTO_ID);
                    graphThemeEventEntity.setOntoName("经费");
                    graphThemeEventEntity.setEventId(event.getId());
                    GraphThemeEventList.add(graphThemeEventEntity);
                }
            }
            graphThemeEventService.saveBatch(GraphThemeEventList);
        }

        //物资
        List<GraphThemeEventEntity> GraphThemeEventGoodsList = new ArrayList<>();
        QueryWrapper<LibEntityEntity> themeEntityQuery = new QueryWrapper<>();
        themeEntityQuery.eq("onto_id",YearbookConstant.MARK_THEME_ONTO_ID);
        themeEntityQuery.eq("name",theme);
        themeEntityQuery.apply("json_value(data,'$.goods') is not null");
        List<LibEntityEntity> themeEntityList = libEntityMapper.selectList(themeEntityQuery);
        int i = 0;
        if (themeEntityList.size()>0){
            Map<String, Object> dataMap = JsonUtil.stringToMap(themeEntityList.get(0).getData());
            if (dataMap.get("goods") == null){return null;}
            if (dataMap.get("goods").toString().isEmpty()){return null;}
            for (LibEntityEntity event:eventEntityList) {
                String[] goods = dataMap.get("goods").toString().split(";");
                for (String good:goods) {
                    GraphThemeEventEntity graphThemeEventEntity = new GraphThemeEventEntity();
                    graphThemeEventEntity.setTheme(theme);
                    graphThemeEventEntity.setEntityId(Long.valueOf(YearbookConstant.MARK_ARTICLES_ONTO_ID.toString().substring(5)+i));
                    i++;
                    graphThemeEventEntity.setEntityName(good);
                    graphThemeEventEntity.setOntoId(YearbookConstant.MARK_ARTICLES_ONTO_ID);
                    graphThemeEventEntity.setOntoName("物资");
                    graphThemeEventEntity.setEventId(event.getId());
                    GraphThemeEventGoodsList.add(graphThemeEventEntity);
                }
            }
        }
        graphThemeEventService.saveBatch(GraphThemeEventGoodsList);
        return null;
    }

    String getNum(Object value){
        String regEx="[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher((CharSequence) value);
        return m.replaceAll("").trim();
    }
    List<Long> returnIdList(String per_attr_name,Map<String, Object> dataMap){
        List<Long> perSet = new ArrayList<>();
        JSONArray perjson = new JSONArray();
        MateAttributeEntity attr = getAttrEntity(per_attr_name);
        Object value = dataMap.get(per_attr_name);
        if (ObjectUtil.isEmpty(value)) {
            return perSet;
        }
        if (attr.getVals() == 1) {
            if ((value instanceof Collection)) {
                return perSet;
            }
            perjson.add(value);
        }
        if (attr.getVals() == 2) {
            if (!(value instanceof Collection)) {
                return perSet;
            }
            perjson = (JSONArray) value;
        }
        for (int j = 0; j < perjson.size(); j++) {
        	System.out.println(perjson.get(j));
            Long obj = (Long) perjson.get(j);
            perSet.add(obj);
        }
        return perSet;
    }

    String getEntityName(Long id){
        LibEntityEntity libEntity = libEntityMapper.selectById(id);
        if (libEntity == null){
            return null;
        }
        return libEntity.getName();
    }
}