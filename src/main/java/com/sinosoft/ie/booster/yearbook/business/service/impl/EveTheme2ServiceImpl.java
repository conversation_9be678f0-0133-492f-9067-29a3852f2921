package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.entity.EveTheme2Entity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveTheme2Mapper;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2Pagination;
import com.sinosoft.ie.booster.yearbook.business.service.EveTheme2Service;

import cn.hutool.core.util.StrUtil;

/**
 *
 * eve_theme2
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 10:45:23
 */
@Service
public class EveTheme2ServiceImpl extends ServiceImpl<EveTheme2Mapper, EveTheme2Entity> implements EveTheme2Service {


    @Override
    public List<EveTheme2Entity> getList(EveTheme2Pagination eveTheme2Pagination){
        QueryWrapper<EveTheme2Entity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(eveTheme2Pagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EveTheme2Entity::getId,eveTheme2Pagination.getId()));
        }

        if(!"null".equals(String.valueOf(eveTheme2Pagination.getCategory1()))){
            queryWrapper.lambda().and(t->t.like(EveTheme2Entity::getCategory1,eveTheme2Pagination.getCategory1()));
        }

        if(!"null".equals(String.valueOf(eveTheme2Pagination.getCategoryName()))){
            queryWrapper.lambda().and(t->t.like(EveTheme2Entity::getCategoryName,eveTheme2Pagination.getCategoryName()));
        }

        //排序
        if(StrUtil.isEmpty(eveTheme2Pagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EveTheme2Entity::getId);
        }else{
            queryWrapper="asc".equals(eveTheme2Pagination.getSort().toLowerCase())?queryWrapper.orderByAsc(eveTheme2Pagination.getSidx()):queryWrapper.orderByDesc(eveTheme2Pagination.getSidx());
        }
        if(eveTheme2Pagination.getCurrentPage()==0) {
        	return this.list();
        }
        Page<EveTheme2Entity> page=new Page<>(eveTheme2Pagination.getCurrentPage(), eveTheme2Pagination.getPageSize());
        IPage<EveTheme2Entity> userIPage=this.page(page,queryWrapper);
        return eveTheme2Pagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<EveTheme2Entity> getTypeList(EveTheme2Pagination eveTheme2Pagination,String dataType){
        QueryWrapper<EveTheme2Entity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(eveTheme2Pagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EveTheme2Entity::getId,eveTheme2Pagination.getId()));
        }

        if(!"null".equals(String.valueOf(eveTheme2Pagination.getCategory1()))){
            queryWrapper.lambda().and(t->t.like(EveTheme2Entity::getCategory1,eveTheme2Pagination.getCategory1()));
        }

        if(!"null".equals(String.valueOf(eveTheme2Pagination.getCategoryName()))){
            queryWrapper.lambda().and(t->t.like(EveTheme2Entity::getCategoryName,eveTheme2Pagination.getCategoryName()));
        }

        //排序
        if(StrUtil.isEmpty(eveTheme2Pagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EveTheme2Entity::getId);
        }else{
            queryWrapper="asc".equals(eveTheme2Pagination.getSort().toLowerCase())?queryWrapper.orderByAsc(eveTheme2Pagination.getSidx()):queryWrapper.orderByDesc(eveTheme2Pagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<EveTheme2Entity> page=new Page<>(eveTheme2Pagination.getCurrentPage(), eveTheme2Pagination.getPageSize());
            IPage<EveTheme2Entity> userIPage=this.page(page,queryWrapper);
            return eveTheme2Pagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public EveTheme2Entity getInfo(Long id){
        QueryWrapper<EveTheme2Entity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(EveTheme2Entity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(EveTheme2Entity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, EveTheme2Entity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(EveTheme2Entity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}