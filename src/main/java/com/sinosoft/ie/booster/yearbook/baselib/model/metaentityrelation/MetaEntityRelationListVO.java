package com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * MetaEntityRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:34:43
 */
@Data
@ApiModel
public class MetaEntityRelationListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 本体1的id
     */
    @ApiModelProperty(value = "本体1的id")
    private String onto1Id;
    
    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的id")
    private String onto2Id;
    
    /**
     * 关系中文名称
     */
    @ApiModelProperty(value = "关系中文名称")
    private String name;

    /**
     * 实体1
     */
    @ApiModelProperty(value = "实体1")
    private String entity1Id;
    
    /**
     * 实体2
     */
    @ApiModelProperty(value = "实体2")
    private String entity2Id;
    
    /**
     * 本体关系id
     */
    @ApiModelProperty(value = "本体关系id")
    private String ontoRelId;
    
    /**
     * 1未导入2已导入3删除
     */
    @ApiModelProperty(value = "1未导入2已导入3删除")
    private Integer state;

    /**
     * 本体1的Name
     */
    @ApiModelProperty(value = "本体1的Name")
    private String onto1Name;

    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的Name")
    private String onto2Name;


    /**
     * 实体1
     */
    @ApiModelProperty(value = "实体1Name")
    private String entity1Name;

    /**
     * 实体2
     */
    @ApiModelProperty(value = "实体2Name")
    private String entity2Name;

    /**
     * 本体关系id
     */
    @ApiModelProperty(value = "本体关系idName")
    private String ontoRelName;

    /**
     * 实体表1
     */
    private String tabName1;

    /**
     * 实体表2
     */
    private String tabName2;
    
    /**
     * 置信度
     */
    @ApiModelProperty(value = "置信度")
    private Integer confidence;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    private String startTime;

    @ApiModelProperty(value = "子级")
    private List<MetaEntityRelationListVO> child;

}