package com.sinosoft.ie.booster.yearbook.graph.model.graphtheme;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * GraphTheme模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 16:14:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class GraphThemeInfoVO extends GraphThemeCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}