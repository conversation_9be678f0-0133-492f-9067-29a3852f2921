package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportJobEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaSchemaEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogExcel;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaPagination;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 *
 * meta_schema
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-02 15:36:32
 */
public interface MetaSchemaService extends IService<MetaSchemaEntity> {

    List<MetaSchemaEntity> getList(MetaSchemaPagination metaSchemaPagination);

    List<MetaSchemaEntity> getTypeList(MetaSchemaPagination metaSchemaPagination,String dataType);



    MetaSchemaEntity getInfo(Long id);

    void delete(MetaSchemaEntity entity);

    void create(MetaSchemaEntity entity);

    boolean update( Long id, MetaSchemaEntity entity);

    String dealConceptFileData(String filePath,Long schemaId);
    String dealNumberFileData(String filePath,Long schemaId);
    String dealObjectFileData(String filePath,Long schemaId);

    void schemaReuse(MetaSchemaEntity entity);

    String buildExcelDocument(Long ontoId)
            throws Exception;

    void dealExcelFileData(String filePath, Long ontoId, FileImportJobEntity fileImportJob);

    List<FileImportLogExcel> fileImportLogExport(long id);

//  子表方法
}
