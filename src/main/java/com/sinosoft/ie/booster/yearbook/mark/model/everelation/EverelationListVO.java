package com.sinosoft.ie.booster.yearbook.mark.model.everelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * Everelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-02 10:13:41
 */
@Data
@ApiModel
public class EverelationListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;


    /**
     * 事件1id
     */
    @ApiModelProperty(value = "事件1id")
    private Integer e1id;

    /**
     * 事件id
     */
    @ApiModelProperty(value = "事件id")
    private Integer e2id;

    /**
     * 关联名称
     */
    @ApiModelProperty(value = "关联名称")
    private String relationName;

}