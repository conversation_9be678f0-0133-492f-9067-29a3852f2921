package com.sinosoft.ie.booster.yearbook.mark.mapper;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkDatasetEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 *
 * mark_dataset
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-11 15:34:34
 */
@Mapper
public interface MarkDatasetMapper extends BaseMapper<MarkDatasetEntity> {

	@Select("select t.* ,f.old_name from mark_dataset  t left join uploadfile f on t.fid = f.id")
	List<MarkDatasetInfoVO>  selectDataSet();
}
