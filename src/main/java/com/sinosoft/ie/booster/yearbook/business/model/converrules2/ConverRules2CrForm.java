package com.sinosoft.ie.booster.yearbook.business.model.converrules2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * ConverRules2模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-20 16:32:03
 */
@Data
@ApiModel
public class ConverRules2CrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 十个分类
     */
    @ApiModelProperty(value = "十个分类")
    private String type;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String attrName;
}