package com.sinosoft.ie.booster.yearbook.mark.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.mark.entity.EverelationEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationUpForm;
import com.sinosoft.ie.booster.yearbook.mark.service.EverelationService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * everelation @版本： V1.0.0 @作者： booster开发平台组 @日期： 2022-09-02 10:13:41
 */
@Slf4j
@RestController
@Api(tags = "everelation")
@RequestMapping("/Everelation")
public class EverelationController {
	@Autowired
	private EverelationService everelationService;

	/**
	 * 列表
	 *
	 * @param everelationPagination
	 * @return
	 */
	@GetMapping
	public R list(EverelationPagination everelationPagination) throws IOException {
		List<EverelationEntity> list = everelationService.getList(everelationPagination);
		// 处理id字段转名称，若无需转或者为空可删除

		for (EverelationEntity entity : list) {
		}
		List<EverelationListVO> listVO = JsonUtil.getJsonToList(list, EverelationListVO.class);
		PageListVO vo = new PageListVO();
		vo.setList(listVO);
		PaginationVO page = JsonUtil.getJsonToBean(everelationPagination, PaginationVO.class);
		vo.setPagination(page);
		return R.ok(vo);
	}

	/**
	 * 创建
	 *
	 * @param everelationCrForm
	 * @return
	 */
	@PostMapping
	@Transactional
	public R create(@RequestBody @Valid EverelationCrForm everelationCrForm) throws DataException {
		EverelationEntity entity = JsonUtil.getJsonToBean(everelationCrForm, EverelationEntity.class);
		everelationService.create(entity);
		return R.ok(null, "新建成功");
	}

	/**
	 * 信息
	 *
	 * @param id
	 * @return
	 */
	@GetMapping("/{id}")
	public R<EverelationInfoVO> info(@PathVariable("id") Long id) {
		EverelationEntity entity = everelationService.getInfo(id);
		EverelationInfoVO vo = JsonUtil.getJsonToBean(entity, EverelationInfoVO.class);
		return R.ok(vo);
	}

	/**
	 * 更新
	 *
	 * @param id
	 * @return
	 */
	@PutMapping("/{id}")
	@Transactional
	public R update(@PathVariable("id") Long id, @RequestBody @Valid EverelationUpForm everelationUpForm)
			throws DataException {
		EverelationEntity entity = everelationService.getInfo(id);
		if (entity != null) {
			entity = JsonUtil.getJsonToBean(everelationUpForm, EverelationEntity.class);
			everelationService.update(id, entity);
			return R.ok(null, "更新成功");
		} else {
			return R.failed("更新失败，数据不存在");
		}
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	@DeleteMapping("/{id}")
	@Transactional
	public R delete(@PathVariable("id") Long id) {
		EverelationEntity entity = everelationService.getInfo(id);
		if (entity != null) {
			everelationService.delete(entity);
		}
		return R.ok(null, "删除成功");
	}

}
