package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRulesEntity;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesListVO;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesPagination;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesUpForm;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRulesService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * conver_rules
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-15 21:23:42
 */
@Slf4j
@RestController
@Api(tags = "conver_rules")
@RequestMapping("/ConverRules")
public class ConverRulesController {
    @Autowired
    private ConverRulesService converRulesService;


    /**
     * 列表
     *
     * @param converRulesPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<ConverRulesListVO>> list(ConverRulesPagination converRulesPagination)throws IOException{
        List<ConverRulesEntity> list= converRulesService.getList(converRulesPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(ConverRulesEntity entity:list){
            }
        List<ConverRulesListVO> listVO=JsonUtil.getJsonToList(list,ConverRulesListVO.class);
        PageListVO<ConverRulesListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(converRulesPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param converRulesCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid ConverRulesCrForm converRulesCrForm) throws DataException {
        ConverRulesEntity entity=JsonUtil.getJsonToBean(converRulesCrForm, ConverRulesEntity.class);
        converRulesService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<ConverRulesInfoVO> info(@PathVariable("id") Long id){
        ConverRulesEntity entity= converRulesService.getInfo(id);
        ConverRulesInfoVO vo=JsonUtil.getJsonToBean(entity, ConverRulesInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid ConverRulesUpForm converRulesUpForm) throws DataException {
        ConverRulesEntity entity= converRulesService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(converRulesUpForm, ConverRulesEntity.class);
            converRulesService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        ConverRulesEntity entity= converRulesService.getInfo(id);
        if(entity!=null){
            converRulesService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }
    
    @GetMapping("/list")
    public R list1()throws IOException{
        List<ConverRulesEntity> list= converRulesService.list();
        List<ConverRulesListVO> listVO=JsonUtil.getJsonToList(list,ConverRulesListVO.class);
        return R.ok(listVO);
    }
    
    @GetMapping("/categoryVal")
    public R categoryVal(Long ontoId )throws IOException{
    	;
        return R.ok(converRulesService.categoryVal(ontoId));
    }

}
