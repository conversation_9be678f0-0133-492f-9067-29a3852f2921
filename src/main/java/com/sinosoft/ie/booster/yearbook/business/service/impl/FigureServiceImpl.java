package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.FigureEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.FigureMapper;
import com.sinosoft.ie.booster.yearbook.business.service.FigureService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigurePagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * figure
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-26 11:45:26
 */
@Service
public class FigureServiceImpl extends ServiceImpl<FigureMapper, FigureEntity> implements FigureService {


    @Override
    public List<FigureEntity> getList(FigurePagination figurePagination){
        QueryWrapper<FigureEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(figurePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FigureEntity::getId,figurePagination.getId()));
        }

        if(!"null".equals(String.valueOf(figurePagination.getName()))){
            queryWrapper.lambda().and(t->t.like(FigureEntity::getName,figurePagination.getName()));
        }

        if(!"null".equals(String.valueOf(figurePagination.getField()))){
            queryWrapper.lambda().and(t->t.like(FigureEntity::getField,figurePagination.getField()));
        }

        //排序
        if(StrUtil.isEmpty(figurePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FigureEntity::getId);
        }else{
            queryWrapper="asc".equals(figurePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(figurePagination.getSidx()):queryWrapper.orderByDesc(figurePagination.getSidx());
        }
        Page<FigureEntity> page=new Page<>(figurePagination.getCurrentPage(), figurePagination.getPageSize());
        IPage<FigureEntity> userIPage=this.page(page,queryWrapper);
        return figurePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<FigureEntity> getTypeList(FigurePagination figurePagination,String dataType){
        QueryWrapper<FigureEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(figurePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FigureEntity::getId,figurePagination.getId()));
        }

        if(!"null".equals(String.valueOf(figurePagination.getName()))){
            queryWrapper.lambda().and(t->t.like(FigureEntity::getName,figurePagination.getName()));
        }

        if(!"null".equals(String.valueOf(figurePagination.getField()))){
            queryWrapper.lambda().and(t->t.like(FigureEntity::getField,figurePagination.getField()));
        }

        //排序
        if(StrUtil.isEmpty(figurePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FigureEntity::getId);
        }else{
            queryWrapper="asc".equals(figurePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(figurePagination.getSidx()):queryWrapper.orderByDesc(figurePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<FigureEntity> page=new Page<>(figurePagination.getCurrentPage(), figurePagination.getPageSize());
            IPage<FigureEntity> userIPage=this.page(page,queryWrapper);
            return figurePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public FigureEntity getInfo(Long id){
        QueryWrapper<FigureEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(FigureEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(FigureEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, FigureEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(FigureEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}