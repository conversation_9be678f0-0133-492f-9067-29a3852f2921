package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportJobEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobPagination;
import java.util.*;

/**
 *
 * file_import_job
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-08 15:13:22
 */
public interface FileImportJobService extends IService<FileImportJobEntity> {

    List<FileImportJobEntity> getList(FileImportJobPagination fileImportJobPagination);

    List<FileImportJobEntity> getTypeList(FileImportJobPagination fileImportJobPagination,String dataType);



    FileImportJobEntity getInfo(Long id);

    void delete(FileImportJobEntity entity);

    void create(FileImportJobEntity entity);

    boolean update( Long id, FileImportJobEntity entity);
    
//  子表方法
}
