package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateRelationAttributeMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateRelationAttributeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributePagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * mate_relation_attribute
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-01 14:33:35
 */
@Service
public class MateRelationAttributeServiceImpl extends ServiceImpl<MateRelationAttributeMapper, MateRelationAttributeEntity> implements MateRelationAttributeService {


    @Override
    public List<MateRelationAttributeEntity> getList(MateRelationAttributePagination mateRelationAttributePagination){
        QueryWrapper<MateRelationAttributeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(MateRelationAttributeEntity::getId,mateRelationAttributePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getNameEn()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getNameEn,mateRelationAttributePagination.getNameEn()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getNameCn()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getNameCn,mateRelationAttributePagination.getNameCn()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getValType()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getValType,mateRelationAttributePagination.getValType()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getMust()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getMust,mateRelationAttributePagination.getMust()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getOnto()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getOnto,mateRelationAttributePagination.getOnto()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getType()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getType,mateRelationAttributePagination.getType()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getVals()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getVals,mateRelationAttributePagination.getVals()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getRelationId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getRelationId,mateRelationAttributePagination.getRelationId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getSocre()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getSocre,mateRelationAttributePagination.getSocre()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getUnit()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getUnit,mateRelationAttributePagination.getUnit()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getSynonym()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getSynonym,mateRelationAttributePagination.getSynonym()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getPoint()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getPoint,mateRelationAttributePagination.getPoint()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getPid,mateRelationAttributePagination.getPid()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getTowards()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getTowards,mateRelationAttributePagination.getTowards()));
        }
        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getAttributeId()))){
            queryWrapper.lambda().and(t->t.eq(MateRelationAttributeEntity::getAttributeId,mateRelationAttributePagination.getAttributeId()));
        }
        //排序
        if(StrUtil.isEmpty(mateRelationAttributePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateRelationAttributeEntity::getId);
        }else{
            queryWrapper="asc".equals(mateRelationAttributePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateRelationAttributePagination.getSidx()):queryWrapper.orderByDesc(mateRelationAttributePagination.getSidx());
        }
        Page<MateRelationAttributeEntity> page=new Page<>(mateRelationAttributePagination.getCurrentPage(), mateRelationAttributePagination.getPageSize());
        IPage<MateRelationAttributeEntity> userIPage=this.page(page,queryWrapper);
        return mateRelationAttributePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MateRelationAttributeEntity> getTypeList(MateRelationAttributePagination mateRelationAttributePagination,String dataType){
        QueryWrapper<MateRelationAttributeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getId,mateRelationAttributePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getNameEn()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getNameEn,mateRelationAttributePagination.getNameEn()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getNameCn()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getNameCn,mateRelationAttributePagination.getNameCn()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getValType()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getValType,mateRelationAttributePagination.getValType()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getMust()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getMust,mateRelationAttributePagination.getMust()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getOnto()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getOnto,mateRelationAttributePagination.getOnto()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getType()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getType,mateRelationAttributePagination.getType()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getVals()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getVals,mateRelationAttributePagination.getVals()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getRelationId()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getRelationId,mateRelationAttributePagination.getRelationId()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getSocre()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getSocre,mateRelationAttributePagination.getSocre()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getUnit()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getUnit,mateRelationAttributePagination.getUnit()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getSynonym()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getSynonym,mateRelationAttributePagination.getSynonym()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getPoint()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getPoint,mateRelationAttributePagination.getPoint()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getPid,mateRelationAttributePagination.getPid()));
        }

        if(!"null".equals(String.valueOf(mateRelationAttributePagination.getTowards()))){
            queryWrapper.lambda().and(t->t.like(MateRelationAttributeEntity::getTowards,mateRelationAttributePagination.getTowards()));
        }

        //排序
        if(StrUtil.isEmpty(mateRelationAttributePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateRelationAttributeEntity::getId);
        }else{
            queryWrapper="asc".equals(mateRelationAttributePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateRelationAttributePagination.getSidx()):queryWrapper.orderByDesc(mateRelationAttributePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MateRelationAttributeEntity> page=new Page<>(mateRelationAttributePagination.getCurrentPage(), mateRelationAttributePagination.getPageSize());
            IPage<MateRelationAttributeEntity> userIPage=this.page(page,queryWrapper);
            return mateRelationAttributePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MateRelationAttributeEntity getInfo(Long id){
        QueryWrapper<MateRelationAttributeEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MateRelationAttributeEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MateRelationAttributeEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MateRelationAttributeEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MateRelationAttributeEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}