package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.sinosoft.ie.booster.yearbook.business.service.UploadfileService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkDatasetEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkDatasetMapper;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkDatasetService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * mark_dataset
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-09 11:56:53
 */
@Slf4j
@Service
public class MarkDatasetServiceImpl extends ServiceImpl<MarkDatasetMapper, MarkDatasetEntity> implements MarkDatasetService {
		
	@Resource
	private MarkDatasetMapper markDatasetMapper;
	@Resource
	private MakrSentenceService makrSentenceService;
	@Resource
	private UploadfileService uploadfileService;
	@Autowired
	private ProjectConfig projectConfig;
    @Override
    public List<MarkDatasetEntity> getList(MarkDatasetPagination markDatasetPagination){
        QueryWrapper<MarkDatasetEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markDatasetPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getId,markDatasetPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markDatasetPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getName,markDatasetPagination.getName()));
        }

        if(!"null".equals(String.valueOf(markDatasetPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getPid,markDatasetPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(markDatasetPagination.getFid()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getFid,markDatasetPagination.getFid()));
        }

        //排序
        if(StrUtil.isEmpty(markDatasetPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkDatasetEntity::getId);
        }else{
            queryWrapper="asc".equals(markDatasetPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markDatasetPagination.getSidx()):queryWrapper.orderByDesc(markDatasetPagination.getSidx());
        }
        Page<MarkDatasetEntity> page=new Page<>(markDatasetPagination.getCurrentPage(), markDatasetPagination.getPageSize());
        IPage<MarkDatasetEntity> userIPage=this.page(page,queryWrapper);
        return markDatasetPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MarkDatasetEntity> getTypeList(MarkDatasetPagination markDatasetPagination,String dataType){
        QueryWrapper<MarkDatasetEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markDatasetPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getId,markDatasetPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markDatasetPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getName,markDatasetPagination.getName()));
        }

        if(!"null".equals(String.valueOf(markDatasetPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getPid,markDatasetPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(markDatasetPagination.getFid()))){
            queryWrapper.lambda().and(t->t.like(MarkDatasetEntity::getFid,markDatasetPagination.getFid()));
        }

        //排序
        if(StrUtil.isEmpty(markDatasetPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkDatasetEntity::getId);
        }else{
            queryWrapper="asc".equals(markDatasetPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markDatasetPagination.getSidx()):queryWrapper.orderByDesc(markDatasetPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MarkDatasetEntity> page=new Page<>(markDatasetPagination.getCurrentPage(), markDatasetPagination.getPageSize());
            IPage<MarkDatasetEntity> userIPage=this.page(page,queryWrapper);
            return markDatasetPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MarkDatasetEntity getInfo(Long id){
        QueryWrapper<MarkDatasetEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MarkDatasetEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MarkDatasetEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MarkDatasetEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MarkDatasetEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public List<MarkDatasetInfoVO> getTreeList() {
		//List<MarkDatasetEntity> list = this.list();
		List<MarkDatasetInfoVO>   list = markDatasetMapper.selectDataSet();
		return list;
	}
	@Override
	public List<MarkSentenceEntity> getSentenceListById(MarkSentencePagination makrSentencePagination) {
		MarkDatasetEntity dataSet = this.getById(makrSentencePagination.getDataSetId());
		makrSentencePagination.setFid(dataSet.getFid());
		List<MarkSentenceEntity> list =makrSentenceService.getList(makrSentencePagination);
		return list;
	}
	@Override
	public void submitFile(MarkDatasetCrForm markDatasetCrForm) {
		//查询文件
		UploadfileEntity file = uploadfileService.getById(markDatasetCrForm.getFid());
		//保存文件至数据集
		MarkDatasetEntity dataset = new MarkDatasetEntity();
		dataset.setFid(markDatasetCrForm.getFid());
		dataset.setName(file.getOldName());
		dataset.setPid(markDatasetCrForm.getPid());
		dataset.setStartPage(markDatasetCrForm.getStartPage());
		this.save(dataset);
		
	//	extractTextDataService.extractText(dataset,file);

	}
}