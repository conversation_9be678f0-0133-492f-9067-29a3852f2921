package com.sinosoft.ie.booster.yearbook.graph.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveThemeMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeEventMapper;
import com.sinosoft.ie.booster.yearbook.graph.service.AiGraph1Service;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.job.EsDateHandle;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class AiGraph1ServiceImpl implements AiGraph1Service {

    @Resource
    private MetaEntityRelationMapper metaEntityRelationMapper;

    @Resource
    private LibEntityMapper libEntityMapper;

    @Resource
    private EveThemeMapper eveThemeMapper;

    @Resource
    private GraphThemeEventMapper graphThemeEventMapper;

    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;

    @Resource
    private ResearchTagMapper researchTagMapper;

    @Resource
    private EsDateHandle esDateHandle;

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public R orgGraph(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_ORG_STR;
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",entityId);
        nodesMap.put("name",libEntity.getName());
        nodesMap.put("onto",libEntity.getOntoId());
        nodesMap.put("isMain","1");
        if (libEntity.getOntoId().equals(YearbookConstant.MARK_ORG_ONTO_ID)){
            if (libEntity.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
                nodesMap.put("image_ids",stringObjectMap.get("icon"));
            }
        }
        nodesList.add(nodesMap);

        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
//        eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
        eventQuery.eq("json_value(data,'$.org_ids')",entityId);
        eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        //查询实体节点
        Map<String,String> querymap = new HashMap<>();
        querymap.put("entityId",entityId);
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEventsOrg(querymap);
        if(nodesEntityList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        nodesList.addAll(nodesEntityList);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        eventMap.put("entityId",entityId);
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEventsOrg(eventMap);
//        linksRelationList = linksRelationList.stream().filter(distinctByKey(o -> o.get("target") )).collect(Collectors.toList());

        // 处理可能的字符编码问题 - 修复title字段乱码
        for (Map<String, Object> link : linksRelationList) {
            String title = (String) link.get("title");
            if (title != null && title.length() > 0) {
                try {
                    // 检测并修复字符编码问题
                    String fixedTitle = fixChineseEncoding(title);
                    link.put("title", fixedTitle);
                } catch (Exception e) {
                    // 如果转换失败，使用原始字符串
                }
            }
        }

        linksList.addAll(linksRelationList);

        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        for (LibEntityEntity org:orgList) {
            Map<String, Object> orgMap = new HashMap<>();
            orgMap.put("entity1Id",entityId);
            orgMap.put("entity2Id",org.getId());
            List<Map<String, Object>> eventRelationlist = metaEntityRelationMapper.aiGraphGetRelationByEventsOrgMain(orgMap);
            if (eventRelationlist.size() == 0){
                continue;
            }

            // 处理可能的字符编码问题 - 修复title字段乱码
            for (Map<String, Object> relation : eventRelationlist) {
                String title = (String) relation.get("title");
                if (title != null && title.length() > 0) {
                    try {
                        // 检测并修复字符编码问题
                        String fixedTitle = fixChineseEncoding(title);
                        relation.put("title", fixedTitle);
                    } catch (Exception e) {
                        // 如果转换失败，使用原始字符串
                    }
                }
            }
            //研究所的节点
            Map<String, Object> nodeMap = new HashMap<>();
            nodeMap.put("id",org.getId());
            nodeMap.put("name",org.getName());
            nodeMap.put("onto",YearbookConstant.MARK_ORG_ONTO_ID);
            nodeMap.put("image_ids",null);
            nodeMap.put("isMain","1");
            nodesList.add(nodeMap);
            //到研究所的links
//            Map<String,Object> linksmapyjs = new HashMap<>();
//            linksmapyjs.put("source",entityId);
//            linksmapyjs.put("target",org.getId());
//            linksmapyjs.put("value","父子机构");
//            linksmapyjs.put("eventid",eventRelationlist.get(0).get("eventid"));
            linksList.add(eventRelationlist.get(0));
        }

        //long 2 string
        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        //去掉和中心实体没关联的数据
        List<Map<String,Object>> nodesListNew = removeNoRelationEntityOrg(nodesList,entityId,linksList);
        retrunMap.put("nodes",nodesListNew);
        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
        return R.ok(retrunMap);
    }

    @Override
    public R orgGraphRelationTime(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_ORG_STR;
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        if (libEntity == null){return R.failed("实体id不存在！");}
        List<Map<String, Object>> eventRelationList = new ArrayList<>();
        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
//            eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
        eventQuery.eq("json_value(data,'$.org_ids')",entityId);
        eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){
            return R.ok(eventRelationList);
        }
        Map<String,String> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("entityId",entityId);
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        eventRelationList = metaEntityRelationMapper.aiGraphGetRelationTimeByEventsOrg(eventMap);
        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        for (LibEntityEntity org:orgList) {
            Map<String, Object> orgMap = new HashMap<>();
            orgMap.put("entity1Id",entityId);
            orgMap.put("entity2Id",org.getId());
            List<Map<String, Object>> eventRelationlist1 = metaEntityRelationMapper.aiGraphGetRelationTimeByEventsOrgMain(orgMap);
            if (eventRelationlist1.size() > 0){
                eventRelationList.add(eventRelationlist1.get(0));
            }
        }
        //去掉和中心实体没关联的数据
        //查询实体节点
        List<Map<String, Object>> nodesList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        //关系
//        List<Map<String, Object>> linksList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        //long 2 string
//        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
//        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        List<Map<String,Object>> nodesListNew = removeNoRelationEntityOrgTime(nodesList,entityId,eventRelationList);
        List<Map<String, Object>> eventRelationListNew = new ArrayList<>();
        for (Map<String, Object> eventRelation:eventRelationList) {
            for (Map<String,Object> nodesNew:nodesListNew) {
                if (eventRelation.get("sourceId").toString().equals(nodesNew.get("id").toString())
                || eventRelation.get("targetId").toString().equals(nodesNew.get("id").toString())){
                    eventRelationListNew.add(eventRelation);
                    break;
                }
            }
        }
        eventRelationListNew.sort(Comparator.comparing(o -> o.get("startTime").toString()));
        Collections.reverse(eventRelationListNew);
        return R.ok(eventRelationListNew);
    }

    @Override
    public R orgGraphRelationTimeChart(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_ORG_STR;
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_ORG_LIST;
        HashSet<String> timeset = new HashSet<>();
        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        List<Long> orgIds = new ArrayList<>();
        for (LibEntityEntity org:orgList) {
            orgIds.add(org.getId());
        }
        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
//        eventQuery.in("json_value(data,'$.org2_id')",orgIds);
        eventQuery.eq("json_value(data,'$.org_ids')",entityId);
        eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){return R.ok(resultMap);}
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("eventids",eventids);
            querymap.put("ontoIds",orgOntoIds);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetOrgTimeChartByEvents(querymap);
            for (Map<String, Object> map:nodesEntityList) {
                timeset.add((String) map.get("time"));
            }
            nodesEntityList = nodesEntityList.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
            resultMap.put(ontoMap.get("id").toString(),nodesEntityList);
            List<String> timeList = new ArrayList<>(timeset);
            Collections.sort(timeList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    int year1 = Integer.valueOf(o1.split("-")[0]);
                    int month1 = Integer.valueOf(o1.split("-")[1]);
                    int year2 = Integer.valueOf(o2.split("-")[0]);
                    int month2 = Integer.valueOf(o2.split("-")[1]);
                    if (year1-year2 == 0){
                        if (month1-month2 == 0){
                            return 0;
                        }else if (month1-month2 < 0){
                            return -1;
                        }else if (month1-month2 > 0){
                            return 1;
                        }
                        return 1;
                    }else if (year1-year2 < 0){
                        return -1;
                    }else if (year1-year2 > 0){
                        return 1;
                    }
                    return 0;
                }
            });
            resultMap.put("timeList",timeList);
        }
        return R.ok(resultMap);
    }

    @Override
    public R humanGraph(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_HUMAN_STR;
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",entityId);
        nodesMap.put("name",libEntity.getName());
        nodesMap.put("onto",libEntity.getOntoId());
        nodesMap.put("isMain","1");
        if (libEntity.getOntoId().equals(YearbookConstant.MARK_PER_ONTO_ID)){
            if (libEntity.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
                nodesMap.put("image_ids",stringObjectMap.get("image_ids").toString());
            }
        }
        nodesList.add(nodesMap);

        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
//        eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
        eventQuery.and(i -> i.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID));
        eventQuery.and(i -> i.like("JSON_EXTRACT(data,'$.person_ids')",entityId).or()
                .like("JSON_EXTRACT(data,'$.person_main')",entityId).or()
                .like("JSON_EXTRACT(data, '$.person_related')",entityId).or()
                .like("JSON_EXTRACT(data,'$.person_cooperation')",entityId).or()
                .like("JSON_EXTRACT(data,'$.person_exchange')",entityId).or()
                .like("JSON_EXTRACT(data,'$.person_achievements')",entityId));
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        //查询实体节点
        Map<String,Object> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        if(nodesEntityList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        nodesList.addAll(nodesEntityList);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            boolean ontoHave = false;
            for (Map<String, Object> relationMap:linksRelationList) {
                if (relationMap.get("source").toString().equals(entityId) &&
                        relationMap.get("targetOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("source",ontoMap.get("id"));
                    ontoHave = true;
                    continue;
                }
                if (relationMap.get("target").toString().equals(entityId) &&
                        relationMap.get("sourceOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("target",ontoMap.get("id"));
                    ontoHave = true;
                }
            }
            if (!ontoHave){continue;}
            nodesList.add(ontoMap);
            //到本体的links
            Map<String,Object> linksmap = new HashMap<>();
            linksmap.put("source",entityId);
            linksmap.put("target",ontoMap.get("id").toString());
            linksmap.put("value","");
            linksmap.put("eventid","");
            linksList.add(linksmap);
        }
        linksList.addAll(linksRelationList);

        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        retrunMap.put("links",linksList);
        //去掉和中心实体没关联的数据
        List<Map<String,Object>> nodesListNew = removeNoRelationEntity(nodesList,nodesOntoList,entityId,linksList);
        retrunMap.put("nodes",nodesListNew);
//        retrunMap.put("nodes",nodesList);
        return R.ok(retrunMap);
    }

    List<Map<String,Object>> removeNoRelationEntity(
            List<Map<String,Object>> nodesList,List<Map<String, Object>> nodesOntoList,
                                                    String entityId,List<Map<String,Object>> linksList){
        //去掉和中心实体没关联的数据
        Set<String> entityidSet = new HashSet<>();
        entityidSet.add(entityId);
        getEntityNode(entityidSet,entityId,linksList);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            entityidSet.add(ontoMap.get("id").toString());
        }
        List<Map<String,Object>> nodesListNew = new ArrayList<>();
        for (Map<String,Object> nodeMap:nodesList) {
            for (String entityNode:entityidSet) {
                if (nodeMap.get("id").toString().equals(entityNode)){
                    nodesListNew.add(nodeMap);
                }
            }
        }
        return nodesListNew;
    }

    List<Map<String,Object>> removeNoRelationEntityOrg(
            List<Map<String,Object>> nodesList,
            String entityId,List<Map<String,Object>> linksList){
        //去掉和中心实体没关联的数据
        Set<String> entityidSet = new HashSet<>();
        entityidSet.add(entityId);
        getEntityNode(entityidSet,entityId,linksList);
        List<Map<String,Object>> nodesListNew = new ArrayList<>();
        for (Map<String,Object> nodeMap:nodesList) {
            for (String entityNode:entityidSet) {
                if (nodeMap.get("id").toString().equals(entityNode)){
                    nodesListNew.add(nodeMap);
                    break;
                }
            }
        }
        return nodesListNew;
    }
    void getEntityNode(Set<String> entityidSet,String entity,List<Map<String,Object>> linksList){
        for (Map<String,Object> linkMap:linksList) {
            if (linkMap.get("source").toString().equals(entity)){
                if (!entityidSet.contains(linkMap.get("target").toString())){
                    entityidSet.add(linkMap.get("target").toString());
                    getEntityNode(entityidSet,linkMap.get("target").toString(),linksList);
                }
            }
            if (linkMap.get("target").toString().equals(entity)){
                if (!entityidSet.contains(linkMap.get("source").toString())){
                    entityidSet.add(linkMap.get("source").toString());
                    getEntityNode(entityidSet,linkMap.get("source").toString(),linksList);
                }
            }
        }
    }
    List<Map<String,Object>> removeNoRelationEntityOrgTime(
            List<Map<String,Object>> nodesList,
            String entityId,List<Map<String,Object>> linksList){
        //去掉和中心实体没关联的数据
        Set<String> entityidSet = new HashSet<>();
        entityidSet.add(entityId);
        getEntityNodeTime(entityidSet,entityId,linksList);
        List<Map<String,Object>> nodesListNew = new ArrayList<>();
        for (Map<String,Object> nodeMap:nodesList) {
            for (String entityNode:entityidSet) {
                if (nodeMap.get("id").toString().equals(entityNode)){
                    nodesListNew.add(nodeMap);
                    break;
                }
            }
        }
        return nodesListNew;
    }
    void getEntityNodeTime(Set<String> entityidSet,String entity,List<Map<String,Object>> linksList){
        for (Map<String,Object> linkMap:linksList) {
            if (linkMap.get("sourceId").toString().equals(entity)){
                if (!entityidSet.contains(linkMap.get("targetId").toString())){
                    entityidSet.add(linkMap.get("targetId").toString());
                    getEntityNodeTime(entityidSet,linkMap.get("targetId").toString(),linksList);
                }
            }
            if (linkMap.get("targetId").toString().equals(entity)){
                if (!entityidSet.contains(linkMap.get("sourceId").toString())){
                    entityidSet.add(linkMap.get("sourceId").toString());
                    getEntityNodeTime(entityidSet,linkMap.get("sourceId").toString(),linksList);
                }
            }
        }
    }
    @Override
    public R humanGraphRelationTime(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_HUMAN_STR;
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        if (libEntity == null){return R.failed("实体id不存在！");}
        List<Map<String, Object>> eventRelationList = new ArrayList<>();
        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
        eventQuery.and(i -> i.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID));
        eventQuery.and(i -> i.like("json_query(data,'$.person_ids')",entityId).or()
                .like("json_query(data,'$.person_main')",entityId).or()
                .like("json_query(data,'$.person_related')",entityId).or()
                .like("json_query(data,'$.person_cooperation')",entityId).or()
                .like("json_query(data,'$.person_exchange')",entityId).or()
                .like("json_query(data,'$.person_achievements')",entityId));
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){
            return R.ok(eventRelationList);
        }
        Map<String,String> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        eventRelationList = metaEntityRelationMapper.aiGraphGetRelationTimeByEvents(eventMap);
        //去掉和中心实体没关联的数据
        //查询实体节点
        List<Map<String, Object>> nodesList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        //关系
//        List<Map<String, Object>> linksList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        //long 2 string
//        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
//        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        List<Map<String,Object>> nodesListNew = removeNoRelationEntityOrgTime(nodesList,entityId,eventRelationList);
        List<Map<String, Object>> eventRelationListNew = new ArrayList<>();
        for (Map<String, Object> eventRelation:eventRelationList) {
            for (Map<String,Object> nodesNew:nodesListNew) {
                if (eventRelation.get("sourceId").toString().equals(nodesNew.get("id").toString())
                        || eventRelation.get("targetId").toString().equals(nodesNew.get("id").toString())){
                    eventRelationListNew.add(eventRelation);
                    break;
                }
            }
        }
        eventRelationListNew.sort(Comparator.comparing(o -> o.get("startTime").toString()));
        Collections.reverse(eventRelationListNew);
        return R.ok(eventRelationListNew);
    }

    @Override
    public R humanGraphRelationTimeChart(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_HUMAN_STR;
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        HashSet<String> timeset = new HashSet<>();

        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
        eventQuery.and(i -> i.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID));
        eventQuery.and(i -> i.like("json_query(data,'$.person_ids')",entityId).or()
                .like("json_query(data,'$.person_main')",entityId).or()
                .like("json_query(data,'$.person_related')",entityId).or()
                .like("json_query(data,'$.person_cooperation')",entityId).or()
                .like("json_query(data,'$.person_exchange')",entityId).or()
                .like("json_query(data,'$.person_achievements')",entityId));
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){return R.ok(resultMap);}
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("eventids",eventids);
            querymap.put("ontoIds",orgOntoIds);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetOrgTimeChartByEvents(querymap);
            for (Map<String, Object> map:nodesEntityList) {
                timeset.add((String) map.get("time"));
            }
            nodesEntityList = nodesEntityList.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
            resultMap.put(ontoMap.get("id").toString(),nodesEntityList);
            List<String> timeList = new ArrayList<>(timeset);
            Collections.sort(timeList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    int year1 = Integer.valueOf(o1.split("-")[0]);
                    int month1 = Integer.valueOf(o1.split("-")[1]);
                    int year2 = Integer.valueOf(o2.split("-")[0]);
                    int month2 = Integer.valueOf(o2.split("-")[1]);
                    if (year1-year2 == 0){
                        if (month1-month2 == 0){
                            return 0;
                        }else if (month1-month2 < 0){
                            return -1;
                        }else if (month1-month2 > 0){
                            return 1;
                        }
                        return 1;
                    }else if (year1-year2 < 0){
                        return -1;
                    }else if (year1-year2 > 0){
                        return 1;
                    }
                    return 0;
                }
            });
            resultMap.put("timeList",timeList);
        }
        return R.ok(resultMap);
    }

    @Override
    public R themeGraph(String theme){
        //判断是标签还是主题
        boolean isTag = false;
        QueryWrapper<EveThemeEntity> themeTagQuery = new QueryWrapper<>();
        themeTagQuery.eq("name",theme);
        List<EveThemeEntity> eveThemeEntityList = eveThemeMapper.selectList(themeTagQuery);
        if (eveThemeEntityList.size() == 0){
            isTag = true;
        }
//        else {
//            if (eveThemeEntityList.get(0).getCategory1() != null){
//                if (eveThemeEntityList.get(0).getCategory1() == 2 && eveThemeEntityList.get(0).getPname() == null){
//                    isTag = true;
//                }
//            }
//        }

        String orgOntoIds = YearbookConstant.GRAPH_THEME_STR;
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_THEME_LIST;
        //主题本体id
        Long themeOnto = YearbookConstant.MARK_THEME_ONTO_ID;
        if (isTag){
            orgOntoIds = YearbookConstant.GRAPH_TAG_STR;
            nodesOntoList = YearbookConstant.GRAPH_TAG_LIST;
            themeOnto = YearbookConstant.MARK_TAG_ONTO_ID;
        }

        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        QueryWrapper<LibEntityEntity> themeQuery = new QueryWrapper<>();
        themeQuery.eq("name",theme);
        themeQuery.eq("onto_id",themeOnto);
        List<LibEntityEntity> libEntityList = libEntityMapper.selectList(themeQuery);
        if (libEntityList.size() == 0){
            //本身节点
            Map<String, Object> nodesMap = new HashMap<>();
            nodesMap.put("id","1");
            nodesMap.put("name",theme);
            nodesMap.put("isMain","1");
            nodesList.add(nodesMap);
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }

        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",libEntityList.get(0).getId());
        nodesMap.put("name",theme);
        nodesMap.put("onto",YearbookConstant.MARK_THEME_ONTO_ID);
        nodesMap.put("isMain","1");
        nodesList.add(nodesMap);

        //查询事件
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        if (isTag){
            libEntityEntityQuery.apply("json_query(data,'$.tag') like '%"+ theme +"%'");
        }else {
            libEntityEntityQuery.apply("json_value(data,'$.theme')= '"+ theme +"'");
        }
        List<LibEntityEntity> eventList = libEntityMapper.selectList(libEntityEntityQuery);
        if (eventList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        //查询实体节点
        Map<String,Object> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        if(nodesEntityList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        nodesList.addAll(nodesEntityList);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            boolean ontoHave = false;
            for (Map<String, Object> relationMap:linksRelationList) {
                if (relationMap.get("source").toString().equals(libEntityList.get(0).getId().toString()) &&
                        relationMap.get("targetOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("source",ontoMap.get("id"));
                    ontoHave = true;
                    continue;
                }
                if (relationMap.get("target").toString().equals(libEntityList.get(0).getId().toString()) &&
                        relationMap.get("sourceOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("target",ontoMap.get("id"));
                    ontoHave = true;
                }
            }
            if (!ontoHave){continue;}
            nodesList.add(ontoMap);
            //到本体的links
            Map<String,Object> linksmap = new HashMap<>();
            linksmap.put("source",libEntityList.get(0).getId().toString());
            linksmap.put("target",ontoMap.get("id").toString());
            linksmap.put("value","");
            linksmap.put("eventid","");
            linksList.add(linksmap);
        }
        linksList.addAll(linksRelationList);

        //物资
        Map<String, Object> ontoMap = new HashMap<>();
        ontoMap.put("id",YearbookConstant.MARK_ARTICLES_ONTO_ID);
        ontoMap.put("name","物资");
        nodesList.add(ontoMap);
        //到本体的links
        Map<String,Object> linksmap = new HashMap<>();
        linksmap.put("source",libEntityList.get(0).getId().toString());
        linksmap.put("target",YearbookConstant.MARK_ARTICLES_ONTO_ID.toString());
        linksmap.put("value","");
        linksmap.put("eventid","");
        linksList.add(linksmap);
        QueryWrapper<LibEntityEntity> themeEntityQuery = new QueryWrapper<>();
        themeEntityQuery.eq("onto_id",themeOnto);
        themeEntityQuery.eq("name",theme);
        themeEntityQuery.apply("json_value(data,'$.goods') is not null");
        List<LibEntityEntity> themeEntityList = libEntityMapper.selectList(themeEntityQuery);
        int i = 0;
        if (themeEntityList.size()>0){
            Map<String, Object> dataMap = JsonUtil.stringToMap(themeEntityList.get(0).getData());
            if (dataMap.get("goods") == null){return null;}
            if (dataMap.get("goods").toString().isEmpty()){return null;}
//            for (LibEntityEntity event:eventList) {
                String[] goods = dataMap.get("goods").toString().split(";");
                for (String good:goods) {
                    Map<String, Object> nodesMapgoods = new HashMap<>();
                    nodesMapgoods.put("id",Long.valueOf(YearbookConstant.MARK_ARTICLES_ONTO_ID.toString().substring(5)+i));
                    nodesMapgoods.put("name",good);
                    nodesMapgoods.put("onto",YearbookConstant.MARK_ARTICLES_ONTO_ID);
                    nodesMapgoods.put("eventid","");
                    nodesList.add(nodesMapgoods);
                    //links
                    Map<String,Object> linksmapgoods = new HashMap<>();
                    linksmapgoods.put("source",YearbookConstant.MARK_ARTICLES_ONTO_ID);
                    linksmapgoods.put("target",Long.valueOf(YearbookConstant.MARK_ARTICLES_ONTO_ID.toString().substring(5)+i));
                    linksmapgoods.put("value","");
                    linksmapgoods.put("eventid","");
                    linksList.add(linksmapgoods);
                    i++;
                }
//            }
        }

        //经费
        Map<String, Object> ontoMapFunds = new HashMap<>();
        ontoMapFunds.put("id",YearbookConstant.MARK_FUNDS_ONTO_ID.toString());
        ontoMapFunds.put("name","经费");
        nodesList.add(ontoMapFunds);
        //到本体的links
        Map<String,Object> linksmapFunds = new HashMap<>();
        linksmapFunds.put("source",libEntityList.get(0).getId().toString());
        linksmapFunds.put("target",YearbookConstant.MARK_FUNDS_ONTO_ID.toString());
        linksmapFunds.put("value","");
        linksmapFunds.put("eventid","");
        linksList.add(linksmapFunds);
        QueryWrapper<LibEntityEntity> themeEntityQueryFunds = new QueryWrapper<>();
        themeEntityQueryFunds.eq("onto_id",themeOnto);
        themeEntityQueryFunds.eq("name",theme);
        themeEntityQueryFunds.apply("json_value(data,'$.funds_str') is not null");
        List<LibEntityEntity> themeEntityListFunds = libEntityMapper.selectList(themeEntityQueryFunds);
        int iFunds = 0;
        if (themeEntityListFunds.size()>0){
            Map<String, Object> dataMap = JsonUtil.stringToMap(themeEntityListFunds.get(0).getData());
            if (dataMap.get("funds_str") == null){return null;}
            if (dataMap.get("funds_str").toString().isEmpty()){return null;}
//            for (LibEntityEntity event:eventList) {
            String[] goods = dataMap.get("funds_str").toString().split(";");
            for (String good:goods) {
                Map<String, Object> nodesMapgoods = new HashMap<>();
                nodesMapgoods.put("id",Long.valueOf(YearbookConstant.MARK_FUNDS_ONTO_ID.toString().substring(5)+iFunds));
                nodesMapgoods.put("name",good);
                nodesMapgoods.put("onto",YearbookConstant.MARK_FUNDS_ONTO_ID);
                nodesMapgoods.put("eventid","");
                nodesList.add(nodesMapgoods);
                //links
                Map<String,Object> linksmapgoods = new HashMap<>();
                linksmapgoods.put("source",YearbookConstant.MARK_FUNDS_ONTO_ID);
                linksmapgoods.put("target",Long.valueOf(YearbookConstant.MARK_FUNDS_ONTO_ID.toString().substring(5)+iFunds));
                linksmapgoods.put("value","");
                linksmapgoods.put("eventid","");
                linksList.add(linksmapgoods);
                iFunds++;
            }
//            }
        }

        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        //去掉和中心实体没关联的数据
        List<Map<String,Object>> nodesListNew = removeNoRelationEntity(nodesList,nodesOntoList,libEntityList.get(0).getId().toString(),linksList);
        //根据id去重
        nodesListNew = nodesListNew.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        //去标签本体
        for (Map<String, Object> linkrelation:nodesListNew) {
            if (linkrelation.get("onto") != null && linkrelation.get("onto").toString().equals(YearbookConstant.MARK_TAG_ONTO_ID.toString())){
                linkrelation.put("onto",YearbookConstant.MARK_THEME_ONTO_ID);
            }
        }
        retrunMap.put("nodes",nodesListNew);

        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
        return R.ok(retrunMap);
    }

    @Override
    public R themeGraphRelationTime(String theme){
        List<Map<String, Object>> eventRelationList = new ArrayList<>();
        //判断是标签还是主题
        boolean isTag = false;
        QueryWrapper<EveThemeEntity> themeTagQuery = new QueryWrapper<>();
        themeTagQuery.eq("name",theme);
        List<EveThemeEntity> eveThemeEntityList = eveThemeMapper.selectList(themeTagQuery);
        if (eveThemeEntityList.size() == 0){
            isTag = true;
        }
//        else {
//            if (eveThemeEntityList.get(0).getCategory1() != null){
//                if (eveThemeEntityList.get(0).getCategory1() == 2 && eveThemeEntityList.get(0).getPname() == null){
//                    isTag = true;
//                }
//            }
//        }
        String orgOntoIds = YearbookConstant.GRAPH_THEME_STR;
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_THEME_LIST;
        //主题本体id
        Long themeOnto = YearbookConstant.MARK_THEME_ONTO_ID;
        if (isTag){
            orgOntoIds = YearbookConstant.GRAPH_TAG_STR;
            nodesOntoList = YearbookConstant.GRAPH_TAG_LIST;
            themeOnto = YearbookConstant.MARK_TAG_ONTO_ID;
        }
        //查询本身节点
        QueryWrapper<LibEntityEntity> themeQuery = new QueryWrapper<>();
        themeQuery.eq("name",theme);
        themeQuery.eq("onto_id",themeOnto);
        List<LibEntityEntity> libEntityList = libEntityMapper.selectList(themeQuery);
        if (libEntityList.size() == 0){
            return R.ok(eventRelationList);
        }
        String entityId = libEntityList.get(0).getId().toString();

        //查询事件
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        if (isTag){
            libEntityEntityQuery.apply("json_query(data,'$.tag') like '%"+ theme +"%'");
        }else {
            libEntityEntityQuery.apply("json_value(data,'$.theme')= '"+ theme +"'");
        }
        List<LibEntityEntity> eventList = libEntityMapper.selectList(libEntityEntityQuery);
        if (eventList.size() == 0){
            return R.ok(eventRelationList);
        }
        Map<String,String> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        eventRelationList = metaEntityRelationMapper.aiGraphGetRelationTimeByEvents(eventMap);

        //去掉和中心实体没关联的数据
        //查询实体节点
        List<Map<String, Object>> nodesList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        //关系
//        List<Map<String, Object>> linksList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        //long 2 string
//        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
//        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        List<Map<String,Object>> nodesListNew = removeNoRelationEntityOrgTime(nodesList,entityId,eventRelationList);
        List<Map<String, Object>> eventRelationListNew = new ArrayList<>();
        for (Map<String, Object> eventRelation:eventRelationList) {
            for (Map<String,Object> nodesNew:nodesListNew) {
                if (eventRelation.get("sourceId").toString().equals(nodesNew.get("id").toString())
                        || eventRelation.get("targetId").toString().equals(nodesNew.get("id").toString())){
                    eventRelationListNew.add(eventRelation);
                    break;
                }
            }
        }
        return R.ok(eventRelationListNew);
    }

    @Override
    public R themeGraphRelationTimeChart(String theme){
        String orgOntoIds = YearbookConstant.GRAPH_HUMAN_STR;
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        HashSet<String> timeset = new HashSet<>();

        //查询事件
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        libEntityEntityQuery.apply("json_value(data,'$.theme')= '"+ theme +"'");
        List<LibEntityEntity> eventList = libEntityMapper.selectList(libEntityEntityQuery);
        if (eventList.size() == 0){return R.ok(resultMap);}
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("eventids",eventids);
            querymap.put("ontoIds",orgOntoIds);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetOrgTimeChartByEvents(querymap);
            for (Map<String, Object> map:nodesEntityList) {
                timeset.add((String) map.get("time"));
            }
            nodesEntityList = nodesEntityList.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
            resultMap.put(ontoMap.get("id").toString(),nodesEntityList);
            List<String> timeList = new ArrayList<>(timeset);
            Collections.sort(timeList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    int year1 = Integer.valueOf(o1.split("-")[0]);
                    int month1 = Integer.valueOf(o1.split("-")[1]);
                    int year2 = Integer.valueOf(o2.split("-")[0]);
                    int month2 = Integer.valueOf(o2.split("-")[1]);
                    if (year1-year2 == 0){
                        if (month1-month2 == 0){
                            return 0;
                        }else if (month1-month2 < 0){
                            return -1;
                        }else if (month1-month2 > 0){
                            return 1;
                        }
                        return 1;
                    }else if (year1-year2 < 0){
                        return -1;
                    }else if (year1-year2 > 0){
                        return 1;
                    }
                    return 0;
                }
            });
            resultMap.put("timeList",timeList);
        }
        return R.ok(resultMap);
    }

    public List<Map<String,Object>>  linkLongToStr(List<Map<String,Object>> linksList ){
        for (int i = 0; i < linksList.size(); i++) {
            Map<String,Object>  map =  linksList.get(i);
            Object source = map.get("source");
            if( source!=null &&  source instanceof Long) {
                map.put("source", (source).toString());
            }
            Object target =  map.get("target");
            if(target!=null && target instanceof Long) {
                map.put("target", target.toString());
            }
            Object eventid =  map.get("eventid");
            if(eventid!=null && eventid instanceof Long) {
                map.put("eventid", (eventid).toString());
            }
        }
        return  linksList;
    }

    public List<Map<String,Object>>  nodeLongToStr(List<Map<String,Object>> nodesList ){
        for (int i = 0; i < nodesList.size(); i++) {
            Map<String,Object>  map =  nodesList.get(i);
            Object id = map.get("id");
            if(id!=null && id instanceof Long) {
                map.put("id", (id).toString());
            }
            Object onto = map.get("onto");
            if(onto!=null && onto instanceof Long) {
                map.put("onto", (onto).toString());
            }
            Object image_ids = map.get("image_ids");
            if(image_ids!=null && image_ids instanceof Long) {
                map.put("image_ids", (image_ids).toString());
            }
        }
        return  nodesList;
    }



    public List<Map<String,Object>>   linkOnlyOne(List<Map<String,Object>> linksList ) {
        List<Map<String,Object>> newlinksList = new ArrayList<>();
        Set<String> setList =  new HashSet<>();
        for (int i = 0; i < linksList.size(); i++) {
            Map<String,Object>  map =  linksList.get(i);
            String source =  (String)map.get("source");
            String target =  (String)map.get("target");
            String value =  (String)map.get("value");
            String join = source + target + value;
            boolean  fal = setList.contains(join);
            if(!fal) {
                setList.add(join);
                newlinksList.add(map);
            }
        }
        return newlinksList;
    }

    @Override
    public R humanGraphRelationTimeChartNew(String entityId){
        Map<String,Object> resultMap = new HashMap<>();
        HashSet<String> timeset = new HashSet<>();
        R humanGraph = humanGraph(entityId);
        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(humanGraph.getData()));
        Object nodes = data.get("nodes");
        JSONArray jsonArr = JSONObject.parseArray(JSONObject.toJSONString(nodes));
        List<Map<String, Object>> listMapData = jsonArr.stream().
                map(s -> JSONObject.parseObject(JSONObject.toJSONString(s)).getInnerMap())
                .collect(Collectors.toList());
        List<Map<String, Object>> listMapDataonto = new ArrayList<>();
        for (Map<String, Object> map:listMapData) {
            if (map.get("onto") != null && map.get("time") != null){
                listMapDataonto.add(map);
            }
        }
        Map<String, List<Map<String, Object>>> listMap =
                listMapDataonto.stream().collect(Collectors.groupingBy(item -> item.get("onto").toString()));
        for (String onto : listMap.keySet()) {
            List<Map<String, Object>> list = listMap.get(onto);
            Map<String, List<Map<String, Object>>> listMapTime =
                    list.stream().collect(Collectors.groupingBy(item -> item.get("time").toString()));
            List<Map<String, Object>> timeEntity = new ArrayList<>();
            for (String time : listMapTime.keySet()) {
                List<Map<String, Object>> list1 = listMapTime.get(time);
                if (list1 == null){continue;}
                timeset.add(time);
                Map<String, Object> timeMap = new HashMap<>();
                timeMap.put("time",time);
                timeMap.put("num",list1.size());
                timeEntity.add(timeMap);
            }
            resultMap.put(onto,timeEntity);
        }
        List<String> timeList = new ArrayList<>(timeset);
        Collections.sort(timeList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                int year1 = Integer.valueOf(o1.split("-")[0]);
                int month1 = Integer.valueOf(o1.split("-")[1]);
                int year2 = Integer.valueOf(o2.split("-")[0]);
                int month2 = Integer.valueOf(o2.split("-")[1]);
                if (year1-year2 == 0){
                    if (month1-month2 == 0){
                        return 0;
                    }else if (month1-month2 < 0){
                        return -1;
                    }else if (month1-month2 > 0){
                        return 1;
                    }
                    return 1;
                }else if (year1-year2 < 0){
                    return -1;
                }else if (year1-year2 > 0){
                    return 1;
                }
                return 0;
            }
        });
        resultMap.put("timeList",timeList);
        return R.ok(resultMap);
    }

    @Override
    public R themeGraphRelationTimeChartNew(String theme){
        Map<String,Object> resultMap = new HashMap<>();
        HashSet<String> timeset = new HashSet<>();
        R themeGraph = themeGraph(theme);
        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(themeGraph.getData()));
        Object nodes = data.get("nodes");
        JSONArray jsonArr = JSONObject.parseArray(JSONObject.toJSONString(nodes));
        List<Map<String, Object>> listMapData = jsonArr.stream().
                map(s -> JSONObject.parseObject(JSONObject.toJSONString(s)).getInnerMap())
                .collect(Collectors.toList());
        List<Map<String, Object>> listMapDataonto = new ArrayList<>();
        for (Map<String, Object> map:listMapData) {
            if (map.get("onto") != null && map.get("time") != null){
                listMapDataonto.add(map);
            }
        }
        Map<String, List<Map<String, Object>>> listMap =
                listMapDataonto.stream().collect(Collectors.groupingBy(item -> item.get("onto").toString()));
        for (String onto : listMap.keySet()) {
            List<Map<String, Object>> list = listMap.get(onto);
            Map<String, List<Map<String, Object>>> listMapTime =
                    list.stream().collect(Collectors.groupingBy(item -> item.get("time").toString()));
            List<Map<String, Object>> timeEntity = new ArrayList<>();
            for (String time : listMapTime.keySet()) {
                List<Map<String, Object>> list1 = listMapTime.get(time);
                if (list1 == null){continue;}
                timeset.add(time);
                Map<String, Object> timeMap = new HashMap<>();
                timeMap.put("time",time);
                timeMap.put("num",list1.size());
                timeEntity.add(timeMap);
            }
            resultMap.put(onto,timeEntity);
        }
        List<String> timeList = new ArrayList<>(timeset);
        Collections.sort(timeList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                int year1 = Integer.valueOf(o1.split("-")[0]);
                int month1 = Integer.valueOf(o1.split("-")[1]);
                int year2 = Integer.valueOf(o2.split("-")[0]);
                int month2 = Integer.valueOf(o2.split("-")[1]);
                if (year1-year2 == 0){
                    if (month1-month2 == 0){
                        return 0;
                    }else if (month1-month2 < 0){
                        return -1;
                    }else if (month1-month2 > 0){
                        return 1;
                    }
                    return 1;
                }else if (year1-year2 < 0){
                    return -1;
                }else if (year1-year2 > 0){
                    return 1;
                }
                return 0;
            }
        });
        resultMap.put("timeList",timeList);
        return R.ok(resultMap);
    }

    @Override
    public R orgGraphRelationTimeChartNew(String entityId){
        Map<String,Object> resultMap = new HashMap<>();
        HashSet<String> timeset = new HashSet<>();
        R orgGraph = orgGraph(entityId);
        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(orgGraph.getData()));
        Object nodes = data.get("nodes");
        JSONArray jsonArr = JSONObject.parseArray(JSONObject.toJSONString(nodes));
        List<Map<String, Object>> listMapData = jsonArr.stream().
                map(s -> JSONObject.parseObject(JSONObject.toJSONString(s)).getInnerMap())
                .collect(Collectors.toList());
        List<Map<String, Object>> listMapDataonto = new ArrayList<>();
        for (Map<String, Object> map:listMapData) {
            if (map.get("onto") != null && map.get("time") != null){
                listMapDataonto.add(map);
            }
        }
        Map<String, List<Map<String, Object>>> listMap =
                listMapDataonto.stream().collect(Collectors.groupingBy(item -> item.get("onto").toString()));
        for (String onto : listMap.keySet()) {
            List<Map<String, Object>> list = listMap.get(onto);
            Map<String, List<Map<String, Object>>> listMapTime =
                    list.stream().collect(Collectors.groupingBy(item -> item.get("time").toString()));
            List<Map<String, Object>> timeEntity = new ArrayList<>();
            for (String time : listMapTime.keySet()) {
                List<Map<String, Object>> list1 = listMapTime.get(time);
                if (list1 == null){continue;}
                timeset.add(time);
                Map<String, Object> timeMap = new HashMap<>();
                timeMap.put("time",time);
                timeMap.put("num",list1.size());
                timeEntity.add(timeMap);
            }
            resultMap.put(onto,timeEntity);
        }
        List<String> timeList = new ArrayList<>(timeset);
        Collections.sort(timeList, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                int year1 = Integer.valueOf(o1.split("-")[0]);
                int month1 = Integer.valueOf(o1.split("-")[1]);
                int year2 = Integer.valueOf(o2.split("-")[0]);
                int month2 = Integer.valueOf(o2.split("-")[1]);
                if (year1-year2 == 0){
                    if (month1-month2 == 0){
                        return 0;
                    }else if (month1-month2 < 0){
                        return -1;
                    }else if (month1-month2 > 0){
                        return 1;
                    }
                    return 1;
                }else if (year1-year2 < 0){
                    return -1;
                }else if (year1-year2 > 0){
                    return 1;
                }
                return 0;
            }
        });
        resultMap.put("timeList",timeList);
        return R.ok(resultMap);
    }

    /**
     * 修复中文字符编码问题
     * @param text 原始文本
     * @return 修复后的文本
     */
    private String fixChineseEncoding(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        try {
            // 尝试不同的编码转换方式
            String[] encodings = {"ISO-8859-1", "GBK", "GB2312", "windows-1252"};

            for (String encoding : encodings) {
                try {
                    byte[] bytes = text.getBytes(encoding);
                    String converted = new String(bytes, "UTF-8");

                    // 检查转换后的字符串是否包含中文字符且没有乱码标识
                    if (converted.matches(".*[\\u4e00-\\u9fa5].*") &&
                        !converted.contains("?") &&
                        !converted.contains("�")) {
                        return converted;
                    }
                } catch (Exception e) {
                    // 继续尝试下一种编码
                }
            }

            // 如果所有转换都失败，返回原始字符串
            return text;

        } catch (Exception e) {
            return text;
        }
    }
}
