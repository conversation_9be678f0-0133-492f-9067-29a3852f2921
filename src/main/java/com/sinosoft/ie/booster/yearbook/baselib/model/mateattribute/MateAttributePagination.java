package com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MateAttribute模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-24 11:19:01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MateAttributePagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String nameEn;

    /**
     * 中文
     */
    @ApiModelProperty(value = "中文")
    private String nameCn;

    /**
     * 1：值类型数字；2：值类型字符串；3：值类型浮点型；4：对象类型
     */
    @ApiModelProperty(value = "1：值类型数字；2：值类型字符串；3：值类型浮点型；4：对象类型")
    private Integer valType;

    /**
     * 是否必填1必填2非必填
     */
    @ApiModelProperty(value = "是否必填1必填2非必填")
    private Integer must;

    /**
     * 所属本体
     */
    @ApiModelProperty(value = "所属本体")
    private Long onto;

    /**
     * 1文本框2下拉框3时间
     */
    @ApiModelProperty(value = "1文本框2下拉框3时间")
    private Integer type;

    /**
     * 对象类型是否是多个值1否2是
     */
    @ApiModelProperty(value = "对象类型是否是多个值1否2是")
    private Integer vals;

    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    private Long relationId;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer socre;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 同义词，多个用分号隔开
     */
    @ApiModelProperty(value = "同义词，多个用分号隔开")
    private String synonymWord;

    /**
     * 如果是对象类型属性值指向所在地本体
     */
    @ApiModelProperty(value = "如果是对象类型属性值指向所在地本体")
    private Long point;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long pid;

    /**
     * 指向：1：正向；2：反向,3:双向
     */
    @ApiModelProperty(value = "指向：1：正向；2：反向,3:双向")
    private Integer towards;
}