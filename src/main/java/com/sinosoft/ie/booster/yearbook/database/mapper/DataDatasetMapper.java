package com.sinosoft.ie.booster.yearbook.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinosoft.ie.booster.yearbook.database.entity.DataDatasetEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * data_dataset
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-06 15:00:08
 */
@Mapper
public interface DataDatasetMapper extends BaseMapper<DataDatasetEntity> {

    List<String> getTableName();

    int getTableNum(String tableName);
}
