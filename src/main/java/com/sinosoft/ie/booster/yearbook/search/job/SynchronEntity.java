package com.sinosoft.ie.booster.yearbook.search.job;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.HashedMap;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.service.impl.EventWeight;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEntityMapper;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEventMapper;
import com.sinosoft.ie.booster.yearbook.search.model.EsEntity;
import com.sinosoft.ie.booster.yearbook.search.model.EsEvent;

import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

@Service
public class SynchronEntity {

	@Resource
	private EventService eventService;
	@Resource
	private LibEntityService libEntityService;
	@Resource
	private LibEntityMapper libEntityMapper;
	@Autowired
	private MateAttributeService mateAttributeService;
	@Resource
	private EsEventMapper esEventMapper;
	@Resource
	private EsEntityMapper esEntityMapper;
	@Resource
	private EsDateHandle esDateHandle;
	@Resource
	private RestHighLevelClient client;
	@Value("${easy-es.address}")
	private String ip;
	@Resource
	private EventWeight eventWeight;
	// 所有的机构字段
	String org_arr[] = { "org_ids","org2_id", "org_main", "org_related", "org_cooperation", "org_external", "org_achievement" };
	String person_arr[] = { "person_ids", "person_main", "person_related", "person_cooperation", "person_exchange",
			"person_achievements" };
	
	/**
	 * 同步 人物，项目，成果，机构
	 * 
	 * @param id
	 */
	@Async
	public void synchronEntity() {

		Long end_id = 0L;
		// 如果索引不存在，则手动创建
		if (!esEntityMapper.existsIndex("entity")) {
			esEntityMapper.createIndex();
		} else {
			// 索引存在则查询id最大的一条数据
			LambdaEsQueryWrapper<EsEntity> es_query = new LambdaEsQueryWrapper<EsEntity>();
			es_query.orderByDesc("id");
			es_query.limit(1);
			List<EsEntity> maxlist = esEntityMapper.selectList(es_query);
			if (maxlist.size() > 0) {
				end_id = Long.valueOf(maxlist.get(0).getId());
			}
		}
		List<Long> ids = new ArrayList<>();
		ids.add(YearbookConstant.MARK_PER_ONTO_ID);
		ids.add(YearbookConstant.MARK_ORG_ONTO_ID);
		ids.add(YearbookConstant.MARK_PROJECT_ONTO_ID);
		ids.add(YearbookConstant.MARK_RESULT_ONTO_ID);
		ids.add(YearbookConstant.MARK_THEME_ONTO_ID);
		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.gt("id", end_id);
		query.in("onto_id", ids);
		long count = libEntityService.count(query);
		int pages = 1;
		if (count > 100) {
			pages = (int) (count / 100 + 1);
		} else {
			pages = 1;
		}

		for (int i = 1; i <= pages; i++) {
			Page<LibEntityEntity> page = new Page<>(i, 100);
			IPage<LibEntityEntity> useIPage = libEntityService.page(page, query);
			List<LibEntityEntity> entList = useIPage.getRecords();
			BulkRequest bulkRequest = new BulkRequest();
			List<EsEntity> eslist = new ArrayList<EsEntity>();
			for (int j = 0; j < entList.size(); j++) {
				if(entList.get(j).getId().equals(1570686960603791362L)) {
					System.out.println("---");
				}
				JSONObject json1 = esDateHandle.coverEntity(entList.get(j));
				if (json1 == null) {
					continue;
				}
				JSONObject json = checkStr(json1);
//				json.set("name", entList.get(j).getName());
//				json.set("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
//				IndexRequest indexRequest = new IndexRequest();
//				indexRequest.index("entity").id(String.valueOf(entList.get(j).getId()));
//				indexRequest.source(json.toString(), XContentType.JSON);
//				bulkRequest.add(indexRequest);
				
				EsEntity entity = new EsEntity();
				entity.setId(entList.get(j).getId().toString());
				entity.setData(json.toString());
				entity.setName(entList.get(j).getName());
				entity.setOnto_id(entList.get(j).getOntoId().toString());
				entity.setEs_show(json.getStr("es_show"));
				entity.setPortrait(json.getStr("portrait"));
				entity.setOrg_rank(json.getStr("org_rank"));
				eslist.add(entity);
				
			}
			if (entList.size() == 0) {
				continue;
			}
//			try {
//				BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
//			} catch (IOException e) {
//				throw new DataException("批量entity==>es数据失败-->" + e);
//			}
			esEntityMapper.insertBatch(eslist);
		}
		// 开启 字段映射
//		Map<String, Object> map = new HashedMap<>();
//		Map<String, Object> image_ids = new HashedMap<String, Object>();
//		image_ids.put("type", "text");
//		image_ids.put("fielddata", true);
//		map.put("image_ids", image_ids);
//
//		Map<String, Object> per_map = new HashedMap<>();
//		per_map.put("properties", map);
//
//		JSONObject json = (JSONObject) JSONUtil.parse(per_map);
//		System.out.println(json);
//		HttpUtil.post(ip + "/entity/_mapping", map);
	}
	
	
	
	public JSONObject   checkStr(JSONObject json) {
		String []  filds =  {"org_rank","es_show","portrait"};
		for (int i = 0; i < filds.length; i++) {
			Object obj = json.get(filds[i]);
			if(obj !=null) {
				if(!(obj instanceof String)) {
					json.set(filds[i], String.valueOf(obj));
				}
			}
		}
		return json;
	}
	
	
}
