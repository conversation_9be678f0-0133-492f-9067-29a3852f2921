package com.sinosoft.ie.booster.yearbook.baselib.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * meta_onto_relation
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:37:39
 */
@Slf4j
@RestController
@Api(tags = "meta_onto_relation")
@RequestMapping("/MetaOntoRelation")
public class MetaOntoRelationController {
    @Autowired
    private MetaOntoRelationService metaOntoRelationService;


    /**
     * 列表
     *
     * @param metaOntoRelationPagination
     * @return
     */
    @GetMapping
    public R list(MetaOntoRelationPagination metaOntoRelationPagination)throws IOException{
        List<MetaOntoRelationEntity> list= metaOntoRelationService.getList(metaOntoRelationPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MetaOntoRelationEntity entity:list){
            }
        List<MetaOntoRelationListVO> listVO=JsonUtil.getJsonToList(list,MetaOntoRelationListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(metaOntoRelationPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param metaOntoRelationCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid MetaOntoRelationCrForm metaOntoRelationCrForm) throws DataException {
        MetaOntoRelationEntity entity=JsonUtil.getJsonToBean(metaOntoRelationCrForm, MetaOntoRelationEntity.class);
        metaOntoRelationService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MetaOntoRelationInfoVO> info(@PathVariable("id") Long id){
        MetaOntoRelationEntity entity= metaOntoRelationService.getInfo(id);
        MetaOntoRelationInfoVO vo=JsonUtil.getJsonToBean(entity, MetaOntoRelationInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid MetaOntoRelationUpForm metaOntoRelationUpForm) throws DataException {
        MetaOntoRelationEntity entity= metaOntoRelationService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(metaOntoRelationUpForm, MetaOntoRelationEntity.class);
            metaOntoRelationService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        MetaOntoRelationEntity entity= metaOntoRelationService.getInfo(id);
        if(entity!=null){
            metaOntoRelationService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }
    
    
    @GetMapping("/getList")
    public R getList(MetaOntoRelationPagination metaOntoRelationPagination)throws IOException{
    	 List<MetaOntoRelationInfoVO> list = new ArrayList<MetaOntoRelationInfoVO>();
    	if(metaOntoRelationPagination.getOnto1Id()!=null) {
            list= metaOntoRelationService.getList1(metaOntoRelationPagination);
    	}
        //处理id字段转名称，若无需转或者为空可删除
        PageListVO vo=new PageListVO();
        vo.setList(list);
        PaginationVO page=JsonUtil.getJsonToBean(metaOntoRelationPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 全部本体list
     */
    @GetMapping("/getAllList")
    public R getAllList(MetaOntoRelationPagination metaOntoRelationPagination)throws IOException{
        List<MetaOntoRelationInfoVO> list = new ArrayList<MetaOntoRelationInfoVO>();
        list= metaOntoRelationService.getList1(metaOntoRelationPagination);
        //处理id字段转名称，若无需转或者为空可删除
        PageListVO vo=new PageListVO();
        vo.setList(list);
        PaginationVO page=JsonUtil.getJsonToBean(metaOntoRelationPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     *传入本体id左关联
     */
    @GetMapping("/getListByOnto1Id")
    public R getListByOnto1Id(Long ontoId){
        return R.ok(metaOntoRelationService.getListByOnto1Id(ontoId));
    }
}
