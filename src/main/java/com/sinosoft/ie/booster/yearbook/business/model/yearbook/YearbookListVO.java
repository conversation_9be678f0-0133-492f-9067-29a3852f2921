package com.sinosoft.ie.booster.yearbook.business.model.yearbook;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;

/**
 *
 * Yearbook模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 13:39:57
 */
@Data
@ApiModel
public class YearbookListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

	/**
	 * 年鉴名称
	 */
	@ApiModelProperty(value = "年鉴名称")
	private String bookName;

	/**
	 * 年鉴日期
	 */
	@ApiModelProperty(value = "年鉴日期")
	private String bookYear;

	/**
	 * 封面图片
	 */
	@ApiModelProperty(value = "封面图片")
	private String coverPic;

    /**
     * 原文件名称
     */
    @ApiModelProperty(value = "原文件名称")
    private String oldName;

    /**
     * 文件类型：1pdf，2word
     */
    @ApiModelProperty(value = "文件类型：1pdf，2word")
    private String type;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String pid;
    
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private String delFlag;

	/**
	 * 文件路径
	 */
	@ApiModelProperty(value = "文件路径")
	private String path;
	
	@ApiModelProperty(value = "关键词")
	private String  tag;
	@ApiModelProperty(value = "关键词")
	private String  tag2;
	@ApiModelProperty(value = "主题词")
	private String  theme;

}