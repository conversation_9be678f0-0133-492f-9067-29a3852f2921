//package com.sinosoft.ie.booster.yearbook.baselib.service.impl;
//
//import java.io.ByteArrayInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.io.ObjectInputStream;
//import java.io.ObjectOutputStream;
//import java.util.List;
//import java.util.Map;
//
//import javax.annotation.Resource;
//
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.sinosoft.ie.booster.common.core.model.Pagination;
//import com.sinosoft.ie.booster.common.core.util.JsonUtil;
//import com.sinosoft.ie.booster.common.core.util.SpringContextHolder;
//import com.sinosoft.ie.booster.yearbook.baselib.controller.LibEntityController;
//import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
//import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
//import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
//import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptListVO;
//import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationInfoVO;
//import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
//import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityServicve;
//import com.sinosoft.ie.booster.yearbook.baselib.service.LibResultService;
//import com.sinosoft.ie.booster.yearbook.baselib.service.MetaEntityRelationService;
//
//import cn.hutool.core.util.ObjectUtil;
//import lombok.extern.slf4j.Slf4j;
//@Slf4j
//@Service
//public class LibEntityServicveImpl implements LibEntityServicve {
//
//	@Resource
//	private LibConceptService libConceptService;
//	@Resource
//	private LibResultService libResultService;
//	@Resource
//	private MetaEntityRelationService metaEntityRelationService;
//	@Resource
//	private MetaEntityRelationMapper metaEntityRelationMapper;
//	
//	@Override
//	public List<Object> queryEntityList(Pagination tion,Long id ) {
//		
////		 List<String>arr = Arrays.asList(SpringContextHolder.getApplicationContext().getBeanDefinitionNames());
////		 for (int i = 0; i <arr.size(); i++) {
////			System.out.println(arr.get(i));
////		}
//		
//		LibConceptEntity concept = libConceptService.getById(id);
//		//查询条件
//		QueryWrapper query = new QueryWrapper();
//		//点击的最上层的节点
//		if(concept.getPid()==0) {
//			//无条件分页查询
//		}
//		//点击其中一个节点
//		if(!StringUtils.isEmpty(concept.getAttribute()) ){
//			List<LibConceptEntity> ontolist  = libConceptService.getAllSubNode(id);
//			if(ontolist.size()==0) {
//				return null;
//			}
//			StringBuffer sb =  new StringBuffer();
//			Long []  ids = new Long[ontolist.size()];
//			for (int i = 0; i < ontolist.size(); i++) {
//				sb.append(ontolist.get(i).getId()).append(",");
//				ids[i] = ontolist.get(i).getId();
//			}
//			query.in(concept.getAttribute(), ids);
//		}
//
//        Page<LibConceptEntity> page=new Page<>(tion.getCurrentPage(), tion.getPageSize());
//        
//		String tableName = concept.getTab();
//		String className = chageClassName(tableName);
//		
//		IService obj = SpringContextHolder.getBean(className+"ServiceImpl");		
//		List<Object> list = obj.list(query);
//		IPage userIPage=obj.page(page,query);
//		
//		return tion.setData(userIPage.getRecords(),userIPage.getTotal());
//		
//	}
//	
//	
//	public static String  chageClassName(String tabName) {
//		String str[] = tabName.split("_");
//		StringBuffer sb = new StringBuffer();
//		sb.append(str[0]);
//		for (int i = 1; i < str.length; i++) {
//			sb.append(str[i].substring(0,1).toUpperCase()).append(str[i].substring(1,str[i].length()));
//		}
//		
//		return sb.toString();
//	}
//	
//	public static<T> T deepClone(T src) throws IOException, ClassNotFoundException {
//        Object obj = null;
//        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
//        objectOutputStream.writeObject(src);
//        objectOutputStream.close();
//        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
//        ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream);
//        obj = objectInputStream.readObject();
//        objectInputStream.close();
//        return (T) obj;
//    }
//	public static void main(String[] args) {
//		System.out.println(chageClassName("lib_person"));
//	}
//
//
//
//	@Override
//	public List<LibConceptListVO> getPropertsList(Long id) {
//    	
//		//查询当前本体
//		LibConceptEntity ent = libConceptService.getById(id);
//    	//查询“属性”本体
//		QueryWrapper<LibConceptEntity> queryWrapper=new QueryWrapper<>();
//		queryWrapper.eq("pid", 0L);
//		queryWrapper.eq("name", "属性");
//    	List<LibConceptEntity> list1 =  libConceptService.list(queryWrapper);
//    	if(list1.size()==0) {
//    		return null;
//    	}
//    	QueryWrapper<LibConceptEntity> queryWrapper1=new QueryWrapper<>();
//    	queryWrapper1.eq("pid", list1.get(0).getId());
//    	queryWrapper1.eq("name", ent.getName());
//        List<LibConceptEntity> ent_pro = libConceptService.list(queryWrapper1);
//        
//        if(ent_pro.size()==0) {
//        	return null;
//        }
//        QueryWrapper<LibConceptEntity> queryWrapper2=new QueryWrapper<>();
//        queryWrapper2.eq("pid", ent_pro.get(0).getId());
//        List<LibConceptEntity> prolist = libConceptService.list(queryWrapper2);
//        
//        List<LibConceptListVO> listVO=JsonUtil.getJsonToList(prolist,LibConceptListVO.class);
//        
//        listVO.forEach(x->{
//        	String pre =x.getAttribute();
//        	x.setProper(pre);
//        });
//        
//		return listVO;
//	}
//
//	public String chagePre(String name ) {
//		if(name.indexOf("_")<0) {
//			return name;
//		}
//		String str[] = name.split("_");
//		StringBuffer sb = new StringBuffer();
//		sb.append(str[0]).append(str[1].substring(0,1).toUpperCase()).append(str[1].substring(1,str[1].length()));
//		return sb.toString();
//	}
//
//
//	@Override
//	public Map<String, Object> queryEntityRelation(Long ontoId,Long entId) {
//		//查询本体
//		LibConceptEntity concept	= libConceptService.getById(ontoId);
//        //查询实体关系
//		QueryWrapper<MetaEntityRelationEntity> wrapper = new QueryWrapper<MetaEntityRelationEntity>();
//		wrapper.eq("onto1_id", ontoId);
//		wrapper.eq("entity1_Id", entId);
//	//	List<MetaEntityRelationEntity>  entRellist = metaEntityRelationService.list(wrapper);
//		
//		
//		List<MetaEntityRelationInfoVO>  entRellist = metaEntityRelationMapper.queryEntityRelation(wrapper);
//		
//		for (int i = 0; i < entRellist.size(); i++) {
//			
//			MetaEntityRelationInfoVO vo = entRellist.get(i);
//			
////			String tableName = vo.get();
////			String className = chageClassName(tableName);
////			//获取对应的service对象
////			IService obj = SpringContextHolder.getBean(className+"ServiceImpl");
//			
//		}
//		
//		
//		String tableName = concept.getTab();
//		String className = chageClassName(tableName);
//		//获取对应的service对象
//		IService obj = SpringContextHolder.getBean(className+"ServiceImpl");
//		
//		
//		return null;
//	}
//}
