package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-06 13:45:32
 */
@Data
@TableName("eve_theme")
@ApiModel(value = "")
public class EveThemeEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String name;

    /**
     * 常态主题还是动态主题
     */
    @ApiModelProperty(value = "常态主题还是动态主题")
    private Integer category1;

    /**
     * 科研类还是管理类
     */
    @ApiModelProperty(value = "科研类还是管理类")
    private Long category2;

    /**
     * 是否默认：0，不；1，默认选中
     */
    @ApiModelProperty(value = "是否默认：0，不；1，默认选中")
    private Integer isdef;

    @ApiModelProperty(value = "父级名称")

    private String pname;

    @ApiModelProperty(value = "简介")

    private String synopsis;
    /**
     * 是否显示 
     */
    private Integer islab;
    /**
     * 是否是目录1是0不是
     */
    private Integer dir;
}
