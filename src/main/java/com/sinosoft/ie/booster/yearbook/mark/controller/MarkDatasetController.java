package com.sinosoft.ie.booster.yearbook.mark.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetUpForm;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentenceListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;
import com.sinosoft.ie.booster.yearbook.mark.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkDatasetEntity;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkDatasetService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mark_dataset
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-09 11:56:53
 */
@Slf4j
@RestController
@Api(tags = "mark_dataset")
@RequestMapping("/MarkDataset")
public class MarkDatasetController {
    @Autowired
    private MarkDatasetService markDatasetService;


    /**
     * 列表
     *
     * @param markDatasetPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MarkDatasetListVO>> list(MarkDatasetPagination markDatasetPagination)throws IOException{
        List<MarkDatasetEntity> list= markDatasetService.getList(markDatasetPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MarkDatasetEntity entity:list){
            }
        List<MarkDatasetListVO> listVO=JsonUtil.getJsonToList(list,MarkDatasetListVO.class);
        PageListVO<MarkDatasetListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(markDatasetPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param markDatasetCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MarkDatasetCrForm markDatasetCrForm) throws DataException {
        MarkDatasetEntity entity=JsonUtil.getJsonToBean(markDatasetCrForm, MarkDatasetEntity.class);
        markDatasetService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MarkDatasetInfoVO> info(@PathVariable("id") Long id){
        MarkDatasetEntity entity= markDatasetService.getInfo(id);
        MarkDatasetInfoVO vo=JsonUtil.getJsonToBean(entity, MarkDatasetInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MarkDatasetUpForm markDatasetUpForm) throws DataException {
        MarkDatasetEntity entity= markDatasetService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(markDatasetUpForm, MarkDatasetEntity.class);
            markDatasetService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MarkDatasetEntity entity= markDatasetService.getInfo(id);
        if(entity!=null){
            markDatasetService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 获取数据集树数据
     * @return
     */
    @GetMapping("/getTreeList")
    @Transactional
    public R getTreeList(){
        List<MarkDatasetInfoVO> list = markDatasetService.getTreeList();
        return R.ok(list);
    }

    /**
     * 根据数据集id查询所有句子
     * @param id
     * @return
     */
    @GetMapping("/getSentenceList")
    @Transactional
    public R getSentenceList( MarkSentencePagination makrSentencePagination ){
        List<MarkSentenceEntity> list = markDatasetService.getSentenceListById(makrSentencePagination);
        List<MarkSentenceListVO> listVO=JsonUtil.getJsonToList(list,MarkSentenceListVO.class);
        PageListVO<MarkSentenceListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(makrSentencePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }
    
    @PostMapping("/submitFile")
    public R   submitFile(@RequestBody @Valid MarkDatasetCrForm markDatasetCrForm) {
    	markDatasetService.submitFile(markDatasetCrForm);
    	return R.ok();
    }
    
    
    
}
