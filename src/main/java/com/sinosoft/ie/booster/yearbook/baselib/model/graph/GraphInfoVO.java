package com.sinosoft.ie.booster.yearbook.baselib.model.graph;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * Graph模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-31 13:39:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class GraphInfoVO extends GraphCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}