package com.sinosoft.ie.booster.yearbook.business.model.homePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：YearbookCount.java
 * @description：首页数字年鉴统计返回类
 * @author：peas
 * @data：2022/11/9 14:36
 **/
@Data
@ApiModel(discriminator = "首页数字年鉴统计返回类")
public class YearbookCount {

    /**
     * 年鉴数量
     */
    @ApiModelProperty(value = "年鉴数量")
    private Integer yearbookCount;

    /**
     * 事件数量
     */
    @ApiModelProperty(value = "事件数量")
    private Integer eventCount;

    /**
     * 人物数量
     */
    @ApiModelProperty(value = "人物数量")
    private Integer personCount;

    /**
     * 课题数量
     */
    @ApiModelProperty(value = "课题数量")
    private Integer taskCount;

    /**
     * 成果数量
     */
    @ApiModelProperty(value = "成果数量")
    private Integer resultCount;

    /**
     * 机构数量
     */
    @ApiModelProperty(value = "机构数量")
    private Integer orgCount;

    /**
     * 年鉴字数
     */
    @ApiModelProperty(value = "年鉴字数")
    private Integer wordCount;

    /**
     * 年鉴图片数
     */
    @ApiModelProperty(value = "年鉴图片数")
    private Integer imgCount;

}
