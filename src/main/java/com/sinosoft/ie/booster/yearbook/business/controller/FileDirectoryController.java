package com.sinosoft.ie.booster.yearbook.business.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationListVO;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationPagination;
import com.sinosoft.ie.booster.yearbook.business.service.YearbookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryPagination;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryListVO;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryUpForm;
import com.sinosoft.ie.booster.yearbook.business.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.sinosoft.ie.booster.yearbook.business.service.FileDirectoryService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * file_directory
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 14:28:43
 */
@Slf4j
@RestController
@Api(tags = "file_directory")
@RequestMapping("/FileDirectory")
public class FileDirectoryController {
    @Autowired
    private FileDirectoryService fileDirectoryService;
	@Autowired
	private YearbookService yearbookService;


    /**
     * 列表
     *
     * @param fileDirectoryPagination
     * @return
     */
    @GetMapping
    public R list(FileDirectoryPagination fileDirectoryPagination)throws IOException{
        List<FileDirectoryEntity> list= fileDirectoryService.getList(fileDirectoryPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(FileDirectoryEntity entity:list){
            }
        List<FileDirectoryListVO> listVO=JsonUtil.getJsonToList(list,FileDirectoryListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(fileDirectoryPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param fileDirectoryCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid FileDirectoryCrForm fileDirectoryCrForm) throws DataException {
        FileDirectoryEntity entity=JsonUtil.getJsonToBean(fileDirectoryCrForm, FileDirectoryEntity.class);
        fileDirectoryService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<FileDirectoryInfoVO> info(@PathVariable("id") Long id){
        FileDirectoryEntity entity= fileDirectoryService.getInfo(id);
        FileDirectoryInfoVO vo=JsonUtil.getJsonToBean(entity, FileDirectoryInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid FileDirectoryUpForm fileDirectoryUpForm) throws DataException {
        FileDirectoryEntity entity= fileDirectoryService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(fileDirectoryUpForm, FileDirectoryEntity.class);
            fileDirectoryService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        FileDirectoryEntity entity= fileDirectoryService.getInfo(id);
        if(entity!=null){
            fileDirectoryService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

//	/**
//	 * 根据年鉴id查询所有目录
//	 * @param fid
//	 * @return
//	 */
//	@GetMapping("/dirList")
//	public R getDirList(Long fid){
//		List<FileDirectoryEntity> list= fileDirectoryService.getList(fid);
//
//		return R.ok(list);
//	}

	/**
	 * 分级列表(tree)
	 *
	 * @param fid
	 * @return
	 */
	@GetMapping("/showDirTree/{fid}")
	public R showDirTree(@PathVariable  Long fid)throws IOException{
		Map<String,Object> map = fileDirectoryService.showDirTree(fid);
		return R.ok(map);
	}
	
	@GetMapping("/showDirTree")
	public R showDirTree()throws IOException{
		List<FileDirectoryListVO> list= fileDirectoryService.showDirTree();
		return R.ok(list);
	}
	
	 @GetMapping("/dirInfo/{id}")
	public R  dirInfo(@PathVariable("id") Long id) {
		 return R.ok(fileDirectoryService.dirInfo(id));
	}
	 @GetMapping("/dirInfo")
	public R  dirInfo(Integer year, Integer pageNumber) {
		 return R.ok(fileDirectoryService.dirInfo(year, pageNumber));
	}
	
}
