package com.sinosoft.ie.booster.yearbook.util;

//import com.aspose.words.Document;
//import com.aspose.words.SaveFormat;

import java.io.*;
import java.util.Iterator;
import java.util.Map;

public class Word2Pdf {

    public static void main(String[] args) throws FileNotFoundException {
        System.out.println("开始");
        try {
            // 新建一个空白pdf文档
            File file = new File("D:\\work\\yearbooks\\2.pdf");
            FileOutputStream os = new FileOutputStream(file);
//            Document doc = new Document("D:\\work\\yearbooks\\1.docx");
//            doc.save(os, SaveFormat.PDF);
        }catch (Exception e){

        }
        System.out.println("结束");
    }

}
