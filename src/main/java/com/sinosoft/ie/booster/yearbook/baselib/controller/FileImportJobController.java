package com.sinosoft.ie.booster.yearbook.baselib.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportJobEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.FileImportJobService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * file_import_job
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-08 15:13:22
 */
@Slf4j
@RestController
@Api(tags = "file_import_job")
@RequestMapping("/FileImportJob")
public class FileImportJobController {
    @Autowired
    private FileImportJobService fileImportJobService;


    /**
     * 列表
     *
     * @param fileImportJobPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<FileImportJobListVO>> list(FileImportJobPagination fileImportJobPagination)throws IOException{
        List<FileImportJobEntity> list= fileImportJobService.getList(fileImportJobPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(FileImportJobEntity entity:list){
            }
        List<FileImportJobListVO> listVO=JsonUtil.getJsonToList(list,FileImportJobListVO.class);
        PageListVO<FileImportJobListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(fileImportJobPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param fileImportJobCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid FileImportJobCrForm fileImportJobCrForm) throws DataException {
        FileImportJobEntity entity=JsonUtil.getJsonToBean(fileImportJobCrForm, FileImportJobEntity.class);
        fileImportJobService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<FileImportJobInfoVO> info(@PathVariable("id") Long id){
        FileImportJobEntity entity= fileImportJobService.getInfo(id);
        FileImportJobInfoVO vo=JsonUtil.getJsonToBean(entity, FileImportJobInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid FileImportJobUpForm fileImportJobUpForm) throws DataException {
        FileImportJobEntity entity= fileImportJobService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(fileImportJobUpForm, FileImportJobEntity.class);
            fileImportJobService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        FileImportJobEntity entity= fileImportJobService.getInfo(id);
        if(entity!=null){
            fileImportJobService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
