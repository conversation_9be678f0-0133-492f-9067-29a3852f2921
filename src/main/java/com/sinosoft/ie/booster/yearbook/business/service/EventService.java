package com.sinosoft.ie.booster.yearbook.business.service;

import cn.hutool.json.JSONArray;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import java.util.*;

/**
 *
 * event
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-15 10:16:34
 */
public interface EventService extends IService<EventEntity> {

    List<EventEntity> getList(EventPagination eventPagination);

    List<EventEntity> getTypeList(EventPagination eventPagination,String dataType);



    EventEntity getInfo(Long id);

    void delete(EventEntity entity);

    void create(EventEntity entity);

    boolean update( Long id, EventEntity entity);
    
//  子表方法

	JSONArray getAllTime();

//	Map<Integer,List<List<EventEntity>> > queryEventByYear(String year,String theme);

    Map<Integer,EventInfoVO > queryEventByYearNoDayOneEvent(String year, String theme, String typeParam);

    Map<String,Integer> queryEventCountByYear(String year, String theme);

	Map<String,Object> queryEventByAll(EventPagination eventPagination);

	List<Map<String,Integer>> mapData(String year,String theme);

	Map<Integer,List<EventEntity> > queryEventByYearNoDay(String year,String theme, String typeParam);

    EventInfoVO getTimeSelect();

    List<String> getTagTop(List<EventEntity> list);

    EventInfoVO getInfoTag(EventInfoVO vo);


    R getTagList(String tag);


    List<Integer> monthlyStats();

    /**
     * 获取按月统计的各类型事件数量
     * @return 包含每月统计数据的Map列表
     */
    List<Map<String, Object>> getMonthlyStats();
}
