package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@TableName("research_tag3")
@ApiModel(value = "")
public class ResearchTag3Entity {
	
	
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;
	
	private String name;
	
	private Long dirId;

}
