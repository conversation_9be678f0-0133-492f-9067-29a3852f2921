package com.sinosoft.ie.booster.yearbook.business.model.researchtag2;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;
import java.util.Map;

/**
 *
 * ResearchTag2模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-20 13:38:22
 */
@Data
@ApiModel
public class ResearchTag2CrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String name;

    /**
     * 分组名称
     */
    @ApiModelProperty(value = "分组名称")
    private String tagGroup;

    /**
     * 标签分类
     */
    @ApiModelProperty(value = "标签分类")
    private String tagType;

    /**
     * 所属本体
     */
    @ApiModelProperty(value = "所属本体")
    private Long ontoId;

    /**
     * 所属本体属性
     */
    @ApiModelProperty(value = "所属本体属性")
    private String attrCategory;

    /**
     * 所属本体属性枚举值id
     */
    @ApiModelProperty(value = "所属本体属性枚举值id")
    private Long attrValId;

    /**
     * 所属本体属性枚举值
     */
    @ApiModelProperty(value = "所属本体属性枚举值")
    private List<MateAttributeValueEntity> attrVal;

    /**
     * 标签分类英文名
     */
    @ApiModelProperty(value = "标签分类英文名")
    private String tagAttr;
}