package com.sinosoft.ie.booster.yearbook.mark.model.marktriplet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MarkTriplet模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkTripletUpForm extends MarkTripletCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}