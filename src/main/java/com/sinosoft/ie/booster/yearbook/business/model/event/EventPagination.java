package com.sinosoft.ie.booster.yearbook.business.model.event;

import java.util.List;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Event模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-21 18:11:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EventPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 地点
     */
    @ApiModelProperty(value = "地点")
    private String venue;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 人物
     */
    @ApiModelProperty(value = "人物")
    private String persons;

    /**
     * 物品
     */
    @ApiModelProperty(value = "物品")
    private String articles;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String organisation;

    /**
     * 触发词
     */
    @ApiModelProperty(value = "触发词")
    private String triggerss;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String multiRelations;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String category;

    private List<String> categorys;
    /**
     * 事件文本
     */
    @ApiModelProperty(value = "事件文本")
    private String content;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String note;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgid;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String tag;

    /**
     * 成果物
     */
    @ApiModelProperty(value = "成果物")
    private String achievements;

    /**
     * 会议
     */
    @ApiModelProperty(value = "会议")
    private String meeting;

    /**
     * 年鉴id
     */
    @ApiModelProperty(value = "年鉴id")
    private Long fid;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String theme;
	@ApiModelProperty(value = "年份")
	private String year;
	@ApiModelProperty(value = "月份")
	private Integer month;
	@ApiModelProperty(value = "天")
	private Integer day;

	/**
	 * 目录id
	 */
	@ApiModelProperty(value = "目录id")
	private Long fdid;

	/**
	 * 课题
	 */
	@ApiModelProperty(value = "课题")
	private String task;

	/**
	 * 政治工作
	 */
	@ApiModelProperty(value = "政治工作")
	private String politicalWork;

	/**
	 * 管理保障
	 */
	@ApiModelProperty(value = "管理保障")
	private String securityManage;

	/**
	 * 综合协调
	 */
	@ApiModelProperty(value = "综合协调")
	private String comprehensiveCoordination;

	/**
	 * 科研管理
	 */
	@ApiModelProperty(value = "科研管理")
	private String scientificManage;

	/**
	 * 领导决策
	 */
	@ApiModelProperty(value = "领导决策")
	private String decisionMaking;

	/**
	 * 科研力量
	 */
	@ApiModelProperty(value = "科研力量")
	private String scientificStrength;

	/**
	 * 科研组织
	 */
	@ApiModelProperty(value = "科研组织")
	private String scientificOrganization;

	/**
	 * 科研课题
	 */
	@ApiModelProperty(value = "科研课题")
	private String scientificSubject;

	/**
	 * 科研成果
	 */
	@ApiModelProperty(value = "科研成果")
	private String scientificResult;

	/**
	 * 权重
	 */
	@ApiModelProperty(value = "权重")
	private Integer weight;

	/**
	 * 目录id列表
	 */
	@ApiModelProperty(value = "多个目录id")
	private String fdids;

	/**
	 * 科研成果
	 */
	@ApiModelProperty(value = "科研经费")
	private String scientificFunding;
	
	/**
	 * 1 近一周
	 * 2 近一月
	 * 3 近一年
	 */
	@ApiModelProperty(value = "查询类型")
	private  Integer queryType;

	/**
	 * research_tag2Ids
	 */
	@ApiModelProperty(value = "research_tag2Ids")
	private  String researchTag2Ids;

	@ApiModelProperty(value = "researchTag2Sql")
	private  String researchTag2Sql;

	@ApiModelProperty(value = "tagsql")
	private  String tagsql;

	@ApiModelProperty(value = "标签2")
	private String tag2;
    @ApiModelProperty(value = "相关批示")
    private String instructions;
    /**
     * 拼接字符串
     */
    private String sqlstr;
}