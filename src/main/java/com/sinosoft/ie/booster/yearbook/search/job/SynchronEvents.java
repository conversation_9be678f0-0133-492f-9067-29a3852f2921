package com.sinosoft.ie.booster.yearbook.search.job;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.HashedMap;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.service.impl.EventWeight;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEntityMapper;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEventMapper;
import com.sinosoft.ie.booster.yearbook.search.model.EsEvent;

import cn.easyes.annotation.rely.Analyzer;
import cn.easyes.annotation.rely.FieldType;
import cn.easyes.core.conditions.index.LambdaEsIndexWrapper;
import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SynchronEvents {

	@Resource
	private EventService eventService;
	@Resource
	private LibEntityService libEntityService;
	@Resource
	private LibEntityMapper libEntityMapper;
	@Autowired
	private MateAttributeService mateAttributeService;
	@Resource
	private EsEventMapper esEventMapper;
	@Resource
	private EsEntityMapper esEntityMapper;
	@Resource
	private EsDateHandle esDateHandle;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	@Resource
	private RestHighLevelClient client;
	@Value("${easy-es.address}")
	private String ip;
	@Resource
	private EventWeight eventWeight;
	// 所有的机构字段
	String org_arr[] = { "org_ids", "org2_id", "org_main", "org_related", "org_cooperation", "org_external",
			"org_achievement" };
	String person_arr[] = { "person_ids", "person_main", "person_related", "person_cooperation", "person_exchange",
			"person_achievements" };

	/**
	 * 先查询实体事件，再转成es数据格式
	 * 
	 * @throws IOException
	 */
	@Async
	public void synchronEvents() throws IOException {
		Long end_id = 0L;
		log.info("esEventMapper---1-->{}",esEventMapper);
		// 如果索引不存在，则手动创建
		if (!esEventMapper.existsIndex("event")) {
			log.info("esEventMapper---2-->{}",esEventMapper);
			LambdaEsIndexWrapper<EsEvent> wrapper = new LambdaEsIndexWrapper<>();
			esEventMapper.createIndex();
			log.info("esEventMapper---3-->{}",esEventMapper);
		} else {
			// 索引存在则查询id最大的一条数据
			LambdaEsQueryWrapper<EsEvent> es_query = new LambdaEsQueryWrapper<EsEvent>();
			es_query.orderByDesc("id");
			es_query.limit(1);
			List<EsEvent> maxlist = esEventMapper.selectList(es_query);
			if (maxlist.size() > 0) {
				end_id = Long.valueOf(maxlist.get(0).getId());
			}
		}

		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.gt("id", end_id);
		query.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
		long count = libEntityService.count(query);
		int pages = 1;
		if (count > 100) {
			pages = (int) (count / 100 + 1);
		} else {
			pages = 1;
		}
		for (int i = 1; i <= pages; i++) {
			Page<LibEntityEntity> page = new Page<>(i, 100);
			IPage<LibEntityEntity> useIPage = libEntityService.page(page, query);
			List<LibEntityEntity> entList = useIPage.getRecords();
			List<EsEvent> eslist = new ArrayList<EsEvent>();
			for (int j = 0; j < entList.size(); j++) {
				LibEntityEntity libEntityEntity = entList.get(j);
				String tit = libEntityEntity.getName();
				String data = libEntityEntity.getData();
				Map<String, Object> dataMap = JsonUtil.stringToMap(data);

				EsEvent esEvent = new EsEvent();
				esEvent.setTitle(tit);
				String content = (String) dataMap.get("content");
				esEvent.setContent(content);
				String start_time = (String) dataMap.get("start_time");
				esEvent.setStart_time(start_time);
				esEvent.setYear(start_time.substring(0, 4));
				String theme = (String) dataMap.get("theme");
				esEvent.setTheme(theme);
				com.alibaba.fastjson.JSONArray tagarry = (com.alibaba.fastjson.JSONArray) dataMap.get("tag");

				if (tagarry.size() != 0) {
					StringBuffer sb = new StringBuffer();
					for (int k = 0; k < tagarry.size(); k++) {
						sb.append(tagarry.get(k)).append(";");
					}
					esEvent.setTag(sb.toString());
				}

				esEvent.setId(libEntityEntity.getId().toString());

				// 人物
				Set<String> perSet = new HashSet<>();
				for (int k = 0; k < person_arr.length; k++) {
					String per_attr_name = person_arr[k];
					Object value = dataMap.get(per_attr_name);
					if (ObjectUtil.isEmpty(value)) {
						continue;
					}
					if ((value instanceof Collection)) {
						JSONArray arry = (JSONArray) value;
						for (int l = 0; l < arry.size(); l++) {
							Long id = arry.getLong(l);
							perSet.add(id.toString());
						}
					}
					if (!(value instanceof Collection)) {
						Long id = (Long) value;
						perSet.add(id.toString());
					}
				}
				esEvent.setPerIds(joinSet(perSet));
				// 机构
				Set<String> orgSet = new HashSet<>();
				for (int k = 0; k < org_arr.length; k++) {
					String org_attr_name = org_arr[k];
					Object value = dataMap.get(org_attr_name);
					if (ObjectUtil.isEmpty(value)) {
						continue;
					}
					if ((value instanceof Collection)) {
						JSONArray arry = (JSONArray) value;
						for (int l = 0; l < arry.size(); l++) {
							Long id = arry.getLong(l);
							orgSet.add(id.toString());
						}
					}
					if (!(value instanceof Collection)) {
						Long id = (Long) value;
						orgSet.add(id.toString());
					}
				}
				esEvent.setOrgIds(joinSet(orgSet));
				Object obj = dataMap.get("org_ids");
				if (obj != null) {
					LibEntityEntity orgEntity = libEntityService.getById((Long) obj);
					if (orgEntity != null) {
						esEvent.setOrg_name(orgEntity.getSynonymWord() + ";" + orgEntity.getName());
						esEvent.setOrg12_name(orgEntity.getName());
					}
				}

				Object pageNum_obj = (dataMap.get("pageNum"));
				if (pageNum_obj != null) {
					MateAttributeValueEntity entity = mateAttributeValueService.getById((Long) pageNum_obj);
					esEvent.setPageNum(entity.getValName());
				}
				eslist.add(esEvent);
			}
			esEventMapper.insertBatch(eslist);
		}
	}

	private Map<String, MateAttributeEntity> attr_Map = new HashedMap<>();

	public MateAttributeEntity getAttrEntity(String attr_name) {
		if (attr_Map.get(attr_name) != null) {
			return attr_Map.get(attr_name);
		}
		QueryWrapper<MateAttributeEntity> query = new QueryWrapper<MateAttributeEntity>();
		query.lambda().and(t -> t.eq(MateAttributeEntity::getNameEn, attr_name));
		query.lambda().and(t -> t.eq(MateAttributeEntity::getOnto, YearbookConstant.MARK_EVENT_ONTO_ID));
		List<MateAttributeEntity> attrList = mateAttributeService.list(query);
		attr_Map.put(attr_name, attrList.get(0));
		return attrList.get(0);
	}

	public String joinSet(Set<String> names) {
		StringBuffer sb = new StringBuffer();
		for (String string : names) {
			sb.append(string).append(";");
		}
		return sb.toString();
	}
}
