package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibConceptMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeAndValueVo;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaEntityRelationService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkEntityMapper;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkSentenceMapper;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkTripletMapper;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityListVO;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkEntityService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkInfoService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkTripletService;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class MarkInfoServiceImpl implements MarkInfoService {

	@Autowired
	private LibConceptService libConceptService;
	@Autowired
	private MetaOntoRelationService metaOntoRelationService;
	@Autowired
	private MarkEntityService markEntityService;
	@Autowired
	private MarkTripletService markTripletService;
	@Autowired
	private MarkSentenceMapper sentenceMapper;
	@Autowired
	private MarkEntityMapper markEntityMapper;
	@Autowired
	private MarkTripletMapper markTripletMapper;
	@Autowired
	private MetaOntoRelationMapper metaOntoRelationMapper;
	@Autowired
	private LibConceptMapper libConceptMapper;
	@Autowired
	private LibEntityService libEntityService;
	@Autowired
	private MateAttributeService mateAttributeService;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	@Resource
	private MetaEntityRelationService metaEntityRelationService;

	@Override
	public List<MateAttributeAndValueVo> getPropertsList(Long id) {

		LibConceptEntity entity = libConceptService.getById(id);
		List<MateAttributeEntity> list = mateAttributeService
				.list(new LambdaQueryWrapper<MateAttributeEntity>().eq(MateAttributeEntity::getOnto, entity.getId()));
		// 查询出所有属性并转换
		List<MateAttributeAndValueVo> attValList = new ArrayList<MateAttributeAndValueVo>();
		list.forEach(x -> {
			if(x.getValType() != YearbookConstant.META_ATTRIBUTE_VAL_OBJECT) {
				MateAttributeAndValueVo vo = new MateAttributeAndValueVo();
				vo.setId(x.getId());
				vo.setNameCn(x.getNameCn());
				vo.setPid(x.getPid());
				vo.setFromType(x.getType());
				vo.setIsVal(1);
				attValList.add(vo);
			}
		});

		for (int i = 0; i < attValList.size(); i++) {
			MateAttributeAndValueVo vo = attValList.get(i);
			// 如果当前属性是下拉列表
			if (vo.getFromType() == YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT) {
				QueryWrapper<MateAttributeValueEntity> valWraper = new QueryWrapper<MateAttributeValueEntity>();
				valWraper.eq("attribute_id", vo.getId());
				List<MateAttributeValueEntity> valueList = mateAttributeValueService.list(valWraper);
				valueList.forEach(val -> {
					MateAttributeAndValueVo vvo = new MateAttributeAndValueVo();
					vvo.setId(val.getId());
					vvo.setPid(val.getPid());
					vvo.setNameCn(val.getValName());
					// 把pid=0的值挂到【属性】下
					if (val.getPid() == 0) {
						vvo.setPid(val.getAttributeId());
					}
					vvo.setIsVal(0);
					attValList.add(vvo);
				});
			}

		}
		return attValList;
	}

	public String chagePre(String name) {
		if (name.indexOf("_") < 0) {
			return name;
		}
		String str[] = name.split("_");
		StringBuffer sb = new StringBuffer();
		sb.append(str[0]).append(str[1].substring(0, 1).toUpperCase()).append(str[1].substring(1, str[1].length()));
		return sb.toString();
	}

	@Override
	public Map<String, Object> getTagInfo(Long id) {
		Map<String, Object> map = new HashMap<>();
		MarkSentenceEntity sentenceEntity = sentenceMapper.selectById(id);
		if (sentenceEntity == null) {
			return null;
		}
		// 查询出当前文本的所有标签 ,并转成map<id,MarkEntityListVO>
		String juzi = sentenceEntity.getSentenceExc();
		map.put("content", juzi);
		QueryWrapper<MarkEntityEntity> war = new QueryWrapper<MarkEntityEntity>();
		war.eq("sentenceId", id);
		//war.gt("startIndex", 0);
		List<MarkEntityEntity> markEntityList = markEntityService.list(war);

		List<MarkEntityListVO> markEntityListVOS = JsonUtil.getJsonToList(markEntityList, MarkEntityListVO.class);

		// List<MarkEntityListVO> markEntityListVOS =
		// markEntityMapper.queryByWapper(id);
		Map<String, MarkEntityListVO> markEntList = markEntityListVOS.stream()
				.collect(Collectors.toMap(MarkEntityListVO::getId, MarkEntityListVO -> MarkEntityListVO));
		// 查询出当前文本的所有关系 并转成map<tag1Id,List<MarkTripletEntity>>
		QueryWrapper<MarkTripletEntity> markTripletEntityQueryWrapper = new QueryWrapper<>();
		markTripletEntityQueryWrapper.eq("sentenceId", id);
		List<MarkTripletEntity> tripletList = markTripletService.list(markTripletEntityQueryWrapper);
		Map<Long, List<MarkTripletEntity>> tripletListMap = tripletList.stream().collect(Collectors.groupingBy(MarkTripletEntity::getTag1id));

		// 把实体和属性分成两个集合
		List<MarkEntityListVO> ent_lib_List = new ArrayList<MarkEntityListVO>();
		List<MarkEntityListVO> attr_lib_List = new ArrayList<MarkEntityListVO>();
		markEntityListVOS.forEach(x -> {
			if (x.getTagname().equals(YearbookConstant.MARK_ENTITY)) {
				ent_lib_List.add(x);
			} else {
				attr_lib_List.add(x);
			}
		});

		// 标签类型集合
		List<Map<String, Object>> labelCategoriesList = new ArrayList<>();
		// 标签集合
		List<Map<String, Object>> labelsList = new ArrayList<>();
		// 关系集合
		List<Map<String, Object>> connectionsList = new ArrayList<>();
		// 关系类型集合
		List<Map<String, Object>> connectionCategoriesList = new ArrayList<>();
		for (int i = 0; i < ent_lib_List.size(); i++) {

			MarkEntityListVO vo = ent_lib_List.get(i);

			// 创建标签类型
			Map<String, Object> labelCategoriesMap = new HashMap<>();
			labelCategoriesMap.put("text", vo.getTagname() + ":" + vo.getOntoname());
			labelCategoriesMap.put("id", vo.getTagId());
			labelCategoriesList.add(labelCategoriesMap);
			// 应该去重，先不做

			// 创建标签map
			Map<String, Object> labelsMap = new HashMap<>();
			labelsMap.put("id", vo.getId());
			labelsMap.put("categoryId", vo.getTagId());
			labelsMap.put("startIndex", vo.getStartindex());
			labelsMap.put("endIndex", vo.getEndindex());
			labelsList.add(labelsMap);

			// 获取当前标签的所有关系
			List<MarkTripletEntity> tripletListByTagId = tripletListMap.get(Long.parseLong(vo.getId()));
			if (tripletListByTagId == null) {
				continue;
			}
			for (int j = 0; j < tripletListByTagId.size(); j++) {
				MarkTripletEntity triEnt = tripletListByTagId.get(j);
				Map<String, Object> connectionsMap = new HashMap<>();
				connectionsMap.put("id", triEnt.getId());
				connectionsMap.put("fromId", triEnt.getTag1id());
				connectionsMap.put("toId", triEnt.getTag2id());
				Map<String, Object> connectionsCategoriesMap = new HashMap<>();
				connectionsCategoriesMap.put("id", triEnt.getRelationid());
				connectionsCategoriesMap.put("text", triEnt.getRelationname());
				connectionCategoriesList.add(connectionsCategoriesMap);
				// 判断是实体-实体 or 实体-属性
				// 获取关联标签
				MarkEntityListVO ent2 = markEntList.get(triEnt.getTag2id());
				// 如果是实体
				connectionsMap.put("categoryId", triEnt.getRelationid());
				connectionsList.add(connectionsMap);
			}

		}
		// 循环属性标签
		for (int i = 0; i < attr_lib_List.size(); i++) {
			MarkEntityListVO vo = attr_lib_List.get(i);

			Map<String, Object> labelCategoriesMap = new HashMap<>();
			labelCategoriesMap.put("text", vo.getTagname() + ":" + vo.getOntoname());
			String tid = "";
			String pid = "";
			// 创建标签类型

			if (vo.getLabType() == 2) {
				tid = vo.getTagId();
				labelCategoriesMap.put("isVal", 1);
			}
			if (vo.getLabType() == 3 || vo.getLabType() == 4) {
				tid = vo.getTagId();
				pid = vo.getTagPid();
				labelCategoriesMap.put("pid", pid);
				labelCategoriesMap.put("isVal", 0);
			}
			labelCategoriesMap.put("id", tid);
			labelCategoriesList.add(labelCategoriesMap);

			// 应该去重，先不做

			// 创建标签map
			Map<String, Object> labelsMap = new HashMap<>();
			labelsMap.put("id", vo.getId());
			labelsMap.put("categoryId", tid);
			labelsMap.put("startIndex", vo.getStartindex());
			labelsMap.put("endIndex", vo.getEndindex());
			labelsList.add(labelsMap);

		}
		map.put("labelCategories", distMap(labelCategoriesList));
		map.put("labels", labelsList);

		map.put("connectionCategories",  distMap(connectionCategoriesList));
		map.put("connections", connectionsList);

		return map;
	}

	public List  distMap(List<Map<String,Object>>  sourceList) {
		List<Map<String,Object>> dataList  =  new ArrayList<Map<String,Object>>();
		List<Object> keyList = new ArrayList<Object>();
		for (Map<String,Object> map: sourceList) {
			Object oid = map.get("id");
			if(!keyList.contains(oid)) {
				keyList.add(oid);
				dataList.add(map);
			}
		}
		return dataList;
	}
	
	
	int long2int(Long longNum, Map<Long, Integer> numMap) {
		int intNuM = (int) (Math.random() * 9000);
		numMap.put(longNum, intNuM);
		return intNuM;
	}

	@Transactional
	public R insertTagInfo(Map<String, Object> map) throws DataException {
		String msg = "";
		// labels的id转long的map
		Map<String, Long> labelsNumMap = new HashMap<>();
		System.out.println("map" + map);
		Long sentenceId = Long.valueOf(map.get("sentenceId").toString());
		MarkSentenceEntity sentenceEntity = sentenceMapper.selectById(sentenceId);
		String juzi = sentenceEntity.getSentenceExc();
		List<Map<String, Object>> labelCategoriesMap = (List<Map<String, Object>>) map.get("labelCategories");

		// 删除旧的数据
		QueryWrapper<MarkEntityEntity> markEntityEntityQueryWrapper = new QueryWrapper<>();
		markEntityEntityQueryWrapper.lambda().and(t -> t.eq(MarkEntityEntity::getSentenceid, sentenceId));
		markEntityMapper.delete(markEntityEntityQueryWrapper);

		QueryWrapper<MarkTripletEntity> markTripletEntityQueryWrapper = new QueryWrapper<>();

		markTripletEntityQueryWrapper.lambda().or(t -> t.in(MarkTripletEntity::getSentenceid, sentenceId));
		markTripletMapper.delete(markTripletEntityQueryWrapper);

		// 标签类型做成map集合
		Map<String, Map<String, Object>> labelTypeMap = new HashMap<>();
		for (Map<String, Object> labelCategories : labelCategoriesMap) {
			String id = labelCategories.get("id").toString();
			labelTypeMap.put(id, labelCategories);
		}

		List<Map<String, String>> connectionCategoriesMap = (List<Map<String, String>>) map.get("connectionCategories");
		// 关系类型做成map
		Map<String, Map<String, String>> conTypeMap = new HashMap<>();
		for (Map<String, String> conlCategories : connectionCategoriesMap) {
			String id = conlCategories.get("id");
			conTypeMap.put(id, conlCategories);
		}

		Map<String, String> conTypeMap1 = new HashMap<>();
		for (Map<String, String> conlCategories : connectionCategoriesMap) {
			if( !StringUtils.isEmpty(conlCategories.get("text").toString()) ) {
				conTypeMap1.put(conlCategories.get("id").toString(), conlCategories.get("text").toString());
			}
		}
		
		Map<Long, MarkEntityEntity> markEntityMap = new HashMap<>();
		for (Map<String, Object> labels : (List<Map<String, Object>>) map.get("labels")) {
			MarkEntityEntity markEntityEntity = new MarkEntityEntity();
			markEntityEntity.setSentenceid(sentenceId);
			markEntityEntity.setValType(0);
			markEntityEntity.setStartindex((Integer) labels.get("startIndex"));
			markEntityEntity.setEndindex((Integer) labels.get("endIndex"));
			String text = juzi.substring((Integer) labels.get("startIndex"), ((Integer) labels.get("endIndex")));
			markEntityEntity.setText(text);

			String categoryId = null;
			if (!ObjectUtil.isEmpty(labels.get("categoryId"))) {
				categoryId = labels.get("categoryId").toString();
			} else {
				continue;
			}

			// 获取标签类型的各个值
			Map<String, Object> labtype = labelTypeMap.get(categoryId);
			String tagNameonto = labtype.get("text").toString();
			String[] tags = tagNameonto.split(":");
			// 如果是实体标签
			if (YearbookConstant.MARK_ENTITY.equals(tags[0])) {
				markEntityEntity.setTagname(tags[0]);
				markEntityEntity.setLabType(1);
				String ontoName = tags[1];
				markEntityEntity.setOntoname(ontoName);
				String cid = labels.get("categoryId").toString();
				markEntityEntity.setTagId(Long.parseLong(cid));
			}
			// 如果是属性标签
//			else {
//				String ontoName = "";
//				markEntityEntity.setTagname(tags[0]);
//				String cid = labels.get("categoryId").toString();
//				markEntityEntity.setTagId(Long.parseLong(cid));
//				Map<String, Object> labelCategories = labelTypeMap.get(cid);
//
//				String isVal = labelCategories.get("isVal").toString();
//				// 属性
//				if (tags.length==2 ) {
//					ontoName = tags[1];
//					markEntityEntity.setOntoname(ontoName);
//					markEntityEntity.setLabType(2);
//					System.out.println("*-*-*-*-*-*-");
//					System.out.println(markEntityEntity.getText());
//				}
//				// 属性值
//				else if(tags.length==3 ){
//					String cpid = labelCategories.get("pid").toString();
//					markEntityEntity.setLabType(3);
//					ontoName = tags[1] + ":" + tags[2];
//					Long pid = Long.parseLong(cpid);
//					markEntityEntity.setTagPid(pid);
//				}
//				markEntityEntity.setOntoname(ontoName);
//			}
			markEntityMapper.insert(markEntityEntity);
			labelsNumMap.put(labels.get("id").toString(), markEntityEntity.getId());
			markEntityMap.put(markEntityEntity.getId(), markEntityEntity);
		}

		// 以左本体id为key,把相同左本体的关系放到集合中为value
		Map<Long, List<MarkTripletEntity>> relationMap = new HashMap<>();
		for (Map<String, Object> connections : (List<Map<String, Object>>) map.get("connections")) {
			MarkTripletEntity markTripletEntity = new MarkTripletEntity();
			markTripletEntity.setTag1id(labelsNumMap.get(connections.get("fromId").toString()));
			markTripletEntity.setTag2id(labelsNumMap.get(connections.get("toId").toString()));
			// 关系id
			String conId = connections.get("categoryId").toString();
			markTripletEntity.setRelationid(Long.valueOf(conId) == 0 ? null : Long.valueOf(conId));
			markTripletEntity.setSentenceid(sentenceId);
			Map<String, String> conMap = conTypeMap.get(conId);
			//markTripletEntity.setRelationname(conMap.get("text").toString());
			
			String  relation_name = conTypeMap1.get(conId);
			markTripletEntity.setRelationname(relation_name);
			markTripletMapper.insert(markTripletEntity);

			List rlist = relationMap.get(markTripletEntity.getTag1id());
			if (rlist == null) {
				List<MarkTripletEntity> list = new ArrayList<MarkTripletEntity>();
				list.add(markTripletEntity);
				relationMap.put(markTripletEntity.getTag1id(), list);
			} else {
				rlist.add(markTripletEntity);
			}
		}

		// 先根据语料id,本体id和实体id从实体表查询，如果有则覆盖
		// 把实体的的txt组装到集合
		List<String> namelist = new ArrayList<String>();
		for (Long key : markEntityMap.keySet()) {
			MarkEntityEntity ent = markEntityMap.get(key);
			if (ent.getTagname().equals(YearbookConstant.MARK_ENTITY)) {
				namelist.add(ent.getText().trim());
			}
		}
//		// 根据语料id查询对象
//		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
//		query.lambda().and(t -> t.eq(LibEntityEntity::getCorpusId, sentenceEntity.getCorpusId()));
//		query.lambda().and(t -> t.in(LibEntityEntity::getName, namelist));
//		List<LibEntityEntity> entlist = libEntityService.list(query);
////		Map<String, LibEntityEntity> nameMap = entlist.stream().collect(Collectors.toMap(LibEntityEntity::getName, LibEntityEntity -> LibEntityEntity));
//		MultiKeyMap<Object, Object> multiKey = new MultiKeyMap<>();
//		for (int i = 0; i < entlist.size(); i++) {
//			LibEntityEntity lib =  entlist.get(i);
//			multiKey.put(lib.getName(),lib.getOntoId(), lib);
//		}
//		
//		List<LibEntityEntity> libEnt_new_List = new ArrayList<LibEntityEntity>();
//		// MarkEntityEntity的id和新保存的LibEntityEntity实体对象做映射
//		Map<Long, LibEntityEntity> libEntityEntityMap = new HashMap<Long, LibEntityEntity>();
//		// 先把所有的实体都保存下
//		for (Long key : markEntityMap.keySet()) {
//			MarkEntityEntity ent = markEntityMap.get(key);
//			if (ent.getTagname().equals(YearbookConstant.MARK_ENTITY)) {
//				LibEntityEntity libEntity = (LibEntityEntity)multiKey.get(ent.getText(),ent.getTagId());
//				//LibEntityEntity libEntity = nameMap.get(ent.getText());
//				if (libEntity == null) {
//					JSONObject json = JSONUtil.createObj();
//					json.set("name", ent.getText());
//					libEntity = new LibEntityEntity();
//					libEntity.setData(json.toString());
//					libEntity.setOntoId(ent.getTagId());
//					libEntity.setName(ent.getText().trim());
//					libEntity.setCorpusId(sentenceEntity.getCorpusId());
//					libEnt_new_List.add(libEntity);
//				}
//				libEntityEntityMap.put(ent.getId(), libEntity);
//			}
//		}
//		libEntityService.saveOrUpdateBatch(libEnt_new_List);

		// 循环标签，判断是否是“实体”
//		for (Long key : markEntityMap.keySet()) {
//			MarkEntityEntity ent = markEntityMap.get(key);
//			// 如果是实体，寻找关系
//			System.out.println(ent.getOntoname());
//			if (ent.getTagname().equals(YearbookConstant.MARK_ENTITY)) {
//				List<MarkTripletEntity> list = relationMap.get(ent.getId());
//				if (list == null) {
//					continue;
//				}
//				LibEntityEntity libEntity = libEntityEntityMap.get(ent.getId());
//				libEntity.setOntoId(ent.getTagId());
//				libEntity.setCorpusId(sentenceEntity.getCorpusId());
//				JSONObject json = JSONUtil.createObj();
//				json.set("name", ent.getText());
//				// 特殊处理 【事件内容】
//				// 获取关系集合
//				for (int i = 0; i < list.size(); i++) {
//					MarkTripletEntity triple_lab = list.get(i);
//					// 获取右实体
//					MarkEntityEntity right_ent = markEntityMap.get(triple_lab.getTag2id());
//					if (right_ent.getTagname().equals(YearbookConstant.MARK_ENTITY)) {
//						// 根据关系id查询属性对象
//						MateAttributeEntity attrEnt = OntoCache
//								.getMateAttributeEntityByRelationId(triple_lab.getRelationid());
//						if (attrEnt == null) {
//							msg = "【" + triple_lab.getRelationname() + "】关系对应的属性为NULL,请修复元数据";
//							throw new DataException(msg);
//						}
//						//LibEntityEntity right_lib_ent = libEntityEntityMap.get(right_ent.getId());
//						
//						// 该属性是单个值
//						if (attrEnt.getVals() == 1) {
//							String vals = json.getStr(attrEnt.getNameEn());
//							if(vals!=null) {
//								msg = "【" + attrEnt.getNameCn() + "】该属性是单一值，但在同一实体中标注了多个值！";
//								throw new DataException(msg);
//							}
//							json.set(attrEnt.getNameEn(), right_lib_ent.getId());
//						} else {
//							JSONArray vals = json.getJSONArray(attrEnt.getNameEn());
//							// 当前json没有值
//							if (vals == null) {
//								vals = JSONUtil.createArray();
//							}
//							vals.add(right_lib_ent.getId());
//							json.set(attrEnt.getNameEn(), vals);
//						}
//						
//						LibEntityEntity right_libEntity = libEntityEntityMap.get(right_ent.getId());
//						//查询实体间关系
//						QueryWrapper<MetaEntityRelationEntity> relation_query  =  new QueryWrapper<MetaEntityRelationEntity>();
//						relation_query.eq("onto1_id", ent.getTagId());
//						relation_query.eq("onto2_id", right_ent.getTagId());
//						relation_query.eq("entity1_Id", libEntity.getId());
//						relation_query.eq("entity2_Id", right_libEntity.getId());
//						relation_query.eq("onto_rel_id", triple_lab.getRelationid());
//						long count = metaEntityRelationService.count(relation_query);
//						if(count==0) {
//							// 保存实体和实体的关系
//							MetaEntityRelationEntity relation_ent = new MetaEntityRelationEntity();
//							relation_ent.setOnto1Id(ent.getTagId());
//							relation_ent.setEntity1Id(libEntity.getId());
//							relation_ent.setOnto2Id(right_ent.getTagId());
//							relation_ent.setEntity2Id(right_libEntity.getId());
//							relation_ent.setOntoRelId(triple_lab.getRelationid());
//							relation_ent.setName(triple_lab.getRelationname());
//							metaEntityRelationService.create(relation_ent);
//						}
//					}
//					if (right_ent.getTagname().equals(YearbookConstant.MARK_ATTRIBUTE)) {
//						Object text = right_ent.getText();
//						MateAttributeEntity attrEnt = null;
//						if (right_ent.getLabType() == 2 || right_ent.getLabType() == 4) {
//							
//							// 从缓存获取属性对象
//							attrEnt = OntoCache.getMateAttributeEntityId(right_ent.getTagId());
//							if (attrEnt == null) {
//								msg = "【" + right_ent.getOntoname() + "】未查询到该属性,请修复元数据;属性id=" + right_ent.getTagId();
//								throw new DataException(msg);
//							}
//							System.out.println(attrEnt.getNameCn());
//							// 如果是对象类型
//							if (attrEnt.getValType() == YearbookConstant.META_ATTRIBUTE_VAL_OBJECT) {
//								msg = "【" + attrEnt.getNameCn() + "】该属性是一个对象属性，应标注为实体类型";
//								throw new DataException(msg);
//							}
//							// 如果是枚举值类型
//							if (attrEnt.getType() == YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT) {
//								// 用鹏宇的通用代码块
//								// 先查询，如果有则用该id，没有则插入数据
//								MateAttributeValueEntity valEnt = new MateAttributeValueEntity();
//								valEnt.setAttributeId(attrEnt.getId());
//								valEnt.setValName(text.toString());
//								valEnt.setPid(0L);
//								valEnt.setOntoId(attrEnt.getOnto());
//								valEnt.setSchemaId(attrEnt.getSchemaId());
//								Long vid = queryAttrVal(valEnt);
//								
//								//如果该属性是但一值
//								if (attrEnt.getVals() == 1) {
//									String vals = json.getStr(attrEnt.getNameEn());
//									if(vals!=null) {
//										msg = "【" + attrEnt.getNameCn() + "】该属性是单一值，但在同一实体中标注了多个值！";
//										throw new DataException(msg);
//									}
//									text = vid;
//								}else {
//									JSONArray vals = json.getJSONArray(attrEnt.getNameEn());
//									// 当前json没有值
//									if (vals == null) {
//										vals = JSONUtil.createArray();
//									}
//									vals.add(vid);
//									text = vals;
//								}
//							}
//						}
//						if (right_ent.getLabType() == 3) {
//							attrEnt = OntoCache.getMateAttributeEntityId(right_ent.getTagPid());
//							if (attrEnt == null) {
//								msg = "【" + right_ent.getOntoname() + "】未查询到该属性,请修复元数据;属性id=" + right_ent.getTagPid();
//								throw new DataException(msg);
//							}
//							
//							//如果是单一值
//							if (attrEnt.getVals() == 1) {
//								String vals = json.getStr(attrEnt.getNameEn());
//								if(vals!=null) {
//									if(vals!=null) {
//										msg = "【" + attrEnt.getNameCn() + "】该属性是单一值，但在同一实体中标注了多个值！";
//										throw new DataException(msg);
//									}
//								}
//								text = right_ent.getTagId();;
//							}
//							else {
//								JSONArray vals = json.getJSONArray(attrEnt.getNameEn());
//								// 当前json没有值
//								if (vals == null) {
//									vals = JSONUtil.createArray();
//								}
//								vals.add(right_ent.getTagId());
//								
//								text = vals;
//							}
//						}
//						// 如果是日期类型
//						if (attrEnt.getValType() == YearbookConstant.META_ATTRIBUTE_VAL_DATA) {
//							text = convertTimeStr(right_ent.getText());
//							if (!isValidDate(text.toString())) {
//								String year =  null;
//								for (MarkTripletEntity  tri: list) {
//									if(tri.getRelationname().equals(YearbookConstant.MARK_EVENT_YEAR)) {
//										MarkEntityEntity year_ent = markEntityMap.get(tri.getTag2id());
//										MateAttributeEntity year_attr = OntoCache.getMateAttributeEntityId(year_ent.getTagPid());
//										List<MateAttributeValueEntity> valList  = OntoCache.getAttrValListByAttrId(year_attr.getId());
//										for (int j = 0; j < valList.size(); j++) {
//											if(valList.get(j).getId().equals(year_ent.getTagId())) {
//												year = valList.get(j).getValName();
//												break;
//											}
//										}
//										break;
//									}
//								}
//								if(year==null) {
//									msg = "未选择【事件年份】的值，转化失败!";
//									throw new DataException(msg);
//								}
//								else {
//									text = year + "-" + text;
//								}
//							}
//						}
//						json.set(attrEnt.getNameEn(), text);
//					}
//				}
//		
//				libEntity.setData(json.toString());
//				libEntityService.saveOrUpdate(libEntity);
//			}
//		}
		sentenceEntity.setStatus(YearbookConstant.MARK_STATS_COMPLE);
		sentenceEntity.setUpdateTime(LocalDateTime.now());
		sentenceMapper.updateById(sentenceEntity);
		
		return R.ok();
	}

	public String convertTimeStr(String str) {
		String year = "(\\d{4})年";
		String meth = "([0-1]?[0-9])月";
		String day = "([1|2|3]?[0-9])日";

		StringBuffer sb = new StringBuffer();
		Pattern pat_y = Pattern.compile(year);
		Matcher mat_y = pat_y.matcher(str);
		if (mat_y.find()) {
			// 将匹配当前正则表达式的字符串即文件名称进行赋值
			String y = mat_y.group();
			System.out.println(y);
			sb.append(y.replace("年", "")).append("-");
		}

		Pattern pat_m = Pattern.compile(meth);
		Matcher mat_m = pat_m.matcher(str);
		if (mat_m.find()) {
			// 将匹配当前正则表达式的字符串即文件名称进行赋值
			String y = mat_m.group();
			String m =y.replace("月", "");
			Integer mi = Integer.parseInt(m);
			if(mi<10) {
				sb.append("0").append(mi).append("-");
			}else {
				sb.append(mi).append("-");
			}
			
		}

		Pattern pat_d = Pattern.compile(day);
		Matcher mat_d = pat_d.matcher(str);
		if (mat_d.find()) {
			// 将匹配当前正则表达式的字符串即文件名称进行赋值
			String y = mat_d.group();
			String d = y.replace("日", "");
			Integer di = Integer.parseInt(d);
			if(di<10) {
				sb.append("0").append(di);
			}else {
				sb.append(di);
			}
		}
		return sb.toString();
	}

	/**
	 * 判断日期格式是否正确
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isValidDate(String str) {
		boolean convertSuccess = true;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		try {
			format.setLenient(false);
			format.parse(str);
		} catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	public Long queryAttrVal(MateAttributeValueEntity ent) {
		QueryWrapper<MateAttributeValueEntity> query = new QueryWrapper<MateAttributeValueEntity>();
		query.lambda().and(t -> t.eq(MateAttributeValueEntity::getAttributeId, ent.getAttributeId()));
		query.lambda().and(t -> t.eq(MateAttributeValueEntity::getValName, ent.getValName()));

		List<MateAttributeValueEntity> list = mateAttributeValueService.list(query);
		if (list.size() > 0) {
			return list.get(0).getId();
		}
		mateAttributeValueService.save(ent);
		return ent.getId();
	}

}
