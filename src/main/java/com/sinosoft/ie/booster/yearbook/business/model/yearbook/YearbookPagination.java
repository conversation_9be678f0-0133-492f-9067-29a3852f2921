package com.sinosoft.ie.booster.yearbook.business.model.yearbook;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Yearbook模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 13:39:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class YearbookPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

	/**
	 * 年鉴名称
	 */
	@ApiModelProperty(value = "年鉴名称")
	private String bookName;

	/**
	 * 年鉴日期
	 */
	@ApiModelProperty(value = "年鉴日期")
	private String bookYear;

	/**
	 * 封面图片
	 */
	@ApiModelProperty(value = "封面图片")
	private String coverPic;

    /**
     * 原文件名称
     */
    @ApiModelProperty(value = "原文件名称")
    private String oldName;

    /**
     * 文件类型：1pdf，2word
     */
    @ApiModelProperty(value = "文件类型：1pdf，2word")
    private String type;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long pid;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private String delFlag;
}