package com.sinosoft.ie.booster.yearbook.business.model.subject;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * Subject模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 14:03:56
 */
@Data
@ApiModel
public class SubjectListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String field;

}