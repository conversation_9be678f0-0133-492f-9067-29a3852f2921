package com.sinosoft.ie.booster.yearbook.search.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibMinEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.WordCloudEntity;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.job.EsDateHandle;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEntityMapper;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEventMapper;
import com.sinosoft.ie.booster.yearbook.search.model.EsEvent;
import com.sinosoft.ie.booster.yearbook.search.model.EsQueryPagination;
import com.sinosoft.ie.booster.yearbook.search.service.SearchServivce;

import cn.easyes.core.conditions.select.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SearchServivceImpl implements SearchServivce {

	@Resource
	private LibEntityService libEntityService;
	@Resource
	private EsEventMapper esEventMapper;
	@Resource
	private EsEntityMapper esEntityMapper;
	@Resource
	private RestHighLevelClient client;
	@Autowired
	private EventService eventService;
	@Autowired
	private EsDateHandle esDateHandle;
	@Autowired
	private LibEntityMapper libEntityMapper;
	@Resource
	private MetaEntityRelationMapper metaEntityRelationMapper;

	/**
	 * 返回信息 exact_match 是否精准匹配 1是 0模糊匹配 entity_onto 精准匹配的对象类型 per:人物;org:机构;thema:主题
	 * entity 精准匹配的实体
	 * 
	 * @throws IOException
	 */
	@Override
	public R search(String str) throws IOException {
		str = str.trim();
		Map<String, Object> rmap = new HashMap<>();
		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.eq("name", str);
		List<Long>  ids =  new ArrayList<Long>();
		ids.add(YearbookConstant.MARK_PER_ONTO_ID);
		ids.add(YearbookConstant.MARK_ORG_ONTO_ID);
		ids.add(YearbookConstant.MARK_THEME_ONTO_ID);
		query.in("onto_id", ids);
		query.apply("json_value(data,'$.portrait') = '1' ");
		List<LibEntityEntity> list = libEntityService.list(query);
		
		//事件查询加权字段
		String weight = "null";
		// 当前页要显示的事件数目
		int eventSize = 10;
		// 计算返回结果总数
		long count = 0;
		// 精准匹配
		if (list.size() > 0) {
			LibEntityEntity entity = list.get(0);
			if (ids.contains(entity.getOntoId())) {
				JSONObject jsonEntity = esDateHandle.coverEntity(entity);
				String id = entity.getId().toString();
				jsonEntity.set("id", id);
				rmap.put("exact_match", 1);
				rmap.put("entity_onto", getEntityCoder(String.valueOf(entity.getOntoId())));
				rmap.put("entity", jsonEntity);
				if(getEntityCoder(String.valueOf(entity.getOntoId())).equals("per")) {
					rmap.put("per_list_size", 1); 
				}
				if(getEntityCoder(String.valueOf(entity.getOntoId())).equals("org")) {
					rmap.put("org_list_size", 1);
				}
				// 第一页事件展示的条目数
				eventSize--;
				// 查询的结果总数
				count++;
				if (entity.getOntoId().equals(YearbookConstant.MARK_THEME_ONTO_ID)) {
					weight =  "theme";
					String data = entity.getData();
					Map<String, Object> dataMap = JsonUtil.stringToMap(data);
					Object funds_int = dataMap.get("funds_int");
					Map map = getEventByTheme(str);
					map.put("founds", funds_int);
					rmap.put("theme", map);
				}
				if (entity.getOntoId().equals(YearbookConstant.MARK_ORG_ONTO_ID)) {
					weight = "org";
				}
			} else {
				// 模糊匹配
				rmap.put("exact_match", 0);
			}
		} else {
			// 模糊匹配
			rmap.put("exact_match", 0);
		}

		EsQueryPagination pagination = new EsQueryPagination();
		pagination.setStart_idx(0);
		pagination.setPageNum(1);
		pagination.setStr(str);

		// 如果是精准匹配到人物，则不查人物列表了
		if (!"per".equals(rmap.get("entity_onto"))) {
			// 查询人物属性 "职务","简介" synopsis
			pagination.setPageSize(7);
			com.alibaba.fastjson.JSONArray per_list = query_string_per(pagination, YearbookConstant.MARK_PER_ONTO_ID);
			if(per_list.size()==0) {
				pagination.setPageSize(4);
				per_list = query_string(pagination, YearbookConstant.MARK_PER_ONTO_ID);
			}
			rmap.put("per_list", per_list);
			rmap.put("per_list_size", pagination.getTotal());
			if (pagination.getTotal() != 0) {
				eventSize--;
				count = count + pagination.getTotal();
			}
		}
		pagination.setPageSize(2);
		// 项目
		com.alibaba.fastjson.JSONArray project_list = query_string(pagination, YearbookConstant.MARK_PROJECT_ONTO_ID);
		rmap.put("project_list", project_list);
		rmap.put("project_list_size", pagination.getTotal());
		if (pagination.getTotal() != 0) {
			eventSize--;
		}
		// 成果
		com.alibaba.fastjson.JSONArray result_list = query_string(pagination, YearbookConstant.MARK_RESULT_ONTO_ID);
		rmap.put("result_list", result_list);
		rmap.put("result_list_size", pagination.getTotal());
		if (pagination.getTotal() != 0) {
			eventSize--;
			count = count + pagination.getTotal();
		}
		pagination.setPageSize(3);
		// 如果是精准匹配到机构，则不查机构列表了
		if (!"org".equals(rmap.get("entity_onto"))) {
			// 机构
			pagination.setPageSize(4);
			com.alibaba.fastjson.JSONArray org_list = query_string(pagination, YearbookConstant.MARK_ORG_ONTO_ID);
			rmap.put("org_list", org_list);
			rmap.put("org_list_size", pagination.getTotal());
			if (pagination.getTotal() != 0) {
				eventSize--;
				count = count + pagination.getTotal();
			}
		}
		EsQueryPagination event_query = new EsQueryPagination();
		event_query.setFirstPageSize(eventSize);
		event_query.setStr(str);

		List<EsEvent> event_list = queryEsEvent(event_query,weight);
		//事件总数
		long eve_count =  event_query.getTotal();
		count = count + event_query.getTotal();
		//页面上的条目总数
		event_query.setTotal((10-eventSize)+eve_count);
		
		rmap.put("event_list", event_list);
		rmap.put("event_list_size", eve_count);
		rmap.put("pages", event_query.getPages());
		rmap.put("searchResultCount", count);
		return R.ok(rmap);
	}

	public Map<String, Object> getEventByTheme(String theme) {
		Map<String, Object> result = new HashedMap<>();
		QueryWrapper<LibEntityEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.apply("json_value(data,'$.theme')= '" + theme + "'");
		List<LibEntityEntity> list = libEntityService.list(queryWrapper);
		int projectCount = 0, resultCount = 0, prizeCount = 0;
		double founds = 0;
		for (int i = 0; i < list.size(); i++) {
			LibEntityEntity entity = list.get(i);
			if (ObjectUtil.isEmpty(entity.getData())) {
				continue;
			}
			Map<String, Object> dataMap = JsonUtil.stringToMap(entity.getData());
			Object pro_obj = dataMap.get("research_project");
			if (pro_obj != null) {
				com.alibaba.fastjson.JSONArray pro_obj_array = (com.alibaba.fastjson.JSONArray) pro_obj;
				projectCount = projectCount + pro_obj_array.size();
			}
			Object resu_obj = dataMap.get("achievement");
			if (resu_obj != null) {
				com.alibaba.fastjson.JSONArray resu_obj_arry = (com.alibaba.fastjson.JSONArray) resu_obj;
				resultCount = resultCount + resu_obj_arry.size();
			}
			Object prize_obj = dataMap.get("prize");
			if (prize_obj != null) {
				com.alibaba.fastjson.JSONArray prize_obj_arry = (com.alibaba.fastjson.JSONArray) prize_obj;
				prizeCount = prizeCount + prize_obj_arry.size();
			}
//			Object funds = dataMap.get("funds");
//			if (funds != null) {
//				if(!(funds instanceof String)) {
//					log.error("id=={}的实体funds数据格式不对",entity.getId());
//				}
//				String []  array = ((String)funds).split(";");
//				for (int j = 0; j < array.length; j++) {
//					String str = (String) array[j];
//					Pattern pattern = Pattern.compile("^[-+]?\\d*\\.\\d+$"); // 正则表达
//					Matcher matcher = pattern.matcher(str);
//					if (matcher.matches()) {
//						Double num = Double.parseDouble(str);
//						founds = founds + num;
//					} else {
//						log.error("[" + entity.getId() + "]实体的[funds]值不符合规范！");
//					}
//				}
//			}
		}
		result.put("projectCount", projectCount);
		result.put("resultCount", resultCount);
		result.put("prizeCount", prizeCount);
//		result.put("founds", founds);
		return result;
	}

	/**
	 * es查询实体 用原生查询 查询逻辑 => "onto_id" == "123" and ("name" like "%name%" or
	 * "synopsis" like "synopsis" or ... )
	 * 
	 * @throws IOException
	 */
//	@Deprecated
//	public com.alibaba.fastjson.JSONArray queryEsEntity(EsQueryPagination query, Long ontoId) throws IOException {
//		String[] whereColunmns = selectColumn(ontoId);
//		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//		BoolQueryBuilder alllquery = new BoolQueryBuilder();
//		BoolQueryBuilder onto_query = new BoolQueryBuilder();
//		onto_query.must().add(QueryBuilders.termQuery("onto_id", String.valueOf(ontoId)));
//		alllquery.must(onto_query);
//		BoolQueryBuilder like_query = new BoolQueryBuilder();
//		like_query.should().add((QueryBuilders.matchQuery("name", query.getStr())));
//		if (whereColunmns != null) {
//			for (int i = 0; i < whereColunmns.length; i++) {
//				String attr_name = whereColunmns[i];
//				like_query.should().add((QueryBuilders.matchQuery(attr_name, query.getStr())));
//			}
//		}
//		alllquery.must(like_query);
//		SortOrder order = SortOrder.ASC;
//
//		if (ontoId.equals(YearbookConstant.MARK_PER_ONTO_ID)) {
//			FieldSortBuilder imgSort = SortBuilders.fieldSort("image_ids").order(order);
//			searchSourceBuilder.query(alllquery).sort(imgSort).from(query.getFirstPageSize()).size(query.getPageSize());
//		} else {
//			if (query.getPageNum() == 0) {
//				searchSourceBuilder.query(alllquery);
//			} else {
//				searchSourceBuilder.query(alllquery).from(query.getFirstPageSize()).size(query.getPageSize());
//			}
//		}
//
////		searchSourceBuilder.query(alllquery).from(query.getFirstPageSize()).size(query.getPageSize());
//
//		SearchRequest request = new SearchRequest();
//		request.source(searchSourceBuilder);
//		SearchResponse respone = client.search(request, RequestOptions.DEFAULT);
//		JSONArray jarry = JSONUtil.createArray();
//		com.alibaba.fastjson.JSONArray arry = new com.alibaba.fastjson.JSONArray();
//		Arrays.stream(respone.getHits().getHits()).forEach(x -> {
//			System.out.println(x);
////			JSONObject json = JSONUtil.parseObj(x);
////			jarry.add(json);
//
//			Object o = JSON.toJSON(x);
//			arry.add(o);
//
//		});
//		String dsl = searchSourceBuilder.toString();
//
//		long size = respone.getHits().getTotalHits().value;
//		query.setTotal(size);
//		return arry;
//	}

	/**
	 * 查询事件 自定义分页
	 */
	@Override
	public List<EsEvent> queryEsEvent(EsQueryPagination query,String weight) {
		List idlist  =  new ArrayList<Long>();
		idlist.add(YearbookConstant.MARK_THEME_ONTO_ID);idlist.add(YearbookConstant.MARK_ORG_ONTO_ID);
		QueryWrapper<LibEntityEntity> entQuery = new QueryWrapper<LibEntityEntity>();
		entQuery.eq("name", query.getStr());
		entQuery.in("onto_id", idlist);
		entQuery.apply("json_value(data,'$.portrait') = '1' ");
		List<LibEntityEntity> list = libEntityService.list(entQuery);
		if(list.size()>0) {
			if(list.get(0).getOntoId().equals(YearbookConstant.MARK_THEME_ONTO_ID)) {
				weight = "theme";
			}else {
				weight = "org";
			}
		}
		LambdaEsQueryWrapper<EsEvent> es_query = new LambdaEsQueryWrapper<EsEvent>();
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder alllquery = new BoolQueryBuilder();
		BoolQueryBuilder str_query = new BoolQueryBuilder();
		MatchPhraseQueryBuilder tq =new  MatchPhraseQueryBuilder("title", query.getStr()).slop(5);
		MatchPhraseQueryBuilder cq =new MatchPhraseQueryBuilder("content", query.getStr()).slop(5);
		str_query.should(tq);
		str_query.should(cq);
		BoolQueryBuilder weigt_query = new BoolQueryBuilder();
		if(weight.equals("org")) {
			MatchPhraseQueryBuilder orgquery  =new  MatchPhraseQueryBuilder("org_name", query.getStr());
			orgquery.boost(10);
			str_query.should(orgquery);
			
		}
		if(weight.equals("theme")) {
			MatchPhraseQueryBuilder themequery =new  MatchPhraseQueryBuilder("theme", query.getStr());
			themequery.boost(11);
			str_query.should(themequery);
		}
		alllquery.must(str_query);
		alllquery.should(weigt_query);
		if(!StringUtils.isEmpty(query.getOrg_name())){
			alllquery.must(new MatchPhraseQueryBuilder("org_name",query.getOrg_name()));
		}
		if(!StringUtils.isEmpty(query.getYear())){
			alllquery.must(QueryBuilders.termQuery("year",query.getYear()));
		}
		
		
        HighlightBuilder highlightBuilder1 = new HighlightBuilder();
        highlightBuilder1.field("title");
        highlightBuilder1.field("content");
        //高亮的字段
        //是否多个字段都高亮
        highlightBuilder1.requireFieldMatch(false);
        //前缀后缀
        highlightBuilder1.preTags("<span style='color:red'>");
        highlightBuilder1.postTags("</span>");
        
        searchSourceBuilder.highlighter(highlightBuilder1);
		
		
		searchSourceBuilder.query(alllquery);
		es_query.setSearchSourceBuilder(searchSourceBuilder);
		
		
		if("start_time".equals(query.getSort_attr())) {
			SortOrder sortOrder = SortOrder.DESC; // 
			FieldSortBuilder  fieldsort = new FieldSortBuilder("start_time.keyword").order(sortOrder);
			searchSourceBuilder.sort(fieldsort);
		}else {
			searchSourceBuilder.sorts();
		}
		// 获取总数 distinct为是否去重 若为ture则必须在wrapper中指定去重字段
		Long size = esEventMapper.selectCount(es_query, false);
		query.setEventTotal(size);
		if (query.getPageNum()==1) {
			searchSourceBuilder.query(alllquery).from(0).size(query.getFirstPageSize());
		} else {
			searchSourceBuilder.query(alllquery).from(query.getStart_idx()).size(query.getPageSize());
		}
		List<EsEvent> resultList = esEventMapper.selectList(es_query);
		
		
		return resultList;
	}

	public Map<String ,Set> queryEsEventYearAndOrg(EsQueryPagination query) {
		Map<String, Set>  map =  new HashedMap<String, Set>();
		LambdaEsQueryWrapper<EsEvent> es_query = new LambdaEsQueryWrapper<EsEvent>();
		
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder alllquery = new BoolQueryBuilder();
		BoolQueryBuilder str_query = new BoolQueryBuilder();
		MatchPhraseQueryBuilder tq =new  MatchPhraseQueryBuilder("title", query.getStr()).slop(5);
		MatchPhraseQueryBuilder cq =new MatchPhraseQueryBuilder("content", query.getStr()).slop(5);
		str_query.should(tq);
		str_query.should(cq);
		BoolQueryBuilder weigt_query = new BoolQueryBuilder();
		
		alllquery.must(str_query);
		alllquery.should(weigt_query);
		
		
		searchSourceBuilder.query(alllquery);
		es_query.setSearchSourceBuilder(searchSourceBuilder);
		List<EsEvent> resultList = esEventMapper.selectList(es_query);
		Set<String> yearHash =  new HashSet<>();
		Set<String> orgHash =  new HashSet<>();
		for (int i = 0; i < resultList.size(); i++) {
			yearHash.add(resultList.get(i).getYear());
			orgHash.add(resultList.get(i).getOrg12_name());
		}
		map.put("years", yearHash);
		map.put("orgs", orgHash);
		return map;
	}
	
	
	@Override
	public Map<String, Object> queryEsPersion(EsQueryPagination query) throws IOException {
		Map<String, Object> map = new HashedMap<String, Object>();
		if (query.getPageNum() == 1) {
			QueryWrapper<LibEntityEntity> wrapper = new QueryWrapper<LibEntityEntity>();
			wrapper.eq("name", query.getStr());
			wrapper.eq("onto_id", YearbookConstant.MARK_PER_ONTO_ID);
			wrapper.apply("json_value(data,'$.portrait') = '1' ");
			List<LibEntityEntity> list = libEntityService.list(wrapper);
			if (list.size() > 0) {
				map.put("match", list.get(0));
				query.setFirstPageSize(9);
			}
		}
		if (map.get("match") == null) {
			query.setPer_type(2);
			com.alibaba.fastjson.JSONArray perList = query_string(query, YearbookConstant.MARK_PER_ONTO_ID);
			map.put("others", perList);
		}
		return map;
	}

	@Override
	public Map<String, Object> queryEsOrg(EsQueryPagination query) throws IOException {
		Map<String, Object> map = new HashedMap<String, Object>();
		if (query.getPageNum() == 1) {
			QueryWrapper<LibEntityEntity> wrapper = new QueryWrapper<LibEntityEntity>();
			wrapper.eq("name", query.getStr());
			wrapper.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
			wrapper.apply("json_value(data,'$.portrait') = '1' ");
			List<LibEntityEntity> list = libEntityService.list(wrapper);
			if (list.size() > 0) {
				map.put("match", list.get(0));
				query.setFirstPageSize(9);
			}
		}
		if (map.get("match") == null) {
			com.alibaba.fastjson.JSONArray perList = query_string(query, YearbookConstant.MARK_ORG_ONTO_ID);
			map.put("others", perList);
		}
		return map;
	}

	public com.alibaba.fastjson.JSONArray query_string(EsQueryPagination query, Long ontoId) throws IOException {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder alllquery = new BoolQueryBuilder();
		// 精准匹配本体id
		BoolQueryBuilder onto_query = new BoolQueryBuilder();
		onto_query.must().add(QueryBuilders.termQuery("onto_id", String.valueOf(ontoId)));
		alllquery.must(onto_query);
		// query_string 模糊查询全部字段
		BoolQueryBuilder like_query = new BoolQueryBuilder();
		//QueryStringQueryBuilder queryBuilder = QueryBuilders.queryStringQuery("*" + query.getStr() + "*");
		MatchPhraseQueryBuilder tq =new  MatchPhraseQueryBuilder("data", query.getStr()).slop(5);
		like_query.must(tq);
		alllquery.must(like_query);
		
		if(ontoId.equals(YearbookConstant.MARK_ORG_ONTO_ID)) {
			SortOrder sortOrder = SortOrder.ASC; // 
			searchSourceBuilder.sort(new FieldSortBuilder("org_rank.keyword").order(sortOrder));
		}
		//是否在es搜索页显示
		//精准匹配 es_show = 1
		BoolQueryBuilder es_show = new BoolQueryBuilder();
		es_show.must().add(QueryBuilders.termQuery("es_show", "1"));
		alllquery.must(es_show);
		
		SearchRequest request = new SearchRequest("entity");
		request.source(searchSourceBuilder);
		//查询总数
        CountRequest countRequest = new CountRequest("entity");
        countRequest.query(alllquery);
		CountResponse countResponse = client.count(countRequest, RequestOptions.DEFAULT);
        long count = countResponse.getCount();

        if( ontoId.equals(YearbookConstant.MARK_PER_ONTO_ID)) {
			SortOrder sortOrder = SortOrder.DESC; // 
			searchSourceBuilder.sort(new FieldSortBuilder("portrait.keyword").order(sortOrder));
        }
        
		query.setTotal(count);
		searchSourceBuilder.query(alllquery).from(query.getStart_idx()).size(query.getPageSize());
		request.source(searchSourceBuilder);
		SearchResponse respone = client.search(request, RequestOptions.DEFAULT);

		com.alibaba.fastjson.JSONArray jsonarry = new com.alibaba.fastjson.JSONArray();
		Arrays.stream(respone.getHits().getHits()).forEach(x -> {
			Object o = JSON.toJSON(x);
			jsonarry.add(o);
		});
		String dsl = searchSourceBuilder.toString();
		log.info(dsl);
		return jsonarry;

	}

	public com.alibaba.fastjson.JSONArray query_string_per(EsQueryPagination query, Long ontoId) throws IOException {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder alllquery = new BoolQueryBuilder();
		// 精准匹配本体id
		BoolQueryBuilder onto_query = new BoolQueryBuilder();
		onto_query.must().add(QueryBuilders.termQuery("onto_id", String.valueOf(ontoId)));
		alllquery.must(onto_query);
		// query_string 模糊查询全部字段
		BoolQueryBuilder like_query = new BoolQueryBuilder();
		//QueryStringQueryBuilder queryBuilder = QueryBuilders.queryStringQuery("*" + query.getStr() + "*");
		MatchPhraseQueryBuilder tq =new  MatchPhraseQueryBuilder("data", query.getStr()).slop(5);
		like_query.must(tq);
		alllquery.must(like_query);

		//是否在es搜索页显示
		//精准匹配 es_show = 1
		BoolQueryBuilder es_show = new BoolQueryBuilder();
		es_show.must().add(QueryBuilders.termQuery("es_show", "1"));
		alllquery.must(es_show);
		
		SearchRequest request = new SearchRequest("entity");
		request.source(searchSourceBuilder);
		//查询总数
        CountRequest countRequest = new CountRequest("entity");
        countRequest.query(alllquery);
		CountResponse countResponse = client.count(countRequest, RequestOptions.DEFAULT);
        long count = countResponse.getCount();

		BoolQueryBuilder port_query = new BoolQueryBuilder();
		port_query.must().add(QueryBuilders.termQuery("portrait", 1));
		alllquery.must(port_query);
        
		query.setTotal(count);
		searchSourceBuilder.query(alllquery).from(query.getStart_idx()).size(query.getPageSize());
		SearchResponse respone = client.search(request, RequestOptions.DEFAULT);

		com.alibaba.fastjson.JSONArray jsonarry = new com.alibaba.fastjson.JSONArray();
		Arrays.stream(respone.getHits().getHits()).forEach(x -> {
			System.out.println("-*-*-*-*-");
			System.out.println(x);
			Object o = JSON.toJSON(x);
			jsonarry.add(o);
		});
		String dsl = searchSourceBuilder.toString();
//		System.out.println(dsl);
//		long size = respone.getHits().getTotalHits().value;
//		query.setTotal(size);

		return jsonarry;

	}

	
	/**
	 * 要查询的各个本体的字段
	 * 
	 * @param ontoid
	 * @return
	 */
	public String[] selectColumn(Long ontoid) {
		if (ontoid.equals(YearbookConstant.MARK_PER_ONTO_ID)) {
			String[] arrs = { "job_id", "skill_domain_Id", "title_Id", "education_id", "military_rank_id",
					"military_job_id", "civil_category_id", "civil_grade_Id", "award_level_id", "expert_type_id",
					"expert_category_id", "skill_job_id", "synopsis" };
			return arrs;
		}
		if (ontoid.equals(YearbookConstant.MARK_ORG_ONTO_ID)) {
			String[] arrs = { "synopsis" };
			return arrs;
		}
		if (ontoid.equals(YearbookConstant.MARK_PROJECT_ONTO_ID)) {
			String[] arrs = { "synopsis" };
			return arrs;
		}
		if (ontoid.equals(YearbookConstant.MARK_RESULT_ONTO_ID)) {
			String[] arrs = { "synopsis" };
			return arrs;
		}
		return null;
	}

	@Override
	public R searchEntityContext(Long id) {
		// 主题
		List<EventEntity> resultlist = null;
		LibEntityEntity entity = libEntityService.getById(id);
		if (entity == null) {
			return R.ok();
		}
		QueryWrapper<EventEntity> query = new QueryWrapper<EventEntity>();
		if (YearbookConstant.MARK_THEME_ONTO_ID.equals(entity.getOntoId())) {
			query.lambda().and(t -> t.eq(EventEntity::getTheme, entity.getName()));
		}
		// 人员 //查询内部人员
		if (YearbookConstant.MARK_PER_ONTO_ID.equals(entity.getOntoId())) {
			query.lambda().and(t -> t.like(EventEntity::getPerson_main, entity.getName()));
		}
//		// 机构 查询所属机构
//		if (YearbookConstant.MARK_ORG_ONTO_ID.equals(entity.getOntoId())) {
//			query.lambda().and(t -> t.eq(EventEntity::getOrg_main, entity.getName()));
//		}
		query.lambda().and(t -> t.isNotNull(EventEntity::getStartTime));
		query.select("id", "start_time", "title","page_num"); 
		List<EventEntity> list = eventService.list(query);
		list =  list.stream().sorted(Comparator.comparing(EventEntity::getStartTime)).collect(Collectors.toList());
		Map<String, Map<String, List<EventEntity>>> allMap = new HashMap<>();
		for (int i = 0; i < list.size(); i++) {
			EventEntity ent = list.get(i);
			String year = ent.getStartTime().substring(0, 4);
			String month = ent.getStartTime().substring(5, 7);
			if (("0").equals(month.substring(0, 1))) {
				month = month.substring(1, 2);
			}
			Map<String, List<EventEntity>> yMap = allMap.get(year);
			if (yMap == null) {
				yMap = new HashMap<>();
			}
			List<EventEntity> entlist = yMap.get(month);
			if (entlist == null) {
				entlist = new ArrayList<EventEntity>();
			}
			entlist.add(ent);
			yMap.put(month, entlist);
			allMap.put(year, yMap);
		}
		return R.ok(allMap);
	}

	@Override
	public R getEntityDetail(Long id) {
		Map<String, Object> map = new HashedMap<String, Object>();
		LibEntityEntity entity = libEntityService.getById(id);
		if (entity == null) {
			return R.ok("请输入合法数据");
		}
		JSONObject jsonEntity = esDateHandle.coverEntity(entity);
		String code = getEntityCoder(entity.getOntoId().toString());
		map.put("entity", jsonEntity);
		map.put("onto", code);
		return R.ok(map);
	}

	/**
	 * 推荐热词
	 * 
	 * @return
	 */
	@Override
	public R getRelationObj(String str) {
		String weight = "";
		List idlist  =  new ArrayList<Long>();
		idlist.add(YearbookConstant.MARK_THEME_ONTO_ID);idlist.add(YearbookConstant.MARK_ORG_ONTO_ID);
		QueryWrapper<LibEntityEntity> entQuery = new QueryWrapper<LibEntityEntity>();
		entQuery.eq("name", str);
		entQuery.in("onto_id", idlist);
		entQuery.apply("json_value(data,'$.portrait') = '1' ");
		List<LibEntityEntity> entlist = libEntityService.list(entQuery);
		if(entlist.size()>0) {
			if(entlist.get(0).getOntoId().equals(YearbookConstant.MARK_THEME_ONTO_ID)) {
				weight = "theme";
			}else {
				weight = "org";
			}
		}
		
		LambdaEsQueryWrapper<EsEvent> es_query = new LambdaEsQueryWrapper<EsEvent>();
		
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder alllquery = new BoolQueryBuilder();
		BoolQueryBuilder str_query = new BoolQueryBuilder();
		MatchPhraseQueryBuilder tq =new  MatchPhraseQueryBuilder("title", str).slop(5);
		MatchPhraseQueryBuilder cq =new MatchPhraseQueryBuilder("content", str).slop(5);
		str_query.should(tq);
		str_query.should(cq);
		BoolQueryBuilder weigt_query = new BoolQueryBuilder();
		if(weight.equals("org")) {
			MatchPhraseQueryBuilder orgquery  =new  MatchPhraseQueryBuilder("org_name", str);
			orgquery.boost(10);
			str_query.should(orgquery);
			
		}
		if(weight.equals("theme")) {
			MatchPhraseQueryBuilder themequery =new  MatchPhraseQueryBuilder("theme", str);
			themequery.boost(11);
			str_query.should(themequery);
		}
		alllquery.must(str_query);
		alllquery.should(weigt_query);
		
		searchSourceBuilder.query(alllquery);
		es_query.setSearchSourceBuilder(searchSourceBuilder);
		List<EsEvent> resultList = esEventMapper.selectList(es_query);
		
		
		Map<String, Object> resuMap = new HashedMap<String, Object>();
		
		
		// 循环事件
		List<WordCloudEntity> wordList = new ArrayList<>();
		Map<String, Integer> tagmap = new HashedMap<String, Integer>();
		Map<String, Integer> per_map = new HashedMap<String, Integer>();
		Map<String, Integer> org_map = new HashedMap<String, Integer>();
		for (int i = 0; i < resultList.size(); i++) {
			// 相关关键词
			String tags = resultList.get(i).getTag();
			if (!org.apache.commons.lang3.StringUtils.isEmpty(tags)) {
				String[] tag_arr = tags.split(";");
				for (int j = 0; j < tag_arr.length; j++) {
					Integer size = tagmap.get(tag_arr[j]);
					size = size == null ? 1 : size++;
					tagmap.put(tag_arr[j], size);
				}
			}
			// 相关人物
			String perIds = resultList.get(i).getPerIds();
			if (!org.apache.commons.lang3.StringUtils.isEmpty(perIds)) {
				String[] per_arr = perIds.split(";");
				for (int j = 0; j < per_arr.length; j++) {
					Integer size = per_map.get(per_arr[j]);
					size = size == null ? 1 : size++;
					per_map.put(per_arr[j], size);
				}
			}
			// 相关机构
			String orgIds = resultList.get(i).getOrgIds();
			if (!org.apache.commons.lang3.StringUtils.isEmpty(orgIds)) {
				String[] org_arr = orgIds.split(";");
				for (int j = 0; j < org_arr.length; j++) {
					Integer size = org_map.get(org_arr[j]);
					size = size == null ? 1 : size++;
					org_map.put(org_arr[j], size);
				}
			}
		}
		// 相关关键词
		for (String tag : tagmap.keySet()) {
			WordCloudEntity ent = new WordCloudEntity();
			ent.setName(tag);
			ent.setWeight(tagmap.get(tag));
			wordList.add(ent);
		}
		List taglist = wordList.stream().sorted(Comparator.comparing(WordCloudEntity::getWeight)).limit(20)
				.collect(Collectors.toList());
		resuMap.put("tagList", taglist);

		
		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.eq("name", str);
		query.apply("json_value(data,'$.portrait') = '1' ");
		List<LibEntityEntity> list = libEntityService.list(query);
		Long  current_id = 1L;
		if(list.size()!=0) {
			current_id = list.get(0).getId();
		}
		// 相关人物
		List<Long> per_ids = new ArrayList<Long>();
		LinkedHashMap<String, Integer> per_linkMap = per_map.entrySet().stream().sorted(Map.Entry.comparingByValue())
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1 + v2,
						LinkedHashMap::new));
		for (String id : per_linkMap.keySet()) {
			per_ids.add(Long.parseLong(id));
		}
		per_ids.remove(current_id);
		if (per_ids.size() != 0) {
			QueryWrapper<LibEntityEntity> per_query = new QueryWrapper<LibEntityEntity>();
			per_query.in("id", per_ids);
			per_query.apply("json_value(data,'$.portrait') = 1    and   json_value(data,'$.image_ids') is not  null    " );
			per_query.last(" limit 12");
			List<LibEntityEntity> perListr = libEntityService.list(per_query);
			List<LibMinEntity> perList1 = new ArrayList<LibMinEntity>();
			perListr.forEach(x -> {
				String data = x.getData();
				Map<String, Object> dataMap = JsonUtil.stringToMap(data);
				Long img = (Long) dataMap.get("image_ids");
				LibMinEntity ent = new LibMinEntity(x.getId().toString(), x.getName(), img.toString());
				perList1.add(ent);
			});
			resuMap.put("perListr", perList1);
		}

		// 相关机构
		List<Long> org_ids = new ArrayList<Long>();
		LinkedHashMap<String, Integer> org_linkMap = org_map.entrySet().stream().sorted(Map.Entry.comparingByValue())
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1 + v2,
						LinkedHashMap::new));
		for (String id : org_linkMap.keySet()) {
			org_ids.add(Long.parseLong(id));
		}
		org_ids.remove(current_id);
		if (org_ids.size() != 0) {
			QueryWrapper<LibEntityEntity> org_query = new QueryWrapper<LibEntityEntity>();
			org_query.in("id", org_ids);
			org_query.apply("json_value(data,'$.es_show')= '1'   and json_value(data,'$.icon') is not null   and ( json_value(data,'$.org_rank')='1' or json_value(data,'$.org_rank')='2'   )  ");
			org_query.last(" limit 12");
			List<LibEntityEntity> orgListr = libEntityService.list(org_query);
			List<LibMinEntity> orgListr1 = new ArrayList<LibMinEntity>();
			
			for (int k = 0; k < orgListr.size(); k++) {
				LibEntityEntity x = orgListr.get(k);
				String data = x.getData();
				Map<String, Object> dataMap = JsonUtil.stringToMap(data);
				Long img = (Long) dataMap.get("icon");
				LibMinEntity ent = new LibMinEntity(x.getId().toString(), x.getName(), img.toString());
				orgListr1.add(ent);
			}
			resuMap.put("orgListr", orgListr1);
		}
		return R.ok(resuMap);
	}

	@Override
	public R searchGraph(Long entityId) {
		Map<String, Object> retrunMap = new HashMap<>();
		List<Map<String, Object>> nodesList = new ArrayList<>();
		List<Map<String, String>> linksList = new ArrayList<>();
		LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
		// 查询本身节点
		Map<String, Object> nodesMap = new HashMap<>();
		nodesMap.put("id", entityId);
		nodesMap.put("name", libEntity.getName());
		nodesMap.put("onto", libEntity.getOntoId());
		nodesList.add(nodesMap);
		List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
		nodesList.addAll(nodesOntoList);
		for (Map<String, Object> ontoMap : nodesOntoList) {
			Map<String, String> querymap = new HashMap<>();
			querymap.put("ontoId", ontoMap.get("id").toString());
			querymap.put("entityId", entityId.toString());
			// 查询实体节点
			List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper
					.aiGraphGetEntityByEntityIdAndOntoid(querymap);
			nodesList.addAll(nodesEntityList);

			// 关系
			List<Map<String, String>> linksRelationList = metaEntityRelationMapper
					.aiGraphGetRelationByEntityIdAndOntoid(querymap);
			linksList.addAll(linksRelationList);

			String relationName = "";
			if (linksRelationList.size() > 0) {
				relationName = linksRelationList.get(0).get("value");
			}
			// 到本体的links
			Map<String, String> linksmap = new HashMap<>();
			linksmap.put("source", entityId.toString());
			linksmap.put("target", ontoMap.get("id").toString());
			linksmap.put("value", relationName);
			linksList.add(linksmap);
		}
		nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
		retrunMap.put("nodes", nodesList);
		retrunMap.put("links", linksList);

		List<Long> ids = new ArrayList<Long>();
		for (int i = 0; i < nodesList.size(); i++) {
			Map<String, Object> map = nodesList.get(i);
			
			Long id = Long.valueOf(map.get("id").toString());
			ids.add(id);
		}
		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.in("id", ids);
		List<LibEntityEntity> list = libEntityService.list(query);

		Map<Long, List<LibEntityEntity>> map = list.stream().collect(Collectors.groupingBy(LibEntityEntity::getOntoId));
		for (Long onto : map.keySet()) {
			List<LibEntityEntity> entList = map.get(onto);
			List<LibMinEntity> relist = new ArrayList<LibMinEntity>();
			entList.forEach(x -> {
				LibMinEntity ent = new LibMinEntity();
				if(!ObjectUtil.isEmpty(x.getData())) {
					JSONObject json =  JSONUtil.parseObj(x.getData());
					String org_rank =  json.getStr("org_rank");
					if("1".equals(org_rank) || "2".equals(org_rank)) {
						Long org_img_id = json.getLong("icon"); 
						if(org_img_id!=null) {
							ent.setImg(org_img_id.toString());
						}
					}
				}
				ent.setId(x.getId().toString());
				ent.setName(x.getName());
				relist.add(ent);
			});
			retrunMap.put(getEntityCoder(onto.toString()), relist);
		}
		return R.ok(retrunMap);
	}
	
	
	public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
	    Map<Object, Boolean> seen = new ConcurrentHashMap<>();
	    return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
	}
	
	

	public String getEntityCoder(String id) {
		String code = "";
		switch (id) {
		case "1586970280968970242":
			code = "per";
			break;
		case "1586973763214368769":
			code = "org";
			break;
		case "1655899624481320962":
			code = "theme";
			break;
		case "1586968527091392513":
			code = "result";
			break;
		case "1586999607391420418":
			code = "project";
			break;
		case "1649952061550292993":
			code = "team";
			break;
		case "1625418542513926146":
			code = "prize";
			break;
		case "16305538085043240982":
			code = "funds";
			break;
		case "16305538085043240981":
			code = "obj";
			break;
		}
		//经费
		//物品资源
		return code;
	}

	@Override
	public com.alibaba.fastjson.JSONArray queryEsEntity(EsQueryPagination query) throws IOException {
		query.setStart_idx(0);
		query.setPageSize(10000);
		com.alibaba.fastjson.JSONArray jsonarry = query_string(query, query.getOntoId());

		return jsonarry;
	}

	@Override
	public List queryEsOrgNameList() throws IOException {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder alllquery = new BoolQueryBuilder();
		// 精准匹配本体id
		BoolQueryBuilder onto_query = new BoolQueryBuilder();
		onto_query.must().add(QueryBuilders.termQuery("onto_id", YearbookConstant.MARK_ORG_ONTO_ID));
		alllquery.must(onto_query);

		//是否在es搜索页显示
		//精准匹配 es_show = 1
		BoolQueryBuilder es_show = new BoolQueryBuilder();
		es_show.must().add(QueryBuilders.termQuery("es_show", "1"));
		alllquery.must(es_show);
		
		//机构登记
		BoolQueryBuilder org_rank = new BoolQueryBuilder();
	//	TermQueryBuilder rank1 = QueryBuilders.termQuery("org_rank", "1");
		TermQueryBuilder rank2 = QueryBuilders.termQuery("org_rank", "2");
	//	org_rank.should().add(rank1);
		org_rank.should().add(rank2);
		alllquery.must(org_rank);
		searchSourceBuilder.query(alllquery);
		SearchRequest request = new SearchRequest("entity");
		request.source(searchSourceBuilder);
		SearchResponse respone = client.search(request, RequestOptions.DEFAULT);

		com.alibaba.fastjson.JSONArray jsonarry = new com.alibaba.fastjson.JSONArray();
		Arrays.stream(respone.getHits().getHits()).forEach(x -> {
			Object o = JSON.toJSON(x);
			jsonarry.add(o);
		});
		System.out.println(searchSourceBuilder.toString());
		
		return jsonarry;
	}
}
