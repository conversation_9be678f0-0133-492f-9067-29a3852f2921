package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.Date;

@Data
@TableName("report_details")
@ApiModel(value = "党委报告详情表")
public class ReportDetails {
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "主题ID")
    private Integer themeId;

    @ApiModelProperty(value="具体数据内容")
    private String detailContent;

    @ApiModelProperty(value = "展示顺序")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
