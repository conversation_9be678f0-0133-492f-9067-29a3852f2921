package com.sinosoft.ie.booster.yearbook.mark.service;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkDatasetEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markdataset.MarkDatasetPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;

import java.util.*;

/**
 *
 * mark_dataset
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-09 11:56:53
 */
public interface MarkDatasetService extends IService<MarkDatasetEntity> {

    List<MarkDatasetEntity> getList(MarkDatasetPagination markDatasetPagination);

    List<MarkDatasetEntity> getTypeList(MarkDatasetPagination markDatasetPagination,String dataType);



    MarkDatasetEntity getInfo(Long id);

    void delete(MarkDatasetEntity entity);

    void create(MarkDatasetEntity entity);

    boolean update( Long id, MarkDatasetEntity entity);
    
//  子表方法
    
    
    List<MarkDatasetInfoVO> getTreeList();
    
    List<MarkSentenceEntity> getSentenceListById(MarkSentencePagination makrSentencePagination);
    
    //上传文件后提交
    
    void submitFile(MarkDatasetCrForm markDatasetCrForm);
    
}
