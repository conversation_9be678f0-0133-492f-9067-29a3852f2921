package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.ResultsEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsPagination;
import java.util.*;

/**
 *
 * results
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-26 13:51:00
 */
public interface ResultsService extends IService<ResultsEntity> {

    List<ResultsEntity> getList(ResultsPagination resultsPagination);

    List<ResultsEntity> getTypeList(ResultsPagination resultsPagination,String dataType);



    ResultsEntity getInfo(Long id);

    void delete(ResultsEntity entity);

    void create(ResultsEntity entity);

    boolean update( Long id, ResultsEntity entity);
    
//  子表方法
}
