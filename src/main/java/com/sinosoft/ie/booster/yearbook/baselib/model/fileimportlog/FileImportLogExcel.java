package com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ColumnWidth(30)
public class FileImportLogExcel implements Serializable {

    /**
     * 行数
     */
    @ExcelProperty("行数")
    private Integer rowNum;


    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;


    /**
     * 日志
     */
    @ExcelProperty(value = "日志")
    private String errorLog;

}