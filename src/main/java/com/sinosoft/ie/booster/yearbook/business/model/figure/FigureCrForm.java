package com.sinosoft.ie.booster.yearbook.business.model.figure;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * Figure模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 11:45:26
 */
@Data
@ApiModel
public class FigureCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String field;
}