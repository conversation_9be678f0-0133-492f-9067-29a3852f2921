package com.sinosoft.ie.booster.yearbook.graph.service;

import com.sinosoft.ie.booster.yearbook.graph.entity.EveTagEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.graph.model.evetag.EveTagPagination;
import java.util.*;

/**
 *
 * eve_tag
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-07-21 10:53:07
 */
public interface EveTagService extends IService<EveTagEntity> {

    List<EveTagEntity> getList(EveTagPagination eveTagPagination);

    List<EveTagEntity> getTypeList(EveTagPagination eveTagPagination,String dataType);



    EveTagEntity getInfo(Long id);

    void delete(EveTagEntity entity);

    void create(EveTagEntity entity);

    boolean update( Long id, EveTagEntity entity);
    
//  子表方法
}
