package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeValueEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValuePagination;
import java.util.*;

/**
 *
 * mate_relation_attribute_value
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-01 14:35:03
 */
public interface MateRelationAttributeValueService extends IService<MateRelationAttributeValueEntity> {

    List<MateRelationAttributeValueEntity> getList(MateRelationAttributeValuePagination mateRelationAttributeValuePagination);

    List<MateRelationAttributeValueEntity> getTypeList(MateRelationAttributeValuePagination mateRelationAttributeValuePagination,String dataType);



    MateRelationAttributeValueEntity getInfo(Long id);

    void delete(MateRelationAttributeValueEntity entity);

    void create(MateRelationAttributeValueEntity entity);

    boolean update( Long id, MateRelationAttributeValueEntity entity);
    
//  子表方法
}
