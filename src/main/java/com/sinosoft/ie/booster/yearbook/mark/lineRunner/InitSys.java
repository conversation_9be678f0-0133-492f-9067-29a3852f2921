package com.sinosoft.ie.booster.yearbook.mark.lineRunner;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.cache.EventCache;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;

@RestController
@Order(value=1)
@Component
public class InitSys implements CommandLineRunner {
	
	@Resource
	private ProjectConfig projectConfig;
	@Resource
	private LibConceptService libConceptService;
	@Resource
	private MateAttributeService mateAttributeService;
	@Resource
	private MetaOntoRelationService metaOntoRelationService;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	
	
	@Override
	public void run(String... args) throws Exception {
		queryData();

	}
	
	@GetMapping("/refreshCache")
	public R refreshCache() {
		queryData();
		
		return R.ok();
	}
	
	public void    queryData() {
		//查询本体数据并缓存
		List<LibConceptEntity> list = libConceptService.list();
		OntoCache.saveOnto(list);
		//MetaDataCache.saveOnto(list);
		//查询本体属性数据并缓存
		List<MateAttributeEntity>  attlist =  mateAttributeService.list();
		OntoCache.saveArrList(attlist);
		//查询本体关系并缓存
		List<MetaOntoRelationEntity>  relationList = metaOntoRelationService.list();
		OntoCache.saveRelationMap(relationList);
		
		//查询本体属性值并缓存
		List<MateAttributeValueEntity> valList = mateAttributeValueService.list();
		OntoCache.saveAttrValList(valList);
		
		//转化规则查询
		
//		//事件属性查询
		List<MateAttributeEntity>  eve_attlist =  attlist.stream().filter(t->t.getOnto().equals(YearbookConstant.MARK_EVENT_ONTO_ID)).collect(Collectors.toList());
		EventCache.setData(eve_attlist,OntoCache.getMetaOntoRelationListByOnto1Id(YearbookConstant.MARK_EVENT_ONTO_ID));
		
		
	}

}
