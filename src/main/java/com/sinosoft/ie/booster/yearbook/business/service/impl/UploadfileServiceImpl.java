package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.UploadfileMapper;
import com.sinosoft.ie.booster.yearbook.business.service.UploadfileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 *
 * uploadfile
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-14 10:19:51
 */
@Slf4j
@Service
public class UploadfileServiceImpl extends ServiceImpl<UploadfileMapper, UploadfileEntity> implements UploadfileService {

	@Autowired
	private ProjectConfig projectConfig;


	@Override
	public void previewPdf(String name,HttpServletResponse response) throws IOException {
		File file = new File(projectConfig.getUploadPath() + ProjectConstant.pdf_prefix + name);
		if (!file.exists()) {
			response.sendError(404, "File not found!");
			return;
		}
		DownUtil.dowloadFile(file);

	}

	@Override
	public void previewPic(String pic,HttpServletResponse response) throws IOException {

		File f = new File(projectConfig.getUploadPath() + ProjectConstant.imag_path + pic);
		System.out.println(projectConfig.getUploadPath() + ProjectConstant.imag_path + pic);
		if (!f.exists()) {
			response.sendError(404, "File not found!");
			return;
		}
		BufferedInputStream br = new BufferedInputStream(new FileInputStream(f));
		byte[] bs = new byte[1024];
		int len = 0;

		String n = pic.replace(projectConfig.getUploadPath(), "");

//		response.reset(); // 非常重要
		// 纯下载方式
		if (pic.contains(".svg")){
			response.setContentType("image/svg+xml");
		}
//		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment;filename=" + pic);
		OutputStream out = response.getOutputStream();
		while ((len = br.read(bs)) > 0) {
			out.write(bs, 0, len);
		}
		out.flush();
		out.close();
		br.close();
	}

	@Override
	public UploadfileEntity getInfo(Long id){
		QueryWrapper<UploadfileEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.lambda().eq(UploadfileEntity::getId,id);
		return this.getOne(queryWrapper);
	}
	
	
	@Override
	public void previewImg(Long id,HttpServletResponse response) throws IOException {
		log.info("id===="+id);
		UploadfileEntity ent = this.getById(id);
		if(ent==null) {
			response.sendError(404, "File not found!");
			return;
		}
		File f = new File(projectConfig.getUploadPath() + File.separator + ent.getPath());
		if (!f.exists()) {
			log.error("该文件不存在");
			response.sendError(404, "File not found!");
			return;
		}
		response = DownUtil.getResponse();
		if (ent.getNewName().contains("svg")){
			response.setContentType("image/svg+xml");
		}
		BufferedInputStream br = new BufferedInputStream(new FileInputStream(f));
		byte[] bs = new byte[1024];
		int len = 0;

		//response.reset(); // 非常重要
		// 纯下载方式
		OutputStream out = response.getOutputStream();
		while ((len = br.read(bs)) > 0) {
			out.write(bs, 0, len);
		}
		out.flush();
		out.close();
		br.close();
		log.info("关闭流");
	}
	
	
}