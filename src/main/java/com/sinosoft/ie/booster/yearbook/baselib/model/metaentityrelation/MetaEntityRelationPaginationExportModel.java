package com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MetaEntityRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:34:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MetaEntityRelationPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 本体1的id
     */
    @ApiModelProperty(value = "本体1的id")
    private Long onto1Id;

    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的id")
    private Long onto2Id;

    /**
     * 关系中文名称
     */
    @ApiModelProperty(value = "关系中文名称")
    private String name;

    /**
     * 实体1
     */
    @ApiModelProperty(value = "实体1")
    private Long entity1Id;

    /**
     * 实体2
     */
    @ApiModelProperty(value = "实体2")
    private Long entity2Id;

    /**
     * 本体关系id
     */
    @ApiModelProperty(value = "本体关系id")
    private Long ontoRelId;

    /**
     * 1未导入2已导入3删除
     */
    @ApiModelProperty(value = "1未导入2已导入3删除")
    private Integer state;
}