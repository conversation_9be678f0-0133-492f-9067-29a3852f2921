package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributePagination;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * mate_attribute
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 14:48:56
 */
@Service
public class MateAttributeServiceImpl extends ServiceImpl<MateAttributeMapper, MateAttributeEntity> implements MateAttributeService {

    @Autowired
    private LibConceptService libConceptService;

    @Resource
    private MateAttributeValueService valueService;
    @Resource
    private UpdateEntityForAttrService updateEntityForAttrService;

    @Override
    public List<MateAttributeEntity> getList(MateAttributePagination mateAttributePagination){
        QueryWrapper<MateAttributeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateAttributePagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getId,mateAttributePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getNameEn()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getNameEn,mateAttributePagination.getNameEn()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getNameCn()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getNameCn,mateAttributePagination.getNameCn()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getValType()))){
            queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getValType,mateAttributePagination.getValType()));
        }
        if(!"null".equals(String.valueOf(mateAttributePagination.getType()))){
            queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getType,mateAttributePagination.getType()));
        }
        if(!"null".equals(String.valueOf(mateAttributePagination.getMust()))){
            queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getMust,mateAttributePagination.getMust()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getOnto()))){
            queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getOnto,mateAttributePagination.getOnto()));
        }
        if(!"null".equals(String.valueOf(mateAttributePagination.getPid()))){
            queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getPid,mateAttributePagination.getPid()));
        }
        //排序
        queryWrapper.lambda().orderByAsc(MateAttributeEntity::getSortNumber);
//        if(StrUtil.isEmpty(mateAttributePagination.getSidx())){
//            queryWrapper.lambda().orderByDesc(MateAttributeEntity::getId);
//        }else{
//            queryWrapper="asc".equals(mateAttributePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateAttributePagination.getSidx()):queryWrapper.orderByDesc(mateAttributePagination.getSidx());
//        }
        if(mateAttributePagination.getCurrentPage()==0) {
        	List<MateAttributeEntity> list  = this.list(queryWrapper);
        	return list;
        }
        Page<MateAttributeEntity> page=new Page<>(mateAttributePagination.getCurrentPage(), mateAttributePagination.getPageSize());
        IPage<MateAttributeEntity> userIPage=this.page(page,queryWrapper);
        return mateAttributePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MateAttributeEntity> getTypeList(MateAttributePagination mateAttributePagination,String dataType){
        QueryWrapper<MateAttributeEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateAttributePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getId,mateAttributePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getNameEn()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getNameEn,mateAttributePagination.getNameEn()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getNameCn()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getNameCn,mateAttributePagination.getNameCn()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getValType()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getValType,mateAttributePagination.getValType()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getMust()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getMust,mateAttributePagination.getMust()));
        }

        if(!"null".equals(String.valueOf(mateAttributePagination.getOnto()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeEntity::getOnto,mateAttributePagination.getOnto()));
        }

        //排序
        if(StrUtil.isEmpty(mateAttributePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateAttributeEntity::getId);
        }else{
            queryWrapper="asc".equals(mateAttributePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateAttributePagination.getSidx()):queryWrapper.orderByDesc(mateAttributePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MateAttributeEntity> page=new Page<>(mateAttributePagination.getCurrentPage(), mateAttributePagination.getPageSize());
            IPage<MateAttributeEntity> userIPage=this.page(page,queryWrapper);
            return mateAttributePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MateAttributeEntity getInfo(Long id){
        QueryWrapper<MateAttributeEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MateAttributeEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MateAttributeEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MateAttributeEntity entity){
        entity.setId(id);
        this.updateById(entity);
        updateEntityForAttrService.UpdateEntityForAttr(id, entity.getVals());
        return  true;
    }

    @Override
    public void delete(MateAttributeEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

    @Override
    public void createNumValueList(List<MateAttributeCrForm> list){
        List<MateAttributeEntity> entityList=JsonUtil.getJsonToList(list, MateAttributeEntity.class);
        for (MateAttributeEntity entity:entityList) {
            entity.setMust(2);
            this.save(entity);
        }
    }

    @Override
    public void createObjectValueList(List<MateAttributeCrForm> list){
        List<MateAttributeEntity> entityList=JsonUtil.getJsonToList(list, MateAttributeEntity.class);
        for (MateAttributeEntity entity:entityList) {
            entity.setMust(2);
            entity.setValType(4);
            this.save(entity);
        }
    }

    @Override
    public MateAttributeListVO getMoShiChild(MateAttributeListVO mateAttributeListVO){
        if (mateAttributeListVO.getPoint() != null && !mateAttributeListVO.getPoint().isEmpty()){
            LibConceptEntity point = libConceptService.getById(mateAttributeListVO.getPoint());
            if (point != null){
                mateAttributeListVO.setPointName(point.getName());
            }
        }
        QueryWrapper<MateAttributeEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().and(t->t.eq(MateAttributeEntity::getPid,mateAttributeListVO.getId()));
        List<MateAttributeEntity> list = this.list(queryWrapper);
        List<MateAttributeListVO> listVO=JsonUtil.getJsonToList(list,MateAttributeListVO.class);
        for (MateAttributeListVO child:listVO) {
            if (child.getPoint() != null && !child.getPoint().isEmpty()){
                LibConceptEntity point = libConceptService.getById(child.getPoint());
                if (point != null){
                    child.setPointName(point.getName());
                }
            }
        }
        mateAttributeListVO.setMoshichild(listVO);
        return mateAttributeListVO;
    }

    @Override
    public void deleteids(String ids){
        this.removeBatchByIds(new ArrayList<>(Arrays.asList(ids.split(","))));
    }
}