package com.sinosoft.ie.booster.yearbook.baselib.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationGraph;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 *
 * meta_entity_relation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-31 16:34:43
 */
@Mapper
public interface MetaEntityRelationMapper extends BaseMapper<MetaEntityRelationEntity> {

    @Select(value = "SELECT " +
            "(c1.name||'名') onto1Name,(c2.name||'名') onto2Name,"+
            "m.*,c1.tab tabName1,c2.tab tabName2 " +
            "FROM meta_entity_relation m " +
            "LEFT JOIN lib_concept c1 ON c1.id = m.onto1_id " +
            "LEFT JOIN lib_concept c2 ON c2.id = m.onto2_id ${ew.customSqlSegment}")
    List<MetaEntityRelationListVO> getByORId(IPage page, @Param(Constants.WRAPPER) Wrapper<MetaEntityRelationEntity> queryWrapper);

    @Select(value = "SELECT count(1)" +
            "FROM meta_entity_relation m " +
            "LEFT JOIN lib_concept c1 ON c1.id = m.onto1_id " +
            "LEFT JOIN lib_concept c2 ON c2.id = m.onto2_id ${ew.customSqlSegment}")
    int getByORIdCount(@Param(Constants.WRAPPER) Wrapper<MetaEntityRelationEntity> queryWrapper);
    
    @Select("select t.*,m.name as relationName, c1.name as onto1Name ,c2.name as onto2Name   from meta_entity_relation  t"
    		+ "LEFT JOIN meta_onto_relation m  on t.onto_rel_id =  m.id "
    		+ "LEFT JOIN lib_concept c1 ON  t.onto1_id =  c1.id "
                + "LEFT JOIN lib_concept c2 ON  t.onto2_id =  c2.id  ${ew.customSqlSegment} ")
    List<MetaEntityRelationInfoVO>  queryEntityRelation(Wrapper<MetaEntityRelationEntity> queryWrapper);

    @Select("select t.*,m.name as relationName, c1.name as onto1Name ,c2.name as onto2Name," +
            "e1.name entity1Name,e2.name entity2Name from meta_entity_relation t "
            + "LEFT JOIN meta_onto_relation m  on t.onto_rel_id =  m.id "
            + "LEFT JOIN lib_concept c1 ON  t.onto1_id =  c1.id "
            + "LEFT JOIN lib_concept c2 ON  t.onto2_id =  c2.id " +
            "LEFT JOIN lib_entity e1 ON t.entity1_Id = e1.id " +
            "LEFT JOIN lib_entity e2 ON t.entity2_Id = e2.id ${ew.customSqlSegment} ")
    List<MetaEntityRelationInfoVO>  queryEntityRelationByEntityId(@Param(Constants.WRAPPER) Wrapper<MetaEntityRelationEntity> queryWrapper);

    List<MetaEntityRelationInfoVO>  ontolistNew(MetaEntityRelationEntity metaEntityRelationEntity) ;

    List<MetaEntityRelationGraph> entityIdList(Map map);
    List<MetaEntityRelationGraph> entityIdListOnto(Long ontoId);

    @MapKey("onto1_id")
    List<Map<String,Object>> aiGraphGetOntoByEntityId(String entityId);

    @MapKey("entity1_Id")
    List<Map<String,Object>> aiGraphGetEntityByEntityIdAndOntoid(Map map);

    @MapKey("entity1_Id")
    List<Map<String,String>> aiGraphGetRelationByEntityIdAndOntoid(Map map);

    @MapKey("entity1_Id")
    List<Map<String,Object>> aiGraphGetRelationByEntityId(Map map);

    @MapKey("entity1_Id")
    List<Map<String,Object>> aiGraphGetHumanTimeChartByEntityIdAndOntoid(Map map);

    @MapKey("")
    List<Map<String,Object>> humanGraphMoreRelationTime(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetEntityByEvents(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetRelationByEvents(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetOrgTimeChartByEvents(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetRelationTimeByEvents(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetEntityByEventsOrg(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetRelationByEventsOrg(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetRelationTimeByEventsOrg(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetRelationTimeByEventsOrgMain(Map map);

    @MapKey("")
    List<Map<String,Object>> aiGraphGetRelationByEventsOrgMain(Map map);
}