package com.sinosoft.ie.booster.yearbook.database.model.dataconnection;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * DataConnection模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-06 15:01:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class DataConnectionInfoVO extends DataConnectionCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}