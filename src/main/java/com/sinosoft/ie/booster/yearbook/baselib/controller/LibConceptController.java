package com.sinosoft.ie.booster.yearbook.baselib.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * 本体概念类
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-30 09:58:21
 */
@Slf4j
@RestController
@Api(tags = "本体概念类")
@RequestMapping("/LibConcept")
public class LibConceptController {
    @Autowired
    private LibConceptService libConceptService;


    /**
     * 列表
     *
     * @param libConceptPagination
     * @return
     */
    @GetMapping
    public R list(LibConceptPagination libConceptPagination)throws IOException{
        List<LibConceptEntity> list= libConceptService.getList(libConceptPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(LibConceptEntity entity:list){
            }
        List<LibConceptListVO> listVO=JsonUtil.getJsonToList(list,LibConceptListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(libConceptPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param libConceptCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid LibConceptCrForm libConceptCrForm) throws DataException {
        LibConceptEntity entity=JsonUtil.getJsonToBean(libConceptCrForm, LibConceptEntity.class);
        libConceptService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<LibConceptInfoVO> info(@PathVariable("id") Long id){
        LibConceptEntity entity= libConceptService.getInfo(id);
        LibConceptInfoVO vo=JsonUtil.getJsonToBean(entity, LibConceptInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid LibConceptUpForm libConceptUpForm) throws DataException {
        LibConceptEntity entity= libConceptService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(libConceptUpForm, LibConceptEntity.class);
            libConceptService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        LibConceptEntity entity= libConceptService.getInfo(id);
        if(entity!=null){
            libConceptService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    
    
    
    @GetMapping("/getChildNode")
    public   R   getChildNode(Long id) {
    	List<LibConceptEntity>  list = libConceptService.getAllSubNode(id);
    	List<LibConceptListVO> listVO=JsonUtil.getJsonToList(list,LibConceptListVO.class);
    	return R.ok(listVO);
    }

    /**
     * 批量新增本体
     */
    @PostMapping("/createList")
    @Transactional
    public R createList(@RequestBody @Valid List<LibConceptCrForm> list) throws DataException {
        if (list == null || list.size() == 0){
            return R.failed("请插入数据！");
        }
        List<LibConceptEntity> entityList=JsonUtil.getJsonToList(list, LibConceptEntity.class);
        for (LibConceptEntity entity:entityList) {
            libConceptService.create(entity);
        }
        return R.ok(null, "新建成功");
    }

    /**
     * 删除自身和子节点
     *
     * @param id
     * @return
     */
    @DeleteMapping("/deleteChild/{id}")
    @Transactional
    public R deleteChild(@PathVariable("id") Long id){
        libConceptService.deleteChild(id);
        return R.ok(null, "删除成功");
    }
}
