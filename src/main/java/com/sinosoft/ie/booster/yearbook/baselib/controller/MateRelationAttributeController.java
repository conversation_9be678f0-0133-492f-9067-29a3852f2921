package com.sinosoft.ie.booster.yearbook.baselib.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributePaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributePagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributeCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributeInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributeListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributeUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateRelationAttributeService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mate_relation_attribute
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-01 14:33:35
 */
@Slf4j
@RestController
@Api(tags = "mate_relation_attribute")
@RequestMapping("/MateRelationAttribute")
public class MateRelationAttributeController {
    @Autowired
    private MateRelationAttributeService mateRelationAttributeService;


    /**
     * 列表
     *
     * @param mateRelationAttributePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MateRelationAttributeListVO>> list(MateRelationAttributePagination mateRelationAttributePagination)throws IOException{
        List<MateRelationAttributeEntity> list= mateRelationAttributeService.getList(mateRelationAttributePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MateRelationAttributeEntity entity:list){
            }
        List<MateRelationAttributeListVO> listVO=JsonUtil.getJsonToList(list,MateRelationAttributeListVO.class);
        PageListVO<MateRelationAttributeListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(mateRelationAttributePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param mateRelationAttributeCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MateRelationAttributeCrForm mateRelationAttributeCrForm) throws DataException {

        MateRelationAttributeEntity entity=JsonUtil.getJsonToBean(mateRelationAttributeCrForm, MateRelationAttributeEntity.class);
        mateRelationAttributeService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MateRelationAttributeInfoVO> info(@PathVariable("id") Long id){
        MateRelationAttributeEntity entity= mateRelationAttributeService.getInfo(id);
        MateRelationAttributeInfoVO vo=JsonUtil.getJsonToBean(entity, MateRelationAttributeInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MateRelationAttributeUpForm mateRelationAttributeUpForm) throws DataException {
        MateRelationAttributeEntity entity= mateRelationAttributeService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(mateRelationAttributeUpForm, MateRelationAttributeEntity.class);
            mateRelationAttributeService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MateRelationAttributeEntity entity= mateRelationAttributeService.getInfo(id);
        if(entity!=null){
            mateRelationAttributeService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 列表-不分页
     */
    @GetMapping("/listNoPage")
    public R<List<MateRelationAttributeListVO>> listNoPage(Long attributeId){
        QueryWrapper<MateRelationAttributeEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().and(t->t.eq(MateRelationAttributeEntity::getAttributeId,attributeId));
        List<MateRelationAttributeEntity> list = mateRelationAttributeService.list(queryWrapper);
        return R.ok(JsonUtil.getJsonToList(list,MateRelationAttributeListVO.class));
    }
}
