package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRulesEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.ConverRulesMapper;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesPagination;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRulesService;

import cn.hutool.core.util.StrUtil;

/**
 *
 * conver_rules
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-15 21:23:42
 */
@Service
public class ConverRulesServiceImpl extends ServiceImpl<ConverRulesMapper, ConverRulesEntity> implements ConverRulesService {

	@Autowired
	private MateAttributeService mateAttributeService;
	@Autowired
	private MateAttributeValueService mateAttributeValueService;
	
    @Override
    public List<ConverRulesEntity> getList(ConverRulesPagination converRulesPagination){
        QueryWrapper<ConverRulesEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(converRulesPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getId,converRulesPagination.getId()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getOntoId,converRulesPagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getOntoName()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getOntoName,converRulesPagination.getOntoName()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getCategoryId()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getCategoryId,converRulesPagination.getCategoryId()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getCategoryVal()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getCategoryVal,converRulesPagination.getCategoryVal()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getType()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getType,converRulesPagination.getType()));
        }

        //排序
        if(StrUtil.isEmpty(converRulesPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(ConverRulesEntity::getId);
        }else{
            queryWrapper="asc".equals(converRulesPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(converRulesPagination.getSidx()):queryWrapper.orderByDesc(converRulesPagination.getSidx());
        }
        Page<ConverRulesEntity> page=new Page<>(converRulesPagination.getCurrentPage(), converRulesPagination.getPageSize());
        IPage<ConverRulesEntity> userIPage=this.page(page,queryWrapper);
        return converRulesPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<ConverRulesEntity> getTypeList(ConverRulesPagination converRulesPagination,String dataType){
        QueryWrapper<ConverRulesEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(converRulesPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getId,converRulesPagination.getId()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getOntoId,converRulesPagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getOntoName()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getOntoName,converRulesPagination.getOntoName()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getCategoryId()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getCategoryId,converRulesPagination.getCategoryId()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getCategoryVal()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getCategoryVal,converRulesPagination.getCategoryVal()));
        }

        if(!"null".equals(String.valueOf(converRulesPagination.getType()))){
            queryWrapper.lambda().and(t->t.like(ConverRulesEntity::getType,converRulesPagination.getType()));
        }

        //排序
        if(StrUtil.isEmpty(converRulesPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(ConverRulesEntity::getId);
        }else{
            queryWrapper="asc".equals(converRulesPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(converRulesPagination.getSidx()):queryWrapper.orderByDesc(converRulesPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<ConverRulesEntity> page=new Page<>(converRulesPagination.getCurrentPage(), converRulesPagination.getPageSize());
            IPage<ConverRulesEntity> userIPage=this.page(page,queryWrapper);
            return converRulesPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public ConverRulesEntity getInfo(Long id){
        QueryWrapper<ConverRulesEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(ConverRulesEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(ConverRulesEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, ConverRulesEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(ConverRulesEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public List categoryVal(Long ontoId) {
		List valList = null;
		QueryWrapper<MateAttributeEntity> query =  new QueryWrapper<MateAttributeEntity>();
		query.eq("onto", ontoId);
		query.eq("name_en", "category_id");
		List<MateAttributeEntity> list =   mateAttributeService.list(query) ;
		if(list.size()!=0) {
			Long  attId = list.get(0).getId();
			QueryWrapper<MateAttributeValueEntity> valquery =  new QueryWrapper<MateAttributeValueEntity>();
			valquery.eq("attribute_id", attId);
			valList =mateAttributeValueService.list(valquery);
			
		}
		return valList;
		
	}
}