package com.sinosoft.ie.booster.yearbook.business.mapper;

import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.EventVeinOut;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.OrgEventOut;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 *
 * event
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-15 10:16:34
 */
@Mapper
public interface EventMapper extends BaseMapper<EventEntity> {
	List<EventEntity> queryAll(EventPagination eventPagination);
	int queryTagCount(EventPagination eventPagination);

	List<Long> pdidCount(EventPagination eventPagination);

	List<EventEntity> queryAllPage(EventPagination eventPagination);
	int queryAllCount(EventPagination eventPagination);

	@MapKey("city")
	List<Map<String,Integer>> mapData(EventPagination eventPagination);

	EventEntity getTimeSelect();

    Integer getNewsCount();

	List<OrgEventOut> getOrgEvent();

	List<EventVeinOut> getEventVein();

	List<EventEntity> queryAllAll(EventPagination eventPagination);

	List<Integer> monthlyStats();


	List<Map<String, Object>> getMonthlyStats();
}
