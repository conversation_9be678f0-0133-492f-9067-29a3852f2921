package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.constant.YearbookLibConstant;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.business.service.HomePagePersonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class HomePagePersonServiceImpl implements HomePagePersonService {

    @Resource
    private LibEntityMapper libEntityMapper;
    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;

    @Override
    public List<Map<String,Object>> getPersonList(){
        List<Map<String,Object>> totalList = new ArrayList<>();

        for (int i = 0; i < YearbookLibConstant.PERSON_CATEGORY_VALUE.size(); i++) {
            Map<String,Object> totalMap = new HashMap<>();
            List<Map<String,Object>> personList = new ArrayList<>();

            QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
            queryWrapper.lambda().and(t->t.eq(LibEntityEntity::getOntoId,YearbookLibConstant.PERSON_ONTO_ID));
            queryWrapper.apply("json_value(data,'$.category_id')= "+YearbookLibConstant.PERSON_CATEGORY_ID.get(i)+"");
            if (i == 0){
                //党委常委有历任和现任的分组
                queryWrapper.apply("json_value(data,'$.office_party') is not null");
            }

            List<LibEntityEntity> libEntityList = libEntityMapper.selectList(queryWrapper);


            for (LibEntityEntity person:libEntityList) {
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(person.getData());
                Map<String,Object> personMap = new HashMap<>();
                personMap.put("name",person.getName());
                String job = "";
                if (stringObjectMap.get(YearbookLibConstant.PERSON_JOB_NAME) != null){
                    MateAttributeValueEntity jobEntity = mateAttributeValueMapper.selectById(
                            (Serializable) stringObjectMap.get(YearbookLibConstant.PERSON_JOB_NAME));
                    if (jobEntity != null){
                        job = jobEntity.getValName();
                    }
                }
                personMap.put("job",job);
                personMap.put("image",stringObjectMap.get(YearbookLibConstant.PERSON_IMAGE_NAME));
                personMap.put("pageNum",stringObjectMap.get("pageNum"));
                personMap.put("year",stringObjectMap.get("year"));
                personMap.put("office_party",stringObjectMap.get("office_party"));
                personList.add(personMap);

            }
            if (i == 0){
                //党委常委有历任和现任的分组
                Map<String, List<Map<String, Object>>> listMap = personList.stream().collect(
                        Collectors.groupingBy(item -> item.get("office_party").toString())
                );
                totalMap.put(YearbookLibConstant.PERSON_CATEGORY_VALUE.get(i),listMap);
            }else {
                totalMap.put(YearbookLibConstant.PERSON_CATEGORY_VALUE.get(i),personList);
            }


            totalList.add(totalMap);
        }

        return totalList;
    }
}
