package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 标注-三元组
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-26 14:30:05
 */
@Data
@TableName("mark_entity")
@ApiModel(value = "标注-三元组")
public class MarkEntityEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 实体/属性
     */
    @ApiModelProperty(value = "实体/属性")
    private String tagname;

    /**
     * 实体类型标注，lib_concept
     */
    @ApiModelProperty(value = "实体类型标注，lib_concept")
    private Long tagId;

    /**
     * 如果是属性枚举值，则保存枚举的id
     */
    @ApiModelProperty(value = "如果是属性枚举值，则保存枚举的id")
    private Long tagPid;

    /**
     * 句子内容
     */
    @ApiModelProperty(value = "句子内容")
    private String text;

    /**
     * 句子id
     */
    @ApiModelProperty(value = "句子id")
    private Long sentenceid;

    /**
     * 实体类型标注
     */
    @ApiModelProperty(value = "实体类型标注")
    private String ontoname;

    /**
     * 开始索引
     */
    @ApiModelProperty(value = "开始索引")
    private Integer startindex;

    /**
     * 结束索引
     */
    @ApiModelProperty(value = "结束索引")
    private Integer endindex;

    /**
     * 标签类型；1实体；2属性3属性值
     */
    @ApiModelProperty(value = "标签类型；1实体;2属性;3可选属性值;4手动录入的属性值")
    private Integer labType;
    
    @ApiModelProperty(value = "是否是对象属性：0:非对象属性；1:对象属性")
    private Integer valType;
    
    public boolean   comparison(MarkEntityEntity ent) {
    	if(ent==null) {
    		return false;
    	}
    	if(this.tagname.equals(ent.getTagname())&&this.ontoname.equals(ent.getOntoname())
    			&& this.text.equals(ent.getText())) {
    		return true;
    	}
    	return false;
    	
    }
}
