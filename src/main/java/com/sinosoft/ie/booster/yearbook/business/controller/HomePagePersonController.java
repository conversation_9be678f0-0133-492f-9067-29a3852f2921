package com.sinosoft.ie.booster.yearbook.business.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.visualdev.util.FileUtil;
import com.sinosoft.ie.booster.yearbook.business.service.HomePagePersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/HomePagePerson")
public class HomePagePersonController {

    @Resource
    private HomePagePersonService homePagePersonService;

    /**
     * 列表
     */
    @GetMapping("/getPersonList")
    public R getPersonList(){
    	StopWatch stopWatch = new StopWatch();
    	stopWatch.start();
//    	 List<Map<String,Object>>  list = homePagePersonService.getPersonList();
        String jsonStr = FileUtil.getString(System.getProperty("user.dir") + File.separator, "getPersonList.json");
     	double uptime = stopWatch.getTotalTimeMillis() / 1000.0;
     	log.info("首页获取人物接口查询时间为===>"+stopWatch.prettyPrint()+" ===> "+uptime);
//        return R.ok(JSONUtil.parseObj(jsonStr));
        JSONArray personList = JSONUtil.parseArray(jsonStr);
        System.out.println(personList);
        return R.ok(personList);
    }

    @GetMapping("/getOrgContent")
    public R getOrgContent(){
        String jsonStr = FileUtil.getString(System.getProperty("user.dir") + File.separator, "describe.json");
        System.out.println(jsonStr);
        return R.ok(JSONUtil.parseObj(jsonStr));
    }


}
