package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-15 10:16:34
 */
@Data
@TableName("event")
@ApiModel(value = "")
public class EventEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    @ApiModelProperty(value = "文档id")
    private  Long fid;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 地点
     */
    @ApiModelProperty(value = "地点")
    private String venue;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 人物
     */
    @ApiModelProperty(value = "人物")
    private String persons;

    /**
     * 物品
     */
    @ApiModelProperty(value = "物品")
    private String articles;

    /**
     * 机构
     */
    @ApiModelProperty(value = "机构")
    private String organisation;

    /**
     * 触发词
     */
    @ApiModelProperty(value = "触发词")
    private String triggerss;

    /**
     * 多元关系
     */
    @ApiModelProperty(value = "多元关系")
    private String multiRelations;

    /**
     * 次数
     */
    @ApiModelProperty(value = "次数")
    private String frequency;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String images;

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String category;

    /**
     * 事件文本
     */
    @ApiModelProperty(value = "事件文本")
    private String content;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String note;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgid;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String tag;
}
