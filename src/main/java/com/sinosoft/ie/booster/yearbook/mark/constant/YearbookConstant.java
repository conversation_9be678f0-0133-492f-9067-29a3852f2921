package com.sinosoft.ie.booster.yearbook.mark.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class YearbookConstant {
	
	//标注数据 实体还是属性
	public static String MARK_ENTITY = "实体";
	public static String MARK_ATTRIBUTE = "属性";
	public static String MARK_EVENT_CONTENT = "事件内容";
	public static String MARK_EVENT_YEAR = "事件年份";
	public static String MARK_EVENT = "事件";
	public static String MARK_PAGE_NUM = "页码";
	//事件本体id
	public static Long MARK_EVENT_ONTO_ID = 1586993312286375938L;
	//主题本体id
	public static Long MARK_THEME_ONTO_ID = 1655899624481320962L;
	//机构本体id
	public static Long MARK_ORG_ONTO_ID = 1586973763214368769L;
	//人物本体id
	public static Long MARK_PER_ONTO_ID = 1586970280968970242L;
	//成果本体id
	public static Long MARK_RESULT_ONTO_ID = 1586968527091392513L;
	//项目本体id
	public static Long MARK_PROJECT_ONTO_ID = 1586999607391420418L;

	//荣耀本体id
	public static Long MARK_AWARD_ONTO_ID = 1625418542513926146L;
	//活动本体id								
	public static Long MARK_ACTIVE_ONTO_ID = 1623985394850742273L;
	//组织本体id
	public static Long MARK_TEAM_ONTO_ID = 1649952061550292993L;
	//科研经费本体id
	public static Long MARK_FUNDS_ONTO_ID = 1604454992306974721L;
	//物资模拟本体id
	public static Long MARK_ARTICLES_ONTO_ID = 1604454992306974722L;
	//关键词本体
	public static Long MARK_TAG_ONTO_ID = 1679775164511866881L;
	
	
	//属性  文本框
	public static Integer META_ATTRIBUTE_FROM_TPYE_TEXT = 1 ; 
	//属性  下拉框-对象类型
	public static Integer META_ATTRIBUTE_FROM_TPYE_SELECT = 2 ; 
	//属性  文本下拉框-枚举值
	public static Integer META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT = 3 ; 
	
	//1：值类型数字；2：值类型字符串；3：值类型浮点型；4：对象类型; 5：日期；6：时间 ；7图片  ；8文件
	public static Integer META_ATTRIBUTE_VAL_NUMBER = 1;
	public static Integer META_ATTRIBUTE_VAL_STR = 2;
	public static Integer META_ATTRIBUTE_VAL_FLOAT = 3;
	public static Integer META_ATTRIBUTE_VAL_OBJECT = 4;
	public static Integer META_ATTRIBUTE_VAL_DATA = 5;
	public static Integer META_ATTRIBUTE_VAL_TIME = 6;
	public static Integer META_ATTRIBUTE_VAL_IMAGE = 7;
	public static Integer META_ATTRIBUTE_VAL_FILE = 8;
	
	
	
	
	
	//默认未处理，就绪
	public static Integer  MARK_STATS_WAIT = 1;
	//处理中
	public static Integer  MARK_STATS_PROCESSING = 2;
	//只能标注已完成
	public static Integer  MARK_STATS_EXT_COMPLETED = 3;
	//全流程完成
	public static Integer  MARK_STATS_COMPLE = 4;
	//error
	public static Integer  MARK_STATS_ERROR = 5;

	public static List<Map<String,Object>> GRAPH_VALUE_LIST = new ArrayList<>();
	static {
		//人物
		Map<String,Object> GRAPH_VALUE_MAP_PER = new HashMap<>();
		GRAPH_VALUE_MAP_PER.put("id",MARK_PER_ONTO_ID);
		GRAPH_VALUE_MAP_PER.put("name","人物");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_PER);
		//机构
		Map<String,Object> GRAPH_VALUE_MAP_ORG = new HashMap<>();
		GRAPH_VALUE_MAP_ORG.put("id",MARK_ORG_ONTO_ID);
		GRAPH_VALUE_MAP_ORG.put("name","机构");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_ORG);
		//成果
		Map<String,Object> GRAPH_VALUE_MAP_RESULT = new HashMap<>();
		GRAPH_VALUE_MAP_RESULT.put("id",MARK_RESULT_ONTO_ID);
		GRAPH_VALUE_MAP_RESULT.put("name","成果");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_RESULT);
		//组织
		Map<String,Object> GRAPH_VALUE_MAP_TEAM = new HashMap<>();
		GRAPH_VALUE_MAP_TEAM.put("id",MARK_TEAM_ONTO_ID);
		GRAPH_VALUE_MAP_TEAM.put("name","组织");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_TEAM);
		//荣誉
		Map<String,Object> GRAPH_VALUE_MAP_AWARD = new HashMap<>();
		GRAPH_VALUE_MAP_AWARD.put("id",MARK_AWARD_ONTO_ID);
		GRAPH_VALUE_MAP_AWARD.put("name","荣誉");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_AWARD);
		//项目
		Map<String,Object> GRAPH_VALUE_MAP_PROJECT = new HashMap<>();
		GRAPH_VALUE_MAP_PROJECT.put("id",MARK_PROJECT_ONTO_ID);
		GRAPH_VALUE_MAP_PROJECT.put("name","项目");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_PROJECT);
		//活动
		Map<String,Object> GRAPH_VALUE_MAP_ACTIVE = new HashMap<>();
		GRAPH_VALUE_MAP_ACTIVE.put("id",MARK_ACTIVE_ONTO_ID);
		GRAPH_VALUE_MAP_ACTIVE.put("name","活动");
		GRAPH_VALUE_LIST.add(GRAPH_VALUE_MAP_ACTIVE);
	}
	public static String GRAPH_HUMAN_STR = MARK_PER_ONTO_ID+","+MARK_RESULT_ONTO_ID+","+MARK_AWARD_ONTO_ID+
			","+MARK_PROJECT_ONTO_ID+","+MARK_ORG_ONTO_ID+","+MARK_TEAM_ONTO_ID+","+MARK_ACTIVE_ONTO_ID;

	public static List<Map<String,Object>> GRAPH_THEME_LIST = new ArrayList<>();
	static {
		//人物
		Map<String,Object> GRAPH_VALUE_MAP_PER = new HashMap<>();
		GRAPH_VALUE_MAP_PER.put("id",MARK_PER_ONTO_ID);
		GRAPH_VALUE_MAP_PER.put("name","人物");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_PER);
		//组织
		Map<String,Object> GRAPH_VALUE_MAP_TEAM = new HashMap<>();
		GRAPH_VALUE_MAP_TEAM.put("id",MARK_TEAM_ONTO_ID);
		GRAPH_VALUE_MAP_TEAM.put("name","组织");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_TEAM);
		//成果
		Map<String,Object> GRAPH_VALUE_MAP_RESULT = new HashMap<>();
		GRAPH_VALUE_MAP_RESULT.put("id",MARK_RESULT_ONTO_ID);
		GRAPH_VALUE_MAP_RESULT.put("name","成果");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_RESULT);
		//荣誉
		Map<String,Object> GRAPH_VALUE_MAP_AWARD = new HashMap<>();
		GRAPH_VALUE_MAP_AWARD.put("id",MARK_AWARD_ONTO_ID);
		GRAPH_VALUE_MAP_AWARD.put("name","荣誉");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_AWARD);
		//项目
		Map<String,Object> GRAPH_VALUE_MAP_PROJECT = new HashMap<>();
		GRAPH_VALUE_MAP_PROJECT.put("id",MARK_PROJECT_ONTO_ID);
		GRAPH_VALUE_MAP_PROJECT.put("name","项目");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_PROJECT);
		//经费
		Map<String,Object> GRAPH_VALUE_MAP_FUNDS = new HashMap<>();
		GRAPH_VALUE_MAP_FUNDS.put("id",MARK_FUNDS_ONTO_ID);
		GRAPH_VALUE_MAP_FUNDS.put("name","经费");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_FUNDS);
		//物资
		Map<String,Object> GRAPH_VALUE_MAP_ARTICLES = new HashMap<>();
		GRAPH_VALUE_MAP_ARTICLES.put("id",MARK_ARTICLES_ONTO_ID);
		GRAPH_VALUE_MAP_ARTICLES.put("name","物资");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_ARTICLES);
		//主题
		Map<String,Object> GRAPH_VALUE_MAP_THEME = new HashMap<>();
		GRAPH_VALUE_MAP_THEME.put("id",MARK_THEME_ONTO_ID);
		GRAPH_VALUE_MAP_THEME.put("name","主题");
		GRAPH_THEME_LIST.add(GRAPH_VALUE_MAP_THEME);

	}
	public static String GRAPH_THEME_STR = MARK_PER_ONTO_ID+","+MARK_RESULT_ONTO_ID+","+MARK_AWARD_ONTO_ID+
			","+MARK_PROJECT_ONTO_ID+","+MARK_FUNDS_ONTO_ID+","+MARK_TEAM_ONTO_ID+","+MARK_ARTICLES_ONTO_ID
			+","+MARK_THEME_ONTO_ID;

	public static List<Map<String,Object>> GRAPH_TAG_LIST = new ArrayList<>();
	static {
		//人物
		Map<String,Object> GRAPH_VALUE_MAP_PER = new HashMap<>();
		GRAPH_VALUE_MAP_PER.put("id",MARK_PER_ONTO_ID);
		GRAPH_VALUE_MAP_PER.put("name","人物");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_PER);
		//组织
		Map<String,Object> GRAPH_VALUE_MAP_TEAM = new HashMap<>();
		GRAPH_VALUE_MAP_TEAM.put("id",MARK_TEAM_ONTO_ID);
		GRAPH_VALUE_MAP_TEAM.put("name","组织");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_TEAM);
		//成果
		Map<String,Object> GRAPH_VALUE_MAP_RESULT = new HashMap<>();
		GRAPH_VALUE_MAP_RESULT.put("id",MARK_RESULT_ONTO_ID);
		GRAPH_VALUE_MAP_RESULT.put("name","成果");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_RESULT);
		//荣誉
		Map<String,Object> GRAPH_VALUE_MAP_AWARD = new HashMap<>();
		GRAPH_VALUE_MAP_AWARD.put("id",MARK_AWARD_ONTO_ID);
		GRAPH_VALUE_MAP_AWARD.put("name","荣誉");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_AWARD);
		//项目
		Map<String,Object> GRAPH_VALUE_MAP_PROJECT = new HashMap<>();
		GRAPH_VALUE_MAP_PROJECT.put("id",MARK_PROJECT_ONTO_ID);
		GRAPH_VALUE_MAP_PROJECT.put("name","项目");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_PROJECT);
		//经费
		Map<String,Object> GRAPH_VALUE_MAP_FUNDS = new HashMap<>();
		GRAPH_VALUE_MAP_FUNDS.put("id",MARK_FUNDS_ONTO_ID);
		GRAPH_VALUE_MAP_FUNDS.put("name","经费");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_FUNDS);
		//物资
		Map<String,Object> GRAPH_VALUE_MAP_ARTICLES = new HashMap<>();
		GRAPH_VALUE_MAP_ARTICLES.put("id",MARK_ARTICLES_ONTO_ID);
		GRAPH_VALUE_MAP_ARTICLES.put("name","物资");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_ARTICLES);
		//主题
		Map<String,Object> GRAPH_VALUE_MAP_THEME = new HashMap<>();
		GRAPH_VALUE_MAP_THEME.put("id",MARK_TAG_ONTO_ID);
		GRAPH_VALUE_MAP_THEME.put("name","主题");
		GRAPH_TAG_LIST.add(GRAPH_VALUE_MAP_THEME);

	}
	public static String GRAPH_TAG_STR = MARK_PER_ONTO_ID+","+MARK_RESULT_ONTO_ID+","+MARK_AWARD_ONTO_ID+
			","+MARK_PROJECT_ONTO_ID+","+MARK_FUNDS_ONTO_ID+","+MARK_TEAM_ONTO_ID+","+MARK_ARTICLES_ONTO_ID
			+","+MARK_TAG_ONTO_ID;

	public static List<Map<String,Object>> GRAPH_ORG_LIST = new ArrayList<>();
	static {
		//机构
		Map<String,Object> GRAPH_VALUE_MAP_ORG = new HashMap<>();
		GRAPH_VALUE_MAP_ORG.put("id",MARK_ORG_ONTO_ID);
		GRAPH_VALUE_MAP_ORG.put("name","机构");
		GRAPH_ORG_LIST.add(GRAPH_VALUE_MAP_ORG);
		//人物
		Map<String,Object> GRAPH_VALUE_MAP_PER = new HashMap<>();
		GRAPH_VALUE_MAP_PER.put("id",MARK_PER_ONTO_ID);
		GRAPH_VALUE_MAP_PER.put("name","人物");
		GRAPH_ORG_LIST.add(GRAPH_VALUE_MAP_PER);
		//成果
		Map<String,Object> GRAPH_VALUE_MAP_RESULT = new HashMap<>();
		GRAPH_VALUE_MAP_RESULT.put("id",MARK_RESULT_ONTO_ID);
		GRAPH_VALUE_MAP_RESULT.put("name","成果");
		GRAPH_ORG_LIST.add(GRAPH_VALUE_MAP_RESULT);
		//荣誉
		Map<String,Object> GRAPH_VALUE_MAP_AWARD = new HashMap<>();
		GRAPH_VALUE_MAP_AWARD.put("id",MARK_AWARD_ONTO_ID);
		GRAPH_VALUE_MAP_AWARD.put("name","荣誉");
		GRAPH_ORG_LIST.add(GRAPH_VALUE_MAP_AWARD);
		//项目
		Map<String,Object> GRAPH_VALUE_MAP_PROJECT = new HashMap<>();
		GRAPH_VALUE_MAP_PROJECT.put("id",MARK_PROJECT_ONTO_ID);
		GRAPH_VALUE_MAP_PROJECT.put("name","项目");
		GRAPH_ORG_LIST.add(GRAPH_VALUE_MAP_PROJECT);
	}
	public static String GRAPH_ORG_STR = MARK_PER_ONTO_ID+","+MARK_RESULT_ONTO_ID+","+MARK_AWARD_ONTO_ID+","+MARK_PROJECT_ONTO_ID+","+MARK_ORG_ONTO_ID;
}
