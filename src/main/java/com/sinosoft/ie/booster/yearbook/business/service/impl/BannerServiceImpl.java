package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.BannerEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.BannerMapper;
import com.sinosoft.ie.booster.yearbook.business.service.BannerService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.business.model.banner.BannerPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * banner
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-10 13:51:42
 */
@Service
public class BannerServiceImpl extends ServiceImpl<BannerMapper, BannerEntity> implements BannerService {


    @Override
    public List<BannerEntity> getList(BannerPagination bannerPagination){
        QueryWrapper<BannerEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(bannerPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getId,bannerPagination.getId()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getYearbookId()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getYearbookId,bannerPagination.getYearbookId()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getSort()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getSort,bannerPagination.getSort()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getDelFlag,bannerPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getCreateTime()))){
            queryWrapper.lambda().eq(BannerEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(bannerPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getCreateBy,bannerPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(BannerEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(bannerPagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getUpdateBy,bannerPagination.getUpdateBy()));
        }

        //排序
        if(StrUtil.isEmpty(bannerPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(BannerEntity::getId);
        }else{
            queryWrapper="asc".equals(bannerPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(bannerPagination.getSidx()):queryWrapper.orderByDesc(bannerPagination.getSidx());
        }
        Page<BannerEntity> page=new Page<>(bannerPagination.getCurrentPage(), bannerPagination.getPageSize());
        IPage<BannerEntity> userIPage=this.page(page,queryWrapper);
        return bannerPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<BannerEntity> getTypeList(BannerPagination bannerPagination,String dataType){
        QueryWrapper<BannerEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(bannerPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getId,bannerPagination.getId()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getYearbookId()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getYearbookId,bannerPagination.getYearbookId()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getSort()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getSort,bannerPagination.getSort()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getDelFlag,bannerPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getCreateTime()))){
            queryWrapper.lambda().eq(BannerEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(bannerPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getCreateBy,bannerPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(BannerEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(bannerPagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(bannerPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(BannerEntity::getUpdateBy,bannerPagination.getUpdateBy()));
        }

        //排序
        if(StrUtil.isEmpty(bannerPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(BannerEntity::getId);
        }else{
            queryWrapper="asc".equals(bannerPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(bannerPagination.getSidx()):queryWrapper.orderByDesc(bannerPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<BannerEntity> page=new Page<>(bannerPagination.getCurrentPage(), bannerPagination.getPageSize());
            IPage<BannerEntity> userIPage=this.page(page,queryWrapper);
            return bannerPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public BannerEntity getInfo(Long id){
        QueryWrapper<BannerEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(BannerEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(BannerEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, BannerEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(BannerEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}