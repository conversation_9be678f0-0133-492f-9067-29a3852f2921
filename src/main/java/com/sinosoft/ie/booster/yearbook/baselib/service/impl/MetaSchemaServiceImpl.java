package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinosoft.ie.booster.visualdev.util.DateUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.*;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogExcel;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaPagination;
import com.sinosoft.ie.booster.yearbook.baselib.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;

import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

/**
 *
 * meta_schema
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-02 15:36:32
 */
@Service
public class MetaSchemaServiceImpl extends ServiceImpl<MetaSchemaMapper, MetaSchemaEntity> implements MetaSchemaService {

    @Resource
    private LibConceptMapper libConceptMapper;
    @Resource
    private LibConceptService libConceptService;

    @Resource
    private MateAttributeMapper mateAttributeMapper;

    @Resource
    private MetaOntoRelationMapper metaOntoRelationMapper;

    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;
    @Resource
    private MateAttributeValueService mateAttributeValueService;

    @Resource
    private MateRelationAttributeMapper mateRelationAttributeMapper;
    @Resource
    private MateRelationAttributeValueMapper mateRelationAttributeValueMapper;
    @Resource
    private MateRelationAttributeValueService mateRelationAttributeValueService;
    @Autowired
    private ProjectConfig projectConfig;
    @Resource
    private LibEntityMapper libEntityMapper;
    @Resource
    private FileImportJobService fileImportJobService;
    @Resource
    private FileImportLogService fileImportLogService;
    @Resource
    private MetaEntityRelationMapper metaEntityRelationMapper;
    @Override
    public List<MetaSchemaEntity> getList(MetaSchemaPagination metaSchemaPagination){
        QueryWrapper<MetaSchemaEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(metaSchemaPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getId,metaSchemaPagination.getId()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getName,metaSchemaPagination.getName()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getDelFlag,metaSchemaPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getCreateTime()))){
            queryWrapper.lambda().eq(MetaSchemaEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(metaSchemaPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getCreateBy,metaSchemaPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(MetaSchemaEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(metaSchemaPagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getUpdateBy,metaSchemaPagination.getUpdateBy()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getState()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getState,metaSchemaPagination.getState()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getSchemaType()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getSchemaType,metaSchemaPagination.getSchemaType()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getExtId()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getExtId,metaSchemaPagination.getExtId()));
        }

        //排序
        if(StrUtil.isEmpty(metaSchemaPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MetaSchemaEntity::getId);
        }else{
            queryWrapper="asc".equals(metaSchemaPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaSchemaPagination.getSidx()):queryWrapper.orderByDesc(metaSchemaPagination.getSidx());
        }
        Page<MetaSchemaEntity> page=new Page<>(metaSchemaPagination.getCurrentPage(), metaSchemaPagination.getPageSize());
        IPage<MetaSchemaEntity> userIPage=this.page(page,queryWrapper);
        return metaSchemaPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MetaSchemaEntity> getTypeList(MetaSchemaPagination metaSchemaPagination,String dataType){
        QueryWrapper<MetaSchemaEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(metaSchemaPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getId,metaSchemaPagination.getId()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getName,metaSchemaPagination.getName()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getDelFlag,metaSchemaPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getCreateTime()))){
            queryWrapper.lambda().eq(MetaSchemaEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(metaSchemaPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getCreateBy,metaSchemaPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(MetaSchemaEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(metaSchemaPagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getUpdateBy,metaSchemaPagination.getUpdateBy()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getState()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getState,metaSchemaPagination.getState()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getSchemaType()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getSchemaType,metaSchemaPagination.getSchemaType()));
        }

        if(!"null".equals(String.valueOf(metaSchemaPagination.getExtId()))){
            queryWrapper.lambda().and(t->t.like(MetaSchemaEntity::getExtId,metaSchemaPagination.getExtId()));
        }

        //排序
        if(StrUtil.isEmpty(metaSchemaPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MetaSchemaEntity::getId);
        }else{
            queryWrapper="asc".equals(metaSchemaPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaSchemaPagination.getSidx()):queryWrapper.orderByDesc(metaSchemaPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MetaSchemaEntity> page=new Page<>(metaSchemaPagination.getCurrentPage(), metaSchemaPagination.getPageSize());
            IPage<MetaSchemaEntity> userIPage=this.page(page,queryWrapper);
            return metaSchemaPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MetaSchemaEntity getInfo(Long id){
        QueryWrapper<MetaSchemaEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MetaSchemaEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MetaSchemaEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MetaSchemaEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MetaSchemaEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

    @Override
    public String dealConceptFileData(String filePath,Long schemaId) {
        // 定义返回值
        String result = "";
        // 定义存放数据的集合
        List<List<String>> list = new ArrayList<>();
        // 创建Workbook工作薄对象
        Workbook workbook = null;
        try {
            // 获取文件
            File file = new File(filePath);
            // 获取文件名
            String fileName = file.getName();
            // 获取文件io流
            InputStream is = new FileInputStream(file);
            // 根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith(".xls")) {
                // 2003
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(".xlsx")) {
                // 2007
                workbook = new XSSFWorkbook(is);
            }

            // 获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            Row headrow = sheet.getRow(1);
            // 获得当前sheet的结束行
            int lastRowNum = sheet.getLastRowNum();
            // 根据行头确定每一行的列数，这里规定了行头的列数 = 数据的列数
            int lastCellNum = headrow.getLastCellNum();

            // 循环除了第一行的所有行
            for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
                // 获得当前行
                Row row = sheet.getRow(rowNum);
                List<String> dataList = new ArrayList<>();
                // 循环当前行
                for (int cellNum = 0; cellNum < lastCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String cellVal = FileUtil.getCellString(cell).toString().trim();

                    // 校验转换后的cellVal
                    if ("error".equals(cellVal)) {
                        System.out.println("data"+ "上传失败,模板中存在不识别内容!");
                        return "上传失败,模板中存在不识别内容!";
                    }
                    dataList.add(cellVal);
                }
                if (dataList.size() > 0) {
                    list.add(dataList);
                }
            }
            dealInsertConceptData(list,schemaId);
            return result;
        } catch (IOException e) {
            System.out.println("data"+ "系统异常，请刷新页面重新请求！");
            e.printStackTrace();
        }
        return result;
    }

    public void dealInsertConceptData(List<List<String>> list,Long schemaId){
        for (List<String> excelData : list) {
            //先插入父级为空的
            if (excelData.get(0).isEmpty()) {
                LibConceptEntity libConceptEntity = new LibConceptEntity();
                libConceptEntity.setName(excelData.get(2));
                libConceptEntity.setDisambiguation(excelData.get(3));
                libConceptEntity.setSchemaId(schemaId);
                libConceptMapper.insert(libConceptEntity);
            }
        }
        for (List<String> excelData : list) {
            //插入父级不为空的
            if (excelData.get(0) != null && !excelData.get(0).isEmpty()) {
                QueryWrapper<LibConceptEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().and(t -> t.eq(LibConceptEntity::getName, excelData.get(0)));
                List<LibConceptEntity> pList = libConceptMapper.selectList(queryWrapper);
                long pid;
                if (pList.size() == 0) {
                    //没有搜到父级，创建一个父级
                    LibConceptEntity libConceptP = new LibConceptEntity();
                    libConceptP.setName(excelData.get(0));
                    libConceptP.setDisambiguation(excelData.get(1));
                    libConceptP.setSchemaId(schemaId);
                    libConceptMapper.insert(libConceptP);
                    pid = libConceptP.getId();
                } else {
                    pid = pList.get(0).getId();
                }
                LibConceptEntity libConceptEntity = new LibConceptEntity();
                libConceptEntity.setPid(pid);
                libConceptEntity.setName(excelData.get(2));
                libConceptEntity.setDisambiguation(excelData.get(3));
                libConceptEntity.setSchemaId(schemaId);
                libConceptMapper.insert(libConceptEntity);
            }
        }
    }

    @Override
    public String dealNumberFileData(String filePath,Long schemaId) {
        // 定义返回值
        String result = "";
        // 定义存放数据的集合
        List<List<String>> list = new ArrayList<>();
        // 创建Workbook工作薄对象
        Workbook workbook = null;
        try {
            // 获取文件
            File file = new File(filePath);
            // 获取文件名
            String fileName = file.getName();
            // 获取文件io流
            InputStream is = new FileInputStream(file);
            // 根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith(".xls")) {
                // 2003
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(".xlsx")) {
                // 2007
                workbook = new XSSFWorkbook(is);
            }

            // 获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            Row headrow = sheet.getRow(1);
            // 获得当前sheet的结束行
            int lastRowNum = sheet.getLastRowNum();
            // 根据行头确定每一行的列数，这里规定了行头的列数 = 数据的列数
            int lastCellNum = headrow.getLastCellNum();

            // 循环除了第一行的所有行
            for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
                // 获得当前行
                Row row = sheet.getRow(rowNum);
                List<String> dataList = new ArrayList<>();
                // 循环当前行
                for (int cellNum = 0; cellNum < lastCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String cellVal = FileUtil.getCellString(cell).toString().trim();

                    // 校验转换后的cellVal
                    if ("error".equals(cellVal)) {
                        System.out.println("data"+ "上传失败,模板中存在不识别内容!");
                        return "上传失败,模板中存在不识别内容!";
                    }
                    dataList.add(cellVal);
                }
                if (dataList.size() > 0) {
                    list.add(dataList);
                }
            }
            dealInsertNumberData(list,schemaId);
            return result;
        } catch (IOException e) {
            System.out.println("data"+ "系统异常，请刷新页面重新请求！");
            e.printStackTrace();
        }
        return result;
    }

    public void dealInsertNumberData(List<List<String>> list,Long schemaId){
        for (List<String> excelData : list) {
            //查找属性定义域
            if (excelData.get(0) != null && !excelData.get(0).isEmpty()) {
                QueryWrapper<LibConceptEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().and(t -> t.eq(LibConceptEntity::getName, excelData.get(2)));
                List<LibConceptEntity> pList = libConceptMapper.selectList(queryWrapper);
                long ontoId;
                if (pList.size() == 0) {
                    //没有搜到，创建一个
                    LibConceptEntity libConceptP = new LibConceptEntity();
                    libConceptP.setName(excelData.get(2));
                    libConceptP.setDisambiguation(excelData.get(3));
                    libConceptP.setSchemaId(schemaId);
                    libConceptMapper.insert(libConceptP);
                    ontoId = libConceptP.getId();
                } else {
                    ontoId = pList.get(0).getId();
                }
                MateAttributeEntity mateAttributeEntity = new MateAttributeEntity();
                mateAttributeEntity.setNameEn(excelData.get(0));
                mateAttributeEntity.setNameCn(excelData.get(1));
                mateAttributeEntity.setOnto(ontoId);
                mateAttributeEntity.setValType(Integer.valueOf(excelData.get(4)));
                mateAttributeEntity.setUnit(excelData.get(5));
                mateAttributeEntity.setSchemaId(schemaId);
                mateAttributeMapper.insert(mateAttributeEntity);
            }
        }
    }

    @Override
    public String dealObjectFileData(String filePath,Long schemaId) {
        // 定义返回值
        String result = "";
        // 定义存放数据的集合
        List<List<String>> list = new ArrayList<>();
        // 创建Workbook工作薄对象
        Workbook workbook = null;
        try {
            // 获取文件
            File file = new File(filePath);
            // 获取文件名
            String fileName = file.getName();
            // 获取文件io流
            InputStream is = new FileInputStream(file);
            // 根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith(".xls")) {
                // 2003
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(".xlsx")) {
                // 2007
                workbook = new XSSFWorkbook(is);
            }

            // 获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            Row headrow = sheet.getRow(1);
            // 获得当前sheet的结束行
            int lastRowNum = sheet.getLastRowNum();
            // 根据行头确定每一行的列数，这里规定了行头的列数 = 数据的列数
            int lastCellNum = headrow.getLastCellNum();

            // 循环除了第一行的所有行
            for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
                // 获得当前行
                Row row = sheet.getRow(rowNum);
                List<String> dataList = new ArrayList<>();
                // 循环当前行
                for (int cellNum = 0; cellNum < lastCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String cellVal = FileUtil.getCellString(cell).toString().trim();

                    // 校验转换后的cellVal
                    if ("error".equals(cellVal)) {
                        System.out.println("data"+ "上传失败,模板中存在不识别内容!");
                        return "上传失败,模板中存在不识别内容!";
                    }
                    dataList.add(cellVal);
                }
                if (dataList.size() > 0) {
                    list.add(dataList);
                }
            }
            dealInsertObjectData(list,schemaId);
            return result;
        } catch (IOException e) {
            System.out.println("data"+ "系统异常，请刷新页面重新请求！");
            e.printStackTrace();
        }
        return result;
    }

    public void dealInsertObjectData(List<List<String>> list,Long schemaId){
        for (List<String> excelData : list) {
            if (excelData.get(0) != null && !excelData.get(0).isEmpty()) {
                //查找属性定义域
                long ontoId;
                QueryWrapper<LibConceptEntity> ontoWrapper = new QueryWrapper<>();
                ontoWrapper.lambda().and(t -> t.eq(LibConceptEntity::getName, excelData.get(2)));
                List<LibConceptEntity> ontoList = libConceptMapper.selectList(ontoWrapper);
                if (ontoList.size() == 0) {
                    //没有搜到，创建一个
                    LibConceptEntity libConceptP = new LibConceptEntity();
                    libConceptP.setName(excelData.get(2));
                    libConceptP.setDisambiguation(excelData.get(3));
                    libConceptP.setSchemaId(schemaId);
                    libConceptMapper.insert(libConceptP);
                    ontoId = libConceptP.getId();
                } else {
                    ontoId = ontoList.get(0).getId();
                }
                //查找属性值域
                long point;
                QueryWrapper<LibConceptEntity> pointWrapper = new QueryWrapper<>();
                pointWrapper.lambda().and(t -> t.eq(LibConceptEntity::getName, excelData.get(4)));
                List<LibConceptEntity> pointList = libConceptMapper.selectList(pointWrapper);
                if (pointList.size() == 0) {
                    //没有搜到，创建一个
                    LibConceptEntity libConceptP = new LibConceptEntity();
                    libConceptP.setName(excelData.get(4));
                    libConceptP.setDisambiguation(excelData.get(5));
                    libConceptP.setSchemaId(schemaId);
                    libConceptMapper.insert(libConceptP);
                    point = libConceptP.getId();
                } else {
                    point = pointList.get(0).getId();
                }

                MateAttributeEntity mateAttributeEntity = new MateAttributeEntity();
                mateAttributeEntity.setNameEn(excelData.get(0));
                mateAttributeEntity.setNameCn(excelData.get(1));
                mateAttributeEntity.setOnto(ontoId);
                mateAttributeEntity.setPoint(point);
                mateAttributeEntity.setSchemaId(schemaId);
                mateAttributeMapper.insert(mateAttributeEntity);
            }
        }
    }

//    @Override
//    public void schemaReuse(MetaSchemaEntity entity){
//        //lib_concept
//        Map<Long,Long> conceptMap = new HashMap<>();
//        //pid=0
//        QueryWrapper<LibConceptEntity> pConceptWrapper = new QueryWrapper<>();
//        pConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getSchemaId, entity.getExtId()));
//        pConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getPid, 0));
//        List<LibConceptEntity> pConceptList = libConceptMapper.selectList(pConceptWrapper);
//        for (LibConceptEntity pconcept:pConceptList) {
//            Long oldId = pconcept.getId();
//            pconcept.setId(null);
//            pconcept.setSchemaId(entity.getId());
//            libConceptMapper.insert(pconcept);
//            conceptMap.put(oldId,pconcept.getId());
//        }
//        //pid！=0
//        QueryWrapper<LibConceptEntity> cConceptWrapper = new QueryWrapper<>();
//        cConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getSchemaId, entity.getExtId()));
//        cConceptWrapper.lambda().and(t -> t.ne(LibConceptEntity::getPid, 0));
//        List<LibConceptEntity> cConceptList = libConceptMapper.selectList(cConceptWrapper);
//        for (LibConceptEntity cconcept:cConceptList) {
//            Long oldId = cconcept.getId();
//            cconcept.setId(null);
//            cconcept.setSchemaId(entity.getId());
//            cconcept.setPid(conceptMap.get(cconcept.getPid()));
//            libConceptMapper.insert(cconcept);
//            conceptMap.put(oldId,cconcept.getId());
//        }
//        //meta_onto_relation
//        Map<Long,Long> ontoRelationMap = new HashMap<>();
//        //pid=0
//        QueryWrapper<MetaOntoRelationEntity> pOntoRelationWrapper = new QueryWrapper<>();
//        pOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getSchemaId, entity.getExtId()));
//        pOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getPid, 0));
//        List<MetaOntoRelationEntity> pOntoRelationList = metaOntoRelationMapper.selectList(pOntoRelationWrapper);
//        for (MetaOntoRelationEntity pOntoRelation:pOntoRelationList) {
//            Long oldId = pOntoRelation.getId();
//            pOntoRelation.setId(null);
//            pOntoRelation.setSchemaId(entity.getId());
//            pOntoRelation.setOnto1Id(conceptMap.get(pOntoRelation.getOnto1Id()));
//            pOntoRelation.setOnto2Id(conceptMap.get(pOntoRelation.getOnto2Id()));
//            metaOntoRelationMapper.insert(pOntoRelation);
//            ontoRelationMap.put(oldId,pOntoRelation.getId());
//        }
//        //pid!=0
//        QueryWrapper<MetaOntoRelationEntity> cOntoRelationWrapper = new QueryWrapper<>();
//        cOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getSchemaId, entity.getExtId()));
//        cOntoRelationWrapper.lambda().and(t -> t.ne(MetaOntoRelationEntity::getPid, 0));
//        List<MetaOntoRelationEntity> cOntoRelationList = metaOntoRelationMapper.selectList(cOntoRelationWrapper);
//        for (MetaOntoRelationEntity cOntoRelation:cOntoRelationList) {
//            Long oldId = cOntoRelation.getId();
//            cOntoRelation.setId(null);
//            cOntoRelation.setSchemaId(entity.getId());
//            cOntoRelation.setOnto1Id(conceptMap.get(cOntoRelation.getOnto1Id()));
//            cOntoRelation.setOnto2Id(conceptMap.get(cOntoRelation.getOnto2Id()));
//            cOntoRelation.setPid(ontoRelationMap.get(cOntoRelation.getPid()));
//            metaOntoRelationMapper.insert(cOntoRelation);
//            ontoRelationMap.put(oldId,cOntoRelation.getId());
//        }
//        //mate_attribute
//        Map<Long,Long> mateAttributeMap = new HashMap<>();
//        //pid暂没用
//        QueryWrapper<MateAttributeEntity> mateAttributeWrapper = new QueryWrapper<>();
//        mateAttributeWrapper.lambda().and(t -> t.eq(MateAttributeEntity::getSchemaId, entity.getExtId()));
//        List<MateAttributeEntity> mateAttributeList = mateAttributeMapper.selectList(mateAttributeWrapper);
//        for (MateAttributeEntity mateAttribute:mateAttributeList) {
//            Long oldId = mateAttribute.getId();
//            mateAttribute.setId(null);
//            mateAttribute.setSchemaId(entity.getId());
//            mateAttribute.setOnto(conceptMap.get(mateAttribute.getOnto()));
//            mateAttribute.setPoint(conceptMap.get(mateAttribute.getPoint()));
//            mateAttribute.setRelationId(ontoRelationMap.get(mateAttribute.getRelationId()));
//            mateAttributeMapper.insert(mateAttribute);
//            mateAttributeMap.put(oldId,mateAttribute.getId());
//        }
//        //mate_attribute_value
//        Map<Long,Long> mateAttributeValueMap = new HashMap<>();
//        //pid=0
//        QueryWrapper<MateAttributeValueEntity> pmateAttributeValueWrapper = new QueryWrapper<>();
//        pmateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getSchemaId, entity.getExtId()));
//        pmateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getPid, 0));
//        List<MateAttributeValueEntity> pmateAttributeValueList = mateAttributeValueMapper.selectList(pmateAttributeValueWrapper);
//        for (MateAttributeValueEntity mateAttributeValue:pmateAttributeValueList) {
//            Long oldId = mateAttributeValue.getId();
//            mateAttributeValue.setId(null);
//            mateAttributeValue.setSchemaId(entity.getId());
//            mateAttributeValue.setOntoId(conceptMap.get(mateAttributeValue.getOntoId()));
//            mateAttributeValue.setAttributeId(mateAttributeMap.get(mateAttributeValue.getAttributeId()));
//            mateAttributeValueMapper.insert(mateAttributeValue);
//            mateAttributeValueMap.put(oldId,mateAttributeValue.getId());
//        }
//        //pid!=0
//        QueryWrapper<MateAttributeValueEntity> cmateAttributeValueWrapper = new QueryWrapper<>();
//        cmateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getSchemaId, entity.getExtId()));
//        cmateAttributeValueWrapper.lambda().and(t -> t.ne(MateAttributeValueEntity::getPid, 0));
//        List<MateAttributeValueEntity> cmateAttributeValueList = mateAttributeValueMapper.selectList(cmateAttributeValueWrapper);
//        for (MateAttributeValueEntity mateAttributeValue:cmateAttributeValueList) {
//            Long oldId = mateAttributeValue.getId();
//            mateAttributeValue.setId(null);
//            mateAttributeValue.setSchemaId(entity.getId());
//            mateAttributeValue.setOntoId(conceptMap.get(mateAttributeValue.getOntoId()));
//            mateAttributeValue.setAttributeId(mateAttributeMap.get(mateAttributeValue.getAttributeId()));
//            mateAttributeValue.setPid(mateAttributeValueMap.get(mateAttributeValue.getPid()));
//            mateAttributeValueMapper.insert(mateAttributeValue);
//            mateAttributeValueMap.put(oldId,mateAttributeValue.getId());
//        }
//
//        //mate_relation_attribute
//        Map<Long,Long> mateRelationAttributeMap = new HashMap<>();
//        //pid暂没用
//        QueryWrapper<MateRelationAttributeEntity> mateRelationAttributeWrapper = new QueryWrapper<>();
//        mateRelationAttributeWrapper.lambda().and(t -> t.eq(MateRelationAttributeEntity::getSchemaId, entity.getExtId()));
//        List<MateRelationAttributeEntity> mateRelationAttributeList = mateRelationAttributeMapper.selectList(mateRelationAttributeWrapper);
//        for (MateRelationAttributeEntity mateRelationAttribute:mateRelationAttributeList) {
//            Long oldId = mateRelationAttribute.getId();
//            mateRelationAttribute.setId(null);
//            mateRelationAttribute.setSchemaId(entity.getId());
//            mateRelationAttribute.setOnto(conceptMap.get(mateRelationAttribute.getOnto()));
//            mateRelationAttribute.setPoint(conceptMap.get(mateRelationAttribute.getPoint()));
//            mateRelationAttribute.setRelationId(ontoRelationMap.get(mateRelationAttribute.getRelationId()));
//            mateRelationAttribute.setAttributeId(mateAttributeMap.get(mateRelationAttribute.getAttributeId()));
//            mateRelationAttributeMapper.insert(mateRelationAttribute);
//            mateRelationAttributeMap.put(oldId,mateRelationAttribute.getId());
//        }
//        //mate_Relation_attribute_value
//        Map<Long,Long> mateRelationAttributeValueMap = new HashMap<>();
//        //pid=0
//        QueryWrapper<MateRelationAttributeValueEntity> pmateRelationAttributeValueWrapper = new QueryWrapper<>();
//        pmateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getSchemaId, entity.getExtId()));
//        pmateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getPid, 0));
//        List<MateRelationAttributeValueEntity> pmateRelationAttributeValueList = mateRelationAttributeValueMapper.selectList(pmateRelationAttributeValueWrapper);
//        for (MateRelationAttributeValueEntity mateRelationAttributeValue:pmateRelationAttributeValueList) {
//            Long oldId = mateRelationAttributeValue.getId();
//            mateRelationAttributeValue.setId(null);
//            mateRelationAttributeValue.setRelationId(ontoRelationMap.get(mateRelationAttributeValue.getRelationId()));
//            mateRelationAttributeValue.setSchemaId(entity.getId());
//            mateRelationAttributeValue.setOntoId(conceptMap.get(mateRelationAttributeValue.getOntoId()));
//            mateRelationAttributeValue.setAttributeId(mateRelationAttributeMap.get(mateRelationAttributeValue.getAttributeId()));
//            mateRelationAttributeValueMapper.insert(mateRelationAttributeValue);
//            mateRelationAttributeValueMap.put(oldId,mateRelationAttributeValue.getId());
//        }
//        //pid!=0
//        QueryWrapper<MateRelationAttributeValueEntity> cmateRelationAttributeValueWrapper = new QueryWrapper<>();
//        cmateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getSchemaId, entity.getExtId()));
//        cmateRelationAttributeValueWrapper.lambda().and(t -> t.ne(MateRelationAttributeValueEntity::getPid, 0));
//        List<MateRelationAttributeValueEntity> cmateRelationAttributeValueList = mateRelationAttributeValueMapper.selectList(cmateRelationAttributeValueWrapper);
//        for (MateRelationAttributeValueEntity mateRelationAttributeValue:cmateRelationAttributeValueList) {
//            Long oldId = mateRelationAttributeValue.getId();
//            mateRelationAttributeValue.setId(null);
//            mateRelationAttributeValue.setRelationId(ontoRelationMap.get(mateRelationAttributeValue.getRelationId()));
//            mateRelationAttributeValue.setSchemaId(entity.getId());
//            mateRelationAttributeValue.setOntoId(conceptMap.get(mateRelationAttributeValue.getOntoId()));
//            mateRelationAttributeValue.setAttributeId(mateRelationAttributeMap.get(mateRelationAttributeValue.getAttributeId()));
//            mateRelationAttributeValue.setPid(mateRelationAttributeValueMap.get(mateRelationAttributeValue.getPid()));
//            mateRelationAttributeValueMapper.insert(mateRelationAttributeValue);
////            mateRelationAttributeValueMap.put(oldId,mateRelationAttributeValue.getId());
//        }
//    }

    @Override
    public void schemaReuse(MetaSchemaEntity entity){
        //lib_concept
        Map<Long,Long> conceptMap = new HashMap<>();
        //pid=0
        QueryWrapper<LibConceptEntity> pConceptWrapper = new QueryWrapper<>();
        pConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getSchemaId, entity.getExtId()));
        pConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getPid, 0));
        List<LibConceptEntity> pConceptList = libConceptMapper.selectList(pConceptWrapper);
        List<Long> pConceptIdsOld = new ArrayList<>();
        for (LibConceptEntity pconcept:pConceptList) {
            Long oldId = pconcept.getId();
            pConceptIdsOld.add(oldId);
            pconcept.setId(null);
            pconcept.setSchemaId(entity.getId());
        }
        libConceptService.saveBatch(pConceptList);
        for (int i = 0; i < pConceptList.size(); i++) {
            conceptMap.put(pConceptIdsOld.get(i),pConceptList.get(i).getId());
        }
        //pid！=0,第一级
        QueryWrapper<LibConceptEntity> cConceptWrapper = new QueryWrapper<>();
        cConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getSchemaId, entity.getExtId()));
//        cConceptWrapper.lambda().and(t -> t.ne(LibConceptEntity::getPid, 0));
        cConceptWrapper.lambda().and(t -> t.in(LibConceptEntity::getPid, pConceptIdsOld));
        List<LibConceptEntity> cConceptList = libConceptMapper.selectList(cConceptWrapper);
        List<Long> cConceptIdsOld = new ArrayList<>();
        for (LibConceptEntity cconcept:cConceptList) {
            Long oldId = cconcept.getId();
            cConceptIdsOld.add(oldId);
            cconcept.setId(null);
            cconcept.setSchemaId(entity.getId());
            cconcept.setPid(conceptMap.get(cconcept.getPid()));
        }
        libConceptService.saveBatch(cConceptList);
        for (int i = 0; i < cConceptList.size(); i++) {
            conceptMap.put(cConceptIdsOld.get(i),cConceptList.get(i).getId());
        }
        //pid！=0,第二级
        QueryWrapper<LibConceptEntity> ccConceptWrapper = new QueryWrapper<>();
        ccConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getSchemaId, entity.getExtId()));
        ccConceptWrapper.lambda().and(t -> t.in(LibConceptEntity::getPid, cConceptIdsOld));
        List<LibConceptEntity> ccConceptList = libConceptMapper.selectList(ccConceptWrapper);
        List<Long> ccConceptIdsOld = new ArrayList<>();
        for (LibConceptEntity ccconcept:ccConceptList) {
            Long oldId = ccconcept.getId();
            ccConceptIdsOld.add(oldId);
            ccconcept.setId(null);
            ccconcept.setSchemaId(entity.getId());
            ccconcept.setPid(conceptMap.get(ccconcept.getPid()));
        }
        libConceptService.saveBatch(ccConceptList);
        for (int i = 0; i < ccConceptList.size(); i++) {
            conceptMap.put(ccConceptIdsOld.get(i),ccConceptList.get(i).getId());
        }
        //pid！=0,第三级
        QueryWrapper<LibConceptEntity> c3ConceptWrapper = new QueryWrapper<>();
        c3ConceptWrapper.lambda().and(t -> t.eq(LibConceptEntity::getSchemaId, entity.getExtId()));
        c3ConceptWrapper.lambda().and(t -> t.in(LibConceptEntity::getPid, ccConceptIdsOld));
        List<LibConceptEntity> c3ConceptList = libConceptMapper.selectList(c3ConceptWrapper);
        List<Long> c3ConceptIdsOld = new ArrayList<>();
        for (LibConceptEntity c3concept:c3ConceptList) {
            Long oldId = c3concept.getId();
            c3ConceptIdsOld.add(oldId);
            c3concept.setId(null);
            c3concept.setSchemaId(entity.getId());
            c3concept.setPid(conceptMap.get(c3concept.getPid()));
        }
        libConceptService.saveBatch(c3ConceptList);
        for (int i = 0; i < c3ConceptList.size(); i++) {
            conceptMap.put(c3ConceptIdsOld.get(i),c3ConceptList.get(i).getId());
        }

        //meta_onto_relation
        Map<Long,Long> ontoRelationMap = new HashMap<>();
        //pid=0
        QueryWrapper<MetaOntoRelationEntity> pOntoRelationWrapper = new QueryWrapper<>();
        pOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getSchemaId, entity.getExtId()));
        pOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getPid, 0));
        List<MetaOntoRelationEntity> pOntoRelationList = metaOntoRelationMapper.selectList(pOntoRelationWrapper);
        for (MetaOntoRelationEntity pOntoRelation:pOntoRelationList) {
            Long oldId = pOntoRelation.getId();
            pOntoRelation.setId(null);
            pOntoRelation.setSchemaId(entity.getId());
            pOntoRelation.setOnto1Id(conceptMap.get(pOntoRelation.getOnto1Id()));
            pOntoRelation.setOnto2Id(conceptMap.get(pOntoRelation.getOnto2Id()));
            metaOntoRelationMapper.insert(pOntoRelation);
            ontoRelationMap.put(oldId,pOntoRelation.getId());
        }
        //pid!=0
        QueryWrapper<MetaOntoRelationEntity> cOntoRelationWrapper = new QueryWrapper<>();
        cOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getSchemaId, entity.getExtId()));
        cOntoRelationWrapper.lambda().and(t -> t.ne(MetaOntoRelationEntity::getPid, 0));
        List<MetaOntoRelationEntity> cOntoRelationList = metaOntoRelationMapper.selectList(cOntoRelationWrapper);
        for (MetaOntoRelationEntity cOntoRelation:cOntoRelationList) {
            Long oldId = cOntoRelation.getId();
            cOntoRelation.setId(null);
            cOntoRelation.setSchemaId(entity.getId());
            cOntoRelation.setOnto1Id(conceptMap.get(cOntoRelation.getOnto1Id()));
            cOntoRelation.setOnto2Id(conceptMap.get(cOntoRelation.getOnto2Id()));
            cOntoRelation.setPid(ontoRelationMap.get(cOntoRelation.getPid()));
            metaOntoRelationMapper.insert(cOntoRelation);
            ontoRelationMap.put(oldId,cOntoRelation.getId());
        }

        //mate_attribute
        Map<Long,Long> mateAttributeMap = new HashMap<>();
        //pid暂没用
        QueryWrapper<MateAttributeEntity> mateAttributeWrapper = new QueryWrapper<>();
        mateAttributeWrapper.lambda().and(t -> t.eq(MateAttributeEntity::getSchemaId, entity.getExtId()));
        List<MateAttributeEntity> mateAttributeList = mateAttributeMapper.selectList(mateAttributeWrapper);
        for (MateAttributeEntity mateAttribute:mateAttributeList) {
            Long oldId = mateAttribute.getId();
            mateAttribute.setId(null);
            mateAttribute.setSchemaId(entity.getId());
            mateAttribute.setOnto(conceptMap.get(mateAttribute.getOnto()));
            mateAttribute.setPoint(conceptMap.get(mateAttribute.getPoint()));
            mateAttribute.setRelationId(ontoRelationMap.get(mateAttribute.getRelationId()));
            mateAttributeMapper.insert(mateAttribute);
            mateAttributeMap.put(oldId,mateAttribute.getId());
        }

        //mate_attribute_value
        Map<Long,Long> mateAttributeValueMap = new HashMap<>();
        //pid=0
        QueryWrapper<MateAttributeValueEntity> pmateAttributeValueWrapper = new QueryWrapper<>();
        pmateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getSchemaId, entity.getExtId()));
        pmateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getPid, 0));
        List<MateAttributeValueEntity> pmateAttributeValueList = mateAttributeValueMapper.selectList(pmateAttributeValueWrapper);
        List<Long> pmateAttributeValueIdsOld = new ArrayList<>();
        for (MateAttributeValueEntity mateAttributeValue:pmateAttributeValueList) {
            Long oldId = mateAttributeValue.getId();
            pmateAttributeValueIdsOld.add(oldId);
            mateAttributeValue.setId(null);
            mateAttributeValue.setSchemaId(entity.getId());
            mateAttributeValue.setOntoId(conceptMap.get(mateAttributeValue.getOntoId()));
            mateAttributeValue.setAttributeId(mateAttributeMap.get(mateAttributeValue.getAttributeId()));
        }
        mateAttributeValueService.saveBatch(pmateAttributeValueList);
        for (int i = 0; i < pmateAttributeValueList.size(); i++) {
            mateAttributeValueMap.put(pmateAttributeValueIdsOld.get(i),pmateAttributeValueList.get(i).getId());
        }
        //pid!=0,第一级
        QueryWrapper<MateAttributeValueEntity> c1mateAttributeValueWrapper = new QueryWrapper<>();
        c1mateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getSchemaId, entity.getExtId()));
        c1mateAttributeValueWrapper.lambda().and(t -> t.in(MateAttributeValueEntity::getPid, pmateAttributeValueIdsOld));
        List<MateAttributeValueEntity> c1mateAttributeValueList = mateAttributeValueMapper.selectList(c1mateAttributeValueWrapper);
        List<Long> c1mateAttributeValueIdsOld = new ArrayList<>();
        for (MateAttributeValueEntity mateAttributeValue:c1mateAttributeValueList) {
            Long oldId = mateAttributeValue.getId();
            c1mateAttributeValueIdsOld.add(oldId);
            mateAttributeValue.setId(null);
            mateAttributeValue.setSchemaId(entity.getId());
            mateAttributeValue.setOntoId(conceptMap.get(mateAttributeValue.getOntoId()));
            mateAttributeValue.setAttributeId(mateAttributeMap.get(mateAttributeValue.getAttributeId()));
            mateAttributeValue.setPid(mateAttributeValueMap.get(mateAttributeValue.getPid()));
        }
        mateAttributeValueService.saveBatch(c1mateAttributeValueList);
        for (int i = 0; i < c1mateAttributeValueList.size(); i++) {
            mateAttributeValueMap.put(c1mateAttributeValueIdsOld.get(i),c1mateAttributeValueList.get(i).getId());
        }
        //pid!=0,第二级
        QueryWrapper<MateAttributeValueEntity> c2mateAttributeValueWrapper = new QueryWrapper<>();
        c2mateAttributeValueWrapper.lambda().and(t -> t.eq(MateAttributeValueEntity::getSchemaId, entity.getExtId()));
        c2mateAttributeValueWrapper.lambda().and(t -> t.in(MateAttributeValueEntity::getPid, c1mateAttributeValueIdsOld));
        List<MateAttributeValueEntity> c2mateAttributeValueList = mateAttributeValueMapper.selectList(c2mateAttributeValueWrapper);
        List<Long> c2mateAttributeValueIdsOld = new ArrayList<>();
        for (MateAttributeValueEntity mateAttributeValue:c2mateAttributeValueList) {
            Long oldId = mateAttributeValue.getId();
            c2mateAttributeValueIdsOld.add(oldId);
            mateAttributeValue.setId(null);
            mateAttributeValue.setSchemaId(entity.getId());
            mateAttributeValue.setOntoId(conceptMap.get(mateAttributeValue.getOntoId()));
            mateAttributeValue.setAttributeId(mateAttributeMap.get(mateAttributeValue.getAttributeId()));
            mateAttributeValue.setPid(mateAttributeValueMap.get(mateAttributeValue.getPid()));
        }
        mateAttributeValueService.saveBatch(c2mateAttributeValueList);
        for (int i = 0; i < c2mateAttributeValueList.size(); i++) {
            mateAttributeValueMap.put(c2mateAttributeValueIdsOld.get(i),c2mateAttributeValueList.get(i).getId());
        }

        //mate_relation_attribute
        Map<Long,Long> mateRelationAttributeMap = new HashMap<>();
        //pid暂没用
        QueryWrapper<MateRelationAttributeEntity> mateRelationAttributeWrapper = new QueryWrapper<>();
        mateRelationAttributeWrapper.lambda().and(t -> t.eq(MateRelationAttributeEntity::getSchemaId, entity.getExtId()));
        List<MateRelationAttributeEntity> mateRelationAttributeList = mateRelationAttributeMapper.selectList(mateRelationAttributeWrapper);
        for (MateRelationAttributeEntity mateRelationAttribute:mateRelationAttributeList) {
            Long oldId = mateRelationAttribute.getId();
            mateRelationAttribute.setId(null);
            mateRelationAttribute.setSchemaId(entity.getId());
            mateRelationAttribute.setOnto(conceptMap.get(mateRelationAttribute.getOnto()));
            mateRelationAttribute.setPoint(conceptMap.get(mateRelationAttribute.getPoint()));
            mateRelationAttribute.setRelationId(ontoRelationMap.get(mateRelationAttribute.getRelationId()));
            mateRelationAttribute.setAttributeId(mateAttributeMap.get(mateRelationAttribute.getAttributeId()));
            mateRelationAttributeMapper.insert(mateRelationAttribute);
            mateRelationAttributeMap.put(oldId,mateRelationAttribute.getId());
        }

        //mate_Relation_attribute_value
        Map<Long,Long> mateRelationAttributeValueMap = new HashMap<>();
        //pid=0
        QueryWrapper<MateRelationAttributeValueEntity> pmateRelationAttributeValueWrapper = new QueryWrapper<>();
        pmateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getSchemaId, entity.getExtId()));
        pmateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getPid, 0));
        List<MateRelationAttributeValueEntity> pmateRelationAttributeValueList = mateRelationAttributeValueMapper.selectList(pmateRelationAttributeValueWrapper);
        List<Long> pmateRelationAttributeValueIdsOld = new ArrayList<>();
        for (MateRelationAttributeValueEntity mateRelationAttributeValue:pmateRelationAttributeValueList) {
            Long oldId = mateRelationAttributeValue.getId();
            pmateRelationAttributeValueIdsOld.add(oldId);
            mateRelationAttributeValue.setId(null);
            mateRelationAttributeValue.setRelationId(ontoRelationMap.get(mateRelationAttributeValue.getRelationId()));
            mateRelationAttributeValue.setSchemaId(entity.getId());
            mateRelationAttributeValue.setOntoId(conceptMap.get(mateRelationAttributeValue.getOntoId()));
            mateRelationAttributeValue.setAttributeId(mateRelationAttributeMap.get(mateRelationAttributeValue.getAttributeId()));
            mateRelationAttributeValueMap.put(oldId,mateRelationAttributeValue.getId());
        }
        mateRelationAttributeValueService.saveBatch(pmateRelationAttributeValueList);
        for (int i = 0; i < pmateRelationAttributeValueList.size(); i++) {
            mateRelationAttributeValueMap.put(pmateRelationAttributeValueIdsOld.get(i),pmateRelationAttributeValueList.get(i).getId());
        }
        //pid!=0第一级
        QueryWrapper<MateRelationAttributeValueEntity> c1mateRelationAttributeValueWrapper = new QueryWrapper<>();
        c1mateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getSchemaId, entity.getExtId()));
        c1mateRelationAttributeValueWrapper.lambda().and(t -> t.in(MateRelationAttributeValueEntity::getPid, pmateRelationAttributeValueIdsOld));
        List<MateRelationAttributeValueEntity> c1mateRelationAttributeValueList = mateRelationAttributeValueMapper.selectList(c1mateRelationAttributeValueWrapper);
        List<Long> c1mateRelationAttributeValueIdsOld = new ArrayList<>();
        for (MateRelationAttributeValueEntity mateRelationAttributeValue:c1mateRelationAttributeValueList) {
            Long oldId = mateRelationAttributeValue.getId();
            c1mateRelationAttributeValueIdsOld.add(oldId);
            mateRelationAttributeValue.setId(null);
            mateRelationAttributeValue.setRelationId(ontoRelationMap.get(mateRelationAttributeValue.getRelationId()));
            mateRelationAttributeValue.setSchemaId(entity.getId());
            mateRelationAttributeValue.setOntoId(conceptMap.get(mateRelationAttributeValue.getOntoId()));
            mateRelationAttributeValue.setAttributeId(mateRelationAttributeMap.get(mateRelationAttributeValue.getAttributeId()));
            mateRelationAttributeValue.setPid(mateRelationAttributeValueMap.get(mateRelationAttributeValue.getPid()));
        }
        mateRelationAttributeValueService.saveBatch(c1mateRelationAttributeValueList);
        for (int i = 0; i < c1mateRelationAttributeValueList.size(); i++) {
            mateRelationAttributeValueMap.put(c1mateRelationAttributeValueIdsOld.get(i),c1mateRelationAttributeValueList.get(i).getId());
        }
        //pid!=0第二级
        QueryWrapper<MateRelationAttributeValueEntity> c2mateRelationAttributeValueWrapper = new QueryWrapper<>();
        c2mateRelationAttributeValueWrapper.lambda().and(t -> t.eq(MateRelationAttributeValueEntity::getSchemaId, entity.getExtId()));
        c2mateRelationAttributeValueWrapper.lambda().and(t -> t.in(MateRelationAttributeValueEntity::getPid, c1mateRelationAttributeValueIdsOld));
        List<MateRelationAttributeValueEntity> c2mateRelationAttributeValueList = mateRelationAttributeValueMapper.selectList(c2mateRelationAttributeValueWrapper);
        List<Long> c2mateRelationAttributeValueIdsOld = new ArrayList<>();
        for (MateRelationAttributeValueEntity mateRelationAttributeValue:c2mateRelationAttributeValueList) {
            Long oldId = mateRelationAttributeValue.getId();
            c2mateRelationAttributeValueIdsOld.add(oldId);
            mateRelationAttributeValue.setId(null);
            mateRelationAttributeValue.setRelationId(ontoRelationMap.get(mateRelationAttributeValue.getRelationId()));
            mateRelationAttributeValue.setSchemaId(entity.getId());
            mateRelationAttributeValue.setOntoId(conceptMap.get(mateRelationAttributeValue.getOntoId()));
            mateRelationAttributeValue.setAttributeId(mateRelationAttributeMap.get(mateRelationAttributeValue.getAttributeId()));
            mateRelationAttributeValue.setPid(mateRelationAttributeValueMap.get(mateRelationAttributeValue.getPid()));
        }
        mateRelationAttributeValueService.saveBatch(c2mateRelationAttributeValueList);
    }

    @Override
    public String buildExcelDocument(Long ontoId)
            throws Exception {

        XSSFSheet sheet;
        XSSFCell cell = null;
        XSSFWorkbook workbook = null;

        //生成excel文件
        String uploaderPath = projectConfig.getDownPath();
        String fileName = ontoId + ".xlsx";
        FileUtil.createDirs(uploaderPath);
        File file = new File(uploaderPath + File.separator + fileName);
        OutputStream out = new FileOutputStream(file);

        //获取属性
        QueryWrapper<MateAttributeEntity> mateAttributeWrapper = new QueryWrapper<>();
        mateAttributeWrapper.select("name_cn");
        mateAttributeWrapper.lambda().and(t -> t.eq(MateAttributeEntity::getOnto,ontoId));
        mateAttributeWrapper.lambda().and(t -> t.ne(MateAttributeEntity::getValType,4));
        mateAttributeWrapper.lambda().and(t -> t.ne(MateAttributeEntity::getType,2));
        List<MateAttributeEntity> mateAttributeList = mateAttributeMapper.selectList(mateAttributeWrapper);
        if (mateAttributeList.size() == 0){return null;}
        try {
            workbook = new XSSFWorkbook();
            sheet = workbook.createSheet("sheet1");

            List<String> titles = new ArrayList<>();
            titles.add("实体名称（必填）");
            titles.add("消歧标识");
            titles.add("置信度");
            for (MateAttributeEntity mateAttribute:mateAttributeList) {
                titles.add(mateAttribute.getNameCn());
            }
            // 标题样式
            XSSFCellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            XSSFFont headerFont = workbook.createFont(); // 标题字体
            headerFont.setBold(false);
            headerFont.setFontHeightInPoints((short) 11);
            headerStyle.setFont(headerFont);
            short width = 20, height = 25 * 20;
            sheet.setDefaultColumnWidth(width);
            XSSFRow row = sheet.createRow(0);
            sheet.getRow(0).setHeight(height);
            // 设置标题
            for (int i = 0; i < titles.size(); i++) {
                String title = titles.get(i);
                cell = row.createCell(i);
                cell.setCellStyle(headerStyle);
                cell.setCellValue(title);
            }
            workbook.write(out);
            return fileName;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            out.close();
            workbook.close();
        }
    }


    @Override
    @Async
    public void dealExcelFileData(String filePath, Long ontoId, FileImportJobEntity fileImportJob) {
        boolean result = true;
        // 定义存放数据的集合
        List<List<String>> list = new ArrayList<>();
        // 创建Workbook工作薄对象
        Workbook workbook = null;
        try {
            // 获取文件
            File file = new File(filePath);
            // 获取文件名
            String fileName = file.getName();
            // 获取文件io流
            InputStream is = new FileInputStream(file);
            // 根据文件后缀名不同(xls和xlsx)获得不同的Workbook实现类对象
            if (fileName.endsWith(".xls")) {
                // 2003
                workbook = new HSSFWorkbook(is);
            } else if (fileName.endsWith(".xlsx")) {
                // 2007
                workbook = new XSSFWorkbook(is);
            }
            // 获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            Row headrow = sheet.getRow(1);
            // 获得当前sheet的结束行
            int lastRowNum = sheet.getLastRowNum();
            // 根据行头确定每一行的列数，这里规定了行头的列数 = 数据的列数
            int lastCellNum = headrow.getLastCellNum();

            // 循环除了第一行的所有行
            for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
                // 获得当前行
                Row row = sheet.getRow(rowNum);
                List<String> dataList = new ArrayList<>();
                // 循环当前行
                for (int cellNum = 0; cellNum < lastCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    String cellVal = FileUtil.getCellString(cell).toString().trim();
                    // 校验转换后的cellVal
                    if ("error".equals(cellVal)) {
                        //报错
                        FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                        fileImportLogEntity.setJobType(fileImportJob.getJobType());
                        fileImportLogEntity.setJobId(fileImportJob.getId());
                        fileImportLogEntity.setErrorLog("第"+(cellNum+1)+"列，中存在不识别内容");
                        fileImportLogEntity.setRowNum(rowNum+1);
                        fileImportLogService.create(fileImportLogEntity);
                        continue;
                    }
                    dataList.add(cellVal);
                }
                if (dataList.size() > 0) {
                    list.add(dataList);
                }
            }
            if (fileImportJob.getJobType() == 1){
                dealInsertEntityData(list,ontoId,fileImportJob);
            }
            if (fileImportJob.getJobType() == 2){
                dealInsertEntityRelationData(list,fileImportJob);
            }
            if (fileImportJob.getJobType() == 3){
                dealInsertEntitySynonymData(list,fileImportJob);
            }
        } catch (IOException e) {
            System.out.println("data"+ "系统异常，请刷新页面重新请求！");
            e.printStackTrace();
            result = false;
        }
        fileImportJob.setUpdateTime(new Date());
        if (result){
            fileImportJob.setJobState(2);
        }else {
            fileImportJob.setJobState(3);
        }
        fileImportJobService.update(fileImportJob.getId(),fileImportJob);
    }

    /*
    处理插入实体
     */
    public void dealInsertEntityData(List<List<String>> list,Long ontoId,FileImportJobEntity fileImportJob){
        //获取属性
        QueryWrapper<MateAttributeEntity> mateAttributeWrapper = new QueryWrapper<>();
        mateAttributeWrapper.select("name_cn","name_en","must");
        mateAttributeWrapper.lambda().and(t -> t.eq(MateAttributeEntity::getOnto,ontoId));
        mateAttributeWrapper.lambda().and(t -> t.ne(MateAttributeEntity::getValType,4));
        mateAttributeWrapper.lambda().and(t -> t.ne(MateAttributeEntity::getType,2));
        List<MateAttributeEntity> mateAttributeList = mateAttributeMapper.selectList(mateAttributeWrapper);
        if (mateAttributeList.size() == 0){return;}
        //循环插入excel数据
        for (int i = 0; i < list.size(); i++) {
            boolean result = true;
            List<String> excelData = list.get(i);
            if (excelData.get(0) == null || excelData.get(0).isEmpty()) {
                //报错
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("实体名称为空");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            for (int j = 0; j < mateAttributeList.size(); j++) {
                if (mateAttributeList.get(j).getMust() == 1){
                    if (excelData.get(j+3) == null || excelData.get(j+3).isEmpty()){
                        //报错
                        FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                        fileImportLogEntity.setJobType(fileImportJob.getJobType());
                        fileImportLogEntity.setJobId(fileImportJob.getId());
                        fileImportLogEntity.setName(excelData.get(0));
                        fileImportLogEntity.setErrorLog(mateAttributeList.get(j).getNameCn() + "为空");
                        fileImportLogEntity.setRowNum(i+2);
                        fileImportLogService.create(fileImportLogEntity);
                        result = false;
                    }
                }
            }
            if (!result){
                continue;
            }
            LibEntityEntity libEntity = new LibEntityEntity();
            libEntity.setName(excelData.get(0));
            libEntity.setDisambiguation(excelData.get(1));
            libEntity.setOntoId(ontoId);
            JSONObject dataJson = new JSONObject();
            for (int j = 0; j < mateAttributeList.size(); j++) {
                dataJson.set(mateAttributeList.get(j).getNameEn(),excelData.get(j+3));
            }
            libEntity.setData(dataJson.toString());
            libEntityMapper.insert(libEntity);
        }
    }

    /*
    处理插入实体关系
     */
    public void dealInsertEntityRelationData(List<List<String>> list,FileImportJobEntity fileImportJob){

        //循环插入excel数据
        for (int i = 0; i < list.size(); i++) {
            boolean result = true;
            List<String> excelData = list.get(i);
            //判断必填项
            if (excelData.get(0) == null || excelData.get(0).isEmpty()
                    || excelData.get(2) == null || excelData.get(2).isEmpty()
                    || excelData.get(4) == null || excelData.get(4).isEmpty()
                    || excelData.get(5) == null || excelData.get(5).isEmpty()
                    || excelData.get(7) == null || excelData.get(7).isEmpty()) {
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                if (excelData.get(0) == null || excelData.get(0).isEmpty()) {
                    fileImportLogEntity.setErrorLog("定义域实体名称为空");
                }
                if (excelData.get(2) == null || excelData.get(2).isEmpty()) {
                    fileImportLogEntity.setErrorLog("定义域实体名称为空");
                }
                if (excelData.get(4) == null || excelData.get(4).isEmpty()) {
                    fileImportLogEntity.setErrorLog("关系名称为空");
                }
                if (excelData.get(5) == null || excelData.get(5).isEmpty()) {
                    fileImportLogEntity.setErrorLog("值域实体名称为空");
                }
                if (excelData.get(7) == null || excelData.get(7).isEmpty()) {
                    fileImportLogEntity.setErrorLog("关系值域为空");
                }
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            //左本体
            QueryWrapper<LibConceptEntity> libConceptWrapper1 = new QueryWrapper<>();
            libConceptWrapper1.select("id");
            libConceptWrapper1.lambda().and(t -> t.eq(LibConceptEntity::getName,excelData.get(0)));
            List<LibConceptEntity> libConcept1List = libConceptMapper.selectList(libConceptWrapper1);
            if (libConcept1List.size() == 0){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("左本体搜索不到");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            long libConceptId1 = libConcept1List.get(0).getId();
            //左实体
            QueryWrapper<LibEntityEntity> libEntityWrapper1 = new QueryWrapper<>();
            libEntityWrapper1.select("id");
            libEntityWrapper1.lambda().and(t -> t.eq(LibEntityEntity::getName,excelData.get(2)));
            libEntityWrapper1.lambda().and(t -> t.eq(LibEntityEntity::getOntoId,libConceptId1));
            List<LibEntityEntity> libEntity1List = libEntityMapper.selectList(libEntityWrapper1);
            if (libEntity1List.size() == 0){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("左实体搜索不到");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            long libEntity1Id = libEntity1List.get(0).getId();

            //右本体
            QueryWrapper<LibConceptEntity> libConceptWrapper2 = new QueryWrapper<>();
            libConceptWrapper2.select("id");
            libConceptWrapper2.lambda().and(t -> t.eq(LibConceptEntity::getName,excelData.get(7)));
            List<LibConceptEntity> libConcept2List = libConceptMapper.selectList(libConceptWrapper2);
            if (libConcept2List.size() == 0){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("右本体搜索不到");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            long libConceptId2 = libConcept2List.get(0).getId();

            //右实体
            QueryWrapper<LibEntityEntity> libEntityWrapper2 = new QueryWrapper<>();
            libEntityWrapper1.select("id");
            libEntityWrapper1.lambda().and(t -> t.eq(LibEntityEntity::getName,excelData.get(5)));
            libEntityWrapper1.lambda().and(t -> t.eq(LibEntityEntity::getOntoId,libConceptId2));
            List<LibEntityEntity> libEntity2List = libEntityMapper.selectList(libEntityWrapper2);
            if (libEntity2List.size() == 0){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("左实体搜索不到");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            long libEntity2Id = libEntity2List.get(0).getId();

            //关系名称
            QueryWrapper<MetaOntoRelationEntity> metaOntoRelationWrapper = new QueryWrapper<>();
            metaOntoRelationWrapper.select("id");
            metaOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getOnto1Id,libConceptId1));
            metaOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getOnto2Id,libConceptId2));
            metaOntoRelationWrapper.lambda().and(t -> t.eq(MetaOntoRelationEntity::getName,excelData.get(4)));
            List<MetaOntoRelationEntity> metaOntoRelationList = metaOntoRelationMapper.selectList(metaOntoRelationWrapper);
            if (metaOntoRelationList.size() == 0){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("本体关系搜索不到");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            long ontoRelId = metaOntoRelationList.get(0).getId();
            MetaEntityRelationEntity metaEntityRelationEntity = new MetaEntityRelationEntity();
            metaEntityRelationEntity.setOnto1Id(libConceptId1);
            metaEntityRelationEntity.setOnto2Id(libConceptId2);
            metaEntityRelationEntity.setName(excelData.get(4));
            metaEntityRelationEntity.setEntity1Id(libEntity1Id);
            metaEntityRelationEntity.setEntity2Id(libEntity2Id);
            metaEntityRelationEntity.setOntoRelId(ontoRelId);
            metaEntityRelationMapper.insert(metaEntityRelationEntity);
        }
    }

    /*
    处理插入实体同义词
     */
    public void dealInsertEntitySynonymData(List<List<String>> list,FileImportJobEntity fileImportJob){

        //循环插入excel数据
        for (int i = 0; i < list.size(); i++) {
            boolean result = true;
            List<String> excelData = list.get(i);
            //判断必填项
            if (excelData.get(0) == null || excelData.get(0).isEmpty()
                    || excelData.get(2) == null || excelData.get(2).isEmpty()) {
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                if (excelData.get(0) == null || excelData.get(0).isEmpty()) {
                    fileImportLogEntity.setErrorLog("实体名称为空");
                }
                if (excelData.get(2) == null || excelData.get(2).isEmpty()) {
                    fileImportLogEntity.setErrorLog("同义词为空");
                }
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            //查询实体
            QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
            libEntityWrapper.eq("name",excelData.get(0));
            if (excelData.get(1) != null || !excelData.get(1).isEmpty()){
                libEntityWrapper.eq("disambiguation",excelData.get(1));
            }
            List<LibEntityEntity> libEntityList = libEntityMapper.selectList(libEntityWrapper);
            if (libEntityList.size() == 0){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("查询不到实体");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            if (libEntityList.size() > 1){
                FileImportLogEntity fileImportLogEntity = new FileImportLogEntity();
                fileImportLogEntity.setJobType(fileImportJob.getJobType());
                fileImportLogEntity.setJobId(fileImportJob.getId());
                fileImportLogEntity.setErrorLog("查询到多个实体");
                fileImportLogEntity.setRowNum(i+2);
                fileImportLogService.create(fileImportLogEntity);
                continue;
            }
            LibEntityEntity libEntity = libEntityList.get(0);
            libEntity.setSynonymWord(excelData.get(2));
            libEntityMapper.updateById(libEntity);
        }
    }

    @Override
    public List<FileImportLogExcel> fileImportLogExport(long id) {
        LambdaQueryWrapper<FileImportLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileImportLogEntity::getJobId, id);
        List<FileImportLogEntity> list = fileImportLogService.list(queryWrapper);
        return BeanUtil.copyToList(list, FileImportLogExcel.class);
    }
}