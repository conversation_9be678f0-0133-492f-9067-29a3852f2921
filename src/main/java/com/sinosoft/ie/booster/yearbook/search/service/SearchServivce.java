package com.sinosoft.ie.booster.yearbook.search.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.search.model.EsEntity;
import com.sinosoft.ie.booster.yearbook.search.model.EsEvent;
import com.sinosoft.ie.booster.yearbook.search.model.EsQueryPagination;

import cn.hutool.json.JSONArray;

public interface SearchServivce {
	
	
	public  R  search(String str) throws IOException;
	
	public  List<EsEvent>  queryEsEvent(EsQueryPagination query,String weight);
	
	public  Map<String,Object>    queryEsPersion(EsQueryPagination query) throws IOException;
	
	public  Map<String,Object>   queryEsOrg(EsQueryPagination query) throws IOException;
	
	public  com.alibaba.fastjson.JSONArray  queryEsEntity(EsQueryPagination query) throws IOException;
	//查询图片
	public R searchGraph(Long id);
	//搜索实体的事件脉络
	public R  searchEntityContext(Long id);
	
	public  R getEntityDetail(Long id);
	
	public R getRelationObj(String str);
	
	
	public List queryEsOrgNameList() throws IOException;
	
	public Map queryEsEventYearAndOrg(EsQueryPagination query);

}
