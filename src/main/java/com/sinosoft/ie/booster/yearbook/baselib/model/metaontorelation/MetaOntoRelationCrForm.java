package com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * MetaOntoRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:37:39
 */
@Data
@ApiModel
public class MetaOntoRelationCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 本体1的id
     */
    @NotNull
    @ApiModelProperty(value = "本体1的id")
    private Long onto1Id;

    /**
     * 本体2的id
     */
    @NotNull
    @ApiModelProperty(value = "本体2的id")
    private Long onto2Id;

    /**
     * 关系名称
     */
    @NotEmpty
    @Length(max = 200)
    @ApiModelProperty(value = "关系名称")
    private String name;

    /**
     * 关系的父id
     */
    @ApiModelProperty(value = "关系的父id")
    private Long pid;
    
    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;
}