package com.sinosoft.ie.booster.yearbook.baselib.constant;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class YearbookLibConstant {

	//默认模式id
	public static Long SCHEMA_ID = 1L;
	//人员本体id
	public static Long PERSON_ONTO_ID = 1586970280968970242L;
	//人员类别字段
	public static String PERSON_CATEGORY_NAME = "category_id";
	//人员姓名字段
	public static String PERSON_NAME_NAME = "name";
	//人员职务字段
	public static String PERSON_JOB_NAME = "job_id";
	//人员图片字段
	public static String PERSON_IMAGE_NAME = "image_ids";
	//人员类别属性值
	public static List<String> PERSON_CATEGORY_VALUE = new ArrayList<>();
	static {
		PERSON_CATEGORY_VALUE.add("党委常委");
//		PERSON_CATEGORY_VALUE.add("军级单位主官");
//		PERSON_CATEGORY_VALUE.add("专业技术三级以上变更");

		PERSON_CATEGORY_VALUE.add("两院院士");
		PERSON_CATEGORY_VALUE.add("先进典型");
	}
	public static List<Long> PERSON_CATEGORY_ID = new ArrayList<>();
	static {
		PERSON_CATEGORY_ID.add(1608372117953327105L);
//		PERSON_CATEGORY_ID.add(1608372670859063297L);
//		PERSON_CATEGORY_ID.add(1608372923402301442L);
		PERSON_CATEGORY_ID.add(1608373530565554178L);
		PERSON_CATEGORY_ID.add(1608373101731524610L);

	}
}
