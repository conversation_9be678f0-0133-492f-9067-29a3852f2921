package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.plugin.excel.vo.ErrorMessage;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.baselib.entity.DomainThesaurusEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.DomainThesaurusMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.DomainThesaurusExcel;
import com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.DomainThesaurusPagination;
import com.sinosoft.ie.booster.yearbook.baselib.service.DomainThesaurusService;
import com.sinosoft.ie.booster.yearbook.constant.enums.GlobalEnum;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * domain_thesaurus
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-14 15:44:58
 */
@Service
public class DomainThesaurusServiceImpl extends ServiceImpl<DomainThesaurusMapper, DomainThesaurusEntity> implements DomainThesaurusService {

    @Resource
    private DomainThesaurusMapper domainThesaurusMapper;

    @Override
    public List<DomainThesaurusEntity> getList(DomainThesaurusPagination domainThesaurusPagination){
        QueryWrapper<DomainThesaurusEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(domainThesaurusPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getId,domainThesaurusPagination.getId()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getName,domainThesaurusPagination.getName()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getCategory()))){
            queryWrapper.lambda().and(t->t.eq(DomainThesaurusEntity::getCategory,domainThesaurusPagination.getCategory()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getPos()))){
            queryWrapper.lambda().and(t->t.eq(DomainThesaurusEntity::getPos,domainThesaurusPagination.getPos()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getSynonym()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getSynonym,domainThesaurusPagination.getSynonym()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getStatus()))){
            queryWrapper.lambda().and(t->t.eq(DomainThesaurusEntity::getStatus,domainThesaurusPagination.getStatus()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getCreateBy,domainThesaurusPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getCreateTime()))){
            queryWrapper.lambda().eq(DomainThesaurusEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(domainThesaurusPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getUpdateBy,domainThesaurusPagination.getUpdateBy()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(DomainThesaurusEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(domainThesaurusPagination.getUpdateTime())));
        }

        queryWrapper.lambda().eq(DomainThesaurusEntity::getDelFlag, GlobalEnum.isDeleteType.NO.getCode());
        //排序
        if(StrUtil.isEmpty(domainThesaurusPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(DomainThesaurusEntity::getId);
        }else{
            queryWrapper="asc".equals(domainThesaurusPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(domainThesaurusPagination.getSidx()):queryWrapper.orderByDesc(domainThesaurusPagination.getSidx());
        }
        Page<DomainThesaurusEntity> page=new Page<>(domainThesaurusPagination.getCurrentPage(), domainThesaurusPagination.getPageSize());
        IPage<DomainThesaurusEntity> userIPage=this.page(page,queryWrapper);
        return domainThesaurusPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<DomainThesaurusEntity> getTypeList(DomainThesaurusPagination domainThesaurusPagination,String dataType){
        QueryWrapper<DomainThesaurusEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(domainThesaurusPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getId,domainThesaurusPagination.getId()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getName,domainThesaurusPagination.getName()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getCategory()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getCategory,domainThesaurusPagination.getCategory()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getPos()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getPos,domainThesaurusPagination.getPos()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getSynonym()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getSynonym,domainThesaurusPagination.getSynonym()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getStatus()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getStatus,domainThesaurusPagination.getStatus()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getDelFlag,domainThesaurusPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getCreateBy,domainThesaurusPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getCreateTime()))){
            queryWrapper.lambda().eq(DomainThesaurusEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(domainThesaurusPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(DomainThesaurusEntity::getUpdateBy,domainThesaurusPagination.getUpdateBy()));
        }

        if(!"null".equals(String.valueOf(domainThesaurusPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(DomainThesaurusEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(domainThesaurusPagination.getUpdateTime())));
        }

        //排序
        if(StrUtil.isEmpty(domainThesaurusPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(DomainThesaurusEntity::getId);
        }else{
            queryWrapper="asc".equals(domainThesaurusPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(domainThesaurusPagination.getSidx()):queryWrapper.orderByDesc(domainThesaurusPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<DomainThesaurusEntity> page=new Page<>(domainThesaurusPagination.getCurrentPage(), domainThesaurusPagination.getPageSize());
            IPage<DomainThesaurusEntity> userIPage=this.page(page,queryWrapper);
            return domainThesaurusPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public DomainThesaurusEntity getInfo(Long id){
        QueryWrapper<DomainThesaurusEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(DomainThesaurusEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DomainThesaurusEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, DomainThesaurusEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(DomainThesaurusEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }

    @Override
    public R<List<ErrorMessage>> importDate(List<DomainThesaurusExcel> excelList, BindingResult bindingResult) {
        // 通用校验获取失败的数据
        List<ErrorMessage> errorMessageList = (List<ErrorMessage>) bindingResult.getTarget();
        List<DomainThesaurusEntity> saveList = CollectionUtil.newArrayList();

        for (int i = 0; i < excelList.size(); i++) {
            DomainThesaurusExcel excel = excelList.get(i);
            Set<String> errorMsg = new HashSet<>();
            // 校验
            if (StrUtil.isEmpty(excel.getName())) {
                errorMsg.add("词汇名称不能为空");
            }
            if (StrUtil.isEmpty(excel.getCategory())) {
                errorMsg.add("类别不能为空");
            }
            if (StrUtil.isEmpty(excel.getPos())) {
                errorMsg.add("词性不能为空");
            }
            // 数据合法情况
            if (CollectionUtil.isEmpty(errorMsg)) {
                DomainThesaurusEntity entity = new DomainThesaurusEntity();
                entity.setName(excel.getName());
                entity.setCategory(excel.getCategory());
                entity.setPos(excel.getPos());
                entity.setSynonym(excel.getSynonym());
                saveList.add(entity);
            } else {
                // 数据不合法情况
                assert errorMessageList != null;
                errorMessageList.add(new ErrorMessage((long) (i + 2), errorMsg));
            }
        }
        if (CollectionUtil.isNotEmpty(errorMessageList)) {
            return R.failed(errorMessageList);
        }
        this.saveBatch(saveList);
        return R.ok();
    }

    @Override
    public List<DomainThesaurusExcel> getExportList(String category,String name) {
        LambdaQueryWrapper<DomainThesaurusEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(category)) {
            queryWrapper.eq(DomainThesaurusEntity::getCategory, category);
        }
        if (StrUtil.isNotEmpty(name)) {
            queryWrapper.eq(DomainThesaurusEntity::getCategory, name);
        }
        queryWrapper.eq(DomainThesaurusEntity::getDelFlag, GlobalEnum.isDeleteType.NO.getCode());
        List<DomainThesaurusEntity> list = domainThesaurusMapper.selectList(queryWrapper);
//        return BeanUtil.copyToList(list, DomainThesaurusExcel.class, CopyOptions.create().setIgnoreNullValue(true));
        return BeanUtil.copyToList(list, DomainThesaurusExcel.class);
    }


}