package com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * DomainThesaurus模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-14 15:44:58
 */
@Data
@ApiModel(value = "DomainThesaurus模型")
public class DomainThesaurusUpForm extends DomainThesaurusCrForm {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
}