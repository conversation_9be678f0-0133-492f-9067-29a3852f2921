package com.sinosoft.ie.booster.yearbook.business.entity;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.sinosoft.ie.booster.common.core.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:08:32
 */
@Data
@TableName("organization")
@ApiModel(value = "")
public class OrganizationEntity  extends BaseEntity {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String name;

    /**
     * 机构级别
     */
    @ApiModelProperty(value = "机构级别")
    private Integer zonelevel;

    /**
     * 父机构id
     */
    @ApiModelProperty(value = "父机构id")
    private Long pid;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    /**
     * 图片路径
     */
    @ApiModelProperty(value = "图片路径")
    private String pic;

    /**
     * 备注
     */
    @JsonRawValue
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;

	@TableField(exist = false)
	private List<OrganizationEntity> sonList;
	
	
	private Integer rotationPage;
	
	private Integer tagPage;
}
