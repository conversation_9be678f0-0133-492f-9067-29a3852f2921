package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EnableAsync
@Service
public class UpdateEntityForAttrService  {
	
	
	@Resource
	private MateAttributeService mateAttributeService;
    @Autowired
    private LibConceptService libConceptService;
    @Autowired
    private LibEntityService libEntityService;
    
	@Async("taskExecutor")
	@Transactional
	public void   UpdateEntityForAttr(Long id ,Integer vals) {
		
		MateAttributeEntity attrEntity = mateAttributeService.getById((id));
		if(attrEntity.getVals()==vals) {
			return;
		}
		//修改属性
		//由单值改为多值同时修改数据库的该属性值为数组
		if(vals==2) {
			QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
			query.eq("onto_id", attrEntity.getOnto());
			List<LibEntityEntity> list = libEntityService.list(query);
			for (int i = 0; i < list.size(); i++) {
				LibEntityEntity entity = list.get(i);
				Map<String, Object> stringObjectMap = JsonUtil.stringToMap(entity.getData());
				Object obj = stringObjectMap.get(attrEntity.getNameEn());
				if(!(obj instanceof Collection)) {
					JSONArray arry =  JSONUtil.createArray();
					arry.add(obj);
					stringObjectMap.put(attrEntity.getNameEn(), arry);
				}
				entity.setData(String.valueOf(new JSONObject(stringObjectMap)));
				libEntityService.updateById(entity);
			}
		}
	}

}
