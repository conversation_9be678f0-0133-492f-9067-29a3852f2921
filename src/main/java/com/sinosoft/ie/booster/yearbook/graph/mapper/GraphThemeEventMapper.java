package com.sinosoft.ie.booster.yearbook.graph.mapper;

import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 *
 * graph_theme_event
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 16:14:57
 */
@Mapper
public interface GraphThemeEventMapper extends BaseMapper<GraphThemeEventEntity> {

    @MapKey("")
    List<Map<String,Object>> themeGraphRelationTime(String theme);
    @MapKey("")
    List<Map<String,Object>> themeGraphRelationTimeChart(String theme);

    @MapKey("")
    List<Map<String,Object>> themeGraphRelation(Map map);

    @MapKey("")
    List<Map<String,Object>> themeGraphRelationTimeChartNew(Map map);
}
