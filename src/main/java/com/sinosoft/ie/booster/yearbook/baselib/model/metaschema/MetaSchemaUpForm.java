package com.sinosoft.ie.booster.yearbook.baselib.model.metaschema;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MetaSchema模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-02 15:36:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MetaSchemaUpForm extends MetaSchemaCrForm{

}