package com.sinosoft.ie.booster.yearbook.business.service;

import java.io.IOException;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;

public interface ElectronicReadService {
	
	
	R generateHighlight(EventPagination eventPagination) throws IOException;
	
	R highlightContext(Integer year, Integer pageNumber,String name) throws IOException;
	
	R highlightContext(Long id ,String name) throws IOException;
	
	R esTohighlight(Integer year, Integer pageNumber,Long  entity_id)throws IOException;

}
