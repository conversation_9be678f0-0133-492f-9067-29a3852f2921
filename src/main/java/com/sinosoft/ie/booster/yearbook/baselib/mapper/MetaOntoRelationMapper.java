package com.sinosoft.ie.booster.yearbook.baselib.mapper;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 *
 * meta_onto_relation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-31 16:37:39
 */
@Mapper
public interface MetaOntoRelationMapper extends BaseMapper<MetaOntoRelationEntity> {
	
	@Select(value = "        select t.*,c1.name as ontoName1 ,c2.name as ontoName2 from meta_onto_relation t "
			+ "		left join lib_concept c1 on t.onto1_id =  c1.id  "
			+ "		left join lib_concept c2 on t.onto2_id =  c2.id ${ew.customSqlSegment}")
	List<MetaOntoRelationInfoVO>   ontolist(IPage page,@Param(Constants.WRAPPER) Wrapper<MetaOntoRelationEntity> queryWrapper) ;


	@Select(value = "        select t.*,c1.name as ontoName1 ,c2.name as ontoName2 from meta_onto_relation t "
			+ "		left join lib_concept c1 on t.onto1_id =  c1.id  "
			+ "		left join lib_concept c2 on t.onto2_id =  c2.id ${ew.customSqlSegment}")
	List<MetaOntoRelationInfoVO>   ontolist(@Param(Constants.WRAPPER) Wrapper<MetaOntoRelationEntity> queryWrapper) ;

}
