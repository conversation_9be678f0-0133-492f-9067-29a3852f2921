package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.sinosoft.ie.booster.yearbook.business.service.UploadfileService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.entity.*;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkCorpusMapper;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkEntityMapper;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkSentenceMapper;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkTripletMapper;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusListVO;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkCorpusService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;

import javax.annotation.Resource;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;

/**
 *
 * mark_corpus
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-24 17:45:08
 */
@Service
public class MarkCorpusServiceImpl extends ServiceImpl<MarkCorpusMapper, MarkCorpusEntity> implements MarkCorpusService {


    @Resource
    private MarkSentenceMapper markSentenceMapper;
	@Resource
	private UploadfileService uploadfileService;
	@Resource
	private ExtractTextDataService2 extractTextDataService2;
    @Resource
    private MarkEntityMapper markEntityMapper;
    @Resource
    private MarkTripletMapper markTripletMapper;
    @Resource
    private ExtractTripletDataService extractTripletDataService;
	@Resource
	private MakrSentenceService sentenceService;
    @Resource
    private MakrSentenceService makrSentenceService;
	@Resource
	private ProjectConfig projectConfig;
	@Resource
    private MarkCorpusMapper markCorpusMapper;

    @Override
    public List<MarkCorpusEntity> getList(MarkCorpusPagination markCorpusPagination){
        QueryWrapper<MarkCorpusEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markCorpusPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getId,markCorpusPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getName,markCorpusPagination.getName()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getPid,markCorpusPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getFid()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getFid,markCorpusPagination.getFid()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getStat()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getStat,markCorpusPagination.getStat()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getStartPage()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getStartPage,markCorpusPagination.getStartPage()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getVersion()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getVersion,markCorpusPagination.getVersion()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getAnnotaionType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getAnnotaionType,markCorpusPagination.getAnnotaionType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getSchemaType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getSchemaType,markCorpusPagination.getSchemaType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getSchemaId,markCorpusPagination.getSchemaId()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getFileType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getFileType,markCorpusPagination.getFileType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getExportType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getExportType,markCorpusPagination.getExportType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getGranularity()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getGranularity,markCorpusPagination.getGranularity()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getDelFlag,markCorpusPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getCreateTime()))){
            queryWrapper.lambda().eq(MarkCorpusEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(markCorpusPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getCreateBy,markCorpusPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(MarkCorpusEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(markCorpusPagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getUpdateBy,markCorpusPagination.getUpdateBy()));
        }

        //排序
        if(StrUtil.isEmpty(markCorpusPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkCorpusEntity::getId);
        }else{
            queryWrapper="asc".equals(markCorpusPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markCorpusPagination.getSidx()):queryWrapper.orderByDesc(markCorpusPagination.getSidx());
        }
        Page<MarkCorpusEntity> page=new Page<>(markCorpusPagination.getCurrentPage(), markCorpusPagination.getPageSize());
        IPage<MarkCorpusEntity> userIPage=this.page(page,queryWrapper);
        return markCorpusPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MarkCorpusEntity> getTypeList(MarkCorpusPagination markCorpusPagination,String dataType){
        QueryWrapper<MarkCorpusEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markCorpusPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getId,markCorpusPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getName,markCorpusPagination.getName()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getPid,markCorpusPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getFid()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getFid,markCorpusPagination.getFid()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getStat()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getStat,markCorpusPagination.getStat()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getStartPage()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getStartPage,markCorpusPagination.getStartPage()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getVersion()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getVersion,markCorpusPagination.getVersion()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getAnnotaionType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getAnnotaionType,markCorpusPagination.getAnnotaionType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getSchemaType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getSchemaType,markCorpusPagination.getSchemaType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getSchemaId()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getSchemaId,markCorpusPagination.getSchemaId()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getFileType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getFileType,markCorpusPagination.getFileType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getExportType()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getExportType,markCorpusPagination.getExportType()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getGranularity()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getGranularity,markCorpusPagination.getGranularity()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getDelFlag,markCorpusPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getCreateTime()))){
            queryWrapper.lambda().eq(MarkCorpusEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(markCorpusPagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getCreateBy,markCorpusPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getUpdateTime()))){
            queryWrapper.lambda().eq(MarkCorpusEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(markCorpusPagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(markCorpusPagination.getUpdateBy()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getUpdateBy,markCorpusPagination.getUpdateBy()));
        }

        //排序
        if(StrUtil.isEmpty(markCorpusPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkCorpusEntity::getId);
        }else{
            queryWrapper="asc".equals(markCorpusPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markCorpusPagination.getSidx()):queryWrapper.orderByDesc(markCorpusPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MarkCorpusEntity> page=new Page<>(markCorpusPagination.getCurrentPage(), markCorpusPagination.getPageSize());
            IPage<MarkCorpusEntity> userIPage=this.page(page,queryWrapper);
            return markCorpusPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MarkCorpusEntity getInfo(Long id){
        QueryWrapper<MarkCorpusEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MarkCorpusEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MarkCorpusEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MarkCorpusEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MarkCorpusEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

    @Override
    public PageListVO<MarkCorpusListVO> getAllList(MarkCorpusPagination markCorpusPagination){
        QueryWrapper<MarkCorpusEntity> queryWrapper=new QueryWrapper<>();

        if(!"null".equals(String.valueOf(markCorpusPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MarkCorpusEntity::getName,markCorpusPagination.getName()));
        }

        queryWrapper.lambda().and(t->t.eq(MarkCorpusEntity::getPid,0));

        //排序
        queryWrapper.lambda().orderByDesc(MarkCorpusEntity::getId);
        Page<MarkCorpusEntity> page=new Page<>(markCorpusPagination.getCurrentPage(), markCorpusPagination.getPageSize());
        IPage<MarkCorpusEntity> userIPage=this.page(page,queryWrapper);
        if (userIPage.getTotal() == 0){
            return null;
        }
        markCorpusPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        List<MarkCorpusListVO> listVO= JsonUtil.getJsonToList(userIPage.getRecords(),MarkCorpusListVO.class);

//        List<MarkCorpusEntity> markCorpusEntityList = this.list(queryWrapper);
//        if (markCorpusEntityList == null || markCorpusEntityList.size() == 0){
//            return null;
//        }

        for (MarkCorpusListVO markCorpus:listVO) {
            QueryWrapper<MarkCorpusEntity> queryWrapper1=new QueryWrapper<>();
            queryWrapper1.lambda().and(t->t.eq(MarkCorpusEntity::getPid,markCorpus.getId()));
            List<MarkCorpusEntity> markCorpusEntities = this.list(queryWrapper1);
            if (markCorpusEntities == null || markCorpusEntities.size() == 0){
                continue;
            }
            List<MarkCorpusListVO> listVO1= JsonUtil.getJsonToList(markCorpusEntities,MarkCorpusListVO.class);
            for (MarkCorpusListVO corpus:listVO1) {
                QueryWrapper<MarkSentenceEntity> sentence=new QueryWrapper<>();
                sentence.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusId,corpus.getPid()));
                sentence.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusVersion,corpus.getVersion()));
                Long total = markSentenceMapper.selectCount(sentence);
                sentence.lambda().and(t->t.eq(MarkSentenceEntity::getStatus,4));
                Long over = markSentenceMapper.selectCount(sentence);
                if (total > 0){
                    corpus.setCorpusNum(Math.toIntExact(total));
                    corpus.setMarkProgress(over*100/total + "%");
                }else {
                    corpus.setCorpusNum(0);
                    corpus.setMarkProgress("0%");
                }

            }
            markCorpus.setChild(listVO1);
        }
        PageListVO<MarkCorpusListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page1=JsonUtil.getJsonToBean(markCorpusPagination,PaginationVO.class);
        vo.setPagination(page1);
        return vo;
    }

    @Override
    public void deleteChild(Long id){
        List<Long> ids = new ArrayList<>();
        QueryWrapper<MarkCorpusEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MarkCorpusEntity::getPid,id);
        List<MarkCorpusEntity> markCorpusList = this.list(queryWrapper);
        if (markCorpusList != null && markCorpusList.size() > 0){
            for (MarkCorpusEntity mark:markCorpusList) {
                ids.add(mark.getId());
            }
            QueryWrapper<MarkCorpusEntity> queryWrapper1=new QueryWrapper<>();
            queryWrapper1.lambda().in(MarkCorpusEntity::getPid,ids);
            List<MarkCorpusEntity> markCorpusEntityList = this.list(queryWrapper1);
            if (markCorpusEntityList != null && markCorpusEntityList.size() > 0){
                for (MarkCorpusEntity mark:markCorpusEntityList) {
                    ids.add(mark.getId());
                }
            }
        }
        ids.add(id);
        this.removeBatchByIds(ids);
    }

    @Override
    public void deleteCorpusVersion(MarkCorpusCrForm markCorpusCrForm){
        List<Long> ids = new ArrayList<>();
        QueryWrapper<MarkCorpusEntity> queryWrapper=new QueryWrapper<>();
        //版本是空，删除所有版本下的文件
        if(!"null".equals(String.valueOf(markCorpusCrForm.getVersion()))){
            queryWrapper.lambda().eq(MarkCorpusEntity::getPid,markCorpusCrForm.getId());
            this.remove(queryWrapper);
        }else {
            QueryWrapper<MarkCorpusEntity> queryWrapper1=new QueryWrapper<>();
            queryWrapper1.lambda().eq(MarkCorpusEntity::getPid,markCorpusCrForm.getPid());
            List<MarkCorpusEntity> list = this.list(queryWrapper1);
            List<Long> markCorpusIds = new ArrayList<>();
            for (MarkCorpusEntity markCorpus:list) {
                markCorpusIds.add(markCorpus.getId());
            }
            queryWrapper.lambda().in(MarkCorpusEntity::getPid,markCorpusIds);
            this.remove(queryWrapper);
        }

        QueryWrapper<MarkSentenceEntity> sentenceWrapper=new QueryWrapper<>();
        sentenceWrapper.lambda().eq(MarkSentenceEntity::getCorpusId,markCorpusCrForm.getPid());
        if(!"null".equals(String.valueOf(markCorpusCrForm.getVersion()))){
            sentenceWrapper.lambda().eq(MarkSentenceEntity::getCorpusVersion,markCorpusCrForm.getVersion());
        }

        List<MarkSentenceEntity> markSentenceEntities = markSentenceMapper.selectList(sentenceWrapper);
        for (MarkSentenceEntity sentence:markSentenceEntities) {
            ids.add(sentence.getId());
        }
        if (ids.size() == 0){
            return;
        }
        markSentenceMapper.delete(sentenceWrapper);
        QueryWrapper<MarkEntityEntity> markentityWrapper=new QueryWrapper<>();
        markentityWrapper.lambda().in(MarkEntityEntity::getSentenceid,ids);
        markEntityMapper.delete(markentityWrapper);
        QueryWrapper<MarkTripletEntity> marktripletWrapper=new QueryWrapper<>();
        marktripletWrapper.lambda().in(MarkTripletEntity::getSentenceid,ids);
        markTripletMapper.delete(marktripletWrapper);
    }

//    @Override
//    public void deleteMarkBySentence(MarkCorpusCrForm markCorpusCrForm){
//        List<Long> ids = new ArrayList<>();
//        QueryWrapper<MarkSentenceEntity> sentenceWrapper=new QueryWrapper<>();
//        sentenceWrapper.lambda().eq(MarkSentenceEntity::getCorpusId,markCorpusCrForm.getPid());
//        sentenceWrapper.lambda().eq(MarkSentenceEntity::getCorpusVersion,markCorpusCrForm.getVersion());
//        List<MarkSentenceEntity> markSentenceEntities = markSentenceMapper.selectList(sentenceWrapper);
//        for (MarkSentenceEntity sentence:markSentenceEntities) {
//            ids.add(sentence.getId());
//        }
//        if (ids.size() == 0){
//            return;
//        }
//        QueryWrapper<MarkEntityEntity> markentityWrapper=new QueryWrapper<>();
//        markentityWrapper.lambda().in(MarkEntityEntity::getSentenceid,ids);
//        markEntityMapper.delete(markentityWrapper);
//        QueryWrapper<MarkTripletEntity> marktripletWrapper=new QueryWrapper<>();
//        marktripletWrapper.lambda().in(MarkTripletEntity::getSentenceid,ids);
//        markTripletMapper.delete(marktripletWrapper);
//    }

    @Override
    public void deleteMarkBySentenceIds(String ids){
        if (ids == null || ids.isEmpty()){
            return;
        }
        String[] idsList = ids.split(",");
//        List<Long> ids = new ArrayList<>();
        QueryWrapper<MarkSentenceEntity> sentenceWrapper=new QueryWrapper<>();
        sentenceWrapper.lambda().in(MarkSentenceEntity::getId,idsList);
//        List<MarkSentenceEntity> markSentenceEntities = markSentenceMapper.selectList(sentenceWrapper);
        MarkSentenceEntity sentence = new MarkSentenceEntity();
        sentence.setStatus(1);
        markSentenceMapper.update(sentence,sentenceWrapper);
        QueryWrapper<MarkEntityEntity> markentityWrapper=new QueryWrapper<>();
        markentityWrapper.lambda().in(MarkEntityEntity::getSentenceid,idsList);
        markEntityMapper.delete(markentityWrapper);
        QueryWrapper<MarkTripletEntity> marktripletWrapper=new QueryWrapper<>();
        marktripletWrapper.lambda().in(MarkTripletEntity::getSentenceid,idsList);
        markTripletMapper.delete(marktripletWrapper);
    }
	@Override
	public R importCorpus(MarkCorpusEntity entity) {
		this.save(entity);
		//执行抽取数据
		//查询文件
		UploadfileEntity file = uploadfileService.getById(entity.getFid());
		String  oldname = file.getOldName();
		int idx = oldname.lastIndexOf(".");
		String prx = oldname.substring(idx, oldname.length());
		if(prx.contains("doc") || oldname.contains("docx")) {
			return extractTextDataService2.extractText(entity,file);
		}
		if(prx.contains("json")) {
			return resJson(entity,file);
		}
		if(prx.contains("xls") || prx.contains("xlsx") || prx.contains("csv")) {
			return resExcel(entity,file);
		}
		return null;
	}

	/**
	 * 解析json文件
	 * @param entity
	 * @param file
	 */
	public R  resJson(MarkCorpusEntity entity,UploadfileEntity file) {
		JSONArray  arry = null;
		try {
			String jsonStr = FileUtil.getString(projectConfig.getUploadPath(),file.getPath());
			arry =  JSONUtil.parseArray(jsonStr);
		} catch (Exception e) {
			throw new DataException("json格式错误");
		}
			List<MarkSentenceEntity>  entlist = new ArrayList<MarkSentenceEntity>();
			for (int i = 0; i < arry.size(); i++) {
				JSONObject json = (JSONObject) arry.get(i);
				MarkSentenceEntity  sentEnt = new MarkSentenceEntity();
				sentEnt.setFid(entity.getFid());
				sentEnt.setSentence((String)json.getStr("title"));
				sentEnt.setSentenceExc((String)json.getStr("text"));
				sentEnt.setStatus(YearbookConstant.MARK_STATS_WAIT);
				entlist.add(sentEnt);
				sentEnt.setCorpusId(entity.getId());
				sentEnt.setCorpusVersion(entity.getVersion());
				sentEnt.setCreateTime(LocalDateTime.now());
				extractTripletDataService.extractTriplet(sentEnt);
			}
			sentenceService.saveBatch(entlist);
			return R.ok();

	}
	/**
	 * 解析excel ,抽取三元组
	 * @param entity
	 * @param file
	 */
	public R  resExcel (MarkCorpusEntity entity,UploadfileEntity file) {
		String path = projectConfig.getUploadPath();
		ExcelReader reader = ExcelUtil.getReader(path);
		List<List<Object>> readAll = reader.read();
		List<MarkSentenceEntity>  entlist = new ArrayList<MarkSentenceEntity>();
		for (int i = 0; i < readAll.size(); i++) {
			List<Object> list = readAll.get(i);
			MarkSentenceEntity  sentEnt = new MarkSentenceEntity();
			sentEnt.setFid(entity.getFid());
			sentEnt.setSentence((String)list.get(0));
			sentEnt.setSentenceExc((String)list.get(1));
			sentEnt.setStatus(YearbookConstant.MARK_STATS_WAIT);
			entlist.add(sentEnt);
			sentEnt.setCorpusId(entity.getId());
			sentEnt.setCorpusVersion(entity.getVersion());
			sentEnt.setCreateTime(LocalDateTime.now());
			extractTripletDataService.extractTriplet(sentEnt);
		}
		sentenceService.saveBatch(entlist);
		return R.ok();
	}
	
    @Override
    public void inheritVersion(MarkCorpusEntity corpus){
        if (corpus.getPversion() == null){
            return;
        }
        QueryWrapper<MarkSentenceEntity> sentenceWrapper=new QueryWrapper<>();
        sentenceWrapper.lambda().in(MarkSentenceEntity::getCorpusId,corpus.getPid());
        sentenceWrapper.lambda().in(MarkSentenceEntity::getCorpusVersion,corpus.getPversion());
        List<MarkSentenceEntity> markSentenceList = markSentenceMapper.selectList(sentenceWrapper);
        for (MarkSentenceEntity sentence:markSentenceList) {
            sentence.setId(null);
            sentence.setStatus(1);
            sentence.setCorpusVersion(corpus.getVersion());
        }
        makrSentenceService.saveBatch(markSentenceList);
        for (MarkSentenceEntity sentence:markSentenceList) {
            extractTripletDataService.extractTriplet(sentence);
        }
    }
}