package com.sinosoft.ie.booster.yearbook.business.model.researchtag2;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * ResearchTag2模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-20 13:38:22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ResearchTag2PaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String name;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String tagGroup;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String tagType;

    /**
     * 标签分类英文名
     */
    @ApiModelProperty(value = "标签分类英文名")
    private String tagAttr;
}