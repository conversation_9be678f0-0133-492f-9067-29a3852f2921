package com.sinosoft.ie.booster.yearbook.graph.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 自定义主题表
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-07-21 10:53:07
 */
@Data
@TableName("eve_tag")
@ApiModel(value = "自定义主题表")
public class EveTagEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String name;

    /**
     * 1常态主题;2动态主题
     */
    @ApiModelProperty(value = "1常态主题;2动态主题")
    private Integer category1;

    /**
     * 1科研类;2管理类
     */
    @ApiModelProperty(value = "1科研类;2管理类")
    private Integer category2;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Integer isdef;

    /**
     * 父主题名称
     */
    @ApiModelProperty(value = "父主题名称")
    private String pname;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String synopsis;

    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示")
    private String islab;
}
