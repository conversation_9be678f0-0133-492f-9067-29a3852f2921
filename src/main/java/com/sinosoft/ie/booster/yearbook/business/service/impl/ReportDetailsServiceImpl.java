package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.ReportDetails;
import com.sinosoft.ie.booster.yearbook.business.entity.ReportThemes;
import com.sinosoft.ie.booster.yearbook.business.mapper.ReportDetailsMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ReportThemesMapper;
import com.sinosoft.ie.booster.yearbook.business.service.ReportDetailsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ReportDetailsServiceImpl implements ReportDetailsService {
    @Resource
    private ReportThemesMapper reportThemesMapper;
    @Resource
    private ReportDetailsMapper reportDetailsMapper;
    @Override
    public List<Map<String,String>> getReportDetails(String theme) {
        List<Map<String,String>> list = new ArrayList<>();
        List<ReportThemes> reportThemesList = reportThemesMapper.getReportThemesList();
        for (ReportThemes reportThemes : reportThemesList) {
            if(reportThemes.getThemeName().equals(theme)){
                List<ReportDetails> reportDetailsList = reportDetailsMapper.getReportDetails(theme);
                int i = 0;
                for (ReportDetails reportDetails : reportDetailsList) {
                    Map<String,String> map = new HashMap<>();
                    i++;
                    String substring = "事件"+i;
                    map.put("title",substring);
                    map.put("content",reportDetails.getDetailContent());
                    list.add(map);
                }
                return list;
            }
        }

        return null;
    }
}
