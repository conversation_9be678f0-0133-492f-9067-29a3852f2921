package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationGraph;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaEntityRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * meta_entity_relation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-31 16:34:43
 */
@Service
public class MetaEntityRelationServiceImpl extends ServiceImpl<MetaEntityRelationMapper, MetaEntityRelationEntity> implements MetaEntityRelationService {

    @Resource
    private MetaEntityRelationMapper metaEntityRelationMapper;

    @Resource
    private LibConceptService libConceptService;

    @Resource
    private MateAttributeMapper mateAttributeMapper;

    @Resource
    private LibEntityMapper libEntityMapper;

    @Override
    public List<MetaEntityRelationEntity> getList(MetaEntityRelationPagination metaEntityRelationPagination){
        QueryWrapper<MetaEntityRelationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getId,metaEntityRelationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getOnto1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto1Id,metaEntityRelationPagination.getOnto1Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getOnto2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto2Id,metaEntityRelationPagination.getOnto2Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getName,metaEntityRelationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getEntity1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity1Id,metaEntityRelationPagination.getEntity1Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getEntity2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity2Id,metaEntityRelationPagination.getEntity2Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getOntoRelId()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOntoRelId,metaEntityRelationPagination.getOntoRelId()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getState()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getState,metaEntityRelationPagination.getState()));
        }

        //排序
        if(StrUtil.isEmpty(metaEntityRelationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MetaEntityRelationEntity::getId);
        }else{
            queryWrapper="asc".equals(metaEntityRelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaEntityRelationPagination.getSidx()):queryWrapper.orderByDesc(metaEntityRelationPagination.getSidx());
        }
        Page<MetaEntityRelationEntity> page=new Page<>(metaEntityRelationPagination.getCurrentPage(), metaEntityRelationPagination.getPageSize());
        IPage<MetaEntityRelationEntity> userIPage=this.page(page,queryWrapper);
        return metaEntityRelationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MetaEntityRelationEntity> getTypeList(MetaEntityRelationPagination metaEntityRelationPagination,String dataType){
        QueryWrapper<MetaEntityRelationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getId,metaEntityRelationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getOnto1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto1Id,metaEntityRelationPagination.getOnto1Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getOnto2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto2Id,metaEntityRelationPagination.getOnto2Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getName,metaEntityRelationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getEntity1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity1Id,metaEntityRelationPagination.getEntity1Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getEntity2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity2Id,metaEntityRelationPagination.getEntity2Id()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getOntoRelId()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOntoRelId,metaEntityRelationPagination.getOntoRelId()));
        }

        if(!"null".equals(String.valueOf(metaEntityRelationPagination.getState()))){
            queryWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getState,metaEntityRelationPagination.getState()));
        }

        //排序
        if(StrUtil.isEmpty(metaEntityRelationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MetaEntityRelationEntity::getId);
        }else{
            queryWrapper="asc".equals(metaEntityRelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaEntityRelationPagination.getSidx()):queryWrapper.orderByDesc(metaEntityRelationPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MetaEntityRelationEntity> page=new Page<>(metaEntityRelationPagination.getCurrentPage(), metaEntityRelationPagination.getPageSize());
            IPage<MetaEntityRelationEntity> userIPage=this.page(page,queryWrapper);
            return metaEntityRelationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MetaEntityRelationEntity getInfo(Long id){
        QueryWrapper<MetaEntityRelationEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MetaEntityRelationEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MetaEntityRelationEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MetaEntityRelationEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MetaEntityRelationEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }

    @Override
    public void createEntity(MetaEntityRelationEntity entity,int type){
        //获取属性名
        QueryWrapper<MateAttributeEntity> queryMateAttribute=new QueryWrapper<>();
        queryMateAttribute.lambda().and(t->t.like(MateAttributeEntity::getRelationId,entity.getOntoRelId()));
        List<MateAttributeEntity> mateAttributeEntities = mateAttributeMapper.selectList(queryMateAttribute);
        if (mateAttributeEntities.size() == 0){return;}
        if (mateAttributeEntities.get(0).getNameEn() == null || mateAttributeEntities.get(0).getNameEn().isEmpty()){return;}
        //属性名
        String nameEn = mateAttributeEntities.get(0).getNameEn();
        //是否多值:1,单；2,多
        Integer vals = mateAttributeEntities.get(0).getVals();

        //获取左实体
        LibEntityEntity libEntityEntity = libEntityMapper.selectById(entity.getEntity1Id());
        String data = libEntityEntity.getData();
        Map<String, Object> dataMap = JsonUtil.stringToMap(data);
        if (vals == 1){
            //单值
            if (type == 1){
                //增改
                dataMap.put(nameEn,entity.getEntity2Id());
            }else {
                //改
                QueryWrapper<MetaEntityRelationEntity> queryMetaEntityRelation=new QueryWrapper<>();
                queryMetaEntityRelation.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity1Id,entity.getEntity1Id()));
                List<MetaEntityRelationEntity> relationList = this.list(queryMetaEntityRelation);
                if (relationList.size() == 0){
                    dataMap.put(nameEn,null);
                }else {
                    dataMap.put(nameEn,relationList.get(0).getEntity2Id());
                }
            }
        }
        if (vals == 2){
            //多值
            //获取所有关系
            JSONArray jsonArray = new JSONArray();
            QueryWrapper<MetaEntityRelationEntity> queryMetaEntityRelation=new QueryWrapper<>();
            queryMetaEntityRelation.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity1Id,entity.getEntity1Id()));
            List<MetaEntityRelationEntity> relationList = this.list(queryMetaEntityRelation);
            for (MetaEntityRelationEntity relation:relationList) {
                jsonArray.add(relation.getEntity2Id());
            }
            dataMap.put(nameEn,jsonArray);
//            if (dataMap.containsKey(nameEn)){
//                jsonArray = (JSONArray) dataMap.get(nameEn);
//                jsonArray.add(entity.getEntity2Id());
//                dataMap.put(nameEn,jsonArray);
//            }else {
//                jsonArray.add(entity.getEntity2Id());
//                dataMap.put(nameEn,jsonArray);
//            }
        }
        libEntityEntity.setData(String.valueOf(new JSONObject(dataMap)));
        libEntityMapper.updateById(libEntityEntity);
    }

    @Override
    public R getGraph(String name, Long relationId) {
        Map<String,Object> totalMap = new HashMap<>();
        //查询实体
        QueryWrapper<LibEntityEntity> entityWrapper=new QueryWrapper<>();
        entityWrapper.select("id");
        entityWrapper.lambda().and(t->t.eq(LibEntityEntity::getName,name));
        List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(entityWrapper);
        if (libEntityEntities.size() == 0){return R.ok(totalMap);}
        long entityId = libEntityEntities.get(0).getId();
        //查询实体关系
        QueryWrapper<MetaEntityRelationEntity> relationWrapper=new QueryWrapper<>();
        relationWrapper.select("entity1_id","entity2_id","name");
        relationWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getEntity1Id,entityId)
                .or(t1->t1.like(MetaEntityRelationEntity::getEntity2Id,entityId)));
        if(!"null".equals(String.valueOf(relationId))){
            relationWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOntoRelId,relationId));
        }
        List<MetaEntityRelationEntity> metaEntityRelationEntities = metaEntityRelationMapper.selectList(relationWrapper);
        List<Map<String,Object>> relationList = new ArrayList<>();
        for (MetaEntityRelationEntity relation:metaEntityRelationEntities) {
            Map<String,Object> relationMap = new HashMap<>();
            relationMap.put("source",relation.getEntity1Id());
            relationMap.put("target",relation.getEntity2Id());
            relationMap.put("value",relation.getName());
            relationList.add(relationMap);
        }
        Map<String,Long> map = new HashMap<>();
        map.put("entityId",entityId);
        map.put("ontoRelId",relationId);
        List<MetaEntityRelationGraph> nodes = metaEntityRelationMapper.entityIdList(map);
        totalMap.put("nodes",nodes);
        totalMap.put("links",relationList);
        return R.ok(totalMap);
    }

    @Override
    public R getGraphOnto(Long ontoId) {
        Map<String,Object> totalMap = new HashMap<>();

        //查询实体关系
        QueryWrapper<MetaEntityRelationEntity> relationWrapper=new QueryWrapper<>();
        relationWrapper.select("entity1_id","entity2_id","name");
        relationWrapper.lambda().and(t->t.like(MetaEntityRelationEntity::getOnto1Id,ontoId)
                .or(t1->t1.like(MetaEntityRelationEntity::getOnto2Id,ontoId)));
        List<MetaEntityRelationEntity> metaEntityRelationEntities = metaEntityRelationMapper.selectList(relationWrapper);
        List<Map<String,Object>> relationList = new ArrayList<>();
        for (MetaEntityRelationEntity relation:metaEntityRelationEntities) {
            Map<String,Object> relationMap = new HashMap<>();
            relationMap.put("source",relation.getEntity1Id());
            relationMap.put("target",relation.getEntity2Id());
            relationMap.put("value",relation.getName());
            relationList.add(relationMap);
        }

        List<MetaEntityRelationGraph> nodes = metaEntityRelationMapper.entityIdListOnto(ontoId);
        totalMap.put("nodes",nodes);
        totalMap.put("links",relationList);
        return R.ok(totalMap);
    }
}