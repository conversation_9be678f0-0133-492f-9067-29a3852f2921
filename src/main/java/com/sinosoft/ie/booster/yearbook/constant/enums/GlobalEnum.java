package com.sinosoft.ie.booster.yearbook.constant.enums;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：GlobalEnum.java
 * @author：peas
 * @data：2022/11/9 11:22
 **/
public interface GlobalEnum {

    enum isDeleteType implements GlobalEnum {
        NO(0, "正常"),
        YES(1, "删除");

        private Integer code;
        private String message;

        private isDeleteType(int code, String message) {
            this.code = code;
            this.message = message;
        }
        public Integer getCode() {
            return code;
        }
        public String getMessage() {
            return message;
        }
    }

    enum currencyEnumYesOrNo implements GlobalEnum {
        NO(0, "否"),
        YES(1, "是");

        private Integer code;
        private String message;

        private currencyEnumYesOrNo(int code, String message) {
            this.code = code;
            this.message = message;
        }
        public Integer getCode() {
            return code;
        }
        public String getMessage() {
            return message;
        }
    }

}
