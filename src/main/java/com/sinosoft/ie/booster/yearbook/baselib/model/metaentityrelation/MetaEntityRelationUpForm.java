package com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MetaEntityRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:34:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MetaEntityRelationUpForm extends MetaEntityRelationCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}