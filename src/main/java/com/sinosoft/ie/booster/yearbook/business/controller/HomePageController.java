package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.ImportantNews;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.YearbookCount;
import com.sinosoft.ie.booster.yearbook.business.service.HomePageService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Api(tags = "homePage")
@RequestMapping("/homePage")
public class HomePageController {
	

    @Autowired
    private HomePageService homePageService;

    /***
     * @description：首页年鉴列表
     * @author：peas
     * @date：2022/11/9 14:48
     **/
    @GetMapping("/banner")
    public R queryBanner()throws IOException {
        return R.ok(homePageService.queryBanner());
    }

    /**
     * @description：首页年鉴统计
     * @author：peas
     * @date：2022/11/10 17:35
     **/
    @GetMapping("/count")
    @Transactional
    public R<YearbookCount> labelRead(){
        return R.ok(homePageService.count());
    }

    /**
     * @description：要事要闻
     * @author：peas
     * @date：2022/11/10 17:36
     **/
    @GetMapping("/news")
    @Transactional
    public R<ImportantNews> getNews(){
        return R.ok(homePageService.getNews());
    }

    /**
     * @description：事件脉络
     * @author：peas
     * @date：2022/11/11 14:12
     **/
    @GetMapping("/eventVein")
    @Transactional
    public R eventVein(){
        return R.ok(homePageService.eventVein());
    }

    
    /**
     * 首页点击 六个主题，如果有事件则返回具体事件，没有则返回null
     * @param eventPagination
     * @return
     */
    @GetMapping("/getEventOrYearbook")
    public R  getEventByTitleYearPage(EventPagination eventPagination) {
    	return 	homePageService.getEventByTitleYearPage(eventPagination);
    }
    

}
