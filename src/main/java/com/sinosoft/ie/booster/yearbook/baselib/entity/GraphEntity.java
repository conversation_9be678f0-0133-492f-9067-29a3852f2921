package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-31 13:39:56
 */
@Data
@TableName("graph")
@ApiModel(value = "")
public class GraphEntity  {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;

    /**
     * 状态：
     */
    @ApiModelProperty(value = "状态：")
    private Integer status;
}
