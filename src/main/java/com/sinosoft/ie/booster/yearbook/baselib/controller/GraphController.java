package com.sinosoft.ie.booster.yearbook.baselib.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.GraphEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.GraphService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * graph
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-31 13:39:56
 */
@Slf4j
@RestController
@Api(tags = "graph")
@RequestMapping("/Graph")
public class GraphController {
    @Autowired
    private GraphService graphService;


    /**
     * 列表
     *
     * @param graphPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<GraphListVO>> list(GraphPagination graphPagination)throws IOException{
        List<GraphEntity> list= graphService.getList(graphPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(GraphEntity entity:list){
            }
        List<GraphListVO> listVO=JsonUtil.getJsonToList(list,GraphListVO.class);
        PageListVO<GraphListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(graphPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param graphCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid GraphCrForm graphCrForm) throws DataException {
        GraphEntity entity=JsonUtil.getJsonToBean(graphCrForm, GraphEntity.class);
        graphService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<GraphInfoVO> info(@PathVariable("id") Long id){
        GraphEntity entity= graphService.getInfo(id);
        GraphInfoVO vo=JsonUtil.getJsonToBean(entity, GraphInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid GraphUpForm graphUpForm) throws DataException {
        GraphEntity entity= graphService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(graphUpForm, GraphEntity.class);
            graphService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        GraphEntity entity= graphService.getInfo(id);
        if(entity!=null){
            graphService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
