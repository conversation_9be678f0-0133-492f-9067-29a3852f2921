package com.sinosoft.ie.booster.yearbook.util;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import com.itextpdf.text.pdf.GrayColor;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;


public class FileUtil {

	private static Logger logger = LoggerFactory.getLogger(FileUtil.class);

	/**
	 * 根据文件路径获取后缀名
	 */
	public static String getFileSuffixName(String filePath) throws IOException {
		File file = new File(filePath);
		String filename = file.getName();
		String extName = filename.substring(filename.lastIndexOf(".") + 1);
		if (extName.indexOf("?") > 0) {
			extName = extName.substring(0, extName.indexOf("?")).toLowerCase();
		}
		return extName;
	}

	/**
	 * 检查绝对路径的文件是否存在
	 */
	public static boolean isFileExists(String absolutePath) {
		boolean isExists = false;
		try {
			File file = new File(absolutePath);
			isExists = file.exists();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("判断指定文件是否存在的操作出现错误！filePhysicalPath=" + absolutePath);
		}
		return isExists;
	}

	/**
	 * @throws IOException 向目标文件写入文本
	 * @Title: writeFile
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param path
	 * @param @param contxt 参数
	 * @date 2019年12月17日
	 * @return void 返回类型
	 * @throws
	 */
	public static void writeFile(String path, String contxt) throws IOException {
		File dir = new File(path);
		// 一、检查放置文件的文件夹路径是否存在，不存在则创建
		FileWriter writer = null;

		// 二、检查目标文件是否存在，不存在则创建
		if (!dir.exists()) {
			dir.createNewFile();// 创建目标文件
		}
		// 三、向目标文件中写入内容
		// FileWriter(File file, boolean append)，append为true时为追加模式，false或缺省则为覆盖模式
		writer = new FileWriter(dir, false);
		writer.append(contxt);
		writer.flush();

		writer.close();

	}

	public static File mkdir(File dir) {
		if (dir == null) {
			return null;
		}
		if (false == dir.exists()) {
			//noinspection ResultOfMethodCallIgnored
			dir.mkdirs();
		}
		return dir;
	}
	
	public static void main(String agrs[]) throws IOException {
		System.out.println("开始");
//		writeFile("D:\\work\\1.json", jsonStr);
	}
}
