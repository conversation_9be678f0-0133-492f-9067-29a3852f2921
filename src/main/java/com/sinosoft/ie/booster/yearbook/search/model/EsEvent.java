package com.sinosoft.ie.booster.yearbook.search.model;

import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;

import cn.easyes.annotation.HighLight;
import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.IndexId;
import cn.easyes.annotation.IndexName;
import cn.easyes.annotation.rely.Analyzer;
import cn.easyes.annotation.rely.FieldType;
import cn.easyes.annotation.rely.IdType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@IndexName("event")
public class EsEvent {
	
	public EsEvent() {}
	
	public EsEvent(EventEntity entity) {
		this.id =  String.valueOf(entity.getId());
		this.title = entity.getTitle();
		this.content =  entity.getContent();
		this.tag =  entity.getTag();
		this.theme =  entity.getTheme();
		this.start_time =  entity.getStartTime();
	}
	
	@IndexId(type=IdType.CUSTOMIZE)
    private String id;

    /**
     * 标题
     */
	@IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
	@HighLight(mappingField = "hig_title")
    @ApiModelProperty(value = "标题")
    private String title;

	private String hig_title;
    /**
     * 事件文本
     */
	@IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART)
	@HighLight(mappingField = "hig_content")
    @ApiModelProperty(value = "事件文本")
    private String content;
	
	private String hig_content;
    /**
     * 关键词
     */
    private String tag;
    /**
     * 主题
     */
    private String  theme;
    /**
     * 开始时间
     */
    private String start_time;
    
    private String year;
    
    private String perIds;
    
    private String orgIds;
    
    private String org_name;
    
    private Integer weight;
    
    private String pageNum;
    
    private String org12_name;
    
}
