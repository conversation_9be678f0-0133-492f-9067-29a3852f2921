package com.sinosoft.ie.booster.yearbook.search.job;

import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sinosoft.ie.booster.common.core.util.R;

@RequestMapping("/toes")
@Controller
public class DataToEsController {

	@Resource
	private DataToEs dataToEs;
	@Resource
	private SynchronEntity synchronEntity;
	@Resource
	private SynchronEvents synchronEvents;
	@ResponseBody
	@GetMapping
	public R  toes() throws IOException {
		synchronEvents.synchronEvents();
		synchronEntity.synchronEntity();
		return R.ok();
	}
	
//	@GetMapping("up")
//	public R  up() throws IOException {
//		
//		return R.ok();
//	}
//	
	
	@ResponseBody
	@GetMapping("/del")
	public R  delEs() {
		dataToEs.delIdx();
		return R.ok();
	}
	
}
