package com.sinosoft.ie.booster.yearbook.mark.model.marktriplet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * MarkTriplet模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:56
 */
@Data
@ApiModel
public class MarkTripletListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String tag1id;
    
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String tag2id;
    
    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String relationid;
    

    /**
     * 关系名称
     */
    @ApiModelProperty(value = "关系名称")
    private String relationname;

    /**
     * 句子id
     */
    @ApiModelProperty(value = "句子id")
    private Long sentenceid;
    
}