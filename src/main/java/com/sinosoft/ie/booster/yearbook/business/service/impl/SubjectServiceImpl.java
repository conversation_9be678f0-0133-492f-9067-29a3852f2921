package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.SubjectEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.SubjectMapper;
import com.sinosoft.ie.booster.yearbook.business.service.SubjectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * subject
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-26 14:03:56
 */
@Service
public class SubjectServiceImpl extends ServiceImpl<SubjectMapper, SubjectEntity> implements SubjectService {


    @Override
    public List<SubjectEntity> getList(SubjectPagination subjectPagination){
        QueryWrapper<SubjectEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(subjectPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(SubjectEntity::getId,subjectPagination.getId()));
        }

        if(!"null".equals(String.valueOf(subjectPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(SubjectEntity::getName,subjectPagination.getName()));
        }

        if(!"null".equals(String.valueOf(subjectPagination.getField()))){
            queryWrapper.lambda().and(t->t.like(SubjectEntity::getField,subjectPagination.getField()));
        }

        //排序
        if(StrUtil.isEmpty(subjectPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(SubjectEntity::getId);
        }else{
            queryWrapper="asc".equals(subjectPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(subjectPagination.getSidx()):queryWrapper.orderByDesc(subjectPagination.getSidx());
        }
        Page<SubjectEntity> page=new Page<>(subjectPagination.getCurrentPage(), subjectPagination.getPageSize());
        IPage<SubjectEntity> userIPage=this.page(page,queryWrapper);
        return subjectPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<SubjectEntity> getTypeList(SubjectPagination subjectPagination,String dataType){
        QueryWrapper<SubjectEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(subjectPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(SubjectEntity::getId,subjectPagination.getId()));
        }

        if(!"null".equals(String.valueOf(subjectPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(SubjectEntity::getName,subjectPagination.getName()));
        }

        if(!"null".equals(String.valueOf(subjectPagination.getField()))){
            queryWrapper.lambda().and(t->t.like(SubjectEntity::getField,subjectPagination.getField()));
        }

        //排序
        if(StrUtil.isEmpty(subjectPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(SubjectEntity::getId);
        }else{
            queryWrapper="asc".equals(subjectPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(subjectPagination.getSidx()):queryWrapper.orderByDesc(subjectPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<SubjectEntity> page=new Page<>(subjectPagination.getCurrentPage(), subjectPagination.getPageSize());
            IPage<SubjectEntity> userIPage=this.page(page,queryWrapper);
            return subjectPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public SubjectEntity getInfo(Long id){
        QueryWrapper<SubjectEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(SubjectEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(SubjectEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, SubjectEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(SubjectEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法

	@Override
	public List<Map<String,Object>> getListSelect(){
		List<Map<String,Object>> listTotal = new ArrayList<>();
		QueryWrapper<SubjectEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.select("DISTINCT(field)");
		List<SubjectEntity> fieldList = this.list(queryWrapper);
		if (fieldList != null && fieldList.size() > 0){
			for (int i=0; i<fieldList.size(); i++){
				Map<String,Object> mapTotal = new HashMap<>();
				mapTotal.put("label",fieldList.get(i).getField());
				QueryWrapper<SubjectEntity> queryWrapper1=new QueryWrapper<>();
				int finalI = i;
				queryWrapper1.lambda().and(t->t.eq(SubjectEntity::getField,fieldList.get(finalI).getField()));
				List<SubjectEntity> subjectEntityList = this.list(queryWrapper1);
				List<Map<String,String>> listSmall = new ArrayList<>();

				if (subjectEntityList != null && subjectEntityList.size() > 0){
					for (int j=0; j<subjectEntityList.size(); j++){
						Map<String,String> mapSmall = new HashMap<>();
						mapSmall.put("value",subjectEntityList.get(j).getName());
						mapSmall.put("label",subjectEntityList.get(j).getName());
						listSmall.add(mapSmall);
					}
				}
				mapTotal.put("options",listSmall);
				listTotal.add(mapTotal);
			}
		}
		return listTotal;
	}
}