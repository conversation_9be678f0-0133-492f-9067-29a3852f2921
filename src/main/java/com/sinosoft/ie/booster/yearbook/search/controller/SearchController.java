package com.sinosoft.ie.booster.yearbook.search.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEventMapper;
import com.sinosoft.ie.booster.yearbook.search.model.EsEvent;
import com.sinosoft.ie.booster.yearbook.search.model.EsQueryPagination;
import com.sinosoft.ie.booster.yearbook.search.service.SearchServivce;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequestMapping("/search")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SearchController { 

	private final EsEventMapper esEventMapper;
	private final  SearchServivce searchServivce; 

	/**
	 * 搜索关键词
	 * 全部频道
	 * @param str
	 * @return
	 * @throws IOException 
	 */
	@GetMapping
	public R search(String str)  {
		try {
			return searchServivce.search(str);
		} catch (IOException e) {
			log.error("{}",e);
		}
		return R.failed();
	}
	
	/**
	 * 搜索事件
	 * 事件频道
	 * @param query
	 * @return
	 */
	@GetMapping("/event")
	public R searchEvent(EsQueryPagination query) {
		List<EsEvent>  list = searchServivce.queryEsEvent(query,"null");
		PageListVO<EsEvent> vo=new PageListVO<>();
		vo.setList(list);
		PaginationVO pagination =  new PaginationVO();
		pagination.setCurrentPage(Long.valueOf(query.getPageNum()));
		pagination.setTotal((int)query.getTotal());
		pagination.setPageSize(Long.valueOf(query.getPageSize()));
		vo.setPagination(pagination);
		return R.ok(vo);
	}
	
	@GetMapping("/queryEsEventYearAndOrg")
	public R queryEsEventYearAndOrg(EsQueryPagination query) {
		return R.ok(searchServivce.queryEsEventYearAndOrg(query));
	}
	
	
	/**
	 * 搜索人物
	 * 人物频道
	 * @param query
	 * @return
	 * @throws IOException
	 */
	@GetMapping("/persion")
	public R searchPersion(EsQueryPagination query) throws IOException {
		Map<String,Object>  map  = searchServivce.queryEsPersion(query);
		map.put("query", query);
		return R.ok(map);
	}
	
	/**
	 * 搜索 机构
	 * 机构频道
	 * @param query
	 * @return
	 * @throws IOException
	 */
	@GetMapping("/org")
	public R searchOrg(EsQueryPagination query) throws IOException {
		Map<String,Object>  map  = searchServivce.queryEsOrg(query);
		map.put("query", query);
		return R.ok(map);
	}
	
	/**
	 * 搜索成果全部页
	 * @param query
	 * @return
	 * @throws IOException
	 */
	@GetMapping("/resultList")
	public R searchResult(EsQueryPagination query) throws IOException {
		query.setOntoId(YearbookConstant.MARK_RESULT_ONTO_ID);
		List  list = searchServivce.queryEsEntity(query);
		return R.ok(list);
	}
	/**
	 * 搜索项目全部页
	 * @param query
	 * @return
	 * @throws IOException
	 */
	@GetMapping("/projectList")
	public R searchProject(EsQueryPagination query) throws IOException {
		query.setOntoId(YearbookConstant.MARK_PROJECT_ONTO_ID);
		List  list = searchServivce.queryEsEntity(query);
		return R.ok(list);
	}
	
	/**
	 * 查询实体详情
	 * 人物
	 * 机构
	 * 主题
	 * @return
	 */
	@GetMapping("/getEntityDetail")
	public R  getEntityDetail(Long id) {
		R r = searchServivce.getEntityDetail(id);
		return R.ok(r);
	}
	/**
	 * 查询实体图谱
	 * @param id
	 * @return
	 */
	@GetMapping("/getEntityGraph")
	public R  getEntityGraph(Long id) {
		
		return searchServivce.searchGraph(id);
	}
	/**
	 * 查询人物事件脉络
	 * @param id
	 * @return
	 */
	@GetMapping("/entityContext")
	public R  per_event_line(Long id) {
		return searchServivce.searchEntityContext(id);
	}
	
	
	
	/**
	 * 查询搜索词 ，右边的相关标签，人物，机构
	 */
	@GetMapping("/getRelationObj")
	public R  getRelationObj(String str) {
		return searchServivce.getRelationObj(str);
	}
	
	@GetMapping("/orgList")
	public R orgList() throws IOException {
		List list =  searchServivce.queryEsOrgNameList();
		return R.ok(list);
	}
}
