package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportLogEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.FileImportLogMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.FileImportLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * file_import_log
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-08 16:35:51
 */
@Service
public class FileImportLogServiceImpl extends ServiceImpl<FileImportLogMapper, FileImportLogEntity> implements FileImportLogService {


    @Override
    public List<FileImportLogEntity> getList(FileImportLogPagination fileImportLogPagination){
        QueryWrapper<FileImportLogEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(fileImportLogPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getId,fileImportLogPagination.getId()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getJobId()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getJobId,fileImportLogPagination.getJobId()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getRowNum()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getRowNum,fileImportLogPagination.getRowNum()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getName,fileImportLogPagination.getName()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getJobType()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getJobType,fileImportLogPagination.getJobType()));
        }

        //排序
        if(StrUtil.isEmpty(fileImportLogPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FileImportLogEntity::getId);
        }else{
            queryWrapper="asc".equals(fileImportLogPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(fileImportLogPagination.getSidx()):queryWrapper.orderByDesc(fileImportLogPagination.getSidx());
        }
        Page<FileImportLogEntity> page=new Page<>(fileImportLogPagination.getCurrentPage(), fileImportLogPagination.getPageSize());
        IPage<FileImportLogEntity> userIPage=this.page(page,queryWrapper);
        return fileImportLogPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<FileImportLogEntity> getTypeList(FileImportLogPagination fileImportLogPagination,String dataType){
        QueryWrapper<FileImportLogEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(fileImportLogPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getId,fileImportLogPagination.getId()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getJobId()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getJobId,fileImportLogPagination.getJobId()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getRowNum()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getRowNum,fileImportLogPagination.getRowNum()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getName,fileImportLogPagination.getName()));
        }

        if(!"null".equals(String.valueOf(fileImportLogPagination.getJobType()))){
            queryWrapper.lambda().and(t->t.like(FileImportLogEntity::getJobType,fileImportLogPagination.getJobType()));
        }

        //排序
        if(StrUtil.isEmpty(fileImportLogPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FileImportLogEntity::getId);
        }else{
            queryWrapper="asc".equals(fileImportLogPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(fileImportLogPagination.getSidx()):queryWrapper.orderByDesc(fileImportLogPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<FileImportLogEntity> page=new Page<>(fileImportLogPagination.getCurrentPage(), fileImportLogPagination.getPageSize());
            IPage<FileImportLogEntity> userIPage=this.page(page,queryWrapper);
            return fileImportLogPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public FileImportLogEntity getInfo(Long id){
        QueryWrapper<FileImportLogEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(FileImportLogEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(FileImportLogEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, FileImportLogEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(FileImportLogEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}