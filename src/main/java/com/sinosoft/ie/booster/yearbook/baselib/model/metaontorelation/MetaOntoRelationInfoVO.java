package com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * MetaOntoRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:37:39
 */
@Data
@ApiModel
public class MetaOntoRelationInfoVO {

    
    private String ontoName1;
    
    private String ontoName2;
    

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String id;

    /**
     * 本体1的id
     */
    @ApiModelProperty(value = "本体1的id")
    private String onto1Id;

    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的id")
    private String onto2Id;

    /**
     * 关系名称
     */
    @ApiModelProperty(value = "关系名称")
    private String name;

    /**
     * 关系的父id
     */
    @ApiModelProperty(value = "关系的父id")
    private String pid;
    
    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private String schemaId;
}