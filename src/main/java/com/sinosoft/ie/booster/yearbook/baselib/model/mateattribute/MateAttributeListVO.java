package com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValueListVO;

/**
 *
 * MateAttribute模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-24 11:19:01
 */
@Data
@ApiModel
public class MateAttributeListVO  extends  MateAttributeInfoVO{

	
    @ApiModelProperty(value = "子级")
    private List<MateAttributeValueListVO> child;
    
    /**
     * 下拉框对应值
     */
    private String valName;
}