package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.GraphEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.graph.GraphPagination;
import java.util.*;

/**
 *
 * graph
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-31 13:39:56
 */
public interface GraphService extends IService<GraphEntity> {

    List<GraphEntity> getList(GraphPagination graphPagination);

    List<GraphEntity> getTypeList(GraphPagination graphPagination,String dataType);



    GraphEntity getInfo(Long id);

    void delete(GraphEntity entity);

    void create(GraphEntity entity);

    boolean update( Long id, GraphEntity entity);
    
//  子表方法
}
