package com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MateRelationAttributeValue模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-01 14:35:03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MateRelationAttributeValuePagination extends Pagination {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 属性id
     */
    @ApiModelProperty(value = "属性id")
    private Long attributeId;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String valName;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private Long pid;

    /**
     * 本体id
     */
    @ApiModelProperty(value = "本体id")
    private Long ontoId;

    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    private Long relationId;
}