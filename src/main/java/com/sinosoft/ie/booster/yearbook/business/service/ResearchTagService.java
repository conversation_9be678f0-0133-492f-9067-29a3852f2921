package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagListVO;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagPagination;

import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.util.*;

/**
 *
 * research_tag
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-15 10:49:22
 */
public interface ResearchTagService extends IService<ResearchTagEntity> {

    List<ResearchTagEntity> getList(ResearchTagPagination researchTagPagination);

    List<ResearchTagEntity> getTypeList(ResearchTagPagination researchTagPagination,String dataType);



    ResearchTagEntity getInfo(Long id);

    void delete(ResearchTagEntity entity);

    void create(ResearchTagEntity entity);

    boolean update( Long id, ResearchTagEntity entity);
    
//  子表方法
	Map<String,List<ResearchTagListVO>> showMapByLegendType(String legendTypeList);

    Map<String,Object> showMapByLegendTypeYear(String legendTypeList, String year, int currentPage);
    
    
    R  wordCloud  (String legendTypeList, String year,String initial,String orgId);
    
    void  computWeight() throws BadHanyuPinyinOutputFormatCombination;
    
    List<String>  searchTag(String tag,Integer year);
}
