package com.sinosoft.ie.booster.yearbook.graph.model.evetag;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * EveTag模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-07-21 10:53:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EveTagPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String id;

    /**
     * 主题名称
     */
    @ApiModelProperty(value = "主题名称")
    private String name;

    /**
     * 1常态主题;2动态主题
     */
    @ApiModelProperty(value = "1常态主题;2动态主题")
    private Integer category1;

    /**
     * 1科研类;2管理类
     */
    @ApiModelProperty(value = "1科研类;2管理类")
    private Integer category2;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Integer isdef;

    /**
     * 父主题名称
     */
    @ApiModelProperty(value = "父主题名称")
    private String pname;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String synopsis;

    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示")
    private String islab;
}