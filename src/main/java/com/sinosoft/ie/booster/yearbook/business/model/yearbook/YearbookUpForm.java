package com.sinosoft.ie.booster.yearbook.business.model.yearbook;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Yearbook模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 13:39:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class YearbookUpForm extends YearbookCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}