package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.text.DocumentException;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.visualdev.util.FileUtil;
import com.sinosoft.ie.booster.visualdev.util.UpUtil;
import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.sinosoft.ie.booster.yearbook.business.service.UploadfileService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * uploadfile
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:19:51
 */
@Slf4j
@RestController
@Api(tags = "uploadfile")
public class UploadfileController {

    @Autowired
    private UploadfileService uploadfileService;

	@Autowired
	private ProjectConfig projectConfig;


	/**
	 * @methodName：previewPdf
	 * @description：预览pdf
	 * @author：peas
	 * @date：2022/9/14 16:51
	 * @param：[id, type]
	 * @return：void
	 **/
	@GetMapping("/upload/pdf/{name}")
	public void previewPdf(@PathVariable("name") String name,HttpServletResponse response) throws IOException, DocumentException {
		uploadfileService.previewPdf(name,response);
	}

	/**
	 * 上传文件
	 *
	 * @return
	 */	@ApiOperation("上传文件" )
	@RequestMapping(method = {RequestMethod.POST}, value = "/upload")
	public R upload(@RequestParam("file") MultipartFile file)  {
		if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
			return R.failed("上传附件操作异常，附件为空，请重新上传");
		}
		String fileType = UpUtil.getFileType(file);
		JSONObject json = JSONUtil.createObj();
		try {
			String originalFilename = file.getOriginalFilename();
			String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
			String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
			FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);

			UploadfileEntity ent = new UploadfileEntity();
			ent.setNewName(fileName);
			ent.setOldName(originalFilename);
			ent.setPath(fileName);
			ent.setCreateTime(LocalDateTime.now());
			uploadfileService.save(ent);
//			json.set("oldName", originalFilename);
//			json.set("path",   fileName);
			json.set("newName", fileName);
			json.set("id", ent.getId());
		} catch (Exception e) {
			log.error("{}", e);
			return R.failed("上传失败:" + e);
		}
		return R.ok(json, "上传成功");
	}
	
	
	/**
	 * 上传
	 * @param file
	 * @param entity
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 * @throws IllegalArgumentException
	 * @throws IllegalAccessException
	 */
	@RequestMapping(method = {RequestMethod.POST}, value = "/uploadPicFile")
	public R uploadOrgPicFile(@RequestParam("file") MultipartFile file, OrganizationEntity entity) throws ServletException, IOException, IllegalArgumentException, IllegalAccessException {
		JSONObject json = JSONUtil.createObj();
		if (file == null || file.getName() == null) {
			return R.failed("上传附件操作异常，附件为空，请重新上传！");
		} else {
			try {
				// 校验附件名称
				if (file.getOriginalFilename().length() > 0) {
					String originalFilename = file.getOriginalFilename();
					String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
					String path = projectConfig.getUploadPath();
					String orgPath = path + ProjectConstant.imag_path;
					FileUtil.createDirs(orgPath);
					String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
					File targetFile = new File(orgPath, fileName);
					file.transferTo(targetFile);
					json.set("fname", fileName);
					if(ProjectConstant.img_suffix.contains(fileSuffixName.toLowerCase())){
						json.set("pic",ProjectConstant.imag_path+fileName);
					}
				}
			} catch (Exception e) {
				log.error("{}", e);
				return R.failed("上传失败:" + e);
			}
			return R.ok(json);
		}
	}

	/**
	 * 下载
	 * @param pic
	 * @throws IOException
	 */
	@GetMapping("/upload/img/{pic}")
	public void previewOrgPic(@PathVariable("pic") String pic,HttpServletResponse response) throws IOException {
		uploadfileService.previewPic(pic,response);
	}
	
	@GetMapping("/upload/image/{id}")
	public void previewImg(@PathVariable("id") String id,HttpServletResponse response) throws IOException, DocumentException {
		uploadfileService.previewImg(Long.valueOf(id),response);
	}
	/**
	 * 信息
	 *
	 * @param id
	 * @return
	 */
	@GetMapping("/upload/{id}")
	public R<UploadfileEntity> info(@PathVariable("id") Long id){
		UploadfileEntity entity= uploadfileService.getInfo(id);
		return R.ok(entity);
	}
}
