package com.sinosoft.ie.booster.yearbook.mark.model.everelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Everelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-02 10:13:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EverelationUpForm extends EverelationCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
}