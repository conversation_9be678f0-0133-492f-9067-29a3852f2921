package com.sinosoft.ie.booster.yearbook.business.model.converrules;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * ConverRules模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-15 21:23:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ConverRulesPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 本体id
     */
    @ApiModelProperty(value = "本体id")
    private Long ontoId;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String ontoName;

    /**
     * 属性值id
     */
    @ApiModelProperty(value = "属性值id")
    private Long categoryId;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String categoryVal;

    /**
     * 十个分类
     */
    @ApiModelProperty(value = "十个分类")
    private String type;
}