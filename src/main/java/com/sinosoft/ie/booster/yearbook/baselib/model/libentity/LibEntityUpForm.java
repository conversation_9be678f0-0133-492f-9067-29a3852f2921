package com.sinosoft.ie.booster.yearbook.baselib.model.libentity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * LibEntity模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-16 15:45:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class LibEntityUpForm extends LibEntityCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}