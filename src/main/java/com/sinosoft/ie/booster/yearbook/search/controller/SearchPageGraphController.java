package com.sinosoft.ie.booster.yearbook.search.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.search.service.SearchGraphService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequestMapping("/searchGraph")
@RestController
public class SearchPageGraphController {
	
	@Resource
	private SearchGraphService SearchGraphService;
    
    
    
    /**
     * 人物图谱
     */
    @GetMapping("/humanGraph")
    public R humanGraph(Long id){
        return SearchGraphService.humanGraph(id);
    }
    
    
    /**
     * 机构图谱
     */
    @GetMapping("/orgGraph")
    public R orgGraph(Long id){
        return SearchGraphService.orgGraph(id);
    }

    /**
     * 主题图谱
     */
    @GetMapping("/themeGraph")
    public R themeGraph(Long id){
        return SearchGraphService.themeGraph(id);
    }
    

}
