package com.sinosoft.ie.booster.yearbook.graph.service;

import com.sinosoft.ie.booster.common.core.util.R;

public interface AiGraphService {

    R humanGraph(String entityId,String startTime,String endTime);

    R humanGraphMore(String entityId, String ontoId);

    R humanGraphList(String entityId);

    R homepageOrgTree();

    R homepageOrgTreeFast();

    R homepageThemeTree();

    R humanGraphOntoList();

    R humanGraphRelationTime(String entityId);

    R humanGraphMoreRelationTime(String entityId, String ontoId);

    R humanGraphRelationTimeChart(String entityId);

    R themeGraph(String theme,String startTime,String endTime);

    R themeGraphRelationTime(String theme);

    R themeGraphRelationTimeChart(String theme);

    R themeGraphDetail(String theme);

    R themeList(String theme);

    R themeGraphOntoList();

    R getEntityDetailPerson(String entityId);

    R judgeTheme(String theme);

    R getEntityDetailOrg(String entityId);

    R orgGraph(String entityId);

    R orgGraphOntoList();

    R orgGraphRelationTime(String entityId);

    R orgGraphRelationTimeChart(String entityId);
}
