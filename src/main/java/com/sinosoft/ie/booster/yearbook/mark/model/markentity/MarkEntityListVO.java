package com.sinosoft.ie.booster.yearbook.mark.model.markentity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * MarkEntity模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-26 14:30:05
 */
@Data
@ApiModel
public class MarkEntityListVO  extends MarkEntityInfoVO{


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String id;
    
    /**
     * 实体/属性
     */
    @ApiModelProperty(value = "实体/属性")
    private String tagname;

    /**
     * 实体类型标注，lib_concept
     */
    @ApiModelProperty(value = "实体类型标注，lib_concept")
    private String tagId;
    
    /**
     * 如果是属性枚举值，则保存枚举的id
     */
    @ApiModelProperty(value = "如果是属性枚举值，则保存枚举的id")
    private String tagPid;
    
    /**
     * 句子内容
     */
    @ApiModelProperty(value = "句子内容")
    private String text;

    /**
     * 句子id
     */
    @ApiModelProperty(value = "句子id")
    private String sentenceid;
    
    /**
     * 实体类型标注
     */
    @ApiModelProperty(value = "实体类型标注")
    private String ontoname;

    /**
     * 开始索引
     */
    @ApiModelProperty(value = "开始索引")
    private Integer startindex;

    /**
     * 结束索引
     */
    @ApiModelProperty(value = "结束索引")
    private Integer endindex;

    /**
     * 标签类型；1实体；2属性3属性值
     */
    @ApiModelProperty(value = "标签类型；1实体；2属性3属性值")
    private Integer labType;

}