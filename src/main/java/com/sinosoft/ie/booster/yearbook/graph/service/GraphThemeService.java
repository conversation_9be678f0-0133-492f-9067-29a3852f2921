package com.sinosoft.ie.booster.yearbook.graph.service;

import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemePagination;
import java.util.*;

/**
 *
 * graph_theme
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 16:14:43
 */
public interface GraphThemeService extends IService<GraphThemeEntity> {

    List<GraphThemeEntity> getList(GraphThemePagination graphThemePagination);

    List<GraphThemeEntity> getTypeList(GraphThemePagination graphThemePagination,String dataType);



    GraphThemeEntity getInfo(Long id);

    void delete(GraphThemeEntity entity);

    void create(GraphThemeEntity entity);

    boolean update( Long id, GraphThemeEntity entity);
    
//  子表方法

    void insertGraphTheme();
}
