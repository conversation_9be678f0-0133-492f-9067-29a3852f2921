package com.sinosoft.ie.booster.yearbook.graph.model.graphtheme;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * GraphTheme模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 16:14:43
 */
@Data
@ApiModel
public class GraphThemeListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;


    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String theme;

    /**
     * 人物
     */
    @ApiModelProperty(value = "人物")
    private String persons;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    private String team;

    /**
     * 成果
     */
    @ApiModelProperty(value = "成果")
    private String achievements;

    /**
     * 物资
     */
    @ApiModelProperty(value = "物资")
    private String articles;

    /**
     * 荣誉
     */
    @ApiModelProperty(value = "荣誉")
    private String prize;

    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private String project;

    /**
     * 经费
     */
    @ApiModelProperty(value = "经费")
    private String funding;

}