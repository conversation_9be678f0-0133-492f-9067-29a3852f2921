package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValuePagination;
import java.util.*;

/**
 *
 * mate_attribute_value
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 17:52:49
 */
public interface MateAttributeValueService extends IService<MateAttributeValueEntity> {

    List<MateAttributeValueEntity> getList(MateAttributeValuePagination mateAttributeValuePagination);

    List<MateAttributeValueEntity> getTypeList(MateAttributeValuePagination mateAttributeValuePagination,String dataType);



    MateAttributeValueEntity getInfo(Long id);

    void delete(MateAttributeValueEntity entity);

    void create(MateAttributeValueEntity entity);

    boolean update( Long id, MateAttributeValueEntity entity);

    List<MateAttributeListVO> getAttributeValueList(String ontoId);

//  子表方法

    List<MateAttributeListVO> getEntityInsertValue(Long ontoId);
    List<MateAttributeValueEntity> getListByAttrId(Long attrId);

    List<MateAttributeValueEntity> getListByAttrIdAndName(Long attrId, String name);
}
