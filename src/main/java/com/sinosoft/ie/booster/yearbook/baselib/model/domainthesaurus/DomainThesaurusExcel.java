package com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * DomainThesaurus模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-14 15:44:58
 */
@Data
@ColumnWidth(30)
public class DomainThesaurusExcel implements Serializable {

    /**
     * 词汇名称
     */
    @ExcelProperty("词汇名称")
    private String name;

    /**
     * 类别,1:军语
     */
    @ExcelProperty("类别,1:军语")
    private String category;

    /**
     * 词性,1:名词,2:动词
     */
    @ExcelProperty("词性,1:名词,2:动词")
    private String pos;

    /**
     * 同义词(多词分号分割)
     */
    @ExcelProperty("同义词(多词分号分割)")
    private String synonym;

}