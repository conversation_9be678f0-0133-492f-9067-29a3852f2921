package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-16 15:45:57
 */
@Data
@TableName("lib_entity")
@ApiModel(value = "")
public class LibEntityEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 所属本体
     */
    @ApiModelProperty(value = "所属本体")
    private Long ontoId;

    /**
     * 属性数据
     */
    @ApiModelProperty(value = "属性数据")
    private String data;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer state;
    /**
     * 语料id
     */
    @ApiModelProperty(value = "语料id")
    private Long corpusId;
    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;
    
    /**
     * 消歧项
     */
    @ApiModelProperty(value = "消歧项")
    private String disambiguation;

    /**
     * 同义词
     */
    @ApiModelProperty(value = "同义词")
    private String synonymWord;
}
