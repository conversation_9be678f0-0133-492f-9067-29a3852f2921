package com.sinosoft.ie.booster.yearbook.business.model.filedirectory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * FileDirectory模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 14:28:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class FileDirectoryInfoVO extends FileDirectoryCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}