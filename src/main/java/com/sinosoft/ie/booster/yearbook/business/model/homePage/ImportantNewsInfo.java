package com.sinosoft.ie.booster.yearbook.business.model.homePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：ImportantNewsInfo.java
 * @description：
 * @author：peas
 * @data：2022/11/9 18:09
 **/
@Data
public class ImportantNewsInfo {

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String date;
}
