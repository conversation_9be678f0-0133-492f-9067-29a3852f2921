package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptPagination;
import java.util.*;

/**
 *
 * 本体概念类
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-30 09:58:21
 */
public interface LibConceptService extends IService<LibConceptEntity> {

    List<LibConceptEntity> getList(LibConceptPagination libConceptPagination);

    List<LibConceptEntity> getTypeList(LibConceptPagination libConceptPagination,String dataType);



    LibConceptEntity getInfo(Long id);

    void delete(LibConceptEntity entity);

    void create(LibConceptEntity entity);

    boolean update( Long id, LibConceptEntity entity);
    
//  子表方法
    
    //递归查询一个本体下的所有节点
    List<LibConceptEntity>  getAllSubNode(Long id);

    void deleteChild(Long id);
}
