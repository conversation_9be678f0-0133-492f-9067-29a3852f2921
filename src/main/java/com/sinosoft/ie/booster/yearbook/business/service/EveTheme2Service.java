package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.EveTheme2Entity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2Pagination;
import java.util.*;

/**
 *
 * eve_theme2
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 10:45:23
 */
public interface EveTheme2Service extends IService<EveTheme2Entity> {

    List<EveTheme2Entity> getList(EveTheme2Pagination eveTheme2Pagination);

    List<EveTheme2Entity> getTypeList(EveTheme2Pagination eveTheme2Pagination,String dataType);



    EveTheme2Entity getInfo(Long id);

    void delete(EveTheme2Entity entity);

    void create(EveTheme2Entity entity);

    boolean update( Long id, EveTheme2Entity entity);
    
//  子表方法
}
