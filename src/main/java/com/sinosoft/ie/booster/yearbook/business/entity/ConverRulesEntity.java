package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-15 21:23:42
 */
@Data
@TableName("conver_rules")
@ApiModel(value = "")
public class ConverRulesEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 本体id
     */
    @ApiModelProperty(value = "本体id")
    private Long ontoId;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String ontoName;

    /**
     * 属性值id
     */
    @ApiModelProperty(value = "属性值id")
    private Long categoryId;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String categoryVal;

    /**
     * 十个分类
     */
    @ApiModelProperty(value = "十个分类")
    private String type;
}
