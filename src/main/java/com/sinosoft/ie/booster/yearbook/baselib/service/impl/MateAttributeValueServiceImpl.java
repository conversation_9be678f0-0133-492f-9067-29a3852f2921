package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValueListVO;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattributevalue.MateAttributeValuePagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * mate_attribute_value
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 17:52:49
 */
@Service
public class MateAttributeValueServiceImpl extends ServiceImpl<MateAttributeValueMapper, MateAttributeValueEntity> implements MateAttributeValueService {

    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;

    @Override
    public List<MateAttributeValueEntity> getList(MateAttributeValuePagination mateAttributeValuePagination){
        QueryWrapper<MateAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getId,mateAttributeValuePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getAttributeId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getAttributeId,mateAttributeValuePagination.getAttributeId()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getValName()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getValName,mateAttributeValuePagination.getValName()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getPid,mateAttributeValuePagination.getPid()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getOntoId,mateAttributeValuePagination.getOntoId()));
        }

        //排序
        if(StrUtil.isEmpty(mateAttributeValuePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateAttributeValueEntity::getId);
        }else{
            queryWrapper="asc".equals(mateAttributeValuePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateAttributeValuePagination.getSidx()):queryWrapper.orderByDesc(mateAttributeValuePagination.getSidx());
        }
        if(mateAttributeValuePagination.getCurrentPage()==0) {
        	List<MateAttributeValueEntity> list  = this.list(queryWrapper);
        	return list;
        }
        Page<MateAttributeValueEntity> page=new Page<>(mateAttributeValuePagination.getCurrentPage(), mateAttributeValuePagination.getPageSize());
        IPage<MateAttributeValueEntity> userIPage=this.page(page,queryWrapper);
        return mateAttributeValuePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MateAttributeValueEntity> getTypeList(MateAttributeValuePagination mateAttributeValuePagination,String dataType){
        QueryWrapper<MateAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getId,mateAttributeValuePagination.getId()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getAttributeId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getAttributeId,mateAttributeValuePagination.getAttributeId()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getValName()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getValName,mateAttributeValuePagination.getValName()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getPid,mateAttributeValuePagination.getPid()));
        }

        if(!"null".equals(String.valueOf(mateAttributeValuePagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getOntoId,mateAttributeValuePagination.getOntoId()));
        }

        //排序
        if(StrUtil.isEmpty(mateAttributeValuePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MateAttributeValueEntity::getId);
        }else{
            queryWrapper="asc".equals(mateAttributeValuePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(mateAttributeValuePagination.getSidx()):queryWrapper.orderByDesc(mateAttributeValuePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MateAttributeValueEntity> page=new Page<>(mateAttributeValuePagination.getCurrentPage(), mateAttributeValuePagination.getPageSize());
            IPage<MateAttributeValueEntity> userIPage=this.page(page,queryWrapper);
            return mateAttributeValuePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MateAttributeValueEntity getInfo(Long id){
        QueryWrapper<MateAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MateAttributeValueEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MateAttributeValueEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MateAttributeValueEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public List<MateAttributeListVO> getAttributeValueList(String ontoId) {
        return mateAttributeValueMapper.getAttributeValueList(ontoId);
    }

    @Override
    public void delete(MateAttributeValueEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public List<MateAttributeValueEntity> getListByAttrId(Long attrId) {
		QueryWrapper<MateAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getAttributeId,attrId));

		return  this.list(queryWrapper);
	}
    public List<MateAttributeListVO> getEntityInsertValue(Long ontoId){
        List<MateAttributeListVO> result = new ArrayList<>();
        List<MateAttributeListVO> vos = mateAttributeValueMapper.getEntityInsertValue(ontoId);
        Map<String, List<MateAttributeListVO>> collect =
                vos.stream().collect(Collectors.groupingBy(MateAttributeListVO::getId));
        collect.forEach((k,v)->{
            MateAttributeListVO vo = JsonUtil.getJsonToBean(v.get(0), MateAttributeListVO.class);
            if (v.get(0).getType() == 2){ 	
                vo.setChild(JsonUtil.getJsonToList(v,MateAttributeValueListVO.class));
            }
            vo.setPid(null);
            vo.setValName(null);
            result.add(vo);
        });
        return result;
    }

    @Override
    public List<MateAttributeValueEntity> getListByAttrIdAndName(Long attrId, String name) {
        QueryWrapper<MateAttributeValueEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().and(t->t.eq(MateAttributeValueEntity::getAttributeId,attrId));
        queryWrapper.lambda().and(t->t.like(MateAttributeValueEntity::getValName,name));
        return  this.list(queryWrapper);
    }
}