//package com.sinosoft.ie.booster.yearbook.business.graph.service.impl;
//
//import com.sinosoft.ie.booster.yearbook.business.graph.service.GraphDbQueryService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.data.neo4j.core.Neo4jClient;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@Service
//public class Neo4jGraphDbQueryServiceImpl implements GraphDbQueryService {
//
//    private final Neo4jClient neo4jClient;
//
//    public Neo4jGraphDbQueryServiceImpl(Neo4jClient neo4jClient) {
//        this.neo4jClient = neo4jClient;
//    }
//
//    @Override
//    public List<String> queryAllNodeLabel() {
//        String cypher = "MATCH (n) WITH DISTINCT labels(n) AS labels UNWIND labels AS label RETURN DISTINCT label ORDER BY label";
//        log.info("cypher: {}", cypher);
//        Collection<Map<String, Object>> result = neo4jClient.query(cypher).fetch().all();
//        List<String> labelList = new ArrayList<>();
//        for (Map<String, Object> map : result) {
//            labelList.add((String) map.get("label"));
//        }
//        return labelList;
//    }
//}