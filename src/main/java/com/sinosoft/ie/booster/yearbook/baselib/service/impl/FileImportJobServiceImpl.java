package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportJobEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.FileImportJobMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.FileImportJobService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportjob.FileImportJobPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * file_import_job
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-08 15:13:22
 */
@Service
public class FileImportJobServiceImpl extends ServiceImpl<FileImportJobMapper, FileImportJobEntity> implements FileImportJobService {


    @Override
    public List<FileImportJobEntity> getList(FileImportJobPagination fileImportJobPagination){
        QueryWrapper<FileImportJobEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(fileImportJobPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getId,fileImportJobPagination.getId()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getFileName()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getFileName,fileImportJobPagination.getFileName()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getFileType()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getFileType,fileImportJobPagination.getFileType()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getFileSize()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getFileSize,fileImportJobPagination.getFileSize()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getJobType()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getJobType,fileImportJobPagination.getJobType()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getJobState()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getJobState,fileImportJobPagination.getJobState()));
        }


        if(!"null".equals(String.valueOf(fileImportJobPagination.getCreateUser()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getCreateUser,fileImportJobPagination.getCreateUser()));
        }

        //排序
        if(StrUtil.isEmpty(fileImportJobPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FileImportJobEntity::getId);
        }else{
            queryWrapper="asc".equals(fileImportJobPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(fileImportJobPagination.getSidx()):queryWrapper.orderByDesc(fileImportJobPagination.getSidx());
        }
        Page<FileImportJobEntity> page=new Page<>(fileImportJobPagination.getCurrentPage(), fileImportJobPagination.getPageSize());
        IPage<FileImportJobEntity> userIPage=this.page(page,queryWrapper);
        return fileImportJobPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<FileImportJobEntity> getTypeList(FileImportJobPagination fileImportJobPagination,String dataType){
        QueryWrapper<FileImportJobEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(fileImportJobPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getId,fileImportJobPagination.getId()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getFileName()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getFileName,fileImportJobPagination.getFileName()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getFileType()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getFileType,fileImportJobPagination.getFileType()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getFileSize()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getFileSize,fileImportJobPagination.getFileSize()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getJobType()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getJobType,fileImportJobPagination.getJobType()));
        }


        if(!"null".equals(String.valueOf(fileImportJobPagination.getJobState()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getJobState,fileImportJobPagination.getJobState()));
        }

        if(!"null".equals(String.valueOf(fileImportJobPagination.getCreateUser()))){
            queryWrapper.lambda().and(t->t.like(FileImportJobEntity::getCreateUser,fileImportJobPagination.getCreateUser()));
        }

        //排序
        if(StrUtil.isEmpty(fileImportJobPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FileImportJobEntity::getId);
        }else{
            queryWrapper="asc".equals(fileImportJobPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(fileImportJobPagination.getSidx()):queryWrapper.orderByDesc(fileImportJobPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<FileImportJobEntity> page=new Page<>(fileImportJobPagination.getCurrentPage(), fileImportJobPagination.getPageSize());
            IPage<FileImportJobEntity> userIPage=this.page(page,queryWrapper);
            return fileImportJobPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public FileImportJobEntity getInfo(Long id){
        QueryWrapper<FileImportJobEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(FileImportJobEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(FileImportJobEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, FileImportJobEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(FileImportJobEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}