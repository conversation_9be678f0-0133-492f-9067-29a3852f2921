package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class DownController {
	@Autowired
	private HttpServletResponse response;
	@Autowired
	private ProjectConfig projectConfig;
	
	@GetMapping("/downfile/{name}")
	public void  downFile(@PathVariable("name") String name) throws IOException {
		
		File f = new File(projectConfig.getDownPath()+File.separator+name);
		if (!f.exists()) {
			response.sendError(404, "File not found!");
			return;
		}
		BufferedInputStream br = new BufferedInputStream(new FileInputStream(f));
		byte[] bs = new byte[1024];
		int len = 0;

		response.reset(); // 非常重要
		// 纯下载方式
		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment;filename=" + name);
		OutputStream out = response.getOutputStream();
		while ((len = br.read(bs)) > 0) {
			out.write(bs, 0, len);
		}
		out.flush();
		out.close();
		br.close();
		
		
		
	}
	

}
