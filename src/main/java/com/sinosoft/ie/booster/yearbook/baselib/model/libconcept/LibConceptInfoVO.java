package com.sinosoft.ie.booster.yearbook.baselib.model.libconcept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * LibConcept模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-07 19:44:28
 */
@Data
@ApiModel
public class LibConceptInfoVO {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 概念名称
     */
    @ApiModelProperty(value = "概念名称")
    private String name;

    /**
     * 概念的父级id
     */
    @ApiModelProperty(value = "概念的父级id")
    private Long pid;

    /**
     * 同义词，多个用分号隔开
     */
    @ApiModelProperty(value = "同义词，多个用分号隔开")
    private String synonymWord;

    /**
     * 唯一标识
     */
    @ApiModelProperty(value = "唯一标识")
    private String 

uniqueid;

    /**
     * 消歧项
     */
    @ApiModelProperty(value = "消歧项")
    private String disambiguation;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private Integer score;

    /**
     * 图片id;uploadfile
     */
    @ApiModelProperty(value = "图片id;uploadfile")
    private Long imgid;

    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;
}