package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.OrganizationMapper;
import com.sinosoft.ie.booster.yearbook.business.service.OrganizationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationPagination;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 *
 * organization
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-14 10:08:32
 */
@Service
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationEntity> implements OrganizationService {

	@Autowired
	private HttpServletResponse response;

	@Autowired
	private ProjectConfig projectConfig;

    @Override
    public List<OrganizationEntity> getList(OrganizationPagination organizationPagination){
        QueryWrapper<OrganizationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(organizationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getId,organizationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(organizationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getName,organizationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(organizationPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getPid,organizationPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(organizationPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getDelFlag,organizationPagination.getDelFlag()));
        }

        queryWrapper.lambda().orderByAsc(OrganizationEntity::getSortNum);
        Page<OrganizationEntity> page=new Page<>(organizationPagination.getCurrentPage(), organizationPagination.getPageSize());
        IPage<OrganizationEntity> userIPage=this.page(page,queryWrapper);
        return organizationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<OrganizationEntity> getTypeList(OrganizationPagination organizationPagination,String dataType){
        QueryWrapper<OrganizationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(organizationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getId,organizationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(organizationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getName,organizationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(organizationPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getPid,organizationPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(organizationPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(OrganizationEntity::getDelFlag,organizationPagination.getDelFlag()));
        }

        //排序
        if(StrUtil.isEmpty(organizationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(OrganizationEntity::getId);
        }else{
            queryWrapper="asc".equals(organizationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(organizationPagination.getSidx()):queryWrapper.orderByDesc(organizationPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<OrganizationEntity> page=new Page<>(organizationPagination.getCurrentPage(), organizationPagination.getPageSize());
            IPage<OrganizationEntity> userIPage=this.page(page,queryWrapper);
            return organizationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public OrganizationEntity getInfo(Long id){
        QueryWrapper<OrganizationEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(OrganizationEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(OrganizationEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, OrganizationEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(OrganizationEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public void previewOrgPic(String pic) throws IOException {


		System.out.println("filePath:" + pic);
		File f = new File(projectConfig.getUploadPath() + ProjectConstant.imag_path + pic);
		if (!f.exists()) {
			response.sendError(404, "File not found!");
			return;
		}
		BufferedInputStream br = new BufferedInputStream(new FileInputStream(f));
		byte[] bs = new byte[1024];
		int len = 0;

		String n = pic.replace(projectConfig.getUploadPath(), "");

		response.reset(); // 非常重要
		// 纯下载方式
		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment;filename=" + n);
		OutputStream out = response.getOutputStream();
		while ((len = br.read(bs)) > 0) {
			out.write(bs, 0, len);
		}
		out.flush();
		out.close();
		br.close();
	}

	public List<OrganizationEntity> showOrgTree(OrganizationPagination organizationPagination){
		//取第一级
		QueryWrapper<OrganizationEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.lambda().and(t->t.eq(OrganizationEntity::getPid,0L));
		//排序
		queryWrapper.lambda().orderByAsc(OrganizationEntity::getSortNum);
		List<OrganizationEntity> list =this.list(queryWrapper);

		if (list != null && list.size() > 0){
			for (int i=0; i<list.size(); i++){
				//取第二级
				QueryWrapper<OrganizationEntity> queryWrapperSon=new QueryWrapper<>();
				int finalI = i;
				queryWrapperSon.lambda().and(t->t.eq(OrganizationEntity::getPid,list.get(finalI).getId()));
				//排序
				queryWrapperSon.lambda().orderByAsc(OrganizationEntity::getSortNum);
				List<OrganizationEntity> userIPageSon=this.list(queryWrapperSon);
				if (userIPageSon != null && userIPageSon.size() > 0){
					for (int j=0; j<userIPageSon.size(); j++){
						//取第三级
						QueryWrapper<OrganizationEntity> queryWrapperSon1=new QueryWrapper<>();
						int finalJ = j;
						queryWrapperSon1.lambda().and(t->t.eq(OrganizationEntity::getPid,userIPageSon.get(finalJ).getId()));
						//排序
						queryWrapperSon1.lambda().orderByAsc(OrganizationEntity::getId);
						List<OrganizationEntity> userIPageSon1=this.list(queryWrapperSon1);
						userIPageSon.get(finalJ).setSonList(userIPageSon1);
					}
				}
				list.get(finalI).setSonList(userIPageSon);
			}
		}
		return list;
	}
	@Override
	public List<OrganizationEntity> queryRotationPage() {
		QueryWrapper<OrganizationEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.lambda().and(t->t.eq(OrganizationEntity::getRotationPage,1));
		//排序
		queryWrapper.lambda().orderByAsc(OrganizationEntity::getSortNum);
		List<OrganizationEntity> list =this.list(queryWrapper);
		return list;
	}
	@Override
	public List<OrganizationEntity> queryTagPage() {
		QueryWrapper<OrganizationEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.lambda().and(t->t.eq(OrganizationEntity::getTagPage,1));
		//排序
		queryWrapper.lambda().orderByAsc(OrganizationEntity::getSortNum);
		List<OrganizationEntity> list =this.list(queryWrapper);
		return list;
	}
}