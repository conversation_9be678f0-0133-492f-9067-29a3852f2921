package com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * MetaEntityRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:34:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MetaEntityRelationInfoVO extends MetaEntityRelationCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    private String relationName;
    private String onto1Name;
    private String onto2Name;
    private String entity1Name;
    private String entity2Name;
    private Integer vals;
}