package com.sinosoft.ie.booster.yearbook.graph.controller;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.graph.service.AiGraph1Service;
import com.sinosoft.ie.booster.yearbook.graph.service.AiGraphService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
//@CrossOrigin(origins = "*")
@Api(tags = "aiGraph")
@RequestMapping("/aiGraph")

public class AiGraphController {

    @Resource
    private AiGraphService aiGraphService;

    @Resource
    private AiGraph1Service aiGraph1Service;

    /**
     * 人物图谱
     */
    @GetMapping("/humanGraph")
    public R humanGraph(String entityId){
        return R.ok(aiGraph1Service.humanGraph(entityId));
    }

    /**
     * 人物图谱-点击实体扩展
     */
    @GetMapping("/humanGraphMore")
    public R humanGraphMore(String entityId, String ontoId){
        return R.ok(aiGraphService.humanGraphMore(entityId,ontoId));
    }

    /**
     * 人物图谱-点击实体扩展-关系时序
     */
    @GetMapping("/humanGraphMoreRelationTime")
    public R humanGraphMoreRelationTime(String entityId, String ontoId){
        return aiGraphService.humanGraphMoreRelationTime(entityId,ontoId);
    }

    /**
     * 人物图谱-图谱列表
     */
    @GetMapping("/humanGraphList")
    public R humanGraphList(String entityId){
        return R.ok(aiGraphService.humanGraphList(entityId));
    }

    /**
     * 首页-机构+人物+主题
     */
    @GetMapping("/homepageOrgTree")
    public R homepageOrgTree(){
        return R.ok(aiGraphService.homepageOrgTreeFast());
    }

    /**
     * 首页-动态主题
     */
    @GetMapping("/homepageThemeTree")
//    @CrossOrigin(origins = "*")
    public R homepageThemeTree(){
        return R.ok(aiGraphService.homepageThemeTree());
    }

    /**
     * 人物图谱-本体列表
     */
    @GetMapping("/humanGraphOntoList")
    public R humanGraphOntoList(){
        return R.ok(aiGraphService.humanGraphOntoList());
    }

    /**
     * 人物图谱-关系时序
     */
    @GetMapping("/humanGraphRelationTime")
    public R humanGraphRelationTime(String entityId){
        return R.ok(aiGraph1Service.humanGraphRelationTime(entityId));
    }

    /**
     * 人物图谱-关系时序-时间轴
     */
    @GetMapping("/humanGraphRelationTimeChart")
    public R humanGraphRelationTimeChart(String entityId){
        return R.ok(aiGraph1Service.humanGraphRelationTimeChartNew(entityId));
    }

    /**
     * 自定义主题图谱
     */
    @GetMapping("/themeGraph")
    public R themeGraph(String theme){
        return R.ok(aiGraph1Service.themeGraph(theme));
    }

    /**
     * 自定义图谱-关系时序
     */
    @GetMapping("/themeGraphRelationTime")
    public R themeGraphRelationTime(String theme){
        return R.ok(aiGraph1Service.themeGraphRelationTime(theme));
    }

    /**
     * 自定义图谱-关系时序-时间轴
     */
    @GetMapping("/themeGraphRelationTimeChart")
    public R themeGraphRelationTimeChart(String theme){
        return R.ok(aiGraph1Service.themeGraphRelationTimeChartNew(theme));
    }

    /**
     * 主题图谱-主题详情
     */
    @GetMapping("/themeGraphDetail")
    public R themeGraphDetail(String theme){
        return R.ok(aiGraphService.themeGraphDetail(theme));
    }

    /**
     * 自定义主题列表
     */
    @GetMapping("/themeList")
    public R themeList(String theme){
        return R.ok(aiGraphService.themeList(theme));
    }

    /**
     * 主题图谱-本体列表
     */
    @GetMapping("/themeGraphOntoList")
    public R themeGraphOntoList(){
        return R.ok(aiGraphService.themeGraphOntoList());
    }

    /**
     * 查询人物实体详情
     */
    @GetMapping("/getEntityDetailPerson")
    public R  getEntityDetailPerson(String id) {
        return R.ok(aiGraphService.getEntityDetailPerson(id));
    }

    /**
     * 判断自定义主题是否在theme表中
     */
    @GetMapping("/judgeTheme")
    public R  judgeTheme(String theme) {
        return aiGraphService.judgeTheme(theme);
    }

    /**
     * 查询机构实体详情
     */
    @GetMapping("/getEntityDetailOrg")
    public R  getEntityDetailOrg(String id) {
        return R.ok(aiGraphService.getEntityDetailOrg(id));
    }

    /**
     * 机构图谱
     */
    @GetMapping("/orgGraph")
    public R orgGraph(String entityId){
        return aiGraph1Service.orgGraph(entityId);
    }

    /**
     * 机构图谱-本体列表
     */
    @GetMapping("/orgGraphOntoList")
    public R orgGraphOntoList(){
        return aiGraphService.orgGraphOntoList();
    }

    /**
     * 机构图谱-关系时序
     */
    @GetMapping("/orgGraphRelationTime")
    public R orgGraphRelationTime(String entityId){
        return aiGraph1Service.orgGraphRelationTime(entityId);
    }

    /**
     * 机构图谱-关系时序-时间轴
     */
    @GetMapping("/orgGraphRelationTimeChart")
    public R orgGraphRelationTimeChart(String entityId){
        return aiGraph1Service.orgGraphRelationTimeChartNew(entityId);
    }
}
