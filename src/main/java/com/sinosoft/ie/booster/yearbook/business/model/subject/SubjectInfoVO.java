package com.sinosoft.ie.booster.yearbook.business.model.subject;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * Subject模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 14:03:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class SubjectInfoVO extends SubjectCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}