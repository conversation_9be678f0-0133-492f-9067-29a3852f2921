package com.sinosoft.ie.booster.yearbook.graph.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.constant.YearbookLibConstant;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveThemeMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import com.sinosoft.ie.booster.yearbook.graph.entity.EveTagEntity;
import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.sinosoft.ie.booster.yearbook.graph.mapper.EveTagMapper;
import com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeEventMapper;
import com.sinosoft.ie.booster.yearbook.graph.service.AiGraphService;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.job.EsDateHandle;
import com.sinosoft.ie.booster.yearbook.search.service.SearchGraphService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

@Service
public class AiGraphServiceImpl implements AiGraphService {

    @Resource
    private MetaEntityRelationMapper metaEntityRelationMapper;

    @Resource
    private LibEntityMapper libEntityMapper;

    @Resource
    private EveThemeMapper eveThemeMapper;

    @Resource
    private GraphThemeEventMapper graphThemeEventMapper;

    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;

    @Resource
    private ResearchTagMapper researchTagMapper;

    @Resource
    private EveTagMapper eveTagMapper;
    
//    public R humanGraph(String entityId){
//        Map<String,Object> retrunMap = new HashMap<>();
//        List<Map<String,Object>> nodesList = new ArrayList<>();
//        List<Map<String,String>> linksList = new ArrayList<>();
//        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
//        //查询本体节点
//        Map<String, Object> nodesMap = new HashMap<>();
//        nodesMap.put("id",entityId);
//        nodesMap.put("name",libEntity.getName());
//        nodesList.add(nodesMap);
//        List<Map<String, Object>> nodesOntoList = metaEntityRelationMapper.aiGraphGetOntoByEntityId(entityId);
//        nodesList.addAll(nodesOntoList);
//        for (Map<String, Object> ontoMap:nodesOntoList) {
//            Map<String,String> querymap = new HashMap<>();
//            querymap.put("ontoId",ontoMap.get("id").toString());
//            querymap.put("entityId",entityId);
//            //查询实体节点
//            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEntityIdAndOntoid(querymap);
//            nodesList.addAll(nodesEntityList);
//
//            //关系
//            List<Map<String, String>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEntityIdAndOntoid(querymap);
//            linksList.addAll(linksRelationList);
//
//            //到本体的links
//            Map<String,String> linksmap = new HashMap<>();
//            linksmap.put("source",entityId);
//            linksmap.put("target",ontoMap.get("id").toString());
//            linksmap.put("value",linksRelationList.get(0).get("value"));
//            linksList.add(linksmap);
//        }
//        retrunMap.put("nodes",nodesList);
//        retrunMap.put("links",linksList);
//        return R.ok(retrunMap);
//    }

    public R humanGraph(String entityId,String startTime,String endTime){
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,String>> linksList = new ArrayList<>();
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        //查询本身节点
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",entityId);
        nodesMap.put("name",libEntity.getName());
        nodesMap.put("onto",libEntity.getOntoId());
        if (libEntity.getOntoId().equals(YearbookConstant.MARK_PER_ONTO_ID)){
            if (libEntity.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
                nodesMap.put("image_ids",stringObjectMap.get("image_ids"));
            }
        }

        nodesList.add(nodesMap);
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
//        nodesList.addAll(nodesOntoList);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("entityId",entityId);
            if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)){
                querymap.put("startTime",startTime);
                querymap.put("endTime",endTime);
            }
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEntityIdAndOntoid(querymap);
            if(nodesEntityList.size() == 0){continue;}
            nodesList.addAll(nodesEntityList);
            nodesList.add(ontoMap);
            //关系
            List<Map<String, String>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEntityIdAndOntoid(querymap);
            for (Map<String, String> linkrelation:linksRelationList) {
                if (linkrelation.get("pageNumber") == null || linkrelation.get("pageNumber").isEmpty()){continue;}
                MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById(linkrelation.get("pageNumber"));
                if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                    linkrelation.put("pageNumber","");
                }else {
                    linkrelation.put("pageNumber",mateAttributeValue.getValName());
                }
            }
            linksList.addAll(linksRelationList);


//            String relationName = "";
//            if (linksRelationList.size() > 0){
//                relationName = linksRelationList.get(0).get("value");
//            }
            //到本体的links
            Map<String,String> linksmap = new HashMap<>();
            linksmap.put("source",entityId);
            linksmap.put("target",ontoMap.get("id").toString());
            linksmap.put("value","");
            linksmap.put("eventid","");
            linksList.add(linksmap);
        }
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        retrunMap.put("nodes",nodesList);
        retrunMap.put("links",linksList);
        return R.ok(retrunMap);
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
    public R humanGraphMore(String entityId, String ontoId){
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();

        Map<String,String> querymap = new HashMap<>();
        querymap.put("entityId",entityId);
        querymap.put("ontoId",ontoId);
        //查询实体节点
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEntityIdAndOntoid(querymap);
        nodesList.addAll(nodesEntityList);

        //关系
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEntityId(querymap);
        linksList.addAll(linksRelationList);
        linksList = linkOnlyOne(linksList);
        retrunMap.put("nodes",nodesList);
        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
        return R.ok(retrunMap);
    }

    @Override
    public R humanGraphList(String entityId){
        List<Map<String, Object>> nodesOntoList = metaEntityRelationMapper.aiGraphGetOntoByEntityId(entityId);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("entityId",entityId);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEntityIdAndOntoid(querymap);
            ontoMap.put("data",nodesEntityList);
        }
        return R.ok(nodesOntoList);
    }

    @Override
    public R homepageOrgTree(){

        //机构，父级
        Map<String,Object> mapP = new HashMap<>();
        QueryWrapper<LibEntityEntity> orgQueryP = new QueryWrapper<>();
        orgQueryP.eq("onto_id",YearbookConstant.MARK_ORG_ONTO_ID);
        orgQueryP.lambda().and(t->t.eq(LibEntityEntity::getName,"军事科学院"));
        List<LibEntityEntity> orgPlist = libEntityMapper.selectList(orgQueryP);
        mapP.put("id",orgPlist.get(0).getId());
        mapP.put("name",orgPlist.get(0).getName());
        if (orgPlist.get(0).getData() != null){
            Map<String, Object> stringObjectMap = JsonUtil.stringToMap(orgPlist.get(0).getData());
            mapP.put("pic",stringObjectMap.get("icon"));
        }


        //相关人物
        Map<String,Object> personQueryP = new HashMap<>();
        personQueryP.put("orgId",orgPlist.get(0).getId());
        personQueryP.put("ontoId",YearbookLibConstant.PERSON_ONTO_ID);
        List<Map> libEntityListPperson = libEntityMapper.orgPersonList(personQueryP);
        mapP.put("person",libEntityListPperson);
        //相关主题
//        QueryWrapper<LibEntityEntity> eventQueryP = new QueryWrapper<>();
//        eventQueryP.select("json_value(data,'$.theme') as name");
//        eventQueryP.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
//        eventQueryP.apply("json_value(data,'$.org_ids')= "+orgPlist.get(0).getId()+"");
//        eventQueryP.groupBy("json_value(data,'$.theme')");
//        List<LibEntityEntity> libEntityListPevent = libEntityMapper.selectList(eventQueryP);

        mapP.put("theme",new ArrayList<>());
        //机构，子级
        QueryWrapper<LibEntityEntity> orgQueryC = new QueryWrapper<>();
        orgQueryC.eq("onto_id",YearbookConstant.MARK_ORG_ONTO_ID);
        orgQueryC.apply("json_value(data,'$.pid')= "+orgPlist.get(0).getId()+"");
        List<LibEntityEntity> orgClist = libEntityMapper.selectList(orgQueryC);

        List<Map<String,Object>> childList = new ArrayList<>();
        for (LibEntityEntity org:orgClist) {
            Map<String,Object> mapC = new HashMap<>();
            mapC.put("id",org.getId());
            mapC.put("name",org.getSynonymWord());
            if (org.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(org.getData());
                mapC.put("pic",stringObjectMap.get("icon"));
            }
            //相关人物
            Map<String,Object> personQueryC = new HashMap<>();
            personQueryC.put("orgId",org.getId());
            personQueryC.put("ontoId",YearbookLibConstant.PERSON_ONTO_ID);
            List<Map> libEntityListC = libEntityMapper.orgPersonList(personQueryC);
            mapC.put("person",libEntityListC);
            //相关主题
            if (org.getName().equals("院机关")){
                List<String> orgyjgList = new ArrayList<>();
                orgyjgList.add("办公室");
                orgyjgList.add("科研部");
                orgyjgList.add("政治工作部");
                orgyjgList.add("管理保障部");
                QueryWrapper<LibEntityEntity> orgyjgquery = new QueryWrapper<>();
                orgyjgquery.in("name",orgyjgList);
                orgyjgquery.eq("onto_id",YearbookConstant.MARK_ORG_ONTO_ID);
                List<LibEntityEntity> orgyjgEntityList = libEntityMapper.selectList(orgyjgquery);
                String orgyjgIdList = "";
                for (LibEntityEntity orgEntity:orgyjgEntityList) {
                    if (!orgyjgIdList.isEmpty()){
                        orgyjgIdList += ",";
                    }
                    orgyjgIdList += orgEntity.getId();
                }
                if (orgyjgIdList.isEmpty()){
                    mapC.put("theme",new ArrayList<>());
                    childList.add(mapC);
                    continue;
                }

                QueryWrapper<LibEntityEntity> eventQueryC = new QueryWrapper<>();
                eventQueryC.select("json_value(data,'$.theme') as name");
                eventQueryC.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
                eventQueryC.isNotNull("json_value(data,'$.theme')");
                eventQueryC.apply("json_value(data,'$.org_ids') in ("+orgyjgIdList+")");
                eventQueryC.groupBy("json_value(data,'$.theme')");
                List<LibEntityEntity> eventListC = libEntityMapper.selectList(eventQueryC);
                mapC.put("theme",eventListC);
                childList.add(mapC);
            }else {
                QueryWrapper<LibEntityEntity> eventQueryC = new QueryWrapper<>();
                eventQueryC.select("json_value(data,'$.theme') as name");
                eventQueryC.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
                eventQueryC.isNotNull("json_value(data,'$.theme')");
                eventQueryC.apply("json_value(data,'$.org_ids')= "+org.getId()+"");
                eventQueryC.groupBy("json_value(data,'$.theme')");
                List<LibEntityEntity> eventListC = libEntityMapper.selectList(eventQueryC);
                mapC.put("theme",eventListC);
                childList.add(mapC);
            }
        }
        mapP.put("childList",childList);
        return R.ok(mapP);
    }

    @Override
    public R homepageOrgTreeFast() {
        // 机构，父级
        Map<String, Object> mapP = new HashMap<>();
        QueryWrapper<LibEntityEntity> orgQueryP = new QueryWrapper<>();
        orgQueryP.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
        orgQueryP.lambda().and(t -> t.eq(LibEntityEntity::getName, "军事科学院"));
        List<LibEntityEntity> orgPlist = libEntityMapper.selectList(orgQueryP);

        if (orgPlist != null && !orgPlist.isEmpty()) {
            mapP.put("id", orgPlist.get(0).getId());
            mapP.put("name", orgPlist.get(0).getName());
            if (orgPlist.get(0).getData() != null) {
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(orgPlist.get(0).getData());
                mapP.put("pic", stringObjectMap.get("icon"));
            }
        } else {
            // 处理没有数据的情况，例如记录日志或者返回默认值
            mapP.put("id", null);
            mapP.put("name", null);
            mapP.put("pic", null);
            return R.ok(mapP); // 如果父级机构为空，直接返回
        }

        // 机构，子级
        QueryWrapper<LibEntityEntity> orgQueryC = new QueryWrapper<>();
        orgQueryC.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
        orgQueryC.apply("json_value(data,'$.pid') = " + orgPlist.get(0).getId());
        orgQueryC.orderByAsc("score");
        List<LibEntityEntity> orgClist = libEntityMapper.selectList(orgQueryC);

        List<Long> orgids = new ArrayList<>();
        orgids.add(orgPlist.get(0).getId());
        if (orgClist != null && !orgClist.isEmpty()) {
            for (LibEntityEntity org : orgClist) {
                orgids.add(org.getId());
            }
        }

        String orgidsStr = orgids.stream().map(String::valueOf).collect(Collectors.joining(","));

        // 院机关的机构ids
        List<Long> orgidsyjg = new ArrayList<>();
        if (orgClist != null && !orgClist.isEmpty()) {
            for (LibEntityEntity org : orgClist) {
                if ("院机关".equals(org.getName())) {
                    List<String> orgyjgList = Arrays.asList("办公室", "科研部", "政治工作部", "管理保障部");
                    QueryWrapper<LibEntityEntity> orgyjgquery = new QueryWrapper<>();
                    orgyjgquery.select("id");
                    orgyjgquery.in("name", orgyjgList);
                    orgyjgquery.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
                    orgyjgquery.eq("json_value(data,'$.pid')", org.getId());
                    List<LibEntityEntity> orgyjgEntityList = libEntityMapper.selectList(orgyjgquery);
                    if (orgyjgEntityList != null && !orgyjgEntityList.isEmpty()) {
                        for (LibEntityEntity orgEntity : orgyjgEntityList) {
                            orgidsyjg.add(orgEntity.getId());
                        }
                    }
                }
            }
        }

        // 研究生院的机构ids
        List<Long> orgidsyjsy = new ArrayList<>();
        if (orgClist != null && !orgClist.isEmpty()) {
            for (LibEntityEntity org : orgClist) {
                if ("研究生院".equals(org.getName())) {
                    List<String> orgyjgList = Arrays.asList("研究生院办公室", "招生培养处", "学科学位处");
                    QueryWrapper<LibEntityEntity> orgyjgquery = new QueryWrapper<>();
                    orgyjgquery.select("id");
                    orgyjgquery.in("name", orgyjgList);
                    orgyjgquery.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
                    orgyjgquery.eq("json_value(data,'$.pid')", org.getId());
                    List<LibEntityEntity> orgyjgEntityList = libEntityMapper.selectList(orgyjgquery);
                    if (orgyjgEntityList != null && !orgyjgEntityList.isEmpty()) {
                        for (LibEntityEntity orgEntity : orgyjgEntityList) {
                            orgidsyjsy.add(orgEntity.getId());
                        }
                    }
                }
            }
        }

        // 相关人物
        Map<String, Object> personQueryP = new HashMap<>();
        personQueryP.put("ontoId", YearbookLibConstant.PERSON_ONTO_ID);
        personQueryP.put("orgId", orgidsStr);
        List<Map> libEntityListpersonAll = libEntityMapper.orgPersonList(personQueryP);
        List<Map> libEntityListPperson = new ArrayList<>();
        if (libEntityListpersonAll != null && !libEntityListpersonAll.isEmpty()) {
            for (Map person : libEntityListpersonAll) {
                if (orgPlist.get(0).getId().toString().equals(person.get("orgId").toString())) {
                    libEntityListPperson.add(person);
                }
            }
        }
        mapP.put("person", libEntityListPperson);

        mapP.put("theme", new ArrayList<>());

        // 相关主题
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("distinct(json_value(data,'$.theme')) as name", "json_value(data,'$.org_ids') as ontoId", "json_value(data,'$.org2_id') as corpusId");
        eventQuery.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
        eventQuery.isNotNull("json_value(data,'$.theme')");
        eventQuery.in("json_value(data,'$.org_ids')", orgids);
        List<LibEntityEntity> eventListtheme = libEntityMapper.selectList(eventQuery);

        // 动态主题
        QueryWrapper<EveThemeEntity> themeQuery = new QueryWrapper<>();
        themeQuery.select("name");
        themeQuery.eq("category1", 2);
        List<EveThemeEntity> eveThemeEntityList = eveThemeMapper.selectList(themeQuery);

        // 取不是动态主题
        List<String> themeListStr = eveThemeEntityList.stream().map(EveThemeEntity::getName).collect(Collectors.toList());
        eventListtheme = eventListtheme.stream().filter(x -> !themeListStr.contains(x.getName())).collect(Collectors.toList());

        List<Map<String, Object>> childList = new ArrayList<>();
        if (orgClist != null && !orgClist.isEmpty()) {
            for (LibEntityEntity org : orgClist) {
                Map<String, Object> mapC = new HashMap<>();
                mapC.put("id", org.getId());
                mapC.put("name", org.getSynonymWord().replaceAll(";", ""));
                if (org.getData() != null) {
                    Map<String, Object> stringObjectMap = JsonUtil.stringToMap(org.getData());
                    mapC.put("pic", stringObjectMap.get("icon"));
                }

                // 相关人物
                List<Map> libEntityListC = new ArrayList<>();
                if (libEntityListpersonAll != null && !libEntityListpersonAll.isEmpty()) {
                    for (Map person : libEntityListpersonAll) {
                        if (org.getId().toString().equals(person.get("orgId").toString())) {
                            libEntityListC.add(person);
                        }
                    }
                }
                mapC.put("person", libEntityListC);

                // 相关主题
                List<LibEntityEntity> eventListC = new ArrayList<>();
                if ("院机关".equals(org.getName())) {
                    for (Long orgid : orgidsyjg) {
                        for (LibEntityEntity theme : eventListtheme) {
                            if (theme.getOntoId().toString().equals(org.getId().toString()) && theme.getCorpusId().toString().equals(orgid.toString())) {
                                eventListC.add(theme);
                            }
                        }
                    }
                } else if ("研究生院".equals(org.getName())) {
                    for (Long orgid : orgidsyjsy) {
                        for (LibEntityEntity theme : eventListtheme) {
                            if (theme.getOntoId().toString().equals(org.getId().toString()) && theme.getCorpusId().toString().equals(orgid.toString())) {
                                eventListC.add(theme);
                            }
                        }
                    }
                } else {
                    for (LibEntityEntity theme : eventListtheme) {
                        if (theme.getOntoId().toString().equals(org.getId().toString())) {
                            eventListC.add(theme);
                        }
                    }
                }
                eventListC = eventListC.stream().distinct().filter(distinctByKey(LibEntityEntity::getName)).collect(Collectors.toList());
                mapC.put("theme", eventListC);
                childList.add(mapC);
            }
        }
        mapP.put("childList", childList);
        return R.ok(mapP);
    }

    @Override
    public R homepageThemeTree(){
        List<Map<String,Object>> resultList = new ArrayList<>();
        QueryWrapper<EveThemeEntity> eveThemeQueryT = new QueryWrapper<>();
        eveThemeQueryT.select("distinct(pname)");
        eveThemeQueryT.eq("category1",2);
        eveThemeQueryT.isNotNull("pname");
        List<EveThemeEntity> eveThemeEntityListT = eveThemeMapper.selectList(eveThemeQueryT);
        for (EveThemeEntity theme:eveThemeEntityListT) {
            Map<String,Object> mapChild = new HashMap<>();
            QueryWrapper<EveThemeEntity> eveThemeQuery = new QueryWrapper<>();
            String pname = "";
//            if (theme == null || theme.getPname() == null){
//                eveThemeQuery.isNull("pname");
//                pname = "自定义主题";
//            }else {
                eveThemeQuery.eq("pname",theme.getPname());
                pname = theme.getPname();
//            }
            eveThemeQuery.eq("category1",2);
            List<EveThemeEntity> eveThemeEntityList = eveThemeMapper.selectList(eveThemeQuery);
            mapChild.put("pname",pname);
            mapChild.put("data",eveThemeEntityList);
            resultList.add(mapChild);
        }
        QueryWrapper<EveTagEntity> eveTagQuery = new QueryWrapper<>();
        eveTagQuery.select("distinct(name)");
        List<EveTagEntity> eveTagList = eveTagMapper.selectList(eveTagQuery);
        Map<String,Object> mapChild = new HashMap<>();
        mapChild.put("pname","自定义主题");
        mapChild.put("data",eveTagList);
        resultList.add(mapChild);
        return R.ok(resultList);
    }

    @Override
    public R humanGraphOntoList(){
        return R.ok(YearbookConstant.GRAPH_VALUE_LIST);
    }

    @Override
    public R themeGraphOntoList(){
        return R.ok(YearbookConstant.GRAPH_THEME_LIST);
    }

    @Override
    public R humanGraphRelationTime(String entityId){
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        if (libEntity == null){return R.failed("实体id不存在！");}
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("entityId",entityId);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEntityIdAndOntoid(querymap);
            nodesList.addAll(nodesEntityList);
        }
        List<Map<String, Object>> eventList = new ArrayList<>();
        for (Map<String,Object> node:nodesList) {
            //查询事件
            if (node.get("eventid") == null || node.get("eventid").toString().isEmpty()){
                continue;
            }
            QueryWrapper<LibEntityEntity> libEntityQuery = new QueryWrapper<>();
            libEntityQuery.select("json_value(data,'$.start_time') as name","json_value(data,'$.content') as data");
            libEntityQuery.eq("id",node.get("eventid"));
            List<LibEntityEntity> libEntityList = libEntityMapper.selectList(libEntityQuery);
//            EventEntity eventEntity = eventMapper.selectById(node.get("eventid").toString());
            if (libEntityList.size() == 0){continue;}
            Map<String,Object> eventMap = new HashMap<>();
            eventMap.put("startTime",libEntityList.get(0).getName());
            eventMap.put("content",libEntityList.get(0).getData());

            eventMap.put("sourceId",entityId);
            eventMap.put("sourceName",libEntity.getName());
            eventMap.put("sourceOnto",libEntity.getOntoId());

            eventMap.put("targetId",node.get("id"));
            eventMap.put("targetName",node.get("name"));
            eventMap.put("targetOnto",node.get("onto"));
            eventList.add(eventMap);
        }
        eventList.sort(Comparator.comparing(o -> o.get("startTime").toString()));
        return R.ok(eventList);
    }

    @Override
    public R humanGraphMoreRelationTime(String entityId, String ontoId){
        Map<String,String> querymap = new HashMap<>();
        querymap.put("entityId",entityId);
        querymap.put("ontoId",ontoId);
        //查询实体节点
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.humanGraphMoreRelationTime(querymap);
        return R.ok(nodesEntityList);
    }

//    @Override
//    public R humanGraphRelationTimeChart(String entityId){
//        Map<String,Object> resultMap = new HashMap<>();
//        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
//        HashSet<String> timeset = new HashSet<>();
//        for (Map<String, Object> ontoMap:nodesOntoList) {
//            Map<String,String> querymap = new HashMap<>();
//            querymap.put("ontoId",ontoMap.get("id").toString());
//            querymap.put("entityId",entityId);
//            //查询实体节点
//            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEntityIdAndOntoid(querymap);
//            List<String> eventids = new ArrayList<>();
//            for (Map<String,Object> node:nodesEntityList) {
//                if (node.get("eventid") == null || node.get("eventid").toString().isEmpty()){
//                    continue;
//                }
//                eventids.add(node.get("eventid").toString());
//            }
//            if (eventids.size() == 0){
//                continue;
//            }
//            QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
//            eventQuery.select("LEFT(json_value(data,'$.start_time'),7) as name,count(1) as score");
//            eventQuery.isNotNull("json_value(data,'$.start_time')");
//            eventQuery.in("id",eventids);
//            eventQuery.groupBy("LEFT(json_value(data,'$.start_time'),7)");
//            eventQuery.orderByAsc("name");
//            List<LibEntityEntity> libEntityList = libEntityMapper.selectList(eventQuery);
////            List<EventEntity> eventEntityList = eventMapper.selectList(eventQuery);
//            List<Map<String,Object>> monthCountList = new ArrayList<>();
//            for (LibEntityEntity event:libEntityList) {
//                Map<String,Object> eventMap = new HashMap<>();
//                eventMap.put("time",event.getName());
//                eventMap.put("count",event.getScore());
//                monthCountList.add(eventMap);
//                timeset.add(event.getName());
//            }
//            resultMap.put(ontoMap.get("id").toString(),monthCountList);
//            List<String> timeList = new ArrayList<>(timeset);
//            Collections.sort(timeList, new Comparator<String>() {
//                        @Override
//                        public int compare(String o1, String o2) {
//                            int year1 = Integer.valueOf(o1.split("-")[0]);
//                            int month1 = Integer.valueOf(o1.split("-")[1]);
//                            int year2 = Integer.valueOf(o2.split("-")[0]);
//                            int month2 = Integer.valueOf(o2.split("-")[1]);
//                            if (year1-year2 == 0){
//                                if (month1-month2 == 0){
//                                    return 0;
//                                }else if (month1-month2 < 0){
//                                    return -1;
//                                }else if (month1-month2 > 0){
//                                    return 1;
//                                }
//                                return 1;
//                            }else if (year1-year2 < 0){
//                                return -1;
//                            }else if (year1-year2 > 0){
//                                return 1;
//                            }
//                            return 0;
//                        }
//                    });
//            resultMap.put("timeList",timeList);
//        }
//        return R.ok(resultMap);
//    }

    @Override
    public R humanGraphRelationTimeChart(String entityId){
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        HashSet<String> timeset = new HashSet<>();
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("entityId",entityId);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetHumanTimeChartByEntityIdAndOntoid(querymap);
            for (Map<String, Object> map:nodesEntityList) {
                timeset.add((String) map.get("time"));
            }
            nodesEntityList = nodesEntityList.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
            resultMap.put(ontoMap.get("id").toString(),nodesEntityList);
            List<String> timeList = new ArrayList<>(timeset);
            Collections.sort(timeList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    int year1 = Integer.valueOf(o1.split("-")[0]);
                    int month1 = Integer.valueOf(o1.split("-")[1]);
                    int year2 = Integer.valueOf(o2.split("-")[0]);
                    int month2 = Integer.valueOf(o2.split("-")[1]);
                    if (year1-year2 == 0){
                        if (month1-month2 == 0){
                            return 0;
                        }else if (month1-month2 < 0){
                            return -1;
                        }else if (month1-month2 > 0){
                            return 1;
                        }
                        return 1;
                    }else if (year1-year2 < 0){
                        return -1;
                    }else if (year1-year2 > 0){
                        return 1;
                    }
                    return 0;
                }
            });
            resultMap.put("timeList",timeList);
        }
        return R.ok(resultMap);
    }

    public R themeGraph(String theme,String startTime,String endTime){
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,String>> linksList = new ArrayList<>();
        //本身节点
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id","1");
        nodesMap.put("name",theme);
        nodesMap.put("isMain","1");
        nodesList.add(nodesMap);
        //本体节点
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_THEME_LIST;
//        nodesList.addAll(nodesOntoList);
        //到本体的links
//        for (Map<String, Object> ontoMap:nodesOntoList) {
//            Map<String,String> linksmap = new HashMap<>();
//            linksmap.put("source","1");
//            linksmap.put("target",ontoMap.get("id").toString());
//            linksmap.put("value","");
//            linksmap.put("eventid","");
//            linksList.add(linksmap);
//        }
        //查询GraphTheme
        QueryWrapper<GraphThemeEventEntity> GraphThemeEventQuery = new QueryWrapper<>();
        GraphThemeEventQuery.eq("theme",theme);
        List<GraphThemeEventEntity> graphThemeList = graphThemeEventMapper.selectList(GraphThemeEventQuery);
        if (graphThemeList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }

        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,Object> queryMap = new HashMap<>();
            queryMap.put("onto",ontoMap.get("id"));
            queryMap.put("theme",theme);
            if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime)){
                queryMap.put("startTime",startTime);
                queryMap.put("endTime",endTime);
            }
            List<Map<String, Object>> list = graphThemeEventMapper.themeGraphRelation(queryMap);
            if (list.size() == 0){continue;}
            nodesList.addAll(list);
            nodesList.add(ontoMap);
            for (Map<String, Object> map:list) {
                Map<String,String> linksmap = new HashMap<>();
                linksmap.put("source",ontoMap.get("id").toString());
                linksmap.put("target",map.get("id").toString());
                linksmap.put("value","");
                linksmap.put("eventid",map.get("eventid").toString());
                linksmap.put("start_time",map.get("start_time")==null?"":map.get("start_time").toString());
                linksmap.put("event_overview",map.get("event_overview")==null?"":map.get("event_overview").toString());
                linksmap.put("title",map.get("title")==null?"":map.get("title").toString());
                linksmap.put("pageNumber",map.get("pageNumber")==null?"":map.get("pageNumber").toString());
                linksList.add(linksmap);
            }
            //到本体的links
            Map<String,String> linksmap = new HashMap<>();
            linksmap.put("source","1");
            linksmap.put("target",ontoMap.get("id").toString());
            linksmap.put("value","");
            linksmap.put("eventid","");
            linksList.add(linksmap);
            linksList = linksList.stream().filter(distinctByKey(o -> o.get("target") )).collect(Collectors.toList());
        }
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        retrunMap.put("nodes",nodesList);
        for (Map<String, String> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null || linkrelation.get("pageNumber").isEmpty()){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById(linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
        return R.ok(retrunMap);
    }

    @Override
    public R themeGraphRelationTime(String theme){
        return R.ok(graphThemeEventMapper.themeGraphRelationTime(theme));
    }

    @Override
    public R themeGraphRelationTimeChart(String theme){
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_THEME_LIST;
        HashSet<String> timeset = new HashSet<>();
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,Object> queryMap = new HashMap<>();
            queryMap.put("theme",theme);
            queryMap.put("onto",ontoMap.get("id"));
            List<Map<String, Object>> graphThemeEventEntityList = graphThemeEventMapper.themeGraphRelationTimeChartNew(queryMap);
            for (Map<String, Object> GraphThemeEvent:graphThemeEventEntityList) {
                timeset.add((String) GraphThemeEvent.get("time"));
            }
            graphThemeEventEntityList = graphThemeEventEntityList.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
            resultMap.put(ontoMap.get("id").toString(),graphThemeEventEntityList);
            List<String> timeList = new ArrayList<>(timeset);
            Collections.sort(timeList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    int year1 = Integer.valueOf(o1.split("-")[0]);
                    int month1 = Integer.valueOf(o1.split("-")[1]);
                    int year2 = Integer.valueOf(o2.split("-")[0]);
                    int month2 = Integer.valueOf(o2.split("-")[1]);
                    if (year1-year2 == 0){
                        if (month1-month2 == 0){
                            return 0;
                        }else if (month1-month2 < 0){
                            return -1;
                        }else if (month1-month2 > 0){
                            return 1;
                        }
                        return 1;
                    }else if (year1-year2 < 0){
                        return -1;
                    }else if (year1-year2 > 0){
                        return 1;
                    }
                    return 0;
                }
            });
            resultMap.put("timeList",timeList);
        }
        return R.ok(resultMap);
    }

    @Override
    public R themeGraphDetail(String theme){
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_THEME_ONTO_ID);
        libEntityEntityQuery.eq("name",theme);
        List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityEntityQuery);
        if (libEntityEntities.size() == 0){
            return R.ok(null);
        }else {
            return R.ok(libEntityEntities.get(0));
        }
    }

    @Override
    public R themeList(String theme){
        Set<String> tagListAll = new HashSet<>();
        //标签
        QueryWrapper<ResearchTagEntity> tagQuery = new QueryWrapper<>();
        tagQuery.select("name");
        tagQuery.like("name",theme);
        tagQuery.isNotNull("name");
        tagQuery.groupBy("name");
        tagQuery.orderByDesc("sum(zonelevel)");
        List<ResearchTagEntity> tagList = researchTagMapper.selectList(tagQuery);
        for (ResearchTagEntity tag:tagList) {
            tagListAll.add(tag.getName());
        }
        //主题
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("json_value(data,'$.theme') as name");
        eventQuery.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
        eventQuery.like("json_value(data,'$.theme')",theme);
        eventQuery.isNotNull("json_value(data,'$.theme')");
        eventQuery.groupBy("json_value(data,'$.theme')");
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        for (LibEntityEntity tag:eventList) {
            tagListAll.add(tag.getName());
        }
        return R.ok(tagListAll);
    }

    @Override
    public R getEntityDetailPerson(String id) {
        LibEntityEntity entity = libEntityMapper.selectById(id);
        if (entity == null) {
            return R.ok("请输入合法数据");
        }
        if (entity.getData() == null) {
            return R.ok("请输入合法数据");
        }
        Map<String, Object> stringObjectMap = JsonUtil.stringToMap(entity.getData());
        Map<String,Object> map = new HashMap<>();
        map.put("image_ids",stringObjectMap.get("image_ids"));
        map.put("name",entity.getName());
        map.put("sex",getMateAttrbuteValue(stringObjectMap.get("sex")));
        map.put("birthday",stringObjectMap.get("birthday"));
        map.put("department",getMateAttrbuteValue(stringObjectMap.get("department")));
        map.put("military_rank_id",getMateAttrbuteValue(stringObjectMap.get("military_rank_id")));
        map.put("title_Id",getMateAttrbuteValue(stringObjectMap.get("rank")));
        map.put("enlistment_join_time",stringObjectMap.get("enlistment_join_time"));
        map.put("party_join_time",stringObjectMap.get("party_join_time"));
        map.put("work_experience",stringObjectMap.get("work_experience"));
        map.put("synopsis",stringObjectMap.get("synopsis"));
        return R.ok(map);
    }

    String getMateAttrbuteValue(Object object){
        String name = "";
        if (object == null){
            return name;
        }
        if (object instanceof Long){
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById(object.toString());
            if (mateAttributeValue == null){
                return name;
            }
            name = mateAttributeValue.getValName();
        }
        if (object.getClass().getName() == "com.alibaba.fastjson.JSONArray"){
            JSONArray jsonArray = (JSONArray) object;
            if (jsonArray.size() == 0){
                return name;
            }
            List<MateAttributeValueEntity> mateAttributeValueEntities = mateAttributeValueMapper.selectBatchIds((Collection<? extends Serializable>) object);
            if (mateAttributeValueEntities.size() == 0){
                return name;
            }
            for (MateAttributeValueEntity mateValue:mateAttributeValueEntities) {
                if (!name.isEmpty()){
                    name += ",";
                }
                name += mateValue.getValName();
            }
        }

        return name;
    }

    @Override
    public R judgeTheme(String theme){
        QueryWrapper<EveThemeEntity> themequery = new QueryWrapper<>();
        themequery.eq("name",theme);
        QueryWrapper<EveTagEntity> tagquery = new QueryWrapper<>();
        tagquery.eq("name",theme);
        if (eveThemeMapper.selectCount(themequery) > 0){
            //图谱
            return R.ok(1);
        } else if (eveTagMapper.selectCount(tagquery) > 0) {
            //自定义，有这个主题，删除
            return R.ok(-1);
        } else {
            //标签，没有这个主题，新增
            return R.ok(0);
        }
    }

    @Override
    public R getEntityDetailOrg(String id) {
        LibEntityEntity entity = libEntityMapper.selectById(id);
        if (entity == null) {
            return R.ok("请输入合法数据");
        }
        if (entity.getData() == null) {
            return R.ok("请输入合法数据");
        }
        Map<String, Object> stringObjectMap = JsonUtil.stringToMap(entity.getData());

        Map<String,Object> map = new HashMap<>();
        map.put("icon",stringObjectMap.get("icon"));
        map.put("name",entity.getName());
        map.put("main_function",stringObjectMap.get("main_function"));
        map.put("staff_size",stringObjectMap.get("staff_size"));
        map.put("historical_evolution",stringObjectMap.get("historical_evolution"));
        return R.ok(map);
    }

//    @Override
//    public R orgGraph(String entityId){
//        Map<String,Object> retrunMap = new HashMap<>();
//        List<Map<String,Object>> nodesList = new ArrayList<>();
//        List<Map<String,Object>> linksList = new ArrayList<>();
//        //查询本身节点
//        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
//        Map<String, Object> nodesMap = new HashMap<>();
//        nodesMap.put("id",entityId);
//        nodesMap.put("name",libEntity.getName());
//        nodesMap.put("onto",libEntity.getOntoId());
//        if (libEntity.getOntoId().equals(YearbookConstant.MARK_ORG_ONTO_ID)){
//            if (libEntity.getData() != null){
//                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
//                nodesMap.put("image_ids",stringObjectMap.get("icon"));
//            }
//        }
//        nodesList.add(nodesMap);
//        //查询机构对应关系
//        QueryWrapper<MetaEntityRelationEntity> relationQuery1 = new QueryWrapper<>();
//        relationQuery1.select("name","entity1_id as id");
//        relationQuery1.eq("entity2_id",entityId);
//        relationQuery1.isNotNull("entity1_id");
//        relationQuery1.isNotNull("name");
//        List<MetaEntityRelationEntity> relationList1 = metaEntityRelationMapper.selectList(relationQuery1);
//        QueryWrapper<MetaEntityRelationEntity> relationQuery2 = new QueryWrapper<>();
//        relationQuery2.select("name","entity2_id as id");
//        relationQuery2.eq("entity1_id",entityId);
//        relationQuery2.isNotNull("entity2_id");
//        relationQuery2.isNotNull("name");
//        List<MetaEntityRelationEntity> relationList2 = metaEntityRelationMapper.selectList(relationQuery2);
//        relationList1.addAll(relationList2);
//        //根据id去重
//        relationList1 = relationList1.stream()
//                .collect(collectingAndThen(toCollection(() ->
//                        new TreeSet<>(Comparator.comparing(MetaEntityRelationEntity::getId))), ArrayList::new));
//        //研究所的集合
//        Map<String,Object> orgQuery = new HashMap<>();
//        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
//        orgQuery.put("orgId",entityId);
//        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
//        for (LibEntityEntity org:orgList) {
//            //研究所的节点
//            Map<String, Object> nodeMap = new HashMap<>();
//            nodeMap.put("id",org.getId());
//            nodeMap.put("name",org.getName());
//            nodeMap.put("onto",YearbookConstant.MARK_ORG_ONTO_ID);
//            nodeMap.put("image_ids",null);
//            nodeMap.put("isMain","1");
//            nodesList.add(nodeMap);
//            //到研究所的links
//            Map<String,Object> linksmapyjs = new HashMap<>();
//            linksmapyjs.put("source",entityId);
//            linksmapyjs.put("target",org.getId());
//            linksmapyjs.put("value","");
//            linksmapyjs.put("eventid","");
//            linksList.add(linksmapyjs);
//            //查询事件
//            QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
//            eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
//            eventQuery.eq("json_value(data,'$.org_ids')",entityId);
//            eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
//            List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
//            if (eventList.size() == 0){continue;}
//            for (LibEntityEntity event:eventList) {
//                if (event.getData() == null){continue;}
//                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(event.getData());
//
//                Set<Long> entityids = new HashSet<>();
//                entityids = orgGraphGetEntity(stringObjectMap.get("person_ids"),entityids);
//                entityids = orgGraphGetEntity(stringObjectMap.get("research_project"),entityids);
//                entityids = orgGraphGetEntity(stringObjectMap.get("achievement"),entityids);
//                entityids = orgGraphGetEntity(stringObjectMap.get("prize"),entityids);
//                if (entityids.size() == 0){continue;}
//                //查询关联节点
//                QueryWrapper<LibEntityEntity> entityQuery = new QueryWrapper<>();
//                entityQuery.select("id","name","onto_id","json_value(data,'$.image_ids') as disambiguation");
//                entityQuery.in("id",entityids);
//                entityQuery.isNotNull("name");
//                List<LibEntityEntity> entityList = libEntityMapper.selectList(entityQuery);
//                for (LibEntityEntity entity:entityList) {
//                    Map<String, Object> nodeMapentity = new HashMap<>();
//                    nodeMapentity.put("id",entity.getId());
//                    nodeMapentity.put("name",entity.getName());
//                    nodeMapentity.put("onto",entity.getOntoId());
//                    nodeMapentity.put("image_ids",entity.getDisambiguation());
//                    nodeMapentity.put("eventid",event.getId());
//                    nodesList.add(nodeMapentity);
//                    //研究所到节点的links
//                    Map<String,Object> linksmap = new HashMap<>();
//                    linksmap.put("source",org.getId());
//                    linksmap.put("target",entity.getId());
//                    linksmap.put("value","");
//                    linksmap.put("eventid",event.getId());
//                    linksmap.put("start_time",stringObjectMap.get("start_time"));
//                    linksmap.put("title",stringObjectMap.get("title"));
//                    linksmap.put("event_overview",stringObjectMap.get("event_overview"));
//                    linksmap.put("pageNumber",stringObjectMap.get("pageNum"));
//                    //关系
//                    Map<Long,String> relationMap = relationList1.stream().
//                            collect(Collectors.toMap
//                                    (MetaEntityRelationEntity::getId,MetaEntityRelationEntity::getName));
//                    linksmap.put("value",relationMap.get(entity.getId()) == null?"":relationMap.get(entity.getId()));
//                    linksList.add(linksmap);
//                }
//            }
//
//        }
//        //根据id去重
//        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
//        retrunMap.put("nodes",nodesList);
//        for (Map<String, Object> linkrelation:linksList) {
//            if (linkrelation.get("pageNumber") == null){continue;}
//            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
//            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
//                linkrelation.put("pageNumber","");
//            }else {
//                linkrelation.put("pageNumber",mateAttributeValue.getValName());
//            }
//        }
//        retrunMap.put("links",linksList);
//        return R.ok(retrunMap);
//    }
    Set<Long> orgGraphGetEntity(Object object,Set<Long> entityids){
        if (object == null){
            return entityids;
        }
        if (object instanceof Long){
            entityids.add((long) object);
        }
        if (object.getClass().getName() == "com.alibaba.fastjson.JSONArray"){
            JSONArray jsonArray = (JSONArray) object;
            if (jsonArray.size() == 0){
                return entityids;
            }
            for (Object id:jsonArray) {
                entityids.add((long) id);
            }
        }
        return entityids;
    }

    @Override
    public R orgGraphOntoList(){
        List<Map<String, Object>> graphOrgList = new ArrayList<>();
        graphOrgList.addAll(YearbookConstant.GRAPH_ORG_LIST);
//        //机构
//        Map<String,Object> GRAPH_VALUE_MAP_ORG = new HashMap<>();
//        GRAPH_VALUE_MAP_ORG.put("id",YearbookConstant.MARK_ORG_ONTO_ID);
//        GRAPH_VALUE_MAP_ORG.put("name","机构");
//        graphOrgList.add(GRAPH_VALUE_MAP_ORG);
        return R.ok(graphOrgList);
    }

//    @Override
//    public R orgGraphRelationTime(String entityId){
//        List<Map<String,Object>> eventMapList = new ArrayList<>();
//        //查询关联事件
//        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
//        eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
//        eventQuery.eq("json_value(data,'$.org_ids')",entityId);
//        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
//        if (eventList.size() == 0){return R.ok(eventMapList);}
//        for (LibEntityEntity event:eventList) {
//            if (event.getData() == null){continue;}
//            Map<String, Object> stringObjectMap = JsonUtil.stringToMap(event.getData());
//            //研究所
//            if (stringObjectMap.get("org2_id") == null){continue;}
//            QueryWrapper<LibEntityEntity> org2query = new QueryWrapper<>();
//            org2query.select("name");
//            org2query.eq("id",stringObjectMap.get("org2_id"));
//            LibEntityEntity org2Entity = libEntityMapper.selectOne(org2query);
//            if (org2Entity == null){continue;}
//
//            if (stringObjectMap.get("person_ids") != null){
//                if (stringObjectMap.get("person_ids").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                    JSONArray jsonArray = (JSONArray) stringObjectMap.get("person_ids");
////                    if (jsonArray.size() == 0){
////                        continue;
////                    }
//                    for (Object id:jsonArray) {
//                        QueryWrapper<LibEntityEntity> libquery = new QueryWrapper<>();
//                        libquery.select("name");
//                        libquery.eq("id",id);
//                        LibEntityEntity libEntity = libEntityMapper.selectOne(libquery);
//                        if (libEntity == null){continue;}
//                        Map<String,Object> eventMap = new HashMap<>();
//                        eventMap.put("startTime",stringObjectMap.get("start_time"));
//                        eventMap.put("content",stringObjectMap.get("content"));
//                        eventMap.put("sourceId",stringObjectMap.get("org2_id"));
//                        eventMap.put("sourceName",org2Entity.getName());
//                        eventMap.put("sourceOnto",YearbookConstant.MARK_ORG_ONTO_ID);
//                        eventMap.put("targetId",id);
//                        eventMap.put("targetName",libEntity.getName());
//                        eventMap.put("targetOnto",YearbookConstant.MARK_PER_ONTO_ID);
//                        eventMapList.add(eventMap);
//                    }
//                }
//            }
//
//            if (stringObjectMap.get("research_project") != null){
//                if (stringObjectMap.get("research_project").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                    JSONArray jsonArray = (JSONArray) stringObjectMap.get("research_project");
////                    if (jsonArray.size() == 0){
////                        continue;
////                    }
//                    for (Object id:jsonArray) {
//                        QueryWrapper<LibEntityEntity> libquery = new QueryWrapper<>();
//                        libquery.select("name");
//                        libquery.eq("id",id);
//                        LibEntityEntity libEntity = libEntityMapper.selectOne(libquery);
//                        if (libEntity == null){continue;}
//                        Map<String,Object> eventMap = new HashMap<>();
//                        eventMap.put("startTime",stringObjectMap.get("start_time"));
//                        eventMap.put("content",stringObjectMap.get("content"));
//                        eventMap.put("sourceId",stringObjectMap.get("org2_id"));
//                        eventMap.put("sourceName",org2Entity.getName());
//                        eventMap.put("sourceOnto",YearbookConstant.MARK_ORG_ONTO_ID);
//                        eventMap.put("targetId",id);
//                        eventMap.put("targetName",libEntity.getName());
//                        eventMap.put("targetOnto",YearbookConstant.MARK_PROJECT_ONTO_ID);
//                        eventMapList.add(eventMap);
//                    }
//                }
//            }
//
//            if (stringObjectMap.get("achievement") != null){
//                if (stringObjectMap.get("achievement").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                    JSONArray jsonArray = (JSONArray) stringObjectMap.get("achievement");
////                    if (jsonArray.size() == 0){
////                        continue;
////                    }
//                    for (Object id:jsonArray) {
//                        QueryWrapper<LibEntityEntity> libquery = new QueryWrapper<>();
//                        libquery.select("name");
//                        libquery.eq("id",id);
//                        LibEntityEntity libEntity = libEntityMapper.selectOne(libquery);
//                        if (libEntity == null){continue;}
//                        Map<String,Object> eventMap = new HashMap<>();
//                        eventMap.put("startTime",stringObjectMap.get("start_time"));
//                        eventMap.put("content",stringObjectMap.get("content"));
//                        eventMap.put("sourceId",stringObjectMap.get("org2_id"));
//                        eventMap.put("sourceName",org2Entity.getName());
//                        eventMap.put("sourceOnto",YearbookConstant.MARK_ORG_ONTO_ID);
//                        eventMap.put("targetId",id);
//                        eventMap.put("targetName",libEntity.getName());
//                        eventMap.put("targetOnto",YearbookConstant.MARK_RESULT_ONTO_ID);
//                        eventMapList.add(eventMap);
//                    }
//                }
//            }
//
//            if (stringObjectMap.get("prize") != null){
//                if (stringObjectMap.get("prize").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                    JSONArray jsonArray = (JSONArray) stringObjectMap.get("prize");
////                    if (jsonArray.size() == 0){
////                        continue;
////                    }
//                    for (Object id:jsonArray) {
//                        QueryWrapper<LibEntityEntity> libquery = new QueryWrapper<>();
//                        libquery.select("name");
//                        libquery.eq("id",id);
//                        LibEntityEntity libEntity = libEntityMapper.selectOne(libquery);
//                        if (libEntity == null){continue;}
//                        Map<String,Object> eventMap = new HashMap<>();
//                        eventMap.put("startTime",stringObjectMap.get("start_time"));
//                        eventMap.put("content",stringObjectMap.get("content"));
//                        eventMap.put("sourceId",stringObjectMap.get("org2_id"));
//                        eventMap.put("sourceName",org2Entity.getName());
//                        eventMap.put("sourceOnto",YearbookConstant.MARK_ORG_ONTO_ID);
//                        eventMap.put("targetId",id);
//                        eventMap.put("targetName",libEntity.getName());
//                        eventMap.put("targetOnto",YearbookConstant.MARK_AWARD_ONTO_ID);
//                        eventMapList.add(eventMap);
//                    }
//                }
//            }
//        }
//        eventMapList.sort(Comparator.comparing(o -> o.get("startTime").toString()));
//        return R.ok(eventMapList);
//    }

//    @Override
//    public R orgGraphRelationTimeChart(String entityId){
//        Map<String,Object> resultMap = new HashMap<>();
//        //月份列表
//        QueryWrapper<LibEntityEntity> monthQuery = new QueryWrapper<>();
//        monthQuery.select("LEFT(json_value(data,'$.start_time'),7) as name");
//        monthQuery.isNotNull("json_value(data,'$.start_time')");
//        monthQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
//        monthQuery.eq("json_value(data,'$.org_ids')",entityId);
//        monthQuery.groupBy("LEFT(json_value(data,'$.start_time'),7)");
//        monthQuery.orderByAsc("name");
//        List<LibEntityEntity> libEntityList = libEntityMapper.selectList(monthQuery);
//        List<String> timeList = new ArrayList<>();
//        for (LibEntityEntity month:libEntityList) {
//            timeList.add(month.getName());
//        }
//        resultMap.put("timeList",timeList);
//        //循环本体，计算实体数量
//        List<Map<String,Object>> monthCountListperson = new ArrayList<>();
//        List<Map<String,Object>> monthCountListresearch = new ArrayList<>();
//        List<Map<String,Object>> monthCountListachieve = new ArrayList<>();
//        List<Map<String,Object>> monthCountListprize = new ArrayList<>();
//        for (String month:timeList) {
//            //查询关联事件
//            QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
//            eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
//            eventQuery.eq("json_value(data,'$.org_ids')",entityId);
//            eventQuery.like("json_value(data,'$.start_time')",month);
//            List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
//            Set<Object> personNum = new HashSet<>();
//            Set<Object> projectNum = new HashSet<>();
//            Set<Object> achiveNum = new HashSet<>();
//            Set<Object> prizeNum = new HashSet<>();
//            for (LibEntityEntity event:eventList) {
//                if (event.getData() == null){continue;}
//                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(event.getData());
//                if (stringObjectMap.get("person_ids") != null){
//                    if (stringObjectMap.get("person_ids").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                        JSONArray jsonArray = (JSONArray) stringObjectMap.get("person_ids");
//                        for (Object id:jsonArray) {
//                            personNum.add(id);
//                        }
//                    }
//                }
//                if (stringObjectMap.get("research_project") != null){
//                    if (stringObjectMap.get("research_project").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                        JSONArray jsonArray = (JSONArray) stringObjectMap.get("research_project");
//                        for (Object id:jsonArray) {
//                            projectNum.add(id);
//                        }
//                    }
//                }
//                if (stringObjectMap.get("achievement") != null){
//                    if (stringObjectMap.get("achievement").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                        JSONArray jsonArray = (JSONArray) stringObjectMap.get("achievement");
//                        for (Object id:jsonArray) {
//                            achiveNum.add(id);
//                        }
//                    }
//                }
//                if (stringObjectMap.get("prize") != null){
//                    if (stringObjectMap.get("prize").getClass().getName() == "com.alibaba.fastjson.JSONArray"){
//                        JSONArray jsonArray = (JSONArray) stringObjectMap.get("prize");
//                        for (Object id:jsonArray) {
//                            prizeNum.add(id);
//                        }
//                    }
//                }
//            }
//
//            Map<String,Object> monthMap1 = new HashMap<>();
//            monthMap1.put("num",personNum.size());
//            monthMap1.put("time",month);
//            monthCountListperson.add(monthMap1);
//            Map<String,Object> monthMap2 = new HashMap<>();
//            monthMap2.put("num",projectNum.size());
//            monthMap2.put("time",month);
//            monthCountListresearch.add(monthMap2);
//            Map<String,Object> monthMap3 = new HashMap<>();
//            monthMap3.put("num",achiveNum.size());
//            monthMap3.put("time",month);
//            monthCountListachieve.add(monthMap3);
//            Map<String,Object> monthMap4 = new HashMap<>();
//            monthMap4.put("num",prizeNum.size());
//            monthMap4.put("time",month);
//            monthCountListprize.add(monthMap4);
//        }
//        monthCountListperson = monthCountListperson.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
//        monthCountListresearch = monthCountListresearch.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
//        monthCountListachieve = monthCountListachieve.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
//        monthCountListprize = monthCountListprize.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
//        resultMap.put(YearbookConstant.MARK_PER_ONTO_ID.toString(),monthCountListperson);
//        resultMap.put(YearbookConstant.MARK_PROJECT_ONTO_ID.toString(),monthCountListresearch);
//        resultMap.put(YearbookConstant.MARK_RESULT_ONTO_ID.toString(),monthCountListachieve);
//        resultMap.put(YearbookConstant.MARK_AWARD_ONTO_ID.toString(),monthCountListprize);
//        return R.ok(resultMap);
//    }

    @Override
    public R orgGraph(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_ORG_STR;
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",entityId);
        nodesMap.put("name",libEntity.getName());
        nodesMap.put("onto",libEntity.getOntoId());
        if (libEntity.getOntoId().equals(YearbookConstant.MARK_ORG_ONTO_ID)){
            if (libEntity.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
                nodesMap.put("image_ids",stringObjectMap.get("icon"));
            }
        }
        nodesList.add(nodesMap);

        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        for (LibEntityEntity org:orgList) {
            //研究所的节点
            Map<String, Object> nodeMap = new HashMap<>();
            nodeMap.put("id",org.getId());
            nodeMap.put("name",org.getName());
            nodeMap.put("onto",YearbookConstant.MARK_ORG_ONTO_ID);
            nodeMap.put("image_ids",null);
            nodeMap.put("isMain","1");
            nodesList.add(nodeMap);
            //到研究所的links
            Map<String,Object> linksmapyjs = new HashMap<>();
            linksmapyjs.put("source",entityId);
            linksmapyjs.put("target",org.getId());
            linksmapyjs.put("value","父子机构");
            linksmapyjs.put("eventid","");
            linksList.add(linksmapyjs);
            //查询事件
            QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
            eventQuery.select("id");
            eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
            eventQuery.eq("json_value(data,'$.org_ids')",entityId);
            eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
            List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
            if (eventList.size() == 0){continue;}
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",orgOntoIds);
            String eventids = "";
            for (LibEntityEntity event:eventList) {
                if (!eventids.isEmpty()){
                    eventids += ",";
                }
                eventids += event.getId();
            }
            querymap.put("eventids",eventids);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
            if(nodesEntityList.size() == 0){continue;}
            nodesList.addAll(nodesEntityList);
            //关系
            Map<String, Object> eventMap = new HashMap<>();
            eventMap.put("id",org.getId());
            eventMap.put("eventids",eventids);
            eventMap.put("ontoId",orgOntoIds);
            List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
            linksRelationList = linksRelationList.stream().filter(distinctByKey(o -> o.get("target") )).collect(Collectors.toList());
            linksList.addAll(linksRelationList);
        }
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        retrunMap.put("nodes",nodesList);
        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
        return R.ok(retrunMap);
    }

    @Override
    public R orgGraphRelationTime(String entityId){
        String orgOntoIds = YearbookConstant.GRAPH_ORG_STR;
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        if (libEntity == null){return R.failed("实体id不存在！");}
        List<Map<String, Object>> eventRelationList = new ArrayList<>();
//        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        for (LibEntityEntity org:orgList) {
            List<Map<String,Object>> nodesList = new ArrayList<>();
            //查询事件
            QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
            eventQuery.select("id");
            eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
            eventQuery.eq("json_value(data,'$.org_ids')",entityId);
            eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
            List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
            if (eventList.size() == 0){continue;}
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",orgOntoIds);
            String eventids = "";
            for (LibEntityEntity event:eventList) {
                if (!eventids.isEmpty()){
                    eventids += ",";
                }
                eventids += event.getId();
            }
            querymap.put("eventids",eventids);
            nodesList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
            for (Map<String,Object> node:nodesList) {
                //查询事件
                if (node.get("eventid") == null || node.get("eventid").toString().isEmpty()){
                    continue;
                }
                QueryWrapper<LibEntityEntity> libEntityQuery = new QueryWrapper<>();
                libEntityQuery.select("json_value(data,'$.start_time') as name","json_value(data,'$.content') as data");
                libEntityQuery.eq("id",node.get("eventid"));
                List<LibEntityEntity> libEntityList = libEntityMapper.selectList(libEntityQuery);
                if (libEntityList.size() == 0){continue;}
                Map<String,Object> eventMap = new HashMap<>();
                eventMap.put("startTime",libEntityList.get(0).getName());
                eventMap.put("content",libEntityList.get(0).getData());

                eventMap.put("sourceId",org.getId());
                eventMap.put("sourceName",org.getName());
                eventMap.put("sourceOnto",YearbookConstant.MARK_ORG_ONTO_ID);

                eventMap.put("targetId",node.get("id"));
                eventMap.put("targetName",node.get("name"));
                eventMap.put("targetOnto",node.get("onto"));
                eventRelationList.add(eventMap);
            }
        }
        eventRelationList.sort(Comparator.comparing(o -> o.get("startTime").toString()));
        return R.ok(eventRelationList);
    }

    @Override
    public R orgGraphRelationTimeChart(String entityId){
        Map<String,Object> resultMap = new HashMap<>();
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_ORG_LIST;
        HashSet<String> timeset = new HashSet<>();
        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        List<Long> orgIds = new ArrayList<>();
        for (LibEntityEntity org:orgList) {
            orgIds.add(org.getId());
        }
        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
        eventQuery.in("json_value(data,'$.org2_id')",orgIds);
        eventQuery.eq("json_value(data,'$.org_ids')",entityId);
        eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){return R.ok(resultMap);}
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        for (Map<String, Object> ontoMap:nodesOntoList) {
            Map<String,String> querymap = new HashMap<>();
            querymap.put("ontoId",ontoMap.get("id").toString());
            querymap.put("eventids",eventids);
            //查询实体节点
            List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetOrgTimeChartByEvents(querymap);
            for (Map<String, Object> map:nodesEntityList) {
                timeset.add((String) map.get("time"));
            }
            nodesEntityList = nodesEntityList.stream().filter(distinctByKey(o -> o.get("time") )).collect(Collectors.toList());
            resultMap.put(ontoMap.get("id").toString(),nodesEntityList);
            List<String> timeList = new ArrayList<>(timeset);
            Collections.sort(timeList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    int year1 = Integer.valueOf(o1.split("-")[0]);
                    int month1 = Integer.valueOf(o1.split("-")[1]);
                    int year2 = Integer.valueOf(o2.split("-")[0]);
                    int month2 = Integer.valueOf(o2.split("-")[1]);
                    if (year1-year2 == 0){
                        if (month1-month2 == 0){
                            return 0;
                        }else if (month1-month2 < 0){
                            return -1;
                        }else if (month1-month2 > 0){
                            return 1;
                        }
                        return 1;
                    }else if (year1-year2 < 0){
                        return -1;
                    }else if (year1-year2 > 0){
                        return 1;
                    }
                    return 0;
                }
            });
            resultMap.put("timeList",timeList);
        }
        return R.ok(resultMap);
    }

    public List<Map<String,Object>>   linkOnlyOne(List<Map<String,Object>> linksList ) {
        List<Map<String,Object>> newlinksList = new ArrayList<>();
        Set<String> setList =  new HashSet<>();
        for (int i = 0; i < linksList.size(); i++) {
            Map<String,Object>  map =  linksList.get(i);
            String source =  map.get("source").toString();
            String target =  map.get("target").toString();
            String value =  map.get("value").toString();
            String join = source + target + value;
            boolean  fal = setList.contains(join);
            if(!fal) {
                setList.add(join);
                newlinksList.add(map);
            }
        }
        return newlinksList;
    }
}
