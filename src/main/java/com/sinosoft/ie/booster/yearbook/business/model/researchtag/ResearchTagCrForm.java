package com.sinosoft.ie.booster.yearbook.business.model.researchtag;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * ResearchTag模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-15 10:49:22
 */
@Data
@ApiModel
public class ResearchTagCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String name;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer zonelevel;

    /**
     * 图例类型
     */
    @ApiModelProperty(value = "图例类型")
    private String legendType;

    /**
     * 工作部门
     */
    @ApiModelProperty(value = "工作部门")
    private String organization;
    
    /**
     * 首字母
     */
    private String  initial;
    /**
     * 机构id
     */
    private String orgId;
    
}