package com.sinosoft.ie.booster.yearbook.graph.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemePaginationExportModel;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemePagination;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemeCrForm;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemeInfoVO;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemeListVO;
import com.sinosoft.ie.booster.yearbook.graph.model.graphtheme.GraphThemeUpForm;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEntity;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphThemeService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

/**
 *
 * graph_theme
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 16:14:43
 */
@Slf4j
@RestController
@Api(tags = "graph_theme")
@RequestMapping("/GraphTheme")
public class GraphThemeController {
    @Autowired
    private GraphThemeService graphThemeService;


    /**
     * 列表
     *
     * @param graphThemePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<GraphThemeListVO>> list(GraphThemePagination graphThemePagination)throws IOException{
        List<GraphThemeEntity> list= graphThemeService.getList(graphThemePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(GraphThemeEntity entity:list){
            }
        List<GraphThemeListVO> listVO=JsonUtil.getJsonToList(list,GraphThemeListVO.class);
        PageListVO<GraphThemeListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(graphThemePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param graphThemeCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid GraphThemeCrForm graphThemeCrForm) throws DataException {
        GraphThemeEntity entity=JsonUtil.getJsonToBean(graphThemeCrForm, GraphThemeEntity.class);
        graphThemeService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<GraphThemeInfoVO> info(@PathVariable("id") Long id){
        GraphThemeEntity entity= graphThemeService.getInfo(id);
        GraphThemeInfoVO vo=JsonUtil.getJsonToBean(entity, GraphThemeInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid GraphThemeUpForm graphThemeUpForm) throws DataException {
        GraphThemeEntity entity= graphThemeService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(graphThemeUpForm, GraphThemeEntity.class);
            graphThemeService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        GraphThemeEntity entity= graphThemeService.getInfo(id);
        if(entity!=null){
            graphThemeService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 计算graph_theme表
     */
    @GetMapping("/insertGraphTheme")
    public R<String> insertGraphTheme(){
        graphThemeService.insertGraphTheme();
        return R.ok();
    }
}
