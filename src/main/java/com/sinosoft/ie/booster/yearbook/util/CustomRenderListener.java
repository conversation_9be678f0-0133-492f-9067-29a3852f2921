package com.sinosoft.ie.booster.yearbook.util;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.awt.geom.RectangularShape;
import com.itextpdf.text.pdf.parser.ImageRenderInfo;
import com.itextpdf.text.pdf.parser.RenderListener;
import com.itextpdf.text.pdf.parser.TextRenderInfo;

/**
 * <AUTHOR>
 * @Date 12:53 2020/3/7
 * @Description pdf签名帮助类
 */
public class CustomRenderListener implements RenderListener{

    private float[] pcoordinate = null;

    private String keyWord;

    private int page;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public float[] getPcoordinate(){
        return pcoordinate;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    @Override
    public void beginTextBlock() {}

    @Override
    public void endTextBlock() {}

    @Override
    public void renderImage(ImageRenderInfo arg0) {}
    
  //用来存放文字的矩形
    public List<Rectangle2D.Float> rectText = new ArrayList<Rectangle2D.Float>();
  	//用来存放文字
    public List<String> textList = new ArrayList<String>();
  	//用来存放文字的y坐标
    public List<Float> listY = new ArrayList<Float>();
  	//用来存放每一行文字的坐标位置
    public List<Map<String,Rectangle2D.Float>> rows_text_rect = new ArrayList<>();
  	
    @Override
    public void renderText(TextRenderInfo renderInfo) {
		String text = renderInfo.getText();
		if(keyWord.contains(text)) {
			if(text.length() > 0){
				RectangularShape rectBase = renderInfo.getBaseline().getBoundingRectange();
				//获取文字下面的矩形
				Rectangle2D.Float rectAscen = renderInfo.getAscentLine().getBoundingRectange();
				//计算出文字的边框矩形
				float leftX = (float) rectBase.getMinX();
				float leftY = (float) rectBase.getMinY()-1;
				float rightX = (float) rectAscen.getMaxX();
				float rightY = (float) rectAscen.getMaxY()+1;
				
				Rectangle2D.Float rect = new Rectangle2D.Float(leftX, leftY, rightX - leftX, rightY - leftY);
				
					
				listY.add(rect.y);
				textList.add(text);
			}	
		}
	}

}