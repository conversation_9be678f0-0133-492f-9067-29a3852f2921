package com.sinosoft.ie.booster.yearbook.baselib.mapper;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * mate_attribute_value
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 17:52:49
 */
@Mapper
public interface MateAttributeValueMapper extends BaseMapper<MateAttributeValueEntity> {

    List<MateAttributeListVO> getAttributeValueList(@Param("ontoId") String ontoId);

    List<MateAttributeListVO> getEntityInsertValue(@Param("ontoId") long ontoId);
}
