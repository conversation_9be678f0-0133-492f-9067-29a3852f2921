package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.ConverRulesEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.converrules.ConverRulesPagination;
import java.util.*;

/**
 *
 * conver_rules
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-15 21:23:42
 */
public interface ConverRulesService extends IService<ConverRulesEntity> {

    List<ConverRulesEntity> getList(ConverRulesPagination converRulesPagination);

    List<ConverRulesEntity> getTypeList(ConverRulesPagination converRulesPagination,String dataType);



    ConverRulesEntity getInfo(Long id);

    void delete(ConverRulesEntity entity);

    void create(ConverRulesEntity entity);

    boolean update( Long id, ConverRulesEntity entity);
    
//  子表方法
    
    List  categoryVal(Long ontoId);
    
}
