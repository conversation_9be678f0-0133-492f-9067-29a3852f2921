package com.sinosoft.ie.booster.yearbook.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class ProjectConfig   implements  InitializingBean{

	@Value("${upload_path}")
	private String uploadPath;
	@Value("${down_path}")
	private String downPath;
	@Value("${extract_ip}")
	private String extractIp;
	//导入excel事件所在语料
	@Value("${corpuId}")
	private Long  corpuId;
	//语料版本
	@Value("${corpuVer}")
	private Integer  corpuVer;
	//默认图片id
	@Value("${def_img_id}")
	private String def_img_id;
	
	private List<Long>  imgs = new ArrayList<Long>();
	
	public String getTripletUrl(){
		return "http://"+extractIp+"/svo_extract";
	}
	public String getEveUrl(){
		return "http://"+extractIp+"/sentence_split";
	}
	public String  getTagResultUrl() {
		return "http://"+extractIp+"/load_database_label";
	}
	@Value("${thread-size}")
	private Integer thread_size;
	@Value("${thread-max}")
	private Integer thread_max;
	@Value("${excel_imgpath}")
	private String excel_imgpath;

	/**
	 * 初始化默认图片，把图片id放入list
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
		String[]  ids =  def_img_id.split(";");
		for (int i = 0; i < ids.length; i++) {
			imgs.add(Long.valueOf(ids[i]));
		}
	}

	public Long  getImgRandom() {
		Integer size =  imgs.size()-1;
		if(imgs.size() ==1) {
			size = 1;
		}
		Random  dom = new Random();
		int   idx = dom.nextInt(size);
		return imgs.get(idx);
	}
	
}
