package com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * MetaEntityRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:34:43
 */
@Data
@ApiModel
public class MetaEntityRelationOut {

    /**
     * 本体1的id
     */
//    @ApiModelProperty(value = "本体1的id")
//    private String onto1Id;
    

    /**
     * 本体关系名称
     */
    @ApiModelProperty(value = "本体关系名称")
    private String ontoRelName;

    /**
     * 本体1的id
     */
//    @ApiModelProperty(value = "本体1的id")
//    private String entity1Id;

    /**
     * 实体Name
     */
//    @ApiModelProperty(value = "实体Name")
//    private String entityName;

    @ApiModelProperty(value = "子级")
    private List<MetaEntityRelationInfoVO> child;

    private Long relationId;
    private Long onto2Id;
//    private String relationName;
//    private String onto1Name;
//    private String onto2Name;
//    private String entity1Name;
//    private String entity2Name;
    private Integer vals;
}