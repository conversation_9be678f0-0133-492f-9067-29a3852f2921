package com.sinosoft.ie.booster.yearbook.business.model.filedirectory;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * FileDirectory模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 14:28:43
 */
@Data
@ApiModel
public class FileDirectoryCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long pid;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Integer zonelevel;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Long fileId;

    /**
     * 图片id
     */
    @ApiModelProperty(value = "图片id")
    private Long picId;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;
}