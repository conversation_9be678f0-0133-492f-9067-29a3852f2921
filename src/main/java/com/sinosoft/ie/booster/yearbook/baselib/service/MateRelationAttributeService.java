package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattribute.MateRelationAttributePagination;
import java.util.*;

/**
 *
 * mate_relation_attribute
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-12-01 14:33:35
 */
public interface MateRelationAttributeService extends IService<MateRelationAttributeEntity> {

    List<MateRelationAttributeEntity> getList(MateRelationAttributePagination mateRelationAttributePagination);

    List<MateRelationAttributeEntity> getTypeList(MateRelationAttributePagination mateRelationAttributePagination,String dataType);



    MateRelationAttributeEntity getInfo(Long id);

    void delete(MateRelationAttributeEntity entity);

    void create(MateRelationAttributeEntity entity);

    boolean update( Long id, MateRelationAttributeEntity entity);
    
//  子表方法
}
