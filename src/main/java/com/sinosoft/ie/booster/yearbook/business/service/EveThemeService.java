package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemePagination;
import java.util.*;

/**
 *
 * eve_theme
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-06 13:45:32
 */
public interface EveThemeService extends IService<EveThemeEntity> {

    List<EveThemeEntity> getList(EveThemePagination eveThemePagination);

    List<EveThemeEntity> getTypeList(EveThemePagination eveThemePagination,String dataType);



    EveThemeEntity getInfo(Long id);

    void delete(EveThemeEntity entity);

    R create(EveThemeEntity entity);

    boolean update( Long id, EveThemeEntity entity);

    R<Map<Integer,List<EveThemeEntity>>> getListGroup();

    R<String> themeMate2Eve();

//  子表方法
    
    R getThemeTree();

    R insertTheme();
}
