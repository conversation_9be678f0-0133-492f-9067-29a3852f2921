package com.sinosoft.ie.booster.yearbook.graph.service;

import com.sinosoft.ie.booster.common.core.util.R;

public interface AiGraph1Service {

    R orgGraph(String entityId);

    R orgGraphRelationTime(String entityId);

    R orgGraphRelationTimeChart(String entityId);

    R humanGraph(String entityId);

    R humanGraphRelationTime(String entityId);

    R humanGraphRelationTimeChart(String entityId);

    R themeGraph(String theme);

    R themeGraphRelationTime(String theme);

    R themeGraphRelationTimeChart(String theme);

    R humanGraphRelationTimeChartNew(String entityId);

    R themeGraphRelationTimeChartNew(String theme);

    R orgGraphRelationTimeChartNew(String entityId);
}
