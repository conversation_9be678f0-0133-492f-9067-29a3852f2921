package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationOut;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationPagination;
import java.util.*;

/**
 *
 * meta_entity_relation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-31 16:34:43
 */
public interface MetaEntityRelationService extends IService<MetaEntityRelationEntity> {

    List<MetaEntityRelationEntity> getList(MetaEntityRelationPagination metaEntityRelationPagination);

    List<MetaEntityRelationEntity> getTypeList(MetaEntityRelationPagination metaEntityRelationPagination,String dataType);



    MetaEntityRelationEntity getInfo(Long id);

    void delete(MetaEntityRelationEntity entity);

    void create(MetaEntityRelationEntity entity);

    boolean update( Long id, MetaEntityRelationEntity entity);

    void createEntity(MetaEntityRelationEntity entity,int type);

    R getGraph(String name, Long relationId);

    R getGraphOnto(Long ontoId);

//  子表方法

}
