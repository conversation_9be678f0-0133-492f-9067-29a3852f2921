package com.sinosoft.ie.booster.yearbook.mark.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityUpForm;
import com.sinosoft.ie.booster.yearbook.mark.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkEntityService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mark_entity
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:44
 */
@Slf4j
@RestController
@Api(tags = "mark_entity")
@RequestMapping("/MarkEntity")
public class MarkEntityController {
    @Autowired
    private MarkEntityService markEntityService;


    /**
     * 列表
     *
     * @param markEntityPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MarkEntityListVO>> list(MarkEntityPagination markEntityPagination)throws IOException{
        List<MarkEntityEntity> list= markEntityService.getList(markEntityPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MarkEntityEntity entity:list){
            }
        List<MarkEntityListVO> listVO=JsonUtil.getJsonToList(list,MarkEntityListVO.class);
        PageListVO<MarkEntityListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(markEntityPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param markEntityCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MarkEntityCrForm markEntityCrForm) throws DataException {
        MarkEntityEntity entity=JsonUtil.getJsonToBean(markEntityCrForm, MarkEntityEntity.class);
        markEntityService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MarkEntityInfoVO> info(@PathVariable("id") Long id){
        MarkEntityEntity entity= markEntityService.getInfo(id);
        MarkEntityInfoVO vo=JsonUtil.getJsonToBean(entity, MarkEntityInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MarkEntityUpForm markEntityUpForm) throws DataException {
        MarkEntityEntity entity= markEntityService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(markEntityUpForm, MarkEntityEntity.class);
            markEntityService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MarkEntityEntity entity= markEntityService.getInfo(id);
        if(entity!=null){
            markEntityService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
