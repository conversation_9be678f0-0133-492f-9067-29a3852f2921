package com.sinosoft.ie.booster.yearbook.search.job;

import java.io.File;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.io.file.FileWriter;

/**
 * 同步数据后，把同步后的最后一条数据都id记录到jar包的同级文件下，文件名为 db_lock
 * 该类暂时不用
 * <AUTHOR>
 *
 */
public class DbLock {
	
	//文件名
	private String  db_lock = "db_lock";
	//事件id
	private Long event_id = 0L;
	//实体id
	private Long ent_id = 0L;
	
	public  DbLock() {
		String  db_lock_path = System.getProperty("user.dir")+File.separator+db_lock;
		File file = new File(db_lock_path);
		if(file.exists()) {
			FileReader fileReader = new FileReader(db_lock_path);
			String result = fileReader.readString();
			String [] ids =  result.split(";");
			event_id = Long.valueOf(ids[0]);
		}
	}
	
	public void  writeLock(String id_str) {
		String  db_lock_path = System.getProperty("user.dir")+File.separator+db_lock;
		File file = new File(db_lock_path);
		if(!file.exists()) {
			FileUtil.touch(file);
		}
		FileWriter writer = new FileWriter(db_lock_path);
		writer.write(id_str);
	}

	public Long getEvent_id() {
		return event_id;
	}

	public Long getEnt_id() {
		return ent_id;
	}
	
	
	
	
}
