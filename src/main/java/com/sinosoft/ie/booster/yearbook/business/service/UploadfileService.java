package com.sinosoft.ie.booster.yearbook.business.service;

import com.itextpdf.text.DocumentException;
import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.IOException;
import java.util.*;

import javax.servlet.http.HttpServletResponse;

/**
 *
 * uploadfile
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-14 10:19:51
 */
public interface UploadfileService extends IService<UploadfileEntity> {



	void previewPdf(String name,HttpServletResponse response) throws IOException;

	void previewPic(String pic,HttpServletResponse response) throws IOException;
	
	void previewImg(Long id ,HttpServletResponse response) throws IOException;

    UploadfileEntity getInfo(Long id);
}
