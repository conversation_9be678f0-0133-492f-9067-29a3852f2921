package com.sinosoft.ie.booster.yearbook.mark.cache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;

public class OntoCache {
	
	//本体map   <本体id,本体对象>
	public static Map<Long ,LibConceptEntity>  ontoIdMap =  new HashMap<>();
	//本体map   <本体名称,本体对象>
	private static Map<String ,LibConceptEntity>  ontoMap =  new HashMap<>();
	//本体属性map <本体id,属性集合>
	private static Map<Long ,List<MateAttributeEntity>>  attmap =  new HashMap<>();
	//本体属性map	<关系id, 对象>
	private static Map<Long ,MateAttributeEntity>  attriMap =  new HashMap<>();
	//本体属性map	<id, 对象>
	private static Map<Long ,MateAttributeEntity>  attrMap =  new HashMap<>();
	//本体属性值<属性id,值list>
	private static Map<Long ,List<MateAttributeValueEntity>>  attrValMap =  new HashMap<>();
	//本体关系<关系id,对象>
	private static Map<Long ,MetaOntoRelationEntity>  relationMap =  new HashMap<>();
	//本体关系<左本体id,关系list>
	private static Map<Long ,List<MetaOntoRelationEntity>>  relationListMap =  new HashMap<>();
	//本体关系<右本体id,关系list>
	private static Map<Long ,List<MetaOntoRelationEntity>>  relationLisOnto2tMap =  new HashMap<>();
	
	public static LibConceptEntity  getOntoCache(String name ){
		return ontoMap.get(name);
	}
	
	public static void saveOnto(List<LibConceptEntity> list  ) {
		list.forEach(x->{
			ontoMap.put(x.getName(), x);
			ontoIdMap.put(x.getId(), x);
		});
	}
	
	//根据本体id获取属性集合，如果是子本体则要继承父级的属性
	public static List<MateAttributeEntity>  getAttListByOntoId(Long ontoId ){
		List<MateAttributeEntity> list = attmap.get(ontoId);
//		LibConceptEntity onto = ontoIdMap.get(ontoId);
//		while(onto.getPid()!=0) {
//			onto = ontoIdMap.get(onto.getPid());
//			list.addAll(attmap.get(ontoId));
//		}
		return list;
	}
	
	public static void  saveArrList( List<MateAttributeEntity> attlist ){
		Map<Long,List<MateAttributeEntity>>  map  = attlist.stream().collect(Collectors.groupingBy(MateAttributeEntity::getOnto));
		attmap = map;
		attlist.forEach(x->{
			attriMap.put(x.getRelationId(),x);
			attrMap.put(x.getId(), x);
		});
	}
	
	public static  MateAttributeEntity  getMateAttributeEntityByRelationId(Long relationId) {
		return 	 attriMap.get(relationId);
	}
	public static  MateAttributeEntity  getMateAttributeEntityId(Long id) {
		return 	 attrMap.get(id);
	}
	
	public static void saveRelationMap(List<MetaOntoRelationEntity> list ) {
		Map<Long,MetaOntoRelationEntity> map = list.stream().collect(Collectors.toMap(MetaOntoRelationEntity::getId,MetaOntoRelationEntity->MetaOntoRelationEntity));
		relationMap = map;
		relationListMap =  list.stream().collect(Collectors.groupingBy(MetaOntoRelationEntity::getOnto1Id));
		relationLisOnto2tMap =  list.stream().collect(Collectors.groupingBy(MetaOntoRelationEntity::getOnto2Id));
		
	}
	public static MetaOntoRelationEntity getMetaOntoRelationById(Long id) {
		return relationMap.get(id);
	}
	public static List<MetaOntoRelationEntity>  getMetaOntoRelationListByOnto1Id(Long id) {
		Set<Long> keySet = relationListMap.keySet();
		for (Long long1 : keySet) {
			System.out.println(long1);
			List<MetaOntoRelationEntity> list = relationListMap.get(long1);
			System.out.println(list);
		}
		return 	relationListMap.get(2315835908009L);
	}
	
	public static  List<MetaOntoRelationEntity>  getMetaOntoRelationListByOnto2Id(Long id) {
		return 	relationLisOnto2tMap.get(id);
	}
	
	
	
	public static void saveAttrValList(List<MateAttributeValueEntity> list) {
		attrValMap = list.stream().collect(Collectors.groupingBy(MateAttributeValueEntity::getAttributeId));
	}
	public static List<MateAttributeValueEntity> getAttrValListByAttrId(Long id ) {
		return attrValMap.get(id);
	}
	
	
	
}
