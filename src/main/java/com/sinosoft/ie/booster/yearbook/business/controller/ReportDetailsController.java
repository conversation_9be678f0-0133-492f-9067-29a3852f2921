package com.sinosoft.ie.booster.yearbook.business.controller;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ReportDetails;
import com.sinosoft.ie.booster.yearbook.business.service.ReportDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/reportDetails")
public class ReportDetailsController {
    @Resource
    private ReportDetailsService reportDetailsService;

    @GetMapping("/getReportDetails")
    public  List<Map<String,String>> getReportDetails(@RequestParam("theme") String theme){

        List<Map<String,String>> map =   reportDetailsService.getReportDetails(theme);

        return map;
    }
}
