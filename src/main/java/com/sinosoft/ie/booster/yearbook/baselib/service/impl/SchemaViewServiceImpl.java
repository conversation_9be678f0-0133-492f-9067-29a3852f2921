package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptSchemaView;
import com.sinosoft.ie.booster.yearbook.baselib.service.*;
import com.sun.org.apache.bcel.internal.generic.NEW;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaSchemaEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.SchemaView;

@Service
public class SchemaViewServiceImpl implements SchemaViewService {

	@Resource
	private LibConceptService libConceptService;
	@Resource
	private MetaSchemaService metaSchemaService;
	@Resource
	private MateAttributeService mateAttributeService;
	@Resource
	private MetaOntoRelationService metaOntoRelationService;
	
	
	@Override
	public R getView(Long id) {
		
		MetaSchemaEntity schema = metaSchemaService.getById(id);
		SchemaView  view = new SchemaView();
		view.setName(schema.getName());
		view.setStyle("#5951F8");
		QueryWrapper<LibConceptEntity> query = new QueryWrapper<LibConceptEntity>();
		query.eq("schema_id", id);
		query.eq("pid", 0);
		List<LibConceptEntity>  conceptList = libConceptService.list(query);
		List<SchemaView>  ontoList = new ArrayList<SchemaView>();
		for (int i = 0; i < conceptList.size(); i++) {
			SchemaView  view1 = new SchemaView();
			view1.setName(conceptList.get(i).getName());
			view1.setStyle("#5951F8");
			ontoList.add(view1);
			QueryWrapper<MateAttributeEntity> attrQuery = new QueryWrapper<MateAttributeEntity>();
			attrQuery.eq("onto", conceptList.get(i).getId());
			List<MateAttributeEntity>  attrList =  mateAttributeService.list(attrQuery);
			List<SchemaView>  attrViewList = new ArrayList<SchemaView>();
			for (int j = 0; j < attrList.size(); j++) {
				SchemaView  view2 = new SchemaView();
				view2.setName(attrList.get(j).getNameCn());
				view2.setStyle("#F05B01");
				attrViewList.add(view2);
			}
			view1.setChildren(attrViewList);
		}
		view.setChildren(ontoList);
		
		return R.ok(view);
	}

	@Override
	public R getSchemaRelation(Long id) {
		Map<String,Object> totalMap = new HashMap<>();
		//查询本体
		QueryWrapper<LibConceptEntity> conceptWrapper=new QueryWrapper<>();
		conceptWrapper.select("id","name");
		conceptWrapper.lambda().and(t->t.eq(LibConceptEntity::getSchemaId,id));
		conceptWrapper.lambda().and(t->t.eq(LibConceptEntity::getPid,0));
		List<LibConceptEntity> conceptlistEntity = libConceptService.list(conceptWrapper);
		List<LibConceptSchemaView> conceptlistVO= JsonUtil.getJsonToList(conceptlistEntity,LibConceptSchemaView.class);
		totalMap.put("nodes",conceptlistVO);
		//查询关系
		QueryWrapper<MetaOntoRelationEntity> relationWrapper=new QueryWrapper<>();
		relationWrapper.select("onto1_id","onto2_id","name");
		relationWrapper.lambda().and(t->t.eq(MetaOntoRelationEntity::getSchemaId,id));
		List<MetaOntoRelationEntity> relationlistEntity = metaOntoRelationService.list(relationWrapper);
		List<Map<String,Object>> relationList = new ArrayList<>();
		for (MetaOntoRelationEntity relation:relationlistEntity) {
			Map<String,Object> relationMap = new HashMap<>();
			relationMap.put("source",relation.getOnto1Id());
			relationMap.put("target",relation.getOnto2Id());
			relationMap.put("value",relation.getName());
			relationList.add(relationMap);
		}
		totalMap.put("links",relationList);
		return R.ok(totalMap);
	}
}
