package com.sinosoft.ie.booster.yearbook.mark.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusUpForm;
import com.sinosoft.ie.booster.yearbook.mark.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkCorpusEntity;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkCorpusService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mark_corpus
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-24 17:45:08
 */
@Slf4j
@RestController
@Api(tags = "mark_corpus")
@RequestMapping("/MarkCorpus")
public class MarkCorpusController {
    @Autowired
    private MarkCorpusService markCorpusService;


    /**
     * 列表
     *
     * @param markCorpusPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MarkCorpusListVO>> list(MarkCorpusPagination markCorpusPagination)throws IOException{
        List<MarkCorpusEntity> list= markCorpusService.getList(markCorpusPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MarkCorpusEntity entity:list){
            }
        List<MarkCorpusListVO> listVO=JsonUtil.getJsonToList(list,MarkCorpusListVO.class);
        PageListVO<MarkCorpusListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(markCorpusPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param markCorpusCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MarkCorpusCrForm markCorpusCrForm) throws DataException {
        MarkCorpusEntity entity=JsonUtil.getJsonToBean(markCorpusCrForm, MarkCorpusEntity.class);
        markCorpusService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MarkCorpusInfoVO> info(@PathVariable("id") Long id){
        MarkCorpusEntity entity= markCorpusService.getInfo(id);
        MarkCorpusInfoVO vo=JsonUtil.getJsonToBean(entity, MarkCorpusInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MarkCorpusUpForm markCorpusUpForm) throws DataException {
        MarkCorpusEntity entity= markCorpusService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(markCorpusUpForm, MarkCorpusEntity.class);
            markCorpusService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MarkCorpusEntity entity= markCorpusService.getInfo(id);
        if(entity!=null){
            markCorpusService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 查询全部列表,带分页
     */
    @GetMapping("/getAllList")
    public R<PageListVO<MarkCorpusListVO>> getAllList(MarkCorpusPagination markCorpusPagination)throws IOException{
        return R.ok(markCorpusService.getAllList(markCorpusPagination));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/deleteChild/{id}")
    @Transactional
    public R<String> deleteChild(@PathVariable("id") Long id){
        markCorpusService.deleteChild(id);
        return R.ok(null, "删除成功");
    }

    /**
     * 创建语料时自动创建版本
     */
    @PostMapping("/createWithVersion")
    @Transactional
    public R<String> createWithVersion(@RequestBody @Valid MarkCorpusCrForm markCorpusCrForm) throws DataException {
        MarkCorpusEntity entity=JsonUtil.getJsonToBean(markCorpusCrForm, MarkCorpusEntity.class);
        markCorpusService.create(entity);
        entity.setPid(entity.getId());
        entity.setId(null);
        entity.setVersion(1);
        entity.setName(null);
        markCorpusService.create(entity);
        return R.ok(null, "新建成功");
    }

    /**
     * 清空版本-清空语料
     */
    @DeleteMapping("/deleteCorpusVersion")
    @Transactional
    public R<String> deleteCorpusVersion(@RequestBody @Valid MarkCorpusCrForm markCorpusCrForm){
        markCorpusService.deleteCorpusVersion(markCorpusCrForm);
        return R.ok(null, "删除成功");
    }

//    /**
//     * 清空标注-参数：id,pid,version
//     */
//    @DeleteMapping("/deleteMarkBySentence")
//    @Transactional
//    public R<String> deleteMarkBySentence(MarkCorpusCrForm markCorpusCrForm){
//        markCorpusService.deleteMarkBySentence(markCorpusCrForm);
//        return R.ok(null, "删除成功");
//    }

    /**
     * 清空标注-参数：句子ids，逗号隔开
     */
    @DeleteMapping("/deleteMarkBySentenceIds")
    @Transactional
    public R<String> deleteMarkBySentence(String ids){
        markCorpusService.deleteMarkBySentenceIds(ids);
        return R.ok(null, "删除成功");
    }
    /**
     * 导入语料文件
     *
     * @param markCorpusCrForm
     * @return
     */
    @PostMapping("/importCorpus")
    @Transactional
    public R<String> importCorpus(@RequestBody @Valid MarkCorpusCrForm markCorpusCrForm) throws DataException {
        MarkCorpusEntity entity=JsonUtil.getJsonToBean(markCorpusCrForm, MarkCorpusEntity.class);
       
        return  markCorpusService.importCorpus(entity);
}

    /**
     * 新增版本
     */
    @PostMapping("/createVersion")
    @Transactional
    public R<String> createVersion(@RequestBody @Valid MarkCorpusCrForm markCorpusCrForm) throws DataException {
        MarkCorpusEntity entity=JsonUtil.getJsonToBean(markCorpusCrForm, MarkCorpusEntity.class);
        markCorpusService.create(entity);
        markCorpusService.inheritVersion(entity);
        return R.ok(null, "新建成功");
    }

    
    
}
