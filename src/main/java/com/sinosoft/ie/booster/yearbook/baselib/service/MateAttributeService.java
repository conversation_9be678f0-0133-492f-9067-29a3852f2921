package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributePagination;
import java.util.*;

/**
 *
 * mate_attribute
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 14:48:56
 */
public interface MateAttributeService extends IService<MateAttributeEntity> {

    List<MateAttributeEntity> getList(MateAttributePagination mateAttributePagination);

    List<MateAttributeEntity> getTypeList(MateAttributePagination mateAttributePagination,String dataType);



    MateAttributeEntity getInfo(Long id);

    void delete(MateAttributeEntity entity);

    void create(MateAttributeEntity entity);

    boolean update( Long id, MateAttributeEntity entity);

    void createNumValueList(List<MateAttributeCrForm> list);

    void createObjectValueList(List<MateAttributeCrForm> list);

    MateAttributeListVO getMoShiChild(MateAttributeListVO mateAttributeListVO);

    void deleteids(String ids);


//  子表方法
}
