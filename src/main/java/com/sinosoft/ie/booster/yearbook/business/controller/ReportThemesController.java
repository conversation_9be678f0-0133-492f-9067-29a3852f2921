package com.sinosoft.ie.booster.yearbook.business.controller;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ReportThemes;
import com.sinosoft.ie.booster.yearbook.business.service.ReportThemesService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/reportThemes")
public class ReportThemesController {

    @Resource
    private ReportThemesService reportThemesService;

    @GetMapping("/getReportThemesList")
    public R getReportThemesList() {
        List<ReportThemes> reportThemesList = reportThemesService.getReportThemesList();
        return R.ok(reportThemesList);
    }
}
