package com.sinosoft.ie.booster.yearbook.util;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * mybatis-flex 项目中的id生成器，
 * 修改后id共14位
 * <AUTHOR>
 *
 */
public class IdWork {

	private static final long INITIAL_TIMESTAMP = 1680411660000L;
	private static final long MAX_CLOCK_SEQ = 99;

	private static  long lastTimeMillis = 0;// 最后一次生成 ID 的时间
	private static long clockSeq = 0; // 时间序列
	private static long workId = 1; // 机器 ID

	
	private static List<Long> idList  =  new ArrayList<>();
	
	public IdWork() {
	}

	public IdWork(long workId) {
		this.workId = workId;
	}

	public static long getId() {
		long id = nextId();
		return id;
	}

	private static synchronized long nextId() {

		// 当前时间
		long currentTimeMillis = System.currentTimeMillis();

		if (currentTimeMillis == lastTimeMillis) {
			clockSeq++;
			if (clockSeq > MAX_CLOCK_SEQ) {
				clockSeq = 0;
				currentTimeMillis++;
			}
		}

		// 出现时间回拨
		else if (currentTimeMillis < lastTimeMillis) {
			currentTimeMillis = lastTimeMillis;
			clockSeq++;

			if (clockSeq > MAX_CLOCK_SEQ) {
				clockSeq = 0;
				currentTimeMillis++;
			}
		} else {
			clockSeq = 0;
		}

		lastTimeMillis = currentTimeMillis;
		long diffTimeMillis = currentTimeMillis - INITIAL_TIMESTAMP;
		// ID组成：时间（7+）| 毫秒内的时间自增 （00~99：2）| 机器ID（00 ~ 99：2）| 随机数（00~99：2）
		return diffTimeMillis * 1000000 + clockSeq * 10000 + workId * 100 + getRandomInt();
	}

	private static int getRandomInt() {
		return ThreadLocalRandom.current().nextInt(1000);
	}

}
