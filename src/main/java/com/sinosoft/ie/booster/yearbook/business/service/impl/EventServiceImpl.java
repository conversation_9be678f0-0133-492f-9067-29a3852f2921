package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTag2Entity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.FileDirectoryMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTag2Mapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventListVO;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.WordCloudEntity;
import com.sinosoft.ie.booster.yearbook.business.service.EveThemeService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTagService;
import com.sinosoft.ie.booster.yearbook.business.service.YearbookService;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 *
 * event 版本： V1.0.0 作者： booster开发平台组 日期： 2022-09-15 10:16:34
 */
@Service
public class EventServiceImpl extends ServiceImpl<EventMapper, EventEntity> implements EventService {

	@Autowired
	private EventMapper eventMapper;

	@Autowired
	private ResearchTagMapper researchTagMapper;
	@Resource
	private ResearchTag2Mapper researchTag2Mapper;
	@Resource
	private LibEntityMapper libEntityMapper;
	@Resource
	private FileDirectoryMapper fileDirectoryMapper;
	@Resource
	private YearbookService yearbookService;
	@Resource
	private ResearchTagService researchTagService;
	@Autowired
	private EveThemeService eveThemeService;

	@Override
	public List<EventEntity> getList(EventPagination eventPagination) {
		QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
		// 关键字（账户、姓名、手机）
		if (eventPagination.getTitle() != null && !eventPagination.getTitle().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getTitle, eventPagination.getTitle());
		}

		if (eventPagination.getId() != null) {
			queryWrapper.lambda().eq(EventEntity::getId, eventPagination.getId());
		}

		if (eventPagination.getVenue() != null && !eventPagination.getVenue().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getVenue, eventPagination.getVenue());
		}

		if (eventPagination.getPersons() != null && !eventPagination.getPersons().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getPersons, eventPagination.getPersons());
		}

		if (eventPagination.getArticles() != null && !eventPagination.getArticles().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getArticles, eventPagination.getArticles());
		}

		if (eventPagination.getOrganisation() != null && !eventPagination.getOrganisation().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getOrganisation, eventPagination.getOrganisation());
		}

		if (eventPagination.getCategory() != null && !eventPagination.getCategory().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getCategory, eventPagination.getCategory());
		}

		if (eventPagination.getOrgid() != null && !eventPagination.getOrgid().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getOrgid, eventPagination.getOrgid());
		}

		if (eventPagination.getTag() != null && !eventPagination.getTag().isEmpty()) {
			queryWrapper.lambda().like(EventEntity::getTag, eventPagination.getTag());
		}

		// 年+月
		if (eventPagination.getYear() != null && eventPagination.getMonth() != null
				&& eventPagination.getDay() == null) {
			String time = String.format("%s-%02d", eventPagination.getYear(), eventPagination.getMonth());
			queryWrapper.lambda().like(EventEntity::getStartTime, time);
		}

		// 月+日（忽略年份）
		if (eventPagination.getQueryType() == null && eventPagination.getDay() != null
				&& eventPagination.getMonth() != null && eventPagination.getYear() == null) {
			String time = String.format("-%02d-%02d", eventPagination.getMonth(), eventPagination.getDay());
			queryWrapper.lambda().like(EventEntity::getStartTime, time);
		}

		// 时间段查询（最近7天/30天/180天）
		if (eventPagination.getQueryType() != null && eventPagination.getMonth() != null && eventPagination.getDay() != null) {
			// 使用动态年份，默认取2020年（可根据实际需求调整）
			int year = eventPagination.getYear() != null ? Integer.parseInt(eventPagination.getYear()) : 2020;
			LocalDate baseDate = LocalDate.of(year, eventPagination.getMonth(), eventPagination.getDay());
			LocalDate startDate = null;

			switch (eventPagination.getQueryType()) {
				case 1: // 最近7天
					startDate = baseDate.minusDays(7);
					break;
				case 2: // 最近30天
					startDate = baseDate.minusDays(30);
					break;
				case 3: // 最近180天
					startDate = baseDate.minusDays(180);
					break;
				default:
					break;
			}

			if (startDate != null) {
				// 日期格式转换（假设数据库中 startTime 格式为 yyyy-MM-dd）
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
				String startDateStr = startDate.format(formatter);
				String endDateStr = baseDate.format(formatter);

				queryWrapper.lambda()
						.ge(EventEntity::getStartTime, startDateStr)
						.lt(EventEntity::getStartTime, endDateStr);
			}
		}

		// 查询事件，不包含 开始时间为01-01，结束时间为12-31的事件
		if (eventPagination.getYear() != null) {
			queryWrapper.lambda()
					.ne(EventEntity::getStartTime, eventPagination.getYear() + "-01-01")
					.ne(EventEntity::getEndTime, eventPagination.getYear() + "-12-31");
		}

		queryWrapper.lambda().orderByAsc(EventEntity::getStartTime);

		// 统一处理分页逻辑
		if (eventPagination.getCurrentPage() <= 0) {
			return this.list(queryWrapper);
		}

		Page<EventEntity> page = new Page<>(eventPagination.getCurrentPage(), eventPagination.getPageSize());
		IPage<EventEntity> userIPage = this.page(page, queryWrapper);
		return eventPagination.setData(userIPage.getRecords(), userIPage.getTotal());
	}
//	public List<EventEntity> getList(EventPagination eventPagination) {
//		QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
//		// 关键字（账户、姓名、手机）
//		if (!"null".equals(String.valueOf(eventPagination.getTitle()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getTitle, eventPagination.getTitle()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getId()))) {
//			queryWrapper.lambda().and(t -> t.eq(EventEntity::getId, eventPagination.getId()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getVenue()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getVenue, eventPagination.getVenue()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getPersons()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getPersons, eventPagination.getPersons()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getArticles()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getArticles, eventPagination.getArticles()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getOrganisation()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getOrganisation, eventPagination.getOrganisation()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getCategory()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getCategory, eventPagination.getCategory()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getOrgid()))) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getOrgid, eventPagination.getOrgid()));
//		}
//
//		if (!"null".equals(String.valueOf(eventPagination.getTag())) && !eventPagination.getTag().isEmpty()) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getTag, eventPagination.getTag()));
//		}
//
//		// 年+月
//		if (eventPagination.getYear() != null && eventPagination.getMonth() != null
//				&& eventPagination.getDay() == null) {
//			String time = eventPagination.getYear();
//			if (eventPagination.getMonth() < 10) {
//				time += "-0" + eventPagination.getMonth();
//			} else {
//				time += "-" + eventPagination.getMonth();
//			}
//			String finalTime = time;
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, finalTime));
//		} else if (eventPagination.getYear() != null && eventPagination.getMonth() != null) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, eventPagination.getYear()));
//		}
//
//		// 月+日
//		if (eventPagination.getQueryType() == null && eventPagination.getDay() != null
//				&& eventPagination.getMonth() != null && eventPagination.getYear() == null) {
//			String time = "";
//			if (eventPagination.getMonth() < 10) {
//				time += "-0" + eventPagination.getMonth();
//			} else {
//				time += "-" + eventPagination.getMonth();
//			}
//			if (eventPagination.getDay() < 10) {
//				time += "-0" + eventPagination.getDay();
//			} else {
//				time += "-" + eventPagination.getDay();
//			}
//			String finalTime = time;
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, finalTime));
//		} else if (eventPagination.getYear() != null && eventPagination.getMonth() != null) {
//			queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, eventPagination.getYear()));
//		}
//
//		if (eventPagination.getQueryType() != null) {
//			String month_day = "";
//			if (eventPagination.getMonth() < 10) {
//				month_day += "-0" + eventPagination.getMonth();
//			} else {
//				month_day += "-" + eventPagination.getMonth();
//			}
//			month_day += "-" + eventPagination.getDay();
//
//			if (eventPagination.getQueryType() == 1) {
//				queryWrapper.lambda().apply("ADD_DAYS('2020" + month_day
//						+ "',-7 ) < CONVERT(date,start_time)  AND '2020" + month_day + "' >CONVERT(date,start_time)");
//			}
//			if (eventPagination.getQueryType() == 2) {
//				queryWrapper.lambda().apply("ADD_DAYS('2020" + month_day
//						+ "', -30 ) < CONVERT(date,start_time) AND '2020" + month_day + "' > CONVERT(date,start_time)");
//			}
//			if (eventPagination.getQueryType() == 3) {
//				queryWrapper.lambda()
//						.apply("ADD_DAYS('2020" + month_day + "', - 180  ) < CONVERT(date,start_time) AND '2020"
//								+ month_day + "' > CONVERT(date,start_time)");
//			}
//		}
//		//查询事件，不包含 开始时间为01-01，结束时间为12-31的事件
//		queryWrapper.lambda().and(t -> t.ne(EventEntity::getStartTime, eventPagination.getYear() + "-01-01"));
//		queryWrapper.lambda().and(t -> t.ne(EventEntity::getEndTime, eventPagination.getYear() + "-12-31"));
//
//		queryWrapper.lambda().orderByAsc(EventEntity::getStartTime);
//		if (eventPagination.getCurrentPage() == 0) {
//			List<EventEntity> list = this.list(queryWrapper);
//			return list;
//		}
//
//		Page<EventEntity> page = new Page<>(eventPagination.getCurrentPage(), eventPagination.getPageSize());
//		IPage<EventEntity> userIPage = this.page(page, queryWrapper);
//		return eventPagination.setData(userIPage.getRecords(), userIPage.getTotal());
//	}

	@Override
	public List<EventEntity> getTypeList(EventPagination eventPagination, String dataType) {
		QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
		// 关键字（账户、姓名、手机）
		if (!"null".equals(String.valueOf(eventPagination.getTitle()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getTitle, eventPagination.getTitle()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getId()))) {
			queryWrapper.lambda().and(t -> t.eq(EventEntity::getId, eventPagination.getId()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getVenue()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getVenue, eventPagination.getVenue()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getPersons()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getPersons, eventPagination.getPersons()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getArticles()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getArticles, eventPagination.getArticles()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getOrganisation()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getOrganisation, eventPagination.getOrganisation()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getCategory()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getCategory, eventPagination.getCategory()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getOrgid()))) {
			queryWrapper.lambda().and(t -> t.eq(EventEntity::getOrgid, eventPagination.getOrgid()));
		}

		if (!"null".equals(String.valueOf(eventPagination.getTag()))) {
			queryWrapper.lambda().and(t -> t.like(EventEntity::getTag, eventPagination.getTag()));
		}

		// 排序
		if (StrUtil.isEmpty(eventPagination.getSidx())) {
			queryWrapper.lambda().orderByDesc(EventEntity::getId);
		} else {
			queryWrapper = "asc".equals(eventPagination.getSort().toLowerCase())
					? queryWrapper.orderByAsc(eventPagination.getSidx())
					: queryWrapper.orderByDesc(eventPagination.getSidx());
		}
		if ("0".equals(dataType)) {
			Page<EventEntity> page = new Page<>(eventPagination.getCurrentPage(), eventPagination.getPageSize());
			IPage<EventEntity> userIPage = this.page(page, queryWrapper);
			return eventPagination.setData(userIPage.getRecords(), userIPage.getTotal());
		} else {
			return this.list(queryWrapper);
		}
	}

	@Override
	public EventEntity getInfo(Long id) {
		QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(EventEntity::getId, id);
		EventEntity ent = this.getOne(queryWrapper);
		if (ObjectUtil.isEmpty(ent.getPageNum()) || ObjectUtil.isEmpty(ent.getStartTime())) {
			return ent;
		}
		String year = ent.getStartTime().substring(0, 4);
		StringBuffer sb = new StringBuffer();
		QueryWrapper<EventEntity> query = new QueryWrapper<>();
		query.like("start_time", year);
		query.eq("page_num", ent.getPageNum());
		List<EventEntity> list = this.list(query);
		Set<String> tagSet = new HashSet<>();
		for (int i = 0; i < list.size(); i++) {
			String tag = list.get(i).getTag();
			if (!StringUtils.isEmpty(tag)) {
				String[] tags = tag.split(";");
				for (int j = 0; j < tags.length; j++) {
					if (!ent.getTag().contains(tags[j])) {
						tagSet.add(tags[j]);
					}
				}
			}
		}
		for (String string : tagSet) {
			sb.append(string).append(";");
		}
		ent.setTag2(sb.toString());
		
		List<EveThemeEntity> themeList =  eveThemeService.list();
		List<String> themeStrList = themeList.stream().filter(x -> x.getDir() != null && x.getDir() == 1).map(EveThemeEntity::getName).collect(Collectors.toList());
		String them = ent.getTheme();
		if(!themeStrList.contains(them)) {
			ent.setTheme(null);
		}
		return ent;
	}

	@Override
	public void create(EventEntity entity) {
		this.save(entity);
	}

	@Override
	public boolean update(Long id, EventEntity entity) {
		entity.setId(id);
		return this.updateById(entity);
	}

	@Override
	public void delete(EventEntity entity) {
		if (entity != null) {
			this.removeById(entity.getId());
		}
	}

	// 子表方法
	@Override
	public JSONArray getAllTime() {
        // 原来的实现
//		QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
//		queryWrapper.select("id","start_time");
//		List<EventEntity> list = this.list(queryWrapper);
//		Map<Integer, List<Integer>> timeMap = new HashMap<>();
//		for (int i = 0; i < list.size(); i++) {
//			String time = list.get(i).getStartTime();
//			if (time != null) {
//				String[] ts = time.split("-");
//				Integer year = Integer.valueOf(ts[0]);
//				List<Integer> monthlist = timeMap.get(year);
//				// 处理时间，分成年月
//				String mo = ts[1];
//				String m = "";
//				if (mo.indexOf("0") == 0) {
//					m = mo.substring(1, mo.length());
//				} else {
//					m = mo;
//				}
//				Integer mot = Integer.valueOf(m);
//				if (monthlist == null) {
//					monthlist = new ArrayList<>();
//				}
//				monthlist.add(mot);
//				timeMap.put(year, monthlist);
//			}
//		}
//		JSONArray arr = JSONUtil.createArray();
//		for (Integer key : timeMap.keySet()) {
//			JSONObject ojb = JSONUtil.createObj();
//			ojb.set("year", key);
//			ojb.set("month", timeMap.get(key));
//			arr.add(ojb);
//		}
//		return arr;

        // 新的实现
        // 1. 先获取所有不同的年份
        QueryWrapper<EventEntity> yearQuery = new QueryWrapper<>();
        yearQuery.select("DISTINCT YEAR(start_time) as year")
                .isNotNull("start_time")
                .orderByAsc("year");
        
        List<Map<String, Object>> years = this.listMaps(yearQuery);
        
        JSONArray result = JSONUtil.createArray();
        
        for (Map<String, Object> yearMap : years) {
            Integer year = ((Number) yearMap.get("year")).intValue();
            
            // 2. 对每个年份查询其对应的月份
            QueryWrapper<EventEntity> monthQuery = new QueryWrapper<>();
            monthQuery.select("DISTINCT MONTH(start_time) as month")
                    .eq("YEAR(start_time)", year)
                    .orderByAsc("month");
            
            List<Map<String, Object>> months = this.listMaps(monthQuery);
            
            // 3. 构建返回数据
            JSONObject yearData = JSONUtil.createObj();
            yearData.set("year", year);
            
            List<Integer> monthList = months.stream()
                    .map(m -> ((Number) m.get("month")).intValue())
                    .collect(Collectors.toList());
            
            yearData.set("month", monthList);
            result.add(yearData);
        }
        
        return result;
	}

	/**
	 * 事件脉络查询-返回数据
	 * 
	 * @param year
	 * @param theme
	 * @return
	 */
	@Override
	public Map<Integer, List<EventEntity>> queryEventByYearNoDay(String year, String theme, String typeParam) {
		Map<Integer, List<EventEntity>> map = new HashMap<>();
		// 标签
		String tagSql = "";
		if (typeParam != null && !typeParam.isEmpty()) {
			tagSql = "( ";
			String[] tagStrList = typeParam.split(",");
			for (int i = 0; i < tagStrList.length; i++) {
				if (i > 0) {
					tagSql += " OR ";  // 改为OR，因为我们要找任一条件满足的记录
				}
				if (tagStrList[i].equals("politicalwork")) {
					tagSql += "(political_work IS NOT NULL AND political_work != '')";
				} else if (tagStrList[i].equals("securitymanage")) {
					tagSql += "(security_manage IS NOT NULL AND security_manage != '')";
				} else if (tagStrList[i].equals("comprehensivecoordination")) {
					tagSql += "(comprehensive_coordination IS NOT NULL AND comprehensive_coordination != '')";
				} else if (tagStrList[i].equals("scientificmanage")) {
					tagSql += "(scientific_manage IS NOT NULL AND scientific_manage != '')";
				} else if (tagStrList[i].equals("decisionmaking")) {
					tagSql += "(decision_making IS NOT NULL AND decision_making != '')";
				} else if (tagStrList[i].equals("scientificstrength")) {
					tagSql += "(scientific_strength IS NOT NULL AND scientific_strength != '')";
				} else if (tagStrList[i].equals("scientificorganization")) {
					tagSql += "(scientific_organization IS NOT NULL AND scientific_organization != '')";
				} else if (tagStrList[i].equals("scientificsubject")) {
					tagSql += "(scientific_subject IS NOT NULL AND scientific_subject != '')";
				} else if (tagStrList[i].equals("scientificresult")) {
					tagSql += "(scientific_result IS NOT NULL AND scientific_result != '')";
				} else if (tagStrList[i].equals("scientificfunding")) {
					tagSql += "(scientific_funding IS NOT NULL AND scientific_funding != '')";
				}
			}
			tagSql += ")";
		}
		for (int i = 1; i < 13; i++) {
			QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
			// 时间
			String time = "";
			if (i > 9) {
				time = year + "-" + i + "-";
			} else {
				time = year + "-0" + i + "-";
			}
			String finalTime = time;
			queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, finalTime));
			// 标签
			if (typeParam != null && !typeParam.isEmpty()) {
				queryWrapper.apply(tagSql);
			}
			if (theme != null && !theme.isEmpty()) {
				queryWrapper.lambda().and(t -> t.eq(EventEntity::getTheme, theme));
			}
			queryWrapper.lambda().orderByAsc(EventEntity::getStartTime);
			queryWrapper.select("id", "title", "start_time", "category", "political_work", "tag", "security_manage",
					"comprehensive_coordination", "scientific_manage", "decision_making", "scientific_strength",
					"scientific_organization", "scientific_subject", "scientific_result", "page_num",
					"scientific_funding");
			List<EventEntity> list1 = this.list(queryWrapper);
			if (list1 != null && list1.size() > 0) {
				map.put(i, list1);
			}
		}
		return map;
	}

	/**
	 * 事件脉络查询-返回数据-取权重最大的一条
	 * 
	 * @param year
	 * @param theme
	 * @return
	 */
	@Override
	public Map<Integer, EventInfoVO> queryEventByYearNoDayOneEvent(String year, String theme, String typeParam) {
		Map<Integer, EventInfoVO> map = new HashMap<>();
		// 标签
		String tagSql = "";
		if (typeParam != null && !typeParam.isEmpty()) {
			tagSql = "( ";
			String[] tagStrList = typeParam.split(",");
			for (int i = 0; i < tagStrList.length; i++) {
				if (i > 0) {
					tagSql += " AND ";
				}
				if (tagStrList[i].equals("politicalwork")) {
					tagSql += "(political_work IS NULL OR political_work = '')";
				} else if (tagStrList[i].equals("securitymanage")) {
					tagSql += "(security_manage IS NULL OR security_manage = '')";
				} else if (tagStrList[i].equals("comprehensivecoordination")) {
					tagSql += "(comprehensive_coordination IS NULL OR comprehensive_coordination = '')";
				} else if (tagStrList[i].equals("scientificmanage")) {
					tagSql += "(scientific_manage IS NULL OR scientific_manage = '')";
				} else if (tagStrList[i].equals("decisionmaking")) {
					tagSql += "(decision_making IS NULL OR decision_making = '')";
				} else if (tagStrList[i].equals("scientificstrength")) {
					tagSql += "(scientific_strength IS NULL OR scientific_strength = '')";
				} else if (tagStrList[i].equals("scientificorganization")) {
					tagSql += "(scientific_organization IS NULL OR scientific_organization = '')";
				} else if (tagStrList[i].equals("scientificsubject")) {
					tagSql += "(scientific_subject IS NULL OR scientific_subject = '')";
				} else if (tagStrList[i].equals("scientificresult")) {
					tagSql += "(scientific_result IS NULL OR scientific_result = '')";
				} else if (tagStrList[i].equals("scientificfunding")) {
					tagSql += "(scientific_funding IS NULL OR scientific_funding = '')";
				}

			}
			tagSql += ")";
		}
		for (int i = 1; i < 13; i++) {
			QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
			// 时间
			String time = "";
			if (i > 9) {
				time = year + "-" + i + "-";
			} else {
				time = year + "-0" + i + "-";
			}
			String finalTime = time;
			queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, finalTime));
			// 标签
			if (typeParam != null && !typeParam.isEmpty()) {
				queryWrapper.lambda().apply(tagSql);
			}
			if (theme != null && !theme.isEmpty()) {
				queryWrapper.lambda().and(t -> t.eq(EventEntity::getTheme, theme));
			}
			queryWrapper.lambda().orderByDesc(EventEntity::getWeight);
			queryWrapper.select("id", "title", "start_time", "category", "political_work", "tag", "security_manage",
					"comprehensive_coordination", "scientific_manage", "decision_making", "scientific_strength",
					"scientific_organization", "scientific_subject", "scientific_result", "page_num");
			queryWrapper.last("limit 1");
			List<EventEntity> list1 = this.list(queryWrapper);
			if (list1 != null && list1.size() > 0) {
				EventInfoVO eventInfoVO = JsonUtil.getJsonToBean(list1.get(0), EventInfoVO.class);
				eventInfoVO.setMonthCount(list1.size());
				map.put(i, eventInfoVO);
			}
		}
		return map;
	}

	@Override
	public Map<String, Integer> queryEventCountByYear(String year, String theme) {
		Map<String, Integer> map = new HashMap<>();
		QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
		// 使用LIKE匹配年份，但确保只匹配年份部分
		queryWrapper.lambda().and(t -> t.like(EventEntity::getStartTime, year + "-"));
		if (theme != null && !theme.isEmpty()) {
			queryWrapper.lambda().and(t -> t.eq(EventEntity::getTheme, theme));
		}
		
		List<EventEntity> list = this.list(queryWrapper);
		int eventCount = list.size();
		Set<String> orgSet = new HashSet<>();
		int achievementsCount = 0;
		int prizeCount = 0;
		
		if (list != null && list.size() > 0) {
			for (EventEntity event : list) {
				// 统计机构数量
				if (event.getOrganisation() != null && !event.getOrganisation().isEmpty()) {
					String orgstr = event.getOrganisation();
					String[] orgs = orgstr.split(";");
					for(String org : orgs) {
						if(!StringUtils.isEmpty(org) && !"无".equals(org.trim())) {
							orgSet.add(org.trim());
						}
					}
				}
				
				// 统计成果数量
				if (event.getScientificResult() != null && !event.getScientificResult().isEmpty() 
					&& !"无".equals(event.getScientificResult().trim())) {
					String[] achievements = event.getScientificResult().split(";");
					for (String achievement : achievements) {
						if (!StringUtils.isEmpty(achievement.trim())) {
							achievementsCount++;
						}
					}
				}
				
				// 统计奖项数量
				if (event.getPrize() != null && !event.getPrize().isEmpty() 
					&& !"无".equals(event.getPrize().trim())) {
					String[] prizes = event.getPrize().split(";");
					for (String prize : prizes) {
						if (!StringUtils.isEmpty(prize.trim())) {
							prizeCount++;
						}
					}
				}
			}
		}
		
		map.put("eventCount", eventCount);
		map.put("orgCount", orgSet.size());
		map.put("achievementsCount", achievementsCount);
		map.put("prizeCount", prizeCount);
		return map;
	}

//	public Map<String,Object> queryEventByAll(EventPagination eventPagination){
//		Map<String,Object> map = new HashMap<>();
//		//查询
//		QueryWrapper<EventEntity> queryWrapper=new QueryWrapper<>();
//		if(!"null".equals(String.valueOf(eventPagination.getPersons()))){
//			queryWrapper.lambda().and(t->t.eq(EventEntity::getPersons,eventPagination.getPersons()));
//		}
//		if(!"null".equals(String.valueOf(eventPagination.getOrganisation()))){
//			queryWrapper.lambda().and(t->t.eq(EventEntity::getOrganisation,eventPagination.getOrganisation()));
//		}
//		if(!"null".equals(String.valueOf(eventPagination.getAchievements()))){
//			queryWrapper.lambda().and(t->t.eq(EventEntity::getAchievements,eventPagination.getAchievements()));
//		}
//		if(!"null".equals(String.valueOf(eventPagination.getTask()))){
//			queryWrapper.lambda().and(t->t.eq(EventEntity::getTask,eventPagination.getTask()));
//		}
//		if(!"null".equals(String.valueOf(eventPagination.getCategory()))){
//			queryWrapper.lambda().and(t->t.eq(EventEntity::getCategory,eventPagination.getCategory()));
//		}
//		if(!"null".equals(String.valueOf(eventPagination.getFdid()))){
//			queryWrapper.lambda().and(t->t.eq(EventEntity::getFdid,eventPagination.getFdid()));
//		}
//		Page<EventEntity> page=new Page<>(eventPagination.getCurrentPage(), eventPagination.getPageSize());
//		IPage<EventEntity> userIPage=this.page(page,queryWrapper);
//		List<EventEntity> eventEntityList = eventPagination.setData(userIPage.getRecords(),userIPage.getTotal());
//		List<EventListVO> listVO= JsonUtil.getJsonToList(eventEntityList,EventListVO.class);
//		PageListVO vo=new PageListVO();
//		vo.setList(listVO);
//		PaginationVO pageVo=JsonUtil.getJsonToBean(eventPagination,PaginationVO.class);
//		vo.setPagination(pageVo);
//		map.put("dataList",vo);
//		//选择筛选条件数量
//		int tagCount = 0;
//		int allTagCount = 0;
//		int halfTagCount = 0;
//		List<Map<String,Long>> tagCountList = new ArrayList<>();
//		//符合数量
//		int containCount = 0;
//		if (eventPagination.getTag() != null && !eventPagination.getTag().isEmpty()){
//			String[] tagStrList = eventPagination.getTag().split(",");
//			tagCount = tagStrList.length;
//			for (int i = 0; i < tagStrList.length; i++){
//				if(!"null".equals(tagStrList[i])){
//					int finalI = i;
//					queryWrapper.lambda().and(t->t.like(EventEntity::getTag,tagStrList[finalI]));
//					Long tagNum = this.count(queryWrapper);
//					Map<String,Long> tagNumMap = new HashMap<>();
//					tagNumMap.put(tagStrList[i],tagNum);
//					containCount += 1;
//					tagCountList.add(tagNumMap);
//				}
//			}
//		}
//		map.put("tagCount",tagCount);
//		map.put("allTagCount",allTagCount);
//		map.put("halfTagCount",halfTagCount);
//		map.put("tagCountList",tagCountList);
//		return map;
//	}

	/**
	 * 暂不用
	 * 
	 * @param eventPagination
	 * @return
	 */
//	public Map<String,Object> queryEventByAll(EventPagination eventPagination){
//		String tag = eventPagination.getTag();
//		Map<String,Object> map = new HashMap<>();
//		//标签
//		if (tag != null && !tag.isEmpty()){
//			String tagSql = "(";
//			String[] tagStrList = tag.split(",");
//			for (int i = 0; i < tagStrList.length; i++){
//				if (i > 0){
//					tagSql += " OR ";
//				}
//				tagSql += "CONCAT(\",\",tag,\",\") LIKE '%," + tagStrList[i] + ",%' ";
//			}
//			tagSql += ")";
//			eventPagination.setTag(tagSql);
//		}
//		//日期
//		if (eventPagination.getStartTime() != null && eventPagination.getEndTime() != null &&
//		!eventPagination.getStartTime().isEmpty() && !eventPagination.getEndTime().isEmpty()){
//			String timeSql = "start_time >= '"+eventPagination.getStartTime()+
//					"' AND start_time <= '"+eventPagination.getEndTime()+"'";
//			eventPagination.setStartTime(timeSql);
//		}
//		long currentPage = eventPagination.getCurrentPage();
//		long limit = (eventPagination.getCurrentPage()-1) * eventPagination.getPageSize();
//		eventPagination.setCurrentPage(limit);
//		//分页数据
//		List<EventEntity> eventListPage = eventMapper.queryAllPage(eventPagination);
//		//去没有命中的标签
//		if (eventListPage != null && eventListPage.size() > 0){
//			for (int i=0; i<eventListPage.size(); i++){
//				String dataTag = eventListPage.get(i).getTag();
//				String hitTag = "";
//				if (dataTag != null && !dataTag.isEmpty()){
//					if (tag != null && !tag.isEmpty()){
//						String[] tagStrList = tag.split(",");
//						for (int j = 0; j < tagStrList.length; j++){
//							if (dataTag.contains(tagStrList[j])){
//								if (!hitTag.isEmpty()){
//									hitTag += ",";
//								}
//								hitTag += tagStrList[j];
//							}
//						}
//					}
//					eventListPage.get(i).setTag(hitTag);
//				}
//			}
//		}
//		eventPagination.setCurrentPage(currentPage);
//		int totalCount = eventMapper.queryAllCount(eventPagination);
//		List<EventEntity> eventEntityListPage = eventPagination.setData(eventListPage,totalCount);
//		List<EventListVO> listVO= JsonUtil.getJsonToList(eventEntityListPage,EventListVO.class);
//		PageListVO vo=new PageListVO();
//		vo.setList(listVO);
//		PaginationVO pageVo=JsonUtil.getJsonToBean(eventPagination,PaginationVO.class);
//		vo.setPagination(pageVo);
//		map.put("dataList",vo);
//
//		//目录查询
//		List<Long> pdidList = eventMapper.pdidCount(eventPagination);
//		map.put("pdidList",pdidList);
//
//		//命中全部(一半)标签数量
//		int allTagCount = 0;
//		int halfTagCount = 0;
//		if (tag != null && !tag.isEmpty()){
//			//总数据
//			List<EventEntity> eventEntityList = eventMapper.queryAll(eventPagination);
//			String[] tagStrList = tag.split(",");
//			for (int j = 0; j < eventEntityList.size(); j++){
//				int tagSameCount = 0;
//				for (int i = 0; i < tagStrList.length; i++){
//					if (eventEntityList.get(j).getTag().contains(tagStrList[i])){
//						tagSameCount ++;
//					}
//				}
//				if (tagSameCount == tagStrList.length){
//					allTagCount ++;
//				}
//				int halfNum = (int)Math.ceil((double)tagStrList.length/2);
//				System.out.println("标签一半"+halfNum);
//				if (tagSameCount == halfNum){
//					halfTagCount ++;
//				}
//			}
//		}
//		map.put("allTagCount",allTagCount);
//		map.put("halfTagCount",halfTagCount);
//
//		//选择筛选条件数量
//		int tagCount = 0;
//		List<Map<String,Integer>> tagCountList = new ArrayList<>();
//		if (tag != null && !tag.isEmpty()){
//			String[] tagStrList = tag.split(",");
//			tagCount = tagStrList.length;
//			for (int i = 0; i < tagStrList.length; i++){
//				eventPagination.setTag(" CONCAT(\",\",tag,\",\") like '%," + tagStrList[i] + ",%'");
//				int tagNum = eventMapper.queryTagCount(eventPagination);
//				Map<String,Integer> tagNumMap = new HashMap<>();
//				tagNumMap.put(tagStrList[i],tagNum);
//				tagCountList.add(tagNumMap);
//			}
//		}
//		map.put("tagCount",tagCount);
//		map.put("tagCountList",tagCountList);
//		return map;
//	}

	/**
	 * 事件脉络-地图数据
	 * 
	 * @param year
	 * @param theme
	 * @return
	 */
	public List<Map<String, Integer>> mapData(String year, String theme) {
//		List<Map<String,Integer>> mapList = new ArrayList<>();
//		Map<String,Integer> map = new HashMap<>();
		EventPagination eventPagination = new EventPagination();
		eventPagination.setStartTime(year);
		eventPagination.setTheme(theme);
		List<Map<String, Integer>> mapList = eventMapper.mapData(eventPagination);
		return mapList;
	}

	public EventInfoVO getTimeSelect() {
		EventEntity eventEntity = eventMapper.getTimeSelect();
		EventInfoVO eventVO = JsonUtil.getJsonToBean(eventEntity, EventInfoVO.class);
		return eventVO;
	}

//	public Map<String,Object> queryEventByAll(EventPagination eventPagination){
//		String tag = eventPagination.getTag();
//		Map<String,Object> map = new HashMap<>();
//		//标签
//		if (tag != null && !tag.isEmpty()){
//			String tagSql = "(";
//			String[] tagStrList = tag.split(",");
//			for (int i = 0; i < tagStrList.length; i++){
//				if (i > 0){
//					tagSql += " OR ";
//				}
//				tagSql += "CONCAT(\";\",tag,\";\") LIKE '%;" + tagStrList[i] + ";%' ";
//			}
//			tagSql += ")";
//			eventPagination.setTag(tagSql);
//		}
//		//日期
//		if (eventPagination.getStartTime() != null && eventPagination.getEndTime() != null &&
//				!eventPagination.getStartTime().isEmpty() && !eventPagination.getEndTime().isEmpty()){
//			String timeSql = "start_time >= '"+eventPagination.getStartTime()+
//					"' AND start_time <= '"+eventPagination.getEndTime()+"'";
//			eventPagination.setStartTime(timeSql);
//		}
//		//主题
//		if (eventPagination.getResearchTag2Ids() != null && !eventPagination.getResearchTag2Ids().isEmpty()){
//			eventPagination.setResearchTag2Sql(queryEventTag2(eventPagination));
//		}
//
//		long currentPage = eventPagination.getCurrentPage();
//		long limit = (eventPagination.getCurrentPage()-1) * eventPagination.getPageSize();
//		eventPagination.setCurrentPage(limit);
//		//分页数据
//		List<EventEntity> eventListPage = eventMapper.queryAllPage(eventPagination);
//		//去没有命中的标签
//		if (eventListPage != null && eventListPage.size() > 0){
//			for (int i=0; i<eventListPage.size(); i++){
//				String dataTag = eventListPage.get(i).getTag();
//				String hitTag = "";
//				if (dataTag != null && !dataTag.isEmpty()){
//					if (tag != null && !tag.isEmpty()){
//						String[] tagStrList = tag.split(",");
//						for (int j = 0; j < tagStrList.length; j++){
//							if (dataTag.contains(tagStrList[j])){
//								if (!hitTag.isEmpty()){
//									hitTag += ",";
//								}
//								hitTag += tagStrList[j];
//							}
//						}
//					}
//					eventListPage.get(i).setTag(hitTag);
//				}
//			}
//		}
//		eventPagination.setCurrentPage(currentPage);
//		int totalCount = eventMapper.queryAllCount(eventPagination);
//		List<EventEntity> eventEntityListPage = eventPagination.setData(eventListPage,totalCount);
//		List<EventListVO> listVO= JsonUtil.getJsonToList(eventEntityListPage,EventListVO.class);
//		PageListVO vo=new PageListVO();
//		vo.setList(listVO);
//		PaginationVO pageVo=JsonUtil.getJsonToBean(eventPagination,PaginationVO.class);
//		vo.setPagination(pageVo);
//		map.put("dataList",vo);
//
//		//目录查询（暂不用）
////		List<Long> pdidList = eventMapper.pdidCount(eventPagination);
////		map.put("pdidList",pdidList);
//
//		//命中全部(一半)标签数量
//		int allTagCount = 0;
//		int halfTagCount = 0;
//		if (tag != null && !tag.isEmpty()){
//			//总数据
//			List<EventEntity> eventEntityList = eventMapper.queryAll(eventPagination);
//			String[] tagStrList = tag.split(",");
//			for (int j = 0; j < eventEntityList.size(); j++){
//				int tagSameCount = 0;
//				for (int i = 0; i < tagStrList.length; i++){
//					if (eventEntityList.get(j).getTag().contains(tagStrList[i])){
//						tagSameCount ++;
//					}
//				}
//				if (tagSameCount == tagStrList.length){
//					allTagCount ++;
//				}
//				int halfNum = (int)Math.ceil((double)tagStrList.length/2);
//				System.out.println("标签一半"+halfNum);
//				if (tagSameCount == halfNum){
//					halfTagCount ++;
//				}
//			}
//		}
//		map.put("allTagCount",allTagCount);
//		map.put("halfTagCount",halfTagCount);
//
//		//选择筛选条件数量
//		int tagCount = 0;
//		List<Map<String,Integer>> tagCountList = new ArrayList<>();
//		if (tag != null && !tag.isEmpty()){
//			String[] tagStrList = tag.split(",");
//			tagCount = tagStrList.length;
//			for (int i = 0; i < tagStrList.length; i++){
//				eventPagination.setTag(" CONCAT(\";\",tag,\";\") like '%;" + tagStrList[i] + ";%'");
//				int tagNum = eventMapper.queryTagCount(eventPagination);
//				Map<String,Integer> tagNumMap = new HashMap<>();
//				tagNumMap.put(tagStrList[i],tagNum);
//				tagCountList.add(tagNumMap);
//			}
//		}
//		map.put("tagCount",tagCount);
//		map.put("tagCountList",tagCountList);
//		return map;
//	}

//	public Map<String,Object> queryEventByAll(EventPagination eventPagination){
//		String tag = eventPagination.getTag();
//		Map<String,Object> map = new HashMap<>();
//		//标签
//		if (tag != null && !tag.isEmpty()){
//			String tagSql = " (";
//			String[] tagStrList = tag.split(",");
//			for (int i = 0; i < tagStrList.length; i++){
//				if (i > 0){
//					tagSql += " OR ";
//				}
//				tagSql += "CONCAT(\";\",tag,\";\") LIKE '%;" + tagStrList[i] + ";%' ";
//			}
//			tagSql += ") ";
//			eventPagination.setTag(tagSql);
//		}
//		//日期
//		if (eventPagination.getStartTime() != null && eventPagination.getEndTime() != null &&
//				!eventPagination.getStartTime().isEmpty() && !eventPagination.getEndTime().isEmpty()){
//			String timeSql = "start_time >= '"+eventPagination.getStartTime()+
//					"' AND start_time <= '"+eventPagination.getEndTime()+"'";
//			eventPagination.setStartTime(timeSql);
//		}
//		//标签2
//		if (eventPagination.getResearchTag2Ids() != null && !eventPagination.getResearchTag2Ids().isEmpty()){
//			eventPagination.setResearchTag2Sql(queryEventTag2OR(eventPagination));
//		}
//
//		String tagsqlor = "";
//		if (eventPagination.getTag() != null && !eventPagination.getTag().isEmpty()){
//			tagsqlor += eventPagination.getTag();
//		}
//		if (eventPagination.getResearchTag2Sql() != null && !eventPagination.getResearchTag2Sql().isEmpty()){
//			if (!tagsqlor.isEmpty()){
//				tagsqlor += " OR ";
//			}
//			tagsqlor += eventPagination.getResearchTag2Sql();
//		}
//		eventPagination.setTagsql(tagsqlor);
//
//		long currentPage = eventPagination.getCurrentPage();
//		long limit = (eventPagination.getCurrentPage()-1) * eventPagination.getPageSize();
//		eventPagination.setCurrentPage(limit);
//		//分页数据
//		List<EventEntity> eventListPage = eventMapper.queryAllPage(eventPagination);
//		//去没有命中的标签
//		if (eventListPage != null && eventListPage.size() > 0){
//			for (int i=0; i<eventListPage.size(); i++){
//				String dataTag = eventListPage.get(i).getTag();
//				String hitTag = "";
//				if (dataTag != null && !dataTag.isEmpty()){
//					if (tag != null && !tag.isEmpty()){
//						String[] tagStrList = tag.split(",");
//						for (int j = 0; j < tagStrList.length; j++){
//							if (dataTag.contains(tagStrList[j])){
//								if (!hitTag.isEmpty()){
//									hitTag += ",";
//								}
//								hitTag += tagStrList[j];
//							}
//						}
//					}
//					eventListPage.get(i).setTag(hitTag);
//				}
//				//tag2
//				String tag2 = "";
//				if (eventPagination.getResearchTag2Sql() != null && !eventPagination.getResearchTag2Sql().isEmpty()){
//					String[] idssplit = eventPagination.getResearchTag2Ids().split(",");
//					for (String id:idssplit) {
//						ResearchTag2Entity researchTag2 = researchTag2Mapper.selectById(id);
//						if (researchTag2 == null){
//							continue;
//						}
//						String entitysql = " data->'$."+researchTag2.getAttrCategory()+"' like '%"+researchTag2.getAttrValId()+"%' ";
//						QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
//						libEntityWrapper.apply(entitysql);
//						List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityWrapper);
//						if (libEntityEntities.size() == 0){continue;}
////						String libentitySql = "";
////						for (LibEntityEntity libEntity:libEntityEntities) {
////							if (!libentitySql.isEmpty()){
////								libentitySql += " OR ";
////							}
////							libentitySql +=  researchTag2.getTagAttr() +" like '%"+ libEntity.getName() + "%' ";
////						}
////						EventPagination eventPagination1 = new EventPagination();
////						eventPagination1.setTagsql(libentitySql);
////						int totalCount = eventMapper.queryAllCount(eventPagination1);
//						boolean isHave = false;
//						for (LibEntityEntity libEntity:libEntityEntities) {
//							if (researchTag2.getTagAttr().equals("political_work")){
//								if ((";"+eventListPage.get(i).getPoliticalWork()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("security_manage")){
//								if ((";"+eventListPage.get(i).getSecurityManage()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("comprehensive_coordination")){
//								if ((";"+eventListPage.get(i).getComprehensiveCoordination()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("scientific_manage")){
//								if ((";"+eventListPage.get(i).getScientificManage()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("decision_making")){
//								if ((";"+eventListPage.get(i).getDecisionMaking()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("scientific_strength")){
//								if ((";"+eventListPage.get(i).getScientificStrength()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("scientific_organization")){
//								if ((";"+eventListPage.get(i).getScientificOrganization()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("scientific_subject")){
//								if ((";"+eventListPage.get(i).getScientificSubject()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("scientific_result")){
//								if ((";"+eventListPage.get(i).getScientificResult()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//							if (researchTag2.getTagAttr().equals("scientific_funding")){
//								if ((";"+eventListPage.get(i).getScientificFunding()).contains(";" + libEntity.getName() + ";") ){
//									isHave = true;
//								}
//							}
//						}
//						if (isHave){
//							if (!tag2.isEmpty()){
//								tag2 += ",";
//							}
//							tag2 += researchTag2.getName();
//						}
//					}
//				}
//				if (tag2.isEmpty()){continue;}
//				if (eventListPage.get(i).getTag() != null && !eventListPage.get(i).getTag().isEmpty()) {
//					tag2 = eventListPage.get(i).getTag() + ","+tag2;
//				}
//				eventListPage.get(i).setTag(tag2);
//			}
//		}
//		eventPagination.setCurrentPage(currentPage);
//		int totalCount = eventMapper.queryAllCount(eventPagination);
//		List<EventEntity> eventEntityListPage = eventPagination.setData(eventListPage,totalCount);
//		List<EventListVO> listVO= JsonUtil.getJsonToList(eventEntityListPage,EventListVO.class);
//		PageListVO vo=new PageListVO();
//		vo.setList(listVO);
//		PaginationVO pageVo=JsonUtil.getJsonToBean(eventPagination,PaginationVO.class);
//		vo.setPagination(pageVo);
//		map.put("dataList",vo);
//
//		//目录查询（暂不用）
////		List<Long> pdidList = eventMapper.pdidCount(eventPagination);
////		map.put("pdidList",pdidList);
//
//		//标签全部命中事件数量
//		String tagsqland = "";
//		if (eventPagination.getTag() != null && !eventPagination.getTag().isEmpty()){
//			tagsqland += eventPagination.getTag().replaceAll("OR","AND");
//		}
//		if (eventPagination.getResearchTag2Sql() != null && !eventPagination.getResearchTag2Sql().isEmpty()){
//			if (!tagsqland.isEmpty()){
//				tagsqland += " AND ";
//			}
//			tagsqland += "(" + eventPagination.getResearchTag2Sql() + " ) ";
//		}
//		eventPagination.setTagsql(tagsqland);
//		int totalCountAND = eventMapper.queryAllCount(eventPagination);
//		map.put("totalCount",totalCount);
//		map.put("totalCountAND",totalCountAND);
//
//		//选择筛选条件数量
//		int tagCount = 0;
//		List<Map<String,Integer>> tagCountList = new ArrayList<>();
//		if (tag != null && !tag.isEmpty()){
//			String[] tagStrList = tag.split(",");
//			tagCount = tagStrList.length;
//
//			for (int i = 0; i < tagStrList.length; i++){
//				eventPagination.setTag(" CONCAT(\";\",tag,\";\") like '%;" + tagStrList[i] + ";%'");
//				int tagNum = eventMapper.queryTagCount(eventPagination);
//				Map<String,Integer> tagNumMap = new HashMap<>();
//				tagNumMap.put(tagStrList[i],tagNum);
//				tagCountList.add(tagNumMap);
//			}
//		}
//		//标签2
//		if (eventPagination.getResearchTag2Ids() != null && !eventPagination.getResearchTag2Ids().isEmpty()){
//			String[] idssplit = eventPagination.getResearchTag2Ids().split(",");
//			for (String id:idssplit) {
//				ResearchTag2Entity researchTag2 = researchTag2Mapper.selectById(id);
//				if (researchTag2 == null){
//					continue;
//				}
//				String entitysql = " data->'$."+researchTag2.getAttrCategory()+"' like '%"+researchTag2.getAttrValId()+"%' ";
//
//				QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
//				libEntityWrapper.apply(entitysql);
//				List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityWrapper);
//				if (libEntityEntities.size() == 0){
//					Map<String,Integer> tagNumMap = new HashMap<>();
//					tagNumMap.put(researchTag2.getName(),0);
//					tagCountList.add(tagNumMap);
//					continue;
//				}
//				String libentitySql = "";
//				for (LibEntityEntity libEntity:libEntityEntities) {
//					if (!libentitySql.isEmpty()){
//						libentitySql += " OR ";
//					}
//					libentitySql +=  researchTag2.getTagAttr() +" like '%"+ libEntity.getName() + "%' ";
//				}
////				EventPagination eventPagination1 = new EventPagination();
//				eventPagination.setTagsql(libentitySql);
//				int totalCount1 = eventMapper.queryAllCount(eventPagination);
//				Map<String,Integer> tagNumMap = new HashMap<>();
//				tagNumMap.put(researchTag2.getName(),totalCount1);
//				tagCountList.add(tagNumMap);
//			}
//		}
//		map.put("tagCount",tagCount);
//		map.put("tagCountList",tagCountList);
//		return map;
//	}

	public Map<String, Object> queryEventByAllTag(EventPagination eventPagination) {
		Map<String, Object> map = new HashMap<>();
		// 标签数量list
		List<Map<String, Integer>> tagCountList = new ArrayList<>();
		// tag2NameList
		List<String> tag2NameList = new ArrayList<>();
		// 日期
		if (eventPagination.getStartTime() != null && eventPagination.getEndTime() != null
				&& !eventPagination.getStartTime().isEmpty() && !eventPagination.getEndTime().isEmpty()) {
			String timeSql = "start_time >= '" + eventPagination.getStartTime() + "' AND start_time <= '"
					+ eventPagination.getEndTime() + "'";
			eventPagination.setStartTime(timeSql);
		}
		// 标签
		String tag = eventPagination.getTag();
		String tagsql1 = "";
		if (tag != null && !tag.isEmpty()) {
			String tagSql = " (";
			String[] tagStrList = tag.split(",");
			for (int i = 0; i < tagStrList.length; i++) {
				if (i > 0) {
					tagSql += " OR ";
				}
				tagSql += "(';'||tag||';') LIKE '%;" + tagStrList[i] + ";%' ";
			}
			tagSql += ") ";
			tagsql1 = tagSql;
		}
		// 标签2
		String tagsql2 = "";
		if (eventPagination.getResearchTag2Ids() != null && !eventPagination.getResearchTag2Ids().isEmpty()) {
			String tagSql = "(";
			String[] idssplit = eventPagination.getResearchTag2Ids().split(",");
			for (String id : idssplit) {
				ResearchTag2Entity researchTag2 = researchTag2Mapper.selectById(id);
				if (researchTag2 == null) {
					continue;
				}
				tag2NameList.add(researchTag2.getName());
				if (!tagSql.equals("(")) {
					tagSql += " OR ";
				}
				tagSql += "(';'||tag2||';') LIKE '%;" + researchTag2.getName() + ";%' ";
			}
			tagSql += ") ";
			tagsql2 = tagSql;
		}
		// 标签查询sql
		String tagsql = tagsql1;
		if (!tagsql1.isEmpty() && !tagsql2.isEmpty()) {
			tagsql += " OR ";
		}
		tagsql += tagsql2;
		eventPagination.setTagsql(tagsql);
		
		//要素
		String cates = eventPagination.getCategory();
		String [] cate_arrs =  cates.split(",");
		StringBuffer sb = new StringBuffer();
		sb.append(" ( ");
		for (int i = 0; i < cate_arrs.length; i++) {
			if(i>0) {
				sb.append(" or ");
			}
			sb.append("  ( category like '%"+cate_arrs[i]+"%' ) ");
		}
		if(cate_arrs.length==0) {
			sb.append("  1=1");
		}
		sb.append(" ) ");
		eventPagination.setSqlstr(sb.toString());
		List<EventEntity> eventListAll = eventMapper.queryAllAll(eventPagination);
		if (eventListAll.size() == 0) {
			PageListVO vo = new PageListVO();
			vo.setList(eventListAll);
			PaginationVO page = JsonUtil.getJsonToBean(eventPagination, PaginationVO.class);
			vo.setPagination(page);

			map.put("dataList", vo);
			map.put("totalCount", 0);
			map.put("totalCountAND", 0);
			map.put("tagCount", 0);
			map.put("tagCountList", tagCountList);
			return map;
		}
		// 计算命中标签数量，排序
		Map<Long, Integer> hitMap = new HashMap<Long, Integer>();
		for (EventEntity event : eventListAll) {
			// 命中标签数量
			int hitCount = 0;
			// tag
			String dataTag = event.getTag();
			if (dataTag != null && !dataTag.isEmpty()) {
				if (tag != null && !tag.isEmpty()) {
					String[] tagStrList = tag.split(",");
					for (int j = 0; j < tagStrList.length; j++) {
						if (dataTag.contains(tagStrList[j])) {
							hitCount++;
						}
					}
				}
			}
			// tag2
			String dataTag2 = event.getTag2();
			if (tag2NameList.size() > 0) {
				if (dataTag2 != null && !dataTag2.isEmpty()) {
					for (String tag2name : tag2NameList) {
						if (dataTag2.contains(tag2name)) {
							hitCount++;
						}
					}
				}
			}
			// 排序
			hitMap.put(event.getId(), hitCount);
		}
		List<Map.Entry<Long, Integer>> hitList = new ArrayList<>(hitMap.entrySet());
		Collections.sort(hitList, new Comparator<Map.Entry<Long, Integer>>() {
			@Override
			public int compare(Map.Entry<Long, Integer> o1, Map.Entry<Long, Integer> o2) {
				return o2.getValue() - o1.getValue();
			}
		});
		for (Map.Entry<Long, Integer> e : hitList) {
			System.out.println(e.getKey() + ":" + e.getValue());
		}

		long currentPage = eventPagination.getCurrentPage();
		int limit = (int) ((eventPagination.getCurrentPage() - 1) * eventPagination.getPageSize());
		int offset = (int) (eventPagination.getCurrentPage() * eventPagination.getPageSize());
		if (offset > hitList.size()) {
			offset = hitList.size();
		}
		System.out.println("limit" + limit);
		System.out.println("offset" + offset);

		List<Long> eventIds = new ArrayList<>();
		for (int i = limit; i < offset; i++) {
			eventIds.add(hitList.get(i).getKey());
		}
		List<EventEntity> eventListPage1 = eventMapper.selectBatchIds(eventIds);
		List<EventEntity> eventListPage = new ArrayList<>();
		for (Long id : eventIds) {
			for (EventEntity event : eventListPage1) {
				if (id.equals(event.getId())) {
					eventListPage.add(event);
					break;
				}
			}
		}
		// 去没有命中的标签
		if (eventListPage != null && eventListPage.size() > 0) {
			for (int i = 0; i < eventListPage.size(); i++) {
				String dataTag = eventListPage.get(i).getTag();
				String hitTag = "";
				if (dataTag != null && !dataTag.isEmpty()) {
					if (tag != null && !tag.isEmpty()) {
						String[] tagStrList = tag.split(",");
						for (int j = 0; j < tagStrList.length; j++) {
							if (dataTag.contains(tagStrList[j])) {
								if (!hitTag.isEmpty()) {
									hitTag += ",";
								}
								hitTag += tagStrList[j];
							}
						}
					}
					eventListPage.get(i).setTag(hitTag);
				}
				// tag2
				String dataTag2 = eventListPage.get(i).getTag2();
				String tag2 = "";
				if (tag2NameList.size() > 0) {
					if (dataTag2 != null && !dataTag2.isEmpty()) {
						for (String tag2name : tag2NameList) {
							if (dataTag2.contains(tag2name)) {
								if (!tag2.isEmpty()) {
									tag2 += ",";
								}
								tag2 += tag2name;
							}
						}
					}
				}
				if (tag2.isEmpty()) {
					continue;
				}
				if (eventListPage.get(i).getTag() != null && !eventListPage.get(i).getTag().isEmpty()) {
					tag2 = eventListPage.get(i).getTag() + "," + tag2;
				}
				eventListPage.get(i).setTag(tag2);
			}
		}
		eventPagination.setCurrentPage(currentPage);
		int totalCount = eventListAll.size();
		List<EventEntity> eventEntityListPage = eventPagination.setData(eventListPage, totalCount);
		List<EventListVO> listVO = JsonUtil.getJsonToList(eventEntityListPage, EventListVO.class);
		PageListVO vo = new PageListVO();
		vo.setList(listVO);
		PaginationVO pageVo = JsonUtil.getJsonToBean(eventPagination, PaginationVO.class);
		vo.setPagination(pageVo);
		map.put("dataList", vo);
		List<WordCloudEntity> wordlIst = getwordList(listVO,eventPagination);
		map.put("wordList", wordlIst);
		Set<String>  set = new HashSet<>();
		for (int i = 0; i < listVO.size(); i++) {
			if(!StringUtils.isEmpty(listVO.get(i).getTag())) {
				String[] tags = listVO.get(i).getTag().split(";");
				for (int j = 0; j < tags.length; j++) {
					set.add(tags[j]);
				}
			}
		}
		map.put("tagSet", set);
		// 标签全部命中事件数量
		String tagsqland = "";
		if (tag != null && !tag.isEmpty()) {
			String tagSql = " (";
			String[] tagStrList = tag.split(",");
			for (int i = 0; i < tagStrList.length; i++) {
				if (i > 0) {
					tagSql += " AND ";
				}
				tagSql += "(';'||tag||';') LIKE '%;" + tagStrList[i] + ";%' ";
			}
			tagSql += ") ";
			tagsqland = tagSql;
		}
		// 标签2
		if (eventPagination.getResearchTag2Ids() != null && !eventPagination.getResearchTag2Ids().isEmpty()) {
			if (!tagsqland.isEmpty()) {
				tagsqland += "AND" + queryEventTag2AND(eventPagination);
			} else {
				tagsqland += queryEventTag2AND(eventPagination);
			}
		}
		eventPagination.setTagsql(tagsqland);
		int totalCountAND = eventMapper.queryAllCount(eventPagination);
		map.put("totalCount", totalCount);
		map.put("totalCountAND", totalCountAND);

		// 选择筛选条件数量
		int tagCount = 0;
		// 标签1
		if (tag != null && !tag.isEmpty()) {
			String[] tagStrList = tag.split(",");
			tagCount = tagStrList.length;

			for (int i = 0; i < tagStrList.length; i++) {
				eventPagination.setTag(" (';'||tag||';') like '%;" + tagStrList[i] + ";%'");
				int tagNum = eventMapper.queryTagCount(eventPagination);
				Map<String, Integer> tagNumMap = new HashMap<>();
				tagNumMap.put(tagStrList[i], tagNum);
				tagCountList.add(tagNumMap);
			}
		}
		// 标签2
		if (eventPagination.getResearchTag2Ids() != null && !eventPagination.getResearchTag2Ids().isEmpty()) {
			if (tag2NameList.size() > 0) {
				for (String tag2name : tag2NameList) {
					eventPagination.setTag(" (';'||tag2||';') like '%;" + tag2name + ";%'");
					int tagNum = eventMapper.queryTagCount(eventPagination);
					Map<String, Integer> tagNumMap = new HashMap<>();
					tagNumMap.put(tag2name, tagNum);
					tagCountList.add(tagNumMap);
				}
			}
		}
		map.put("tagCount", tagCount);
		map.put("tagCountList", tagCountList);
		return map;
	}

	public Map<String, Object> queryEventByAllNoTAG(EventPagination eventPagination) {
		System.out.println(eventPagination);
		Map<String, Object> map = new HashMap<>();
		// 日期
		if (eventPagination.getStartTime() != null && eventPagination.getEndTime() != null
				&& !eventPagination.getStartTime().isEmpty() && !eventPagination.getEndTime().isEmpty()) {
			String timeSql = "start_time >= '" + eventPagination.getStartTime() + "' AND start_time <= '"
					+ eventPagination.getEndTime() + "'";
			eventPagination.setStartTime(timeSql);
		}

		long currentPage = eventPagination.getCurrentPage();
		long limit = (eventPagination.getCurrentPage() - 1) * eventPagination.getPageSize();
		eventPagination.setCurrentPage(limit);
		
		//要素
		String cates = eventPagination.getCategory();
		String [] cate_arrs =  cates.split(",");
		StringBuffer sb = new StringBuffer();
		sb.append(" ( ");
		for (int i = 0; i < cate_arrs.length; i++) {
			if(i>0) {
				sb.append(" or ");
			}
			sb.append("  ( category like '%"+cate_arrs[i]+"%' ) ");
		}
		if(cate_arrs.length==0) {
			sb.append("  1=1");
		}
		sb.append(" ) ");
		eventPagination.setSqlstr(sb.toString());
		// 分页数据
		List<EventEntity> eventListPage = eventMapper.queryAllPage(eventPagination);
		eventPagination.setCurrentPage(currentPage);
		int totalCount = eventMapper.queryAllCount(eventPagination);
		List<EventEntity> eventEntityListPage = eventPagination.setData(eventListPage, totalCount);
		for (EventEntity event : eventEntityListPage) {
			event.setTag(null);
		}
		List<EventListVO> listVO = JsonUtil.getJsonToList(eventEntityListPage, EventListVO.class);
		PageListVO vo = new PageListVO();
		vo.setList(listVO);
		PaginationVO pageVo = JsonUtil.getJsonToBean(eventPagination, PaginationVO.class);
		vo.setPagination(pageVo);
		map.put("dataList", vo);
		List<WordCloudEntity> wordlIst = getwordList(listVO,eventPagination);
		map.put("wordList", wordlIst);
		// 标签全部命中事件数量
		map.put("totalCount", totalCount);
		map.put("totalCountAND", 0);

		// 选择筛选条件数量
		int tagCount = 0;
		List<Map<String, Integer>> tagCountList = new ArrayList<>();
		// 标签2
		map.put("tagCount", tagCount);
		map.put("tagCountList", tagCountList);
		return map;
	}

	@Override
	public Map<String, Object> queryEventByAll(EventPagination eventPagination) {
		Map<String, Object> map = new HashMap<>();
		// 标签
		if (StringUtils.isEmpty(eventPagination.getTag()) && StringUtils.isEmpty( eventPagination.getResearchTag2Ids())){
			// 没标签，直接分页查询
			map = queryEventByAllNoTAG(eventPagination);
		} else {
			// 有标签需要查询全部，然后再分页
			map = queryEventByAllTag(eventPagination);
		}
		return map;
	}

	public List<WordCloudEntity>     getwordList(List<EventListVO> listVO,EventPagination eventPagination ) {
		
		
		Set<String>  set = new HashSet<>();
		for (int i = 0; i < listVO.size(); i++) {
			if(!StringUtils.isEmpty(listVO.get(i).getTag())) {
				String[] tags = listVO.get(i).getTag().split(";");
				for (int j = 0; j < tags.length; j++) {
					set.add(tags[j]);
				}
			}
		}
		if(eventPagination.getFdid()==null || eventPagination.getFid()==null) {
			return null;
		}
		QueryWrapper<ResearchTagEntity> query = new QueryWrapper<ResearchTagEntity>();
		if(eventPagination.getFdid()!=null) {
			FileDirectoryEntity dir = fileDirectoryMapper.selectById(eventPagination.getFdid());
			eventPagination.setFid(dir.getId());
		}
		YearbookEntity yearbok = yearbookService.getById(eventPagination.getFid());
		query.eq("year", yearbok.getBookYear());
		query.in("name", set);
		
		List<ResearchTagEntity>  list = researchTagService.list(query);;
		
		Map<String,List<ResearchTagEntity>>  tagMap = list.stream().collect(Collectors.groupingBy(ResearchTagEntity::getName));
		List<WordCloudEntity> wordList =  new ArrayList<>();
		for (String  str : tagMap.keySet()) {
			if(StringUtils.isEmpty(str)) {
				continue;
			}
			List<ResearchTagEntity>  taglist = tagMap.get(str);
			WordCloudEntity  word = new WordCloudEntity();
			word.setName(str);
			if(taglist.size()>1) {
				int  weight = 0;
				for (int i = 0; i < taglist.size(); i++) {
					weight+=taglist.get(i).getZonelevel();
				}
				word.setWeight(weight);
			}else {
				word.setWeight(taglist.get(0).getZonelevel());
			}
			wordList.add(word);
		}
		return wordList;
	}
	
	public List<String> getTagTop(List<EventEntity> list) {
		Map<String, Integer> paramMap = new HashMap<>();
		List<String> strList = new ArrayList<>();
		List<String> sortList = new ArrayList<>();
		List<String> resultList = new ArrayList<>();
		if (list != null && list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				if (list.get(i).getTag() != null && !list.get(i).getTag().isEmpty()) {
					String[] stringTag = list.get(i).getTag().split(";");
					for (int j = 0; j < stringTag.length; j++) {
						strList.add(stringTag[j]);
					}
				}
			}
			if (strList.size() > 0) {
				for (int i = 0; i < strList.size(); i++) {
					String param = strList.get(i);
					if (paramMap.containsKey(param)) {
						Integer num = paramMap.get(param) + 1;
						paramMap.put(param, num);
					} else {
						paramMap.put(param, 1);
					}
				}
				for (String key : paramMap.keySet()) {
					Integer num = paramMap.get(key);
					sortList.add(encapsulation(num, "_" + key));
				}
				Collections.sort(sortList, Comparator.reverseOrder());
				int topNum = 0;
				for (int i = 0; i < sortList.size(); i++) {
					resultList.add(sortList.get(i).split("_")[1]);
					topNum++;
					if (topNum == 3) {
						break;
					}
				}
			}
		}

		return resultList;
	}

	/**
	 * 方法描述：整理成对应的数据结构num_element
	 * 
	 * @param codeNum  出现次数
	 * @param codeName 元素名称
	 * @return
	 */
	public static String encapsulation(Integer codeNum, String codeName) {
		String codeString = null;
		if (codeNum <= 9) {
			codeString = "00" + codeNum + codeName;
		} else if (codeNum <= 99) {
			codeString = "0" + codeNum + codeName;
		} else if (codeNum <= 999) {
			codeString = codeNum + codeName;
		}
		return codeString;
	}

	/**
	 * 排序tag，取权重最大的五个
	 * 
	 * @param vo
	 * @return
	 */
	public EventInfoVO getInfoTag(EventInfoVO vo) {
		if (vo != null && vo.getTag() != null && !vo.getTag().isEmpty()) {
			// 排序tag，取权重最大的五个
			String tags = vo.getTag().replaceAll(";", "','");
			String tagSql = " name in ('" + tags + "')";
			tagSql += " and  name is not null  and name!='' ";
			QueryWrapper<ResearchTagEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.select("id", "name");
			queryWrapper.lambda().apply(tagSql);
			queryWrapper.lambda().groupBy(ResearchTagEntity::getName);
			queryWrapper.lambda().orderByDesc(ResearchTagEntity::getZonelevel);
//			queryWrapper.lambda().last("limit 0,5");
			List<ResearchTagEntity> researchTagEntityList = researchTagMapper.selectList(queryWrapper);
			vo.setTagList(researchTagEntityList);
		}
		return vo;
	}

	public String queryEventTag2(EventPagination eventPagination) {
		String libentitySql = "";
		// 主题
		String[] idssplit = eventPagination.getResearchTag2Ids().split(",");
		// 标签分类英文名
		QueryWrapper<ResearchTag2Entity> researchTagAttrWrapper = new QueryWrapper<>();
		researchTagAttrWrapper.select("tag_attr");
		researchTagAttrWrapper.in("id", idssplit);
		researchTagAttrWrapper.groupBy("tag_attr");
		List<ResearchTag2Entity> researchTagAttrEntityList = researchTag2Mapper.selectList(researchTagAttrWrapper);
		// 本体id
		QueryWrapper<ResearchTag2Entity> researchTagOntoWrapper = new QueryWrapper<>();
		researchTagOntoWrapper.select("onto_id");
		researchTagOntoWrapper.in("id", idssplit);
		researchTagOntoWrapper.groupBy("onto_id");
		List<ResearchTag2Entity> researchTagOntoEntityList = researchTag2Mapper.selectList(researchTagOntoWrapper);

		for (ResearchTag2Entity researchTagAttr : researchTagAttrEntityList) {
			if (researchTagAttr.getTagAttr() == null || researchTagAttr.getTagAttr().isEmpty()) {
				continue;
			}
			String ontoSql = "";
			for (ResearchTag2Entity researchTagOnto : researchTagOntoEntityList) {
				if (researchTagOnto.getOntoId() == null) {
					continue;
				}
				// 根据本体id和标签分类英文名查找值
				QueryWrapper<ResearchTag2Entity> researchTagValueWrapper = new QueryWrapper<>();
				researchTagValueWrapper.select("attr_category", "attr_val_id");
				researchTagValueWrapper.eq("onto_id", researchTagOnto.getOntoId());
				researchTagValueWrapper.eq("tag_attr", researchTagAttr.getTagAttr());
				researchTagValueWrapper.in("id", idssplit);
				List<ResearchTag2Entity> researchTagValueEntityList = researchTag2Mapper
						.selectList(researchTagValueWrapper);
				if (researchTagValueEntityList.size() == 0) {
					continue;
				}
				if (!ontoSql.isEmpty()) {
					ontoSql += " or ";
				}
				ontoSql += " (onto_id = " + researchTagOnto.getOntoId();
				for (ResearchTag2Entity researchTagValue : researchTagValueEntityList) {
					ontoSql += " AND data->'$." + researchTagValue.getAttrCategory() + "' like '%"
							+ researchTagValue.getAttrValId() + "%' ";
				}
				ontoSql += ")";
			}
			QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
			libEntityWrapper.apply(ontoSql);
			List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityWrapper);
			if (libEntityEntities.size() == 0) {
				continue;
			}
			for (LibEntityEntity libEntity : libEntityEntities) {
				libentitySql += " AND " + researchTagAttr.getTagAttr() + " like '%" + libEntity.getName() + "%' ";
			}
		}
		return libentitySql;
	}

	public String queryEventTag2OR(EventPagination eventPagination) {
		String libentitySql = "";
		// tag2
		String[] idssplit = eventPagination.getResearchTag2Ids().split(",");
		// 标签分类英文名
		QueryWrapper<ResearchTag2Entity> researchTagAttrWrapper = new QueryWrapper<>();
		researchTagAttrWrapper.select("tag_attr");
		researchTagAttrWrapper.in("id", idssplit);
		researchTagAttrWrapper.groupBy("tag_attr");
		List<ResearchTag2Entity> researchTagAttrEntityList = researchTag2Mapper.selectList(researchTagAttrWrapper);
		// 本体id
		QueryWrapper<ResearchTag2Entity> researchTagOntoWrapper = new QueryWrapper<>();
		researchTagOntoWrapper.select("onto_id");
		researchTagOntoWrapper.in("id", idssplit);
		researchTagOntoWrapper.groupBy("onto_id");
		List<ResearchTag2Entity> researchTagOntoEntityList = researchTag2Mapper.selectList(researchTagOntoWrapper);

		for (ResearchTag2Entity researchTagAttr : researchTagAttrEntityList) {
			if (researchTagAttr == null) {
				continue;
			}
			if (researchTagAttr.getTagAttr() == null || researchTagAttr.getTagAttr().isEmpty()) {
				continue;
			}
			String ontoSql = "";
			for (ResearchTag2Entity researchTagOnto : researchTagOntoEntityList) {
				if (researchTagOnto.getOntoId() == null) {
					continue;
				}
				// 根据本体id和标签分类英文名查找值
				QueryWrapper<ResearchTag2Entity> researchTagValueWrapper = new QueryWrapper<>();
				researchTagValueWrapper.select("attr_category", "attr_val_id");
				researchTagValueWrapper.eq("onto_id", researchTagOnto.getOntoId());
				researchTagValueWrapper.eq("tag_attr", researchTagAttr.getTagAttr());
				researchTagValueWrapper.in("id", idssplit);
				List<ResearchTag2Entity> researchTagValueEntityList = researchTag2Mapper
						.selectList(researchTagValueWrapper);
				if (researchTagValueEntityList.size() == 0) {
					continue;
				}
				if (!ontoSql.isEmpty()) {
					ontoSql += " or ";
				}
				ontoSql += " (onto_id = " + researchTagOnto.getOntoId() + " and ( ";
				String ontosql1 = "";
				for (ResearchTag2Entity researchTagValue : researchTagValueEntityList) {
					if (!ontosql1.isEmpty()) {
						ontosql1 += " or ";
					}
					ontosql1 += " data->'$." + researchTagValue.getAttrCategory() + "' like '%"
							+ researchTagValue.getAttrValId() + "%' ";
				}
				ontoSql += ontosql1 + "))";
			}
			QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
			libEntityWrapper.apply(ontoSql);
			List<LibEntityEntity> libEntityEntities = libEntityMapper.selectList(libEntityWrapper);
			if (libEntityEntities.size() == 0) {
				continue;
			}
			for (LibEntityEntity libEntity : libEntityEntities) {
				if (!libentitySql.isEmpty()) {
					libentitySql += " OR ";
				}
				libentitySql += researchTagAttr.getTagAttr() + " like '%" + libEntity.getName() + "%' ";
			}
		}
		if (libentitySql.isEmpty()) {
			libentitySql = " 1 != 1 ";
		}
		return libentitySql;
	}

	public String queryEventTag2AND(EventPagination eventPagination) {
		String tagSql = "";
		List<String> sqlList = new ArrayList<>();
		// tag2
		String[] idssplit = eventPagination.getResearchTag2Ids().split(",");
		// 标签分类英文名
		QueryWrapper<ResearchTag2Entity> researchTagAttrWrapper = new QueryWrapper<>();
		researchTagAttrWrapper.select("tag_attr");
		researchTagAttrWrapper.in("id", idssplit);
		researchTagAttrWrapper.groupBy("tag_attr");
		List<ResearchTag2Entity> researchTagAttrEntityList = researchTag2Mapper.selectList(researchTagAttrWrapper);

		// 全部
		QueryWrapper<ResearchTag2Entity> researchTagWrapper = new QueryWrapper<>();
		researchTagWrapper.in("id", idssplit);
		List<ResearchTag2Entity> researchTagList = researchTag2Mapper.selectList(researchTagWrapper);

		Map<String, List<ResearchTag2Entity>> map = researchTagList.stream()
				.collect(Collectors.groupingBy(ResearchTag2Entity::getTagAttr));
		map.forEach((k, v) -> {
			String tagOntoSql = "";
			for (ResearchTag2Entity tag2 : v) {
				if (!tagOntoSql.isEmpty()) {
					tagOntoSql += " OR ";
				}
				tagOntoSql += "(';'||tag2||';') LIKE '%;" + tag2.getName() + ";%' ";
			}
			sqlList.add(tagOntoSql);
		});
		for (String sql : sqlList) {
			if (tagSql.isEmpty()) {
				tagSql = tagSql + " (" + sql + ") ";
			} else {
				tagSql = tagSql + " AND (" + sql + ") ";
			}

		}
		return tagSql;
	}

	public void eventGetFdid(EventPagination eventPagination) {
		List<FileDirectoryEntity> fileDirectoryList = fileDirectoryMapper.selectList(new QueryWrapper<>());
	}

	@Override
	public R getTagList(String tag){
		HashSet<String> tagSet = new HashSet<>();
		QueryWrapper<EventEntity> tagQuery = new QueryWrapper<>();
		tagQuery.select("tag");
		tagQuery.like("tag",tag);
		tagQuery.groupBy("tag");
		List<EventEntity> eventlist = this.list(tagQuery);
		for (EventEntity event:eventlist) {
			if (event.getTag() == null || event.getTag().isEmpty()){
				continue;
			}
			String[] split = event.getTag().split(";");
			for (String tagEvent:split) {
				if (tagEvent == null || tagEvent.isEmpty()){
					continue;
				}
				if (tagEvent.contains(tag)){
					tagSet.add(tagEvent);
				}
			}
		}
		return R.ok(tagSet);
	}

    @Override
    public List<Integer> monthlyStats() {

		return eventMapper.monthlyStats();
    }

    @Override
    public List<Map<String, Object>> getMonthlyStats() {
        return eventMapper.getMonthlyStats();
    }

}