package com.sinosoft.ie.booster.yearbook.mark.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import com.sinosoft.ie.booster.yearbook.mark.service.EventInsertService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkInfoService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Api(tags = "本体概念类")
@RequestMapping("/markInfo")
public class MarkInfoController {
	
    @Autowired
    private LibConceptService libConceptService;
    @Autowired
    private MetaOntoRelationService metaOntoRelationService;
    @Autowired
    private MarkInfoService markInfoService;
    @Autowired
    private EventInsertService eventInsertService;
    /**
	 * 一级本体
	 */
    @GetMapping("/getOntoList")
	public R getInfo() {
    	LibConceptPagination  libConceptPagination = new LibConceptPagination();
    	//libConceptPagination.setPid(0L);
    	libConceptPagination.setCurrentPage(0);
        List<LibConceptEntity> list= libConceptService.getList(libConceptPagination);
        //处理id字段转名称，若无需转或者为空可删除
        List<LibConceptListVO> listVO=JsonUtil.getJsonToList(list,LibConceptListVO.class);
        return R.ok(listVO);
	}
	/**
	 * 查询关系 包含子级关系
	 */
    @GetMapping("/getAllRelationByOntoId")
	public  R getAllRelationByOntoId(Long ontoId) {
    	List<MetaOntoRelationInfoVO>  list = metaOntoRelationService.getList2(ontoId);
        return R.ok(list);
	}
    
    @GetMapping("/getProperts")
	public R  getProperts(Long ontoId) {
    	List list = markInfoService.getPropertsList(ontoId);
    	return R.ok(list );
	}
    /**
     * 根据句子id查询标签和三元组
     * @param sentenceId
     * @return
     */
    @GetMapping("/getTagInfo")
    public R getTagInfo(Long sentenceId) {
        Map<String,Object> tagInfo = markInfoService.getTagInfo(sentenceId);
        return R.ok(tagInfo);
    }


    @Transactional
    @PostMapping("/insertTagInfo")
    public R<String> insertTagInfo(@RequestBody Map<String,Object> map) throws DataException {
       
        return  markInfoService.insertTagInfo(map);
    }

    
    @Transactional
    @PostMapping("/insertExcelData")
    public R<String> insertExcelData( Long id) throws DataException {
       
        return  eventInsertService.reloveExcel(id);
    }
    
    @GetMapping("/checkDataBase")
    public R checkDataBase( ) throws DataException {
       
        return  eventInsertService.checkDataBase();
    }
    
    
}
