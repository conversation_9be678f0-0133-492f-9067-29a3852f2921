package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTag3Entity;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTag3Mapper;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.FileDirectoryService;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTag3Service;

@Service
public class ResearchTag3ServiceImpl extends ServiceImpl<ResearchTag3Mapper, ResearchTag3Entity>
		implements ResearchTag3Service {

	@Resource
	private FileDirectoryService fileDirectoryService;
	@Resource
	private EventService eventService;
	@Override
	public void compuit() {

		List<FileDirectoryEntity> dirlist = fileDirectoryService.list();
		
		dirlist.forEach(x->{
			List<ResearchTag3Entity>  tag3list = new ArrayList<>();
			QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("fid", x.getFileId());
			queryWrapper.eq("page_num", x.getPageNum());
			List<EventEntity>	evelist = eventService.list(queryWrapper);
			Set<String>  set = new HashSet<>();
			for (int i = 0; i < evelist.size(); i++) {
				if(!StringUtils.isEmpty(evelist.get(i).getTag())) {
					String[] tags = evelist.get(i).getTag().split(";");
					for (int j = 0; j < tags.length; j++) {
						set.add(tags[j]);
					}
				}
			}
			if(set.size()==0) {
				return;
			}
			for (String str : set) {
				ResearchTag3Entity ent = new ResearchTag3Entity();
				ent.setName(str);
				ent.setDirId(x.getId());
				tag3list.add(ent);
			}
			this.saveBatch(tag3list);
		});
		
	}

}
