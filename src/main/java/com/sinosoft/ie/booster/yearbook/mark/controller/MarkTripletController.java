package com.sinosoft.ie.booster.yearbook.mark.controller;

import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.mark.service.EventInsertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.marktriplet.MarkTripletUpForm;
import com.sinosoft.ie.booster.yearbook.mark.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkTripletService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mark_triplet
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:56
 */
@Slf4j
@RestController
@Api(tags = "mark_triplet")
@RequestMapping("/MarkTriplet")
public class MarkTripletController {
    @Autowired
    private MarkTripletService markTripletService;

    @Resource
    private EventInsertService eventInsertService;


    /**
     * 列表
     *
     * @param markTripletPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MarkTripletListVO>> list(MarkTripletPagination markTripletPagination)throws IOException{
        List<MarkTripletEntity> list= markTripletService.getList(markTripletPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MarkTripletEntity entity:list){
            }
        List<MarkTripletListVO> listVO=JsonUtil.getJsonToList(list,MarkTripletListVO.class);
        PageListVO<MarkTripletListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(markTripletPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param markTripletCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MarkTripletCrForm markTripletCrForm) throws DataException {
        MarkTripletEntity entity=JsonUtil.getJsonToBean(markTripletCrForm, MarkTripletEntity.class);
        markTripletService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MarkTripletInfoVO> info(@PathVariable("id") Long id){
        MarkTripletEntity entity= markTripletService.getInfo(id);
        MarkTripletInfoVO vo=JsonUtil.getJsonToBean(entity, MarkTripletInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MarkTripletUpForm markTripletUpForm) throws DataException {
        MarkTripletEntity entity= markTripletService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(markTripletUpForm, MarkTripletEntity.class);
            markTripletService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MarkTripletEntity entity= markTripletService.getInfo(id);
        if(entity!=null){
            markTripletService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
