package com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * FileImportLog模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-08 16:35:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class FileImportLogPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long jobId;

    /**
     * 行数
     */
    @ApiModelProperty(value = "行数")
    private Integer rowNum;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 任务类型：1.实体导入；2实体关系导入；3同义导入
     */
    @ApiModelProperty(value = "任务类型：1.实体导入；2实体关系导入；3同义导入")
    private Integer jobType;

    /**
     * 日志
     */
    @ApiModelProperty(value = "日志")
    private String errorLog;
}