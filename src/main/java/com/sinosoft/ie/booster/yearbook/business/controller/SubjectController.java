package com.sinosoft.ie.booster.yearbook.business.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectPagination;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectListVO;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectUpForm;
import com.sinosoft.ie.booster.yearbook.business.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.SubjectEntity;
import com.sinosoft.ie.booster.yearbook.business.service.SubjectService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * subject
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 14:03:56
 */
@Slf4j
@RestController
@Api(tags = "subject")
@RequestMapping("/Subject")
public class SubjectController {
    @Autowired
    private SubjectService subjectService;


    /**
     * 列表
     *
     * @param subjectPagination
     * @return
     */
    @GetMapping
    public R list(SubjectPagination subjectPagination)throws IOException{
        List<SubjectEntity> list= subjectService.getList(subjectPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(SubjectEntity entity:list){
            }
        List<SubjectListVO> listVO=JsonUtil.getJsonToList(list,SubjectListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(subjectPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param subjectCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid SubjectCrForm subjectCrForm) throws DataException {
        SubjectEntity entity=JsonUtil.getJsonToBean(subjectCrForm, SubjectEntity.class);
        subjectService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<SubjectInfoVO> info(@PathVariable("id") Long id){
        SubjectEntity entity= subjectService.getInfo(id);
        SubjectInfoVO vo=JsonUtil.getJsonToBean(entity, SubjectInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid SubjectUpForm subjectUpForm) throws DataException {
        SubjectEntity entity= subjectService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(subjectUpForm, SubjectEntity.class);
            subjectService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        SubjectEntity entity= subjectService.getInfo(id);
        if(entity!=null){
            subjectService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

	/**
	 * 列表
	 *
	 * @param
	 * @return
	 */
	@GetMapping("/listSelect")
	public R listSelect()throws IOException{
		List<Map<String,Object>> list= subjectService.getListSelect();
		return R.ok(list);
	}
}
