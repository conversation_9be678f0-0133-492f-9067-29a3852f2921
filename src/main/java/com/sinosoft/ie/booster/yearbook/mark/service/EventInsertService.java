package com.sinosoft.ie.booster.yearbook.mark.service;

import java.util.List;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;

public interface EventInsertService {
	R lib2eventNewTable(EventEntity even);

	R lib2eventNewTable2(EventCrForm even);
	
	R  lib2eventNewTable3(EventCrForm even);
	
	R  reloveExcel(Long id );
	
	R  checkDataBase();
	
}
