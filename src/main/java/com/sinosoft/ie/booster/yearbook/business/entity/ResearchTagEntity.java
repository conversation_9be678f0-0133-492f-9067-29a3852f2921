package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-15 10:49:22
 */
@Data
@TableName("research_tag")
@ApiModel(value = "")
public class ResearchTagEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String name;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer zonelevel;

    /**
     * 图例类型
     */
    @ApiModelProperty(value = "图例类型")
    private String legendType;

    /**
     * 工作部门
     */
    @ApiModelProperty(value = "工作部门")
    private String organization;

    @ApiModelProperty(value = "年")
    private String year;
    
    /**
     * 首字母
     */
    private String  initial;
    /**
     * 机构id
     */
    private String  orgId;
    
    
    
}
