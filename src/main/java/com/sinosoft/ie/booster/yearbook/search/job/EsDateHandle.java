package com.sinosoft.ie.booster.yearbook.search.job;

import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEntityMapper;
import com.sinosoft.ie.booster.yearbook.search.mapper.EsEventMapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class EsDateHandle {

	@Resource
	private EventService eventService;
	@Resource
	private LibEntityService libEntityService;
	@Resource
	private LibEntityMapper libEntityMapper;
	@Resource
	private MateAttributeMapper mateAttributeMapper;
	@Resource
	private MateAttributeValueMapper mateAttributeValueMapper;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	@Resource
	private EsEventMapper esEventMapper;
	@Resource
	private EsEntityMapper esEntityMapper;
	@Resource
	private RestHighLevelClient client;


	public boolean updateEntity(Long id) {
		LibEntityEntity entity = libEntityService.getById(id);
		JSONObject json = coverEntity(entity);
		BulkRequest bulkRequest = new BulkRequest();
		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest.index("entity").id(String.valueOf(id));
		updateRequest.doc(json.toString(), XContentType.JSON);
		try {
			client.update(updateRequest, RequestOptions.DEFAULT);
		} catch (IOException e) {
			throw new DataException("修改es数据失败-->" + id + "==>" + e);
		}
		return true;
	}

	public JSONObject coverEntity(LibEntityEntity entity) {
		log.info("当前entity=="+entity.getId());
		if(entity.getId().equals(1678288187377627145L)) {
			System.out.println("------");
		}
		JSONObject json = JSONUtil.createObj();
		if (ObjectUtil.isEmpty(entity.getData()) || ObjectUtil.isAllEmpty(entity.getOntoId())) {
			return json;
		}
		json.set("name", entity.getName());
		if (!ObjectUtil.isEmpty(entity.getOntoId())) {
			json.set("onto_id", String.valueOf(entity.getOntoId()));
		}
		List<MateAttributeEntity> attrList = OntoCache.getAttListByOntoId(entity.getOntoId());
		Map<String, Object> stringObjectMap = JsonUtil.stringToMap(entity.getData());
		if(attrList==null) {
			return json;
		}
		for (MateAttributeEntity mate : attrList) {
			String nameEn = mate.getNameEn();
			Object o = stringObjectMap.get(nameEn);
			if (o == null) {
				continue;
			}
			Object value = "";
			if(mate.getVals()==1) {
				value = objToVal(mate,o);
				json.set(nameEn, value);
			}else {
				if(!(o instanceof Collection)) {
					throw new DataException(entity.getId()+"的["+mate.getNameCn()+"]属性值和配置不符");
				}
				JSONArray val_arry = (JSONArray) o;
				JSONArray val_name_arry = new JSONArray();
				for (int i = 0; i < val_arry.size(); i++) {
					Object val = objToVal(mate,val_arry.get(i));
					val_name_arry.add(val);
				}
				if(val_name_arry.size()!=0) {
					json.set(nameEn, join_arry(val_name_arry));
				}
				
			}
		}
		return json;
	}

	public  String  join_arry(JSONArray arry) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < arry.size(); i++) {
			Object obj =  arry.get(i);
			if(obj instanceof Long || obj instanceof Integer) {
				sb.append(String.valueOf(obj));
			}if(obj instanceof String) {
				sb.append((String)obj);
			}
			sb.append(";");
		}
		String str = sb.toString().substring(0,sb.length()-1 );
		return str;
	}
	
	
	public Object  objToVal(MateAttributeEntity mate,Object obj) {
		log.debug("mate------------"+mate.getId()+"----------"+mate.getNameEn());
		if(mate.getValType()==YearbookConstant.META_ATTRIBUTE_VAL_IMAGE || mate.getValType()==YearbookConstant.META_ATTRIBUTE_VAL_FILE  ) {
			Long id = (Long)obj;
			return id.toString();
		}
		Object value = "";
		if (mate.getType() == 2) {
			LibEntityEntity objentityt = getLibEntity((Long) obj);
			if (objentityt != null) {
				value = (objentityt.getName());
			}
		}
		if (mate.getType() == 3) {
			MateAttributeValueEntity valEnt = getValEntity((Long) obj);
			if (valEnt != null) {
				value = (valEnt.getValName());
			}
		}
		if (mate.getType() == 1) {
				value =  obj;
		}
		return value;
	}
	
	
	
	Map<Long, LibEntityEntity> libMap = new HashMap<Long, LibEntityEntity>();

	public LibEntityEntity getLibEntity(Long id) {
		LibEntityEntity entity = libMap.get(id);
		if (entity == null) {
			entity = libEntityService.getById(id);
			libMap.put(id, entity);
		}
		return entity;
	}

	Map<Long, MateAttributeValueEntity> valMap = new HashMap<Long, MateAttributeValueEntity>();

	public MateAttributeValueEntity getValEntity(Long id) {
		MateAttributeValueEntity entity = valMap.get(id);
		if (entity == null) {
			entity = mateAttributeValueService.getById(id);
			valMap.put(id, entity);
		}
		return entity;
	}

}
