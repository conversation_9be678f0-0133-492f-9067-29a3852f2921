package com.sinosoft.ie.booster.yearbook.business.model.homePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：ImportantNews.java
 * @description：首页要事要闻返回类
 * @author：peas
 * @data：2022/11/9 18:09
 **/
@Data
@ApiModel(discriminator = "首页要事要闻返回类")
public class ImportantNews implements Serializable{

    /**
     * 机构数量
     */
    @ApiModelProperty(value = "机构数量")
    private Integer orgCount;

    /**
     * 要事要闻条数
     */
    @ApiModelProperty(value = "要事要闻总数")
    private Integer newsCount;

    /**
     * 各个机构事件数量
     */
    @ApiModelProperty(value = "各个机构的事件数量")
    private List<OrgEventOut> orgEvent;

    /**
     * 要事要闻列表
     */
    @ApiModelProperty(value = "要事要闻列表")
    private List<ImportantNewsInfo> infoList;

}
