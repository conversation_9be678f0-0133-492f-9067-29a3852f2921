package com.sinosoft.ie.booster.yearbook.business.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsPagination;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsListVO;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsUpForm;
import com.sinosoft.ie.booster.yearbook.business.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.ResultsEntity;
import com.sinosoft.ie.booster.yearbook.business.service.ResultsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * results
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 13:51:00
 */
@Slf4j
@RestController
@Api(tags = "results")
@RequestMapping("/Results")
public class ResultsController {
    @Autowired
    private ResultsService resultsService;


    /**
     * 列表
     *
     * @param resultsPagination
     * @return
     */
    @GetMapping
    public R list(ResultsPagination resultsPagination)throws IOException{
        List<ResultsEntity> list= resultsService.getList(resultsPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(ResultsEntity entity:list){
            }
        List<ResultsListVO> listVO=JsonUtil.getJsonToList(list,ResultsListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(resultsPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param resultsCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid ResultsCrForm resultsCrForm) throws DataException {
        ResultsEntity entity=JsonUtil.getJsonToBean(resultsCrForm, ResultsEntity.class);
        resultsService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<ResultsInfoVO> info(@PathVariable("id") Long id){
        ResultsEntity entity= resultsService.getInfo(id);
        ResultsInfoVO vo=JsonUtil.getJsonToBean(entity, ResultsInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid ResultsUpForm resultsUpForm) throws DataException {
        ResultsEntity entity= resultsService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(resultsUpForm, ResultsEntity.class);
            resultsService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        ResultsEntity entity= resultsService.getInfo(id);
        if(entity!=null){
            resultsService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
