package com.sinosoft.ie.booster.yearbook.baselib.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.service.SchemaViewService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 模式视图
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@Api(tags = "schemaView")
@RequestMapping("/schemaView")
public class SchemaViewController {

	@Resource
	private SchemaViewService schemaViewService;
	@GetMapping
	public  R getView(Long id) {
		
		return schemaViewService.getView(id);
	}

	/**
	 * 模式试图-关系可视化
	 * @param id
	 * @return
	 */
	@GetMapping("/getSchemaRelation")
	public  R getSchemaRelation(Long id) {

		return schemaViewService.getSchemaRelation(id);
	}
}
