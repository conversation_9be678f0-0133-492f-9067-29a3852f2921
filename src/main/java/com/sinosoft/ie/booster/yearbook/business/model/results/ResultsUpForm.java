package com.sinosoft.ie.booster.yearbook.business.model.results;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Results模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 13:51:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ResultsUpForm extends ResultsCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}