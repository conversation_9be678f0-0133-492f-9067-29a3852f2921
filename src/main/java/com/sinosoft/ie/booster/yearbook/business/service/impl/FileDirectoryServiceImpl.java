package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.FileDirectoryMapper;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventListVO;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryListVO;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationListVO;
import com.sinosoft.ie.booster.yearbook.business.service.FileDirectoryService;
import com.sinosoft.ie.booster.yearbook.business.service.YearbookService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryPagination;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * file_directory
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-16 14:28:43
 */
@Service
public class FileDirectoryServiceImpl extends ServiceImpl<FileDirectoryMapper, FileDirectoryEntity> implements FileDirectoryService {

	@Autowired
	private YearbookService yearbookService;

	@Override
    public List<FileDirectoryEntity> getList(FileDirectoryPagination fileDirectoryPagination){
        QueryWrapper<FileDirectoryEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(fileDirectoryPagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getId,fileDirectoryPagination.getId()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getName,fileDirectoryPagination.getName()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getPageNum()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getPageNum,fileDirectoryPagination.getPageNum()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getDelFlag,fileDirectoryPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getCreateBy,fileDirectoryPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getPid()))){
            queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getPid,fileDirectoryPagination.getPid()));
        }
		if(!"null".equals(String.valueOf(fileDirectoryPagination.getFileId()))){
			queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getFileId,fileDirectoryPagination.getFileId()));
		}
        //排序
        if(StrUtil.isEmpty(fileDirectoryPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FileDirectoryEntity::getId);
        }else{
            queryWrapper="asc".equals(fileDirectoryPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(fileDirectoryPagination.getSidx()):queryWrapper.orderByDesc(fileDirectoryPagination.getSidx());
        }
        Page<FileDirectoryEntity> page=new Page<>(fileDirectoryPagination.getCurrentPage(), fileDirectoryPagination.getPageSize());
        IPage<FileDirectoryEntity> userIPage=this.page(page,queryWrapper);
        return fileDirectoryPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<FileDirectoryEntity> getTypeList(FileDirectoryPagination fileDirectoryPagination,String dataType){
        QueryWrapper<FileDirectoryEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(fileDirectoryPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getId,fileDirectoryPagination.getId()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getName,fileDirectoryPagination.getName()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getPageNum()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getPageNum,fileDirectoryPagination.getPageNum()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getDelFlag,fileDirectoryPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getCreateBy()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getCreateBy,fileDirectoryPagination.getCreateBy()));
        }

        if(!"null".equals(String.valueOf(fileDirectoryPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(FileDirectoryEntity::getPid,fileDirectoryPagination.getPid()));
        }

        //排序
        if(StrUtil.isEmpty(fileDirectoryPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(FileDirectoryEntity::getId);
        }else{
            queryWrapper="asc".equals(fileDirectoryPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(fileDirectoryPagination.getSidx()):queryWrapper.orderByDesc(fileDirectoryPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<FileDirectoryEntity> page=new Page<>(fileDirectoryPagination.getCurrentPage(), fileDirectoryPagination.getPageSize());
            IPage<FileDirectoryEntity> userIPage=this.page(page,queryWrapper);
            return fileDirectoryPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public FileDirectoryEntity getInfo(Long id){
        QueryWrapper<FileDirectoryEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(FileDirectoryEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(FileDirectoryEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, FileDirectoryEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }

	@Override
    public void delete(FileDirectoryEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
	/**
	 * 查询某个年鉴的目录
	 */
	@Override
	public Map<String,Object> showDirTree(Long fid) {
		Map<String,Object>  resMap = new HashMap<>();

		//取第一级
		QueryWrapper<FileDirectoryEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getFileId,fid));
//		queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getPid,0L));
		queryWrapper.lambda().orderByAsc(FileDirectoryEntity::getId);
		List<FileDirectoryEntity> entityList1 = this.list(queryWrapper);
		Long firstId = 0L;
		for (int i = 0; i < entityList1.size(); i++) {
			FileDirectoryEntity en = entityList1.get(i);
			if(en.getPageNum()==1) {
				firstId = en.getId();
			}
		}
		List<FileDirectoryListVO> listVO1 = JsonUtil.getJsonToList(entityList1, FileDirectoryListVO.class);
        List<FileDirectoryListVO> listVO2 = new ArrayList<>();
		if (listVO1 != null && listVO1.size() > 0){
            Map<String,List<FileDirectoryListVO>> map = new HashMap<>();
            for (int i=0; i<listVO1.size(); i++){
                if (map.containsKey(listVO1.get(i).getPid())){
                    map.get(listVO1.get(i).getPid()).add(listVO1.get(i));
                }else {
                    List<FileDirectoryListVO> samePidList = new ArrayList<>();
                    samePidList.add(listVO1.get(i));
                    map.put(listVO1.get(i).getPid(),samePidList);
                }
            }
            for (String pid:map.keySet()) {
                if(pid.equals("0")) {
                    continue;
                }
                for (int i=0; i<listVO1.size(); i++){
                    if (pid.equals(listVO1.get(i).getId())){
                        listVO1.get(i).setSonList(map.get(pid));
                    }
                }
            }
            for (int i = 0; i < listVO1.size(); i++) {
                if (listVO1.get(i).getPid().equals("0")){
                    listVO2.add(listVO1.get(i));
                }
            }
		}
//		YearbookEntity yearbook = yearbookService.getInfoFid(fid);
		YearbookEntity yearbook = yearbookService.getInfo(fid);
		resMap.put("bookName",yearbook.getBookName());
		resMap.put("fileDir",listVO2);
		resMap.put("idxId",firstId);
		return resMap;
	}

	/**
	 * 查询全部目录
	 * @return
	 */
	public List<FileDirectoryListVO> showDirTree() {
		//
		//取第一级
		QueryWrapper<FileDirectoryEntity> queryWrapper=new QueryWrapper<>();
		List<FileDirectoryEntity> entityList1 = this.list(queryWrapper);
		List<FileDirectoryListVO> listVO1 = JsonUtil.getJsonToList(entityList1, FileDirectoryListVO.class);
        List<FileDirectoryListVO> listVO2 = new ArrayList<>();
		if (listVO1 != null && listVO1.size() > 0){
            Map<String,List<FileDirectoryListVO>> map = new HashMap<>();
            for (int i=0; i<listVO1.size(); i++){
                if (map.containsKey(listVO1.get(i).getPid())){
                    map.get(listVO1.get(i).getPid()).add(listVO1.get(i));
                }else {
                    List<FileDirectoryListVO> samePidList = new ArrayList<>();
                    samePidList.add(listVO1.get(i));
                    map.put(listVO1.get(i).getPid(),samePidList);
                }
            }
            for (String pid:map.keySet()) {
                if(pid.equals("0")) {
                    continue;
                }
                for (int i=0; i<listVO1.size(); i++){
                    if (pid.equals(listVO1.get(i).getId())){
                        listVO1.get(i).setSonList(map.get(pid));
                    }
                }
            }
            for (int i = 0; i < listVO1.size(); i++) {
                if (listVO1.get(i).getPid().equals("0")){
                    listVO2.add(listVO1.get(i));
                }
            }
		}
		return listVO2;
	}
	@Override
	public Map<String, Object> dirInfo(Long id) {
		Map<String, Object> map =  new HashedMap<>();
		FileDirectoryEntity ent = getInfo(id);
		map.put("dir_id", ent.getId());
		map.put("page_num", ent.getPageNum());
		YearbookEntity yearEnt = yearbookService.getInfo(ent.getFileId());
		if(yearEnt==null) {
			throw new DataException("未查询到相关年鉴");
		}
		map.put("book_id", yearEnt.getId());
		map.put("book_page_count", yearEnt.getPageNum());
		return map;
	}
	@Override
	public Map<String, Object> dirInfo(Integer year, Integer pageNumber) {
		Map<String, Object> map =  new HashedMap<>();
		QueryWrapper<YearbookEntity>  query = new QueryWrapper<YearbookEntity>();
		query.eq("book_year", year);
		List<YearbookEntity>  list = yearbookService.list(query);
		if(list.size()==0) {
			throw new DataException("未查询到相关年鉴");
		}
		map.put("book_id", list.get(0).getId());
		map.put("book_page_count", list.get(0).getPageNum());
		
		QueryWrapper<YearbookEntity>  pagequery = new QueryWrapper<YearbookEntity>();
		pagequery.eq("pid", list.get(0).getId());
		pagequery.eq("page_num", pageNumber);
		List<YearbookEntity>  list1 = yearbookService.list(pagequery);
		if(list1.size()==0) {
			throw new DataException("未查询到相关年鉴文件");
		}
		map.put("pdf", list1.get(0).getPath());
		
		
		QueryWrapper<FileDirectoryEntity>  dirQuery = new QueryWrapper<FileDirectoryEntity>();
		dirQuery.eq("file_id", list.get(0).getId());
		//dirQuery
		List<FileDirectoryEntity> dirlist = this.list(dirQuery);
		if(dirlist.size()==0) {
			throw new DataException("未查询到相关目录");
		}
		
		for (int i = dirlist.size(); i > dirlist.size(); i--) {
			Integer num =  dirlist.get(i).getPageNum();
			if(pageNumber > num || num ==pageNumber) {
				map.put("dir_id", dirlist.get(i).getId());
				break;
			}
		}
		
		return map;
	}
	
//    @Override
//    public List<FileDirectoryListVO> showDirTree(Long fid) {
//        //
//        //取第一级
//        QueryWrapper<FileDirectoryEntity> queryWrapper=new QueryWrapper<>();
//        queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getFileId,fid));
//        queryWrapper.lambda().and(t->t.eq(FileDirectoryEntity::getPid,0L));
//        queryWrapper.lambda().orderByAsc(FileDirectoryEntity::getId);
//        List<FileDirectoryEntity> entityList1 = this.list(queryWrapper);
//        List<FileDirectoryListVO> listVO1 = JsonUtil.getJsonToList(entityList1, FileDirectoryListVO.class);
//        if (listVO1 != null && listVO1.size() > 0){
//            for (int i=0; i<listVO1.size(); i++){
//                //取第二级
//                QueryWrapper<FileDirectoryEntity> queryWrapper1 = new QueryWrapper<>();
//                int finalI = i;
//                queryWrapper1.lambda().and(t->t.eq(FileDirectoryEntity::getPid,listVO1.get(finalI).getId()));
//                queryWrapper1.lambda().and(t->t.eq(FileDirectoryEntity::getFileId,fid));
//                List<FileDirectoryEntity> entityList2 = this.list(queryWrapper1);
//                listVO1.get(finalI).setSonList(entityList2);
//            }
//        }
//
//        return listVO1;
//    }
}