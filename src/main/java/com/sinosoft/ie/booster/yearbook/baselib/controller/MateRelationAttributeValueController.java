package com.sinosoft.ie.booster.yearbook.baselib.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValuePaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValuePagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValueCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValueInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValueListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue.MateRelationAttributeValueUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateRelationAttributeValueService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * mate_relation_attribute_value
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-01 14:35:03
 */
@Slf4j
@RestController
@Api(tags = "mate_relation_attribute_value")
@RequestMapping("/MateRelationAttributeValue")
public class MateRelationAttributeValueController {
    @Autowired
    private MateRelationAttributeValueService mateRelationAttributeValueService;


    /**
     * 列表
     *
     * @param mateRelationAttributeValuePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MateRelationAttributeValueListVO>> list(MateRelationAttributeValuePagination mateRelationAttributeValuePagination)throws IOException{
        List<MateRelationAttributeValueEntity> list= mateRelationAttributeValueService.getList(mateRelationAttributeValuePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MateRelationAttributeValueEntity entity:list){
            }
        List<MateRelationAttributeValueListVO> listVO=JsonUtil.getJsonToList(list,MateRelationAttributeValueListVO.class);
        PageListVO<MateRelationAttributeValueListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(mateRelationAttributeValuePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param mateRelationAttributeValueCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MateRelationAttributeValueCrForm mateRelationAttributeValueCrForm) throws DataException {
        MateRelationAttributeValueEntity entity=JsonUtil.getJsonToBean(mateRelationAttributeValueCrForm, MateRelationAttributeValueEntity.class);
        mateRelationAttributeValueService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MateRelationAttributeValueInfoVO> info(@PathVariable("id") Long id){
        MateRelationAttributeValueEntity entity= mateRelationAttributeValueService.getInfo(id);
        MateRelationAttributeValueInfoVO vo=JsonUtil.getJsonToBean(entity, MateRelationAttributeValueInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MateRelationAttributeValueUpForm mateRelationAttributeValueUpForm) throws DataException {
        MateRelationAttributeValueEntity entity= mateRelationAttributeValueService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(mateRelationAttributeValueUpForm, MateRelationAttributeValueEntity.class);
            mateRelationAttributeValueService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MateRelationAttributeValueEntity entity= mateRelationAttributeValueService.getInfo(id);
        if(entity!=null){
            mateRelationAttributeValueService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
