package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.HotInformationEntity;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventListVO;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventUpForm;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationListVO;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationPagination;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.HotInformationService;
import com.sinosoft.ie.booster.yearbook.mark.service.EventInsertService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * event @版本： V1.0.0 @作者： booster开发平台组 @日期： 2022-09-15 10:16:34
 */
@Slf4j
@RestController
@Api(tags = "event")
@RequestMapping("/Event")
public class EventController {
	@Autowired
	private EventService eventService;
	@Autowired
	private HotInformationService hotInformationService;
	@Autowired
	private EventInsertService eventInsertService;

	/**
	 * 列表
	 *
	 * @param eventPagination
	 * @return
	 */
	@GetMapping
	public R list(EventPagination eventPagination) throws IOException {
		System.out.println("执行了event方法");
		List<EventEntity> list = eventService.getList(eventPagination);
		// 处理id字段转名称，若无需转或者为空可删除
		List<EventListVO> listVO = JsonUtil.getJsonToList(list, EventListVO.class);
		PageListVO vo = new PageListVO();
		vo.setList(listVO);
		PaginationVO page = JsonUtil.getJsonToBean(eventPagination, PaginationVO.class);
		vo.setPagination(page);
		return R.ok(vo);
	}

	/**
	 * 创建
	 *
	 * @param eventCrForm
	 * @return
	 */
	@PostMapping
	@Transactional
	public R create(@RequestBody @Valid EventCrForm eventCrForm) throws DataException {
		EventEntity entity = JsonUtil.getJsonToBean(eventCrForm, EventEntity.class);
		eventService.create(entity);
		return R.ok(null, "新建成功");
	}

	/**
	 * 信息
	 *
	 * @param id
	 * @return
	 */
	@GetMapping("/{id}")
	public R<EventInfoVO> info(@PathVariable("id") Long id) {
		EventEntity entity = eventService.getInfo(id);
		EventInfoVO vo = JsonUtil.getJsonToBean(entity, EventInfoVO.class);
		return R.ok(vo);
	}

	/**
	 * 更新
	 *
	 * @param id
	 * @return
	 */
	@PutMapping("/{id}")
	@Transactional
	public R update(@PathVariable("id") Long id, @RequestBody @Valid EventUpForm eventUpForm) throws DataException {
		EventEntity entity = eventService.getInfo(id);
		if (entity != null) {
			entity = JsonUtil.getJsonToBean(eventUpForm, EventEntity.class);
			eventService.update(id, entity);
			return R.ok(null, "更新成功");
		} else {
			return R.failed("更新失败，数据不存在");
		}
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	@DeleteMapping("/{id}")
	@Transactional
	public R delete(@PathVariable("id") Long id) {
		EventEntity entity = eventService.getInfo(id);
		if (entity != null) {
			eventService.delete(entity);
		}
		return R.ok(null, "删除成功");
	}

	@GetMapping("/queryTime")
	public R queryTime() {
		return R.ok(eventService.getAllTime());
	}

	/**
	 * 事件脉络-详情
	 * 
	 * @param year
	 * @return
	 */
	@GetMapping("/queryEventByYear")
	public R queryEventByYear(String year, String theme, String typeParam) {
		Map<Integer, List<EventEntity>> listMap = eventService.queryEventByYearNoDay(year, theme, typeParam);
		Map<String, Integer> countMap = eventService.queryEventCountByYear(year, theme);
		HotInformationPagination hotPagination = new HotInformationPagination();
		hotPagination.setYear(year);
		hotPagination.setTheme(theme);
//		List<HotInformationEntity> hotEntityList = hotInformationService.getList(hotPagination);
		List<HotInformationEntity> hotEntityList =  host_tag(year, theme, typeParam);
		List<HotInformationListVO> hotListVO = JsonUtil.getJsonToList(hotEntityList, HotInformationListVO.class);
		Map<String, Object> map = new HashMap<>();
		// 地图数据
		List<Map<String, Integer>> mapData = eventService.mapData(year, theme);
		map.put("list", listMap);
		map.put("count", countMap);
		map.put("hot", hotListVO);
		map.put("mapData", mapData);
		return R.ok(map);
	}

	/**
	 * 事件脉络-概要
	 * 
	 * @param year
	 * @return
	 */
	@GetMapping("/queryEventByYearOneEvent")
	public R queryEventByYearOneEvent(String year, String theme, String typeParam) {
		Map<Integer, EventInfoVO> listMap = eventService.queryEventByYearNoDayOneEvent(year, theme, typeParam);
		Map<String, Integer> countMap = eventService.queryEventCountByYear(year, theme);
		HotInformationPagination hotPagination = new HotInformationPagination();
		hotPagination.setYear(year);
		hotPagination.setTheme(theme);
//		List<HotInformationEntity> hotEntityList = hotInformationService.getList(hotPagination);
//		List<HotInformationListVO> hotListVO = JsonUtil.getJsonToList(hotEntityList, HotInformationListVO.class);
		Map<String, Object> map = new HashMap<>();
		// 地图数据
		
		List<HotInformationEntity> hotEntityList =  host_tag(year, theme, typeParam);
		List<HotInformationListVO> hotListVO = JsonUtil.getJsonToList(hotEntityList, HotInformationListVO.class);
		List<Map<String, Integer>> mapData = eventService.mapData(year, theme);
		map.put("list", listMap);
		map.put("count", countMap);
		map.put("hot", hotListVO);
		map.put("mapData", mapData);
		return R.ok(map);
	}

	/**
	 * 抽取关键词排序
	 */
	public List<HotInformationEntity>  host_tag(String year, String theme, String typeParam) {
		List<HotInformationEntity> hotEntityList = new ArrayList<HotInformationEntity>();
		Map<Integer, List<EventEntity>> listMap = eventService.queryEventByYearNoDay(year, theme, typeParam);
		Map<String, Integer> gropu = new HashedMap<String, Integer>();
		for (Integer key : listMap.keySet()) {
			List<EventEntity> evelist = listMap.get(key);
			for (int i = 0; i < evelist.size(); i++) {
				String tags = evelist.get(i).getTag();
				if (tags == null || tags.isEmpty()) {
					continue;
				}
				String[] tag_arrs = tags.split(",");
				for (int j = 0; j < tag_arrs.length; j++) {
					String tag = tag_arrs[j];
					Integer count = gropu.get(tag);
					if (count == null) {
						count = 1;  // 第一次出现时计数为1
					} else {
						count++;
					}
					gropu.put(tag, count);
				}
			}
		}
		for (String key : gropu.keySet()) {
			HotInformationEntity ent = new HotInformationEntity();
			ent.setName(key);
			ent.setNumber(gropu.get(key));
			hotEntityList.add(ent);
		}
		// 按照数量降序排序
		hotEntityList = hotEntityList.stream()
			.sorted(Comparator.comparingInt(HotInformationEntity::getNumber).reversed())
			.collect(Collectors.toList());  
		return hotEntityList;
	}

	/**
	 * 标签查阅
	 * 
	 * @param eventPagination
	 * @return
	 */
	@GetMapping("/queryEventByAll")
	public R queryEventByAll(EventPagination eventPagination) {
		return R.ok(eventService.queryEventByAll(eventPagination));
	}

	/**
	 * 依据查证-时间范围
	 * 
	 * @return
	 */
	@GetMapping("/getTimeSelect")
	public R getTimeSelect() {
		return R.ok(eventService.getTimeSelect());
	}

	/**
	 * 年鉴上的今天
	 *
	 * @param eventPagination
	 * @return
	 */
	@GetMapping("/yearbookToday")
	public R yearbookToday(EventPagination eventPagination) throws IOException {
		Map<String, Object> map = new HashMap<>();
		List<EventEntity> list = eventService.getList(eventPagination);
		List<String> themeList = eventService.getTagTop(list);
		map.put("tagList", themeList);
		// 处理id字段转名称，若无需转或者为空可删除
		List<EventListVO> listVO = JsonUtil.getJsonToList(list, EventListVO.class);
		PageListVO vo = new PageListVO();
		vo.setList(listVO);
		PaginationVO page = JsonUtil.getJsonToBean(eventPagination, PaginationVO.class);
		vo.setPagination(page);
		map.put("vo", vo);
		return R.ok(map);
	}

	/**
	 * 跳转到电子阅读根据id查询事件信息 查询当前事件所在页的其他事件的标签
	 * 
	 * @param id
	 * @return
	 */
	@GetMapping("/getInfoById/{id}")
	public R<EventInfoVO> getInfoById(@PathVariable("id") Long id) {
		EventEntity entity = eventService.getInfo(id)EventInfoVO vo = JsonUtil.getJsonToBean(entity, EventInfoVO.class);
		return R.ok(vo);
	}

	@PostMapping("/lib2eventNewTable")
	public R lib2eventNewTable(@RequestBody EventEntity even) throws IOException {
		return eventInsertService.lib2eventNewTable(even);
	}

	@PostMapping("/lib2eventNewTable2")
	public R lib2eventNewTable2(@RequestBody EventCrForm even) throws IOException {
		return eventInsertService.lib2eventNewTable2(even);
	}

	@PostMapping("/lib2eventNewTable3")
	public R lib2eventNewTable3(@RequestBody EventCrForm even) throws IOException {
		return eventInsertService.lib2eventNewTable3(even);
	}

	/**
	 * 标签查阅-标签搜索-tag
	 */
	@GetMapping("/getTagList")
	public R<EventInfoVO> getTagList(String tag) {
		return eventService.getTagList(tag);
	}


	@GetMapping("/monthlyStats")
	public R monthlyStats() {
		List<Integer> stats = eventService.monthlyStats();
		return R.ok(stats);
	}

	@GetMapping("/getMonthlyStats")
	public R getMonthlyStats() {
		List<Map<String, Object>> stats = eventService.getMonthlyStats();
		
		// 初始化12个月的数据数组
		int[] eventCounts = new int[12];
		int[] projectCounts = new int[12];
		int[] resultCounts = new int[12];
		int[] prizeCounts = new int[12];

		// 填充数据
		for (Map<String, Object> stat : stats) {
			// 安全地获取月份和数量
			Object monthObj = stat.get("month");
			if (monthObj == null) {
				continue;
			}
			
			int month = ((Number)monthObj).intValue() - 1; // 转为0-11的索引
			
			// 确保月份在有效范围内
			if (month < 0 || month >= 12) {
				continue;
			}
			
			// 获取各类型的数量
			Number eventCount = (Number)stat.get("event_count");
			Number projectCount = (Number)stat.get("project_count");
			Number resultCount = (Number)stat.get("result_count");
			Number prizeCount = (Number)stat.get("prize_count");
			
			eventCounts[month] = eventCount != null ? eventCount.intValue() : 0;
			projectCounts[month] = projectCount != null ? projectCount.intValue() : 0;
			resultCounts[month] = resultCount != null ? resultCount.intValue() : 0;
			prizeCounts[month] = prizeCount != null ? prizeCount.intValue() : 0;
		}

		// 构建返回数据结构
		List<Map<String, Object>> series = new ArrayList<>();

		// 添加事件数量
		Map<String, Object> eventSeries = new HashMap<>();
		eventSeries.put("name", "新增事件数量");
		eventSeries.put("type", "bar");
		eventSeries.put("data", eventCounts);
		Map<String, Object> eventStyle = new HashMap<>();
		eventStyle.put("color", null);
		eventSeries.put("itemStyle", eventStyle);
		series.add(eventSeries);

		// 添加项目数量
		Map<String, Object> projectSeries = new HashMap<>();
		projectSeries.put("name", "新增项目数量");
		projectSeries.put("type", "bar");
		projectSeries.put("data", projectCounts);
		Map<String, Object> projectStyle = new HashMap<>();
		projectStyle.put("color", null);
		projectSeries.put("itemStyle", projectStyle);
		series.add(projectSeries);

		// 添加成果数量
		Map<String, Object> resultSeries = new HashMap<>();
		resultSeries.put("name", "新增成果数量");
		resultSeries.put("type", "bar");
		resultSeries.put("data", resultCounts);
		Map<String, Object> resultStyle = new HashMap<>();
		resultStyle.put("color", null);
		resultSeries.put("itemStyle", resultStyle);
		series.add(resultSeries);

		// 添加奖励数量
		Map<String, Object> prizeSeries = new HashMap<>();
		prizeSeries.put("name", "新增奖励数量");
		prizeSeries.put("type", "bar");
		prizeSeries.put("data", prizeCounts);
		Map<String, Object> prizeStyle = new HashMap<>();
		prizeStyle.put("color", null);
		prizeSeries.put("itemStyle", prizeStyle);
		series.add(prizeSeries);

		Map<String, Object> data = new HashMap<>();
		data.put("series", series);
		
		return R.ok(data);
	}
}
