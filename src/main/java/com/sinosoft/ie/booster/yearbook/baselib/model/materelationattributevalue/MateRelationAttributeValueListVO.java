package com.sinosoft.ie.booster.yearbook.baselib.model.materelationattributevalue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * MateRelationAttributeValue模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-01 14:35:03
 */
@Data
@ApiModel
public class MateRelationAttributeValueListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 属性id
     */
    @ApiModelProperty(value = "属性id")
    private String attributeId;
    
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String valName;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private String pid;
    
    /**
     * 本体id
     */
    @ApiModelProperty(value = "本体id")
    private String ontoId;
    
    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    private String relationId;
    
}