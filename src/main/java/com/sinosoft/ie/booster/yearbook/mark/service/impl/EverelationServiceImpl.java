package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.mark.entity.EverelationEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.EverelationMapper;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationPagination;
import com.sinosoft.ie.booster.yearbook.mark.service.EverelationService;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * everelation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-02 10:13:41
 */
@Service
public class EverelationServiceImpl extends ServiceImpl<EverelationMapper, EverelationEntity> implements EverelationService {


    @Override
    public List<EverelationEntity> getList(EverelationPagination everelationPagination){
        QueryWrapper<EverelationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(everelationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getId,everelationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(everelationPagination.getE1id()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getE1id,everelationPagination.getE1id()));
        }

        if(!"null".equals(String.valueOf(everelationPagination.getE2id()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getE2id,everelationPagination.getE2id()));
        }

        if(!"null".equals(String.valueOf(everelationPagination.getRelationName()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getRelationName,everelationPagination.getRelationName()));
        }

        //排序
        if(StrUtil.isEmpty(everelationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EverelationEntity::getId);
        }else{
            queryWrapper="asc".equals(everelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(everelationPagination.getSidx()):queryWrapper.orderByDesc(everelationPagination.getSidx());
        }
        Page<EverelationEntity> page=new Page<>(everelationPagination.getCurrentPage(), everelationPagination.getPageSize());
        IPage<EverelationEntity> userIPage=this.page(page,queryWrapper);
        return everelationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<EverelationEntity> getTypeList(EverelationPagination everelationPagination,String dataType){
        QueryWrapper<EverelationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(everelationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getId,everelationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(everelationPagination.getE1id()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getE1id,everelationPagination.getE1id()));
        }

        if(!"null".equals(String.valueOf(everelationPagination.getE2id()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getE2id,everelationPagination.getE2id()));
        }

        if(!"null".equals(String.valueOf(everelationPagination.getRelationName()))){
            queryWrapper.lambda().and(t->t.like(EverelationEntity::getRelationName,everelationPagination.getRelationName()));
        }

        //排序
        if(StrUtil.isEmpty(everelationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(EverelationEntity::getId);
        }else{
            queryWrapper="asc".equals(everelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(everelationPagination.getSidx()):queryWrapper.orderByDesc(everelationPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<EverelationEntity> page=new Page<>(everelationPagination.getCurrentPage(), everelationPagination.getPageSize());
            IPage<EverelationEntity> userIPage=this.page(page,queryWrapper);
            return everelationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public EverelationEntity getInfo(Long id){
        QueryWrapper<EverelationEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(EverelationEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(EverelationEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, EverelationEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(EverelationEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}