package com.sinosoft.ie.booster.yearbook.business.model.researchtag;

import lombok.Data;

@Data
public class ResearchTagConfig {
	
	//总数
	public Integer  total = 100;
	//单个分类
	public Integer  singleCate = 50;
	//平均数
	public Integer  average = 10;
	
	private static ResearchTagConfig researchTagConfig;
	
	public  static     ResearchTagConfig getTagConfig() {
		if(researchTagConfig==null) {
			researchTagConfig =  new  ResearchTagConfig();
		}
		return researchTagConfig;
	}
	
	
	public  void   setConfig(Integer total,Integer singleCate,Integer  average) {
		ResearchTagConfig config  =   new  ResearchTagConfig();
		config.setTotal(total);
		config.setSingleCate(singleCate);
		config.setAverage(average);
		this.researchTagConfig =  config;
	}

}
