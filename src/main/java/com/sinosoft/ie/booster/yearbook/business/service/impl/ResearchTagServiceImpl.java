package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.keyvalue.MultiKey;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.PageConfigEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.PageConfigMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagListVO;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagPagination;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.WordCloudEntity;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTagService;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 *
 * research_tag 版本： V1.0.0 作者： booster开发平台组 日期： 2022-09-15 10:49:22
 */
@Service
public class ResearchTagServiceImpl extends ServiceImpl<ResearchTagMapper, ResearchTagEntity>
		implements ResearchTagService {

	@Autowired
	private ResearchTagMapper researchTagMapper;
	@Autowired
	private EventService eventService;
	@Resource
	private PageConfigMapper pageConfigMapper;

	@Override
	public List<ResearchTagEntity> getList(ResearchTagPagination researchTagPagination) {
		QueryWrapper<ResearchTagEntity> queryWrapper = new QueryWrapper<>();
		// 关键字（账户、姓名、手机）
		if (!"null".equals(String.valueOf(researchTagPagination.getId()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTagEntity::getId, researchTagPagination.getId()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getName()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTagEntity::getName, researchTagPagination.getName()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getZonelevel()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTagEntity::getZonelevel, researchTagPagination.getZonelevel()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getLegendType()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTagEntity::getLegendType, researchTagPagination.getLegendType()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getOrganization()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTagEntity::getOrganization, researchTagPagination.getOrganization()));
		}

		// 排序
		if (StrUtil.isEmpty(researchTagPagination.getSidx())) {
			queryWrapper.lambda().orderByDesc(ResearchTagEntity::getId);
		} else {
			queryWrapper = "asc".equals(researchTagPagination.getSort().toLowerCase())
					? queryWrapper.orderByAsc(researchTagPagination.getSidx())
					: queryWrapper.orderByDesc(researchTagPagination.getSidx());
		}
		Page<ResearchTagEntity> page = new Page<>(researchTagPagination.getCurrentPage(),
				researchTagPagination.getPageSize());
		IPage<ResearchTagEntity> userIPage = this.page(page, queryWrapper);
		return researchTagPagination.setData(userIPage.getRecords(), userIPage.getTotal());
	}

	@Override
	public List<ResearchTagEntity> getTypeList(ResearchTagPagination researchTagPagination, String dataType) {
		QueryWrapper<ResearchTagEntity> queryWrapper = new QueryWrapper<>();
		// 关键字（账户、姓名、手机）
		if (!"null".equals(String.valueOf(researchTagPagination.getId()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTagEntity::getId, researchTagPagination.getId()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getName()))) {
			queryWrapper.lambda().and(t -> t.like(ResearchTagEntity::getName, researchTagPagination.getName()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getZonelevel()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTagEntity::getZonelevel, researchTagPagination.getZonelevel()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getLegendType()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTagEntity::getLegendType, researchTagPagination.getLegendType()));
		}

		if (!"null".equals(String.valueOf(researchTagPagination.getOrganization()))) {
			queryWrapper.lambda()
					.and(t -> t.like(ResearchTagEntity::getOrganization, researchTagPagination.getOrganization()));
		}

		// 排序
		if (StrUtil.isEmpty(researchTagPagination.getSidx())) {
			queryWrapper.lambda().orderByDesc(ResearchTagEntity::getId);
		} else {
			queryWrapper = "asc".equals(researchTagPagination.getSort().toLowerCase())
					? queryWrapper.orderByAsc(researchTagPagination.getSidx())
					: queryWrapper.orderByDesc(researchTagPagination.getSidx());
		}
		if ("0".equals(dataType)) {
			Page<ResearchTagEntity> page = new Page<>(researchTagPagination.getCurrentPage(),
					researchTagPagination.getPageSize());
			IPage<ResearchTagEntity> userIPage = this.page(page, queryWrapper);
			return researchTagPagination.setData(userIPage.getRecords(), userIPage.getTotal());
		} else {
			return this.list(queryWrapper);
		}
	}

	@Override
	public ResearchTagEntity getInfo(Long id) {
		QueryWrapper<ResearchTagEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ResearchTagEntity::getId, id);
		return this.getOne(queryWrapper);
	}

	@Override
	public void create(ResearchTagEntity entity) {
		this.save(entity);
	}

	@Override
	public boolean update(Long id, ResearchTagEntity entity) {
		entity.setId(id);
		return this.updateById(entity);
	}

	@Override
	public void delete(ResearchTagEntity entity) {
		if (entity != null) {
			this.removeById(entity.getId());
		}
	}
	// 子表方法

	@Override
	public Map<String, List<ResearchTagListVO>> showMapByLegendType(String legendTypeList) {
		Map<String, List<ResearchTagListVO>> map = new HashMap<>();
		List<String> stringList = new ArrayList<>();
		if (legendTypeList == null || legendTypeList.isEmpty()) {
			stringList = researchTagMapper.selectLegendTypeStr();
		} else {
			String[] legendTypeStrList = legendTypeList.split(",");
			stringList = Arrays.asList(legendTypeStrList);
		}

		int tagNum = 100;
		PageConfigEntity pageConfigEntity = pageConfigMapper.selectOne(new QueryWrapper<>());
		if (pageConfigEntity != null) {
			// 数量
			if (stringList.size() == 1) {
				tagNum = pageConfigEntity.getTagSinglecate();
			} else {
				tagNum = pageConfigEntity.getTagTotal();
			}
		}
		int aveTagNum = tagNum / stringList.size();

		List<String> finalStringList = stringList;
		if (finalStringList != null && finalStringList.size() > 0) {
			for (int i = 0; i < finalStringList.size(); i++) {
				QueryWrapper<ResearchTagEntity> queryWrapper = new QueryWrapper<>();
				int finalI = i;
				queryWrapper.lambda().and(t -> t.like(ResearchTagEntity::getLegendType, finalStringList.get(finalI)));
				queryWrapper.orderByDesc("zonelevel");
				queryWrapper.last("limit 0," + aveTagNum);
				List<ResearchTagEntity> researchTagList = this.list(queryWrapper);
				List<ResearchTagListVO> listVO = JsonUtil.getJsonToList(researchTagList, ResearchTagListVO.class);
				map.put(finalStringList.get(finalI), listVO);
			}
		}
		return map;
	}

	@Override
	public Map<String, Object> showMapByLegendTypeYear(String legendTypeList, String year, int currentPage) {
		Map<String, Object> totalmap = new HashMap<>();
		Map<String, Object> pagemap = new HashMap<>();
		pagemap.put("currentPage", currentPage);

		Map<String, Object> map = new HashMap<>();
		List<String> stringList = new ArrayList<>();
		if (legendTypeList == null || legendTypeList.isEmpty()) {
			stringList = researchTagMapper.selectLegendTypeStrYear(year);
		} else {
			String[] legendTypeStrList = legendTypeList.split(",");
			stringList = Arrays.asList(legendTypeStrList);
		}

		int tagNum = 100;
		int offset = 0;
		PageConfigEntity pageConfigEntity = pageConfigMapper.selectOne(new QueryWrapper<>());
		if (pageConfigEntity != null) {
			// 数量
			if (stringList.size() == 1) {
				tagNum = pageConfigEntity.getTagSinglecate();
				offset = (currentPage - 1) * tagNum;
			} else {
				tagNum = pageConfigEntity.getTagTotal();
			}
		}
		int aveTagNum = tagNum / stringList.size();
		pagemap.put("pageSize", aveTagNum);
		pagemap.put("total", 0);

		Page<ResearchTagEntity> page = new Page<>(currentPage, aveTagNum);

		List<String> finalStringList = stringList;
		if (finalStringList != null && finalStringList.size() > 0) {
			for (int i = 0; i < finalStringList.size(); i++) {
				IPage<ResearchTagEntity> researchTagList = new Page<>();
				if (year == null || year.isEmpty()) {
					// 没有年份，权重求和排序
//                    Map<String,Object> yearmap = new HashMap<>();
//                    yearmap.put("legendType",stringList.get(i));
//                    yearmap.put("offset",offset);
//                    yearmap.put("upset",aveTagNum);

					QueryWrapper<ResearchTagEntity> researchTagWrapper = new QueryWrapper<>();
					researchTagWrapper.select("id,name,legend_type,SUM(zonelevel) AS zonelevel");
					researchTagWrapper.eq("legend_type", stringList.get(i));
					researchTagWrapper.orderByDesc("zonelevel");
					researchTagWrapper.groupBy("name");
//                    researchTagWrapper.last("LIMIT "+offset+","+aveTagNum+"");

					researchTagList = this.page(page, researchTagWrapper);
//                    researchTagList = researchTagMapper.selectByLegendTypeNoYear(yearmap);
				} else {
					QueryWrapper<ResearchTagEntity> queryWrapper = new QueryWrapper<>();
					int finalI = i;
					queryWrapper.lambda().and(t -> t.eq(ResearchTagEntity::getLegendType, finalStringList.get(finalI)));
					queryWrapper.lambda().and(t -> t.eq(ResearchTagEntity::getYear, year));
					queryWrapper.orderByDesc("zonelevel");
//                    queryWrapper.last("limit "+offset+","+aveTagNum);
					researchTagList = this.page(page, queryWrapper);
				}
//                List<ResearchTagListVO> listVO = JsonUtil.getJsonToList(researchTagList, ResearchTagListVO.class);
				map.put(finalStringList.get(i), researchTagList.getRecords());
				pagemap.put("total", page.getTotal());
			}
		}
		totalmap.put("data", map);
		totalmap.put("pagination", pagemap);
		return totalmap;
	}

	@Override
	public R wordCloud(String legendTypeList, String year, String initial, String orgId) {
		List<String> stringList = new ArrayList<>();
		if (!StringUtils.isEmpty(legendTypeList)) {
			String[] legendTypeStrList = legendTypeList.split(",");
			stringList = Arrays.asList(legendTypeStrList);
		}
		PageConfigEntity pageConfigEntity = pageConfigMapper.selectOne(new QueryWrapper<>());
		QueryWrapper<ResearchTagEntity> researchTagWrapper = new QueryWrapper<>();
		researchTagWrapper.eq(!StringUtils.isEmpty(year), "year", year);
		researchTagWrapper.in(stringList.size() > 0, "legend_type", stringList);
		researchTagWrapper.eq((!StringUtils.isEmpty(initial)), "initial", initial);
		researchTagWrapper.eq((!StringUtils.isEmpty(orgId)), "org_id", orgId);
		researchTagWrapper.gt("zonelevel", pageConfigEntity.getTagAverage());
		Integer count = !StringUtils.isEmpty(initial) ? pageConfigEntity.getTagLetter() : pageConfigEntity.getTagTotal();
		if (count == null) {
			count = 10; // 设置一个默认值，例如10
		}
		researchTagWrapper.last(" limit " + count);
		List<ResearchTagEntity> list = this.list(researchTagWrapper);

		Map<String, List<ResearchTagEntity>> tagMap = list.stream()
				.collect(Collectors.groupingBy(ResearchTagEntity::getName));
		List<WordCloudEntity> wordList = new ArrayList<>();
		for (String str : tagMap.keySet()) {
			if (StringUtils.isEmpty(str)) {
				continue;
			}
			List<ResearchTagEntity> taglist = tagMap.get(str);
			WordCloudEntity word = new WordCloudEntity();
			word.setName(str);
			if (taglist.size() > 1) {
				int weight = 0;
				for (int i = 0; i < taglist.size(); i++) {
					weight += taglist.get(i).getZonelevel();
				}
				word.setWeight(weight);
			} else {
				word.setWeight(taglist.get(0).getZonelevel());
			}
			wordList.add(word);
		}
		return R.ok(wordList);
	}

	/**
	 * 异步计算tag 权重
	 * 
	 * @throws BadHanyuPinyinOutputFormatCombination
	 */
	@Async("taskExecutor")
	@Override
	public void computWeight() throws BadHanyuPinyinOutputFormatCombination {

		QueryWrapper<ResearchTagEntity> del_query = new QueryWrapper<ResearchTagEntity>();
		this.remove(del_query);

		QueryWrapper<EventEntity> query = new QueryWrapper<EventEntity>();
		query.select("start_time", "category", "tag", "weight", "orgid");
		List<EventEntity> list = eventService.list(query);
		list = list.stream()
				.filter(x -> (!StringUtils.isEmpty(x.getTag()) && !StringUtils.isEmpty(x.getCategory())
						&& !StringUtils.isEmpty(x.getStartTime()) && !StringUtils.isEmpty(x.getOrgid())))
				.collect(Collectors.toList());
		MultiKeyMap<Object, Set<String>> multiKey = new MultiKeyMap<>();
		for (int i = 0; i < list.size(); i++) {
			String year = list.get(i).getStartTime().substring(0, 4);
			String tags[] = list.get(i).getTag().split(";");
			List<String> alist = Arrays.asList(tags);
			String category = list.get(i).getCategory();
			String labs[] = category.split(";");
			String orgid = list.get(i).getOrgid();
			for (int j = 0; j < labs.length; j++) {
				Set<String> set = multiKey.get(year, labs[j],orgid);
				if (set == null) {
					set = new HashSet<String>();
				}
				set.addAll(alist);
				multiKey.put(year, labs[j], orgid, set);
			}
		}

		ArrayList<ResearchTagEntity> tagEntityList = new ArrayList<ResearchTagEntity>();
		for (MultiKey<? extends Object> iterable_element : multiKey.keySet()) {
			String year = (String) iterable_element.getKeys()[0];
			String lab = (String) iterable_element.getKeys()[1];
			String orgid = (String) iterable_element.getKeys()[2];
			Set<String> set = multiKey.get(year, lab, orgid);
			if (ObjectUtil.isEmpty(set)) {
				continue;
			}
			if (set.size() == 0) {
				continue;
			}
			for (String tag : set) {
				if (StringUtils.isEmpty(tag)) {
					continue;
				}
				List<EventEntity> tagList = list.stream().filter(x -> x.getStartTime().contains(year)
						&& x.getCategory().contains(lab) && x.getTag().contains(tag)).collect(Collectors.toList());
				Integer sum = tagList.stream().map(EventEntity::getWeight).mapToInt(Integer::intValue).sum();

				ResearchTagEntity ent = new ResearchTagEntity();
				ent.setName(tag);
				ent.setLegendType(lab);
				ent.setZonelevel(sum + tagList.size());
				ent.setYear(year);
				ent.setInitial(getInital(tag));
				ent.setOrgId(orgid);
				tagEntityList.add(ent);
			}
		}
		this.saveBatch(tagEntityList);

		// 先 查询全部 要素 和关键词 ，给关键词 分到十个要素中
		// 查询关键词所在事件，并且有某个要素， 全部事件的个数+ 全部事件的权重和
		//
	}

	public String getInital(String tag) throws BadHanyuPinyinOutputFormatCombination {
		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		char ch = tag.charAt(0);
		String[] helper = PinyinHelper.toHanyuPinyinStringArray(ch, format);
		if (helper.length == 0) {
			return null;
		}
		if (helper[0] == null) {
			return null;
		}
		String init = helper[0].substring(0, 1);
		return init;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<String> searchTag(String tag, Integer year) {
		QueryWrapper<ResearchTagEntity> query = new QueryWrapper<ResearchTagEntity>();
		query.likeRight("name", tag);
		query.eq(!ObjectUtil.isEmpty(year), "year", year);
		List<ResearchTagEntity> list = this.list(query);
		List<String> tagList = list.stream().map(ResearchTagEntity::getName).distinct().collect(Collectors.toList());
		Comparator comparator = Collator.getInstance(Locale.CHINESE);
		Collections.sort(tagList, comparator);
		QueryWrapper<ResearchTagEntity> query1 = new QueryWrapper<ResearchTagEntity>();
		query1.like("name", tag);
		query1.notLikeRight("name", tag);
		query1.eq(!ObjectUtil.isEmpty(year), "year", year);
		List<ResearchTagEntity> list1 = this.list(query1);
		List<String> tagList1 = list1.stream().map(ResearchTagEntity::getName).distinct().collect(Collectors.toList());
		Collections.sort(tagList1, comparator);
		tagList.addAll(tagList1);
		return tagList;
	}
}