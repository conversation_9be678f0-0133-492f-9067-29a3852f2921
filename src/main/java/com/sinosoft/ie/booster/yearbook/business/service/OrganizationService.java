package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.organization.OrganizationPagination;

import java.io.IOException;
import java.util.*;

/**
 *
 * organization
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-14 10:08:32
 */
public interface OrganizationService extends IService<OrganizationEntity> {

    List<OrganizationEntity> getList(OrganizationPagination organizationPagination);

    List<OrganizationEntity> getTypeList(OrganizationPagination organizationPagination,String dataType);



    OrganizationEntity getInfo(Long id);

    void delete(OrganizationEntity entity);

    void create(OrganizationEntity entity);

    boolean update( Long id, OrganizationEntity entity);
    
//  子表方法
	void previewOrgPic(String pic) throws IOException;

	List<OrganizationEntity> showOrgTree(OrganizationPagination organizationPagination);
	
	
	List<OrganizationEntity>   queryRotationPage();
	
	List<OrganizationEntity>   queryTagPage();
}
