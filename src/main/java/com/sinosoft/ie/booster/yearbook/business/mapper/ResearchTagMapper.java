package com.sinosoft.ie.booster.yearbook.business.mapper;

import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 *
 * research_tag
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-15 10:49:22
 */
@Mapper
public interface ResearchTagMapper extends BaseMapper<ResearchTagEntity> {
	List<String> selectLegendTypeStr();

	List<String> selectLegendTypeStrYear(String year);

	List<ResearchTagEntity> selectByLegendTypeNoYear(Map<String,Object> pagemap);
}
