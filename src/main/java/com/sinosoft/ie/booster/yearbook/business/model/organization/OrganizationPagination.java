package com.sinosoft.ie.booster.yearbook.business.model.organization;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Organization模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:08:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class OrganizationPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String name;

    /**
     * 父机构id
     */
    @ApiModelProperty(value = "父机构id")
    private Long pid;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;
}