package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.ie.booster.common.core.entity.BaseEntity;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:19:51
 */
@Data
@TableName("uploadfile")
@ApiModel(value = "")
public class UploadfileEntity  extends BaseEntity {

    /**
     * 
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件新名字
     */
    @ApiModelProperty(value = "文件新名字")
    private String newName;

    /**
     * 原文件名称
     */
    @ApiModelProperty(value = "原文件名称")
    private String oldName;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String path;

    /**
     * 文件类型：1pdf，2word
     */
    @ApiModelProperty(value = "文件类型：1pdf，2word")
    private Integer type;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;

	/**
	 * 父id
	 */
	@ApiModelProperty(value = "父id")
	private Long pid;

	/**
	 * 页码
	 */
	@ApiModelProperty(value = "页码")
	private Integer pageNum;
}
