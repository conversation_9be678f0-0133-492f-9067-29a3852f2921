package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EveTheme2Entity;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2CrForm;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2InfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2ListVO;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2Pagination;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme2.EveTheme2UpForm;
import com.sinosoft.ie.booster.yearbook.business.service.EveTheme2Service;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * eve_theme2
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-05-08 10:45:23
 */
@Slf4j
@RestController
@Api(tags = "eve_theme2")
@RequestMapping("/EveTheme2")
public class EveTheme2Controller {
	
    @Autowired
    private EveTheme2Service eveTheme2Service;


    /**
     * 列表
     *
     * @param eveTheme2Pagination
     * @return
     */
    @GetMapping
    public R<PageListVO<EveTheme2ListVO>> list(EveTheme2Pagination eveTheme2Pagination)throws IOException{
        List<EveTheme2Entity> list= eveTheme2Service.getList(eveTheme2Pagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(EveTheme2Entity entity:list){
            }
        List<EveTheme2ListVO> listVO=JsonUtil.getJsonToList(list,EveTheme2ListVO.class);
        PageListVO<EveTheme2ListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(eveTheme2Pagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param eveTheme2CrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid EveTheme2CrForm eveTheme2CrForm) throws DataException {
        EveTheme2Entity entity=JsonUtil.getJsonToBean(eveTheme2CrForm, EveTheme2Entity.class);
        eveTheme2Service.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<EveTheme2InfoVO> info(@PathVariable("id") Long id){
        EveTheme2Entity entity= eveTheme2Service.getInfo(id);
        EveTheme2InfoVO vo=JsonUtil.getJsonToBean(entity, EveTheme2InfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid EveTheme2UpForm eveTheme2UpForm) throws DataException {
        EveTheme2Entity entity= eveTheme2Service.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(eveTheme2UpForm, EveTheme2Entity.class);
            eveTheme2Service.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        EveTheme2Entity entity= eveTheme2Service.getInfo(id);
        if(entity!=null){
            eveTheme2Service.delete(entity);
        }
        return R.ok(null, "删除成功");
    }
    
    
    @GetMapping("/tree")
    public R tree()throws IOException{
        List<EveTheme2Entity> list= eveTheme2Service.list();
        return R.ok(list);
    }
    

}
