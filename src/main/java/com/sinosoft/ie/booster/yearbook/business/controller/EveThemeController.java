package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.yearbook.graph.entity.EveTagEntity;
import com.sinosoft.ie.booster.yearbook.graph.service.EveTagService;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphThemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemeCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemeInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemeListVO;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemePagination;
import com.sinosoft.ie.booster.yearbook.business.model.evetheme.EveThemeUpForm;
import com.sinosoft.ie.booster.yearbook.business.service.EveThemeService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * eve_theme
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-06 13:45:32
 */
@Slf4j
@RestController
@Api(tags = "eve_theme")
@RequestMapping("/EveTheme")
public class EveThemeController {
    @Autowired
    private EveThemeService eveThemeService;


    @Autowired
    private EveTagService eveTagService;

    @Autowired
    private GraphThemeService graphThemeService;

    /**
     * 列表
     *
     * @param eveThemePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<EveThemeListVO>> list(EveThemePagination eveThemePagination)throws IOException{
        List<EveThemeEntity> list= eveThemeService.getList(eveThemePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(EveThemeEntity entity:list){
            }
        List<EveThemeListVO> listVO=JsonUtil.getJsonToList(list,EveThemeListVO.class);
        PageListVO<EveThemeListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(eveThemePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param eveThemeCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid EveThemeCrForm eveThemeCrForm) throws DataException {
        EveThemeEntity entity=JsonUtil.getJsonToBean(eveThemeCrForm, EveThemeEntity.class);
        return eveThemeService.create(entity);
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<EveThemeInfoVO> info(@PathVariable("id") Long id){
        EveThemeEntity entity= eveThemeService.getInfo(id);
        EveThemeInfoVO vo=JsonUtil.getJsonToBean(entity, EveThemeInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid EveThemeUpForm eveThemeUpForm) throws DataException {
        EveThemeEntity entity= eveThemeService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(eveThemeUpForm, EveThemeEntity.class);
            eveThemeService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        EveThemeEntity entity= eveThemeService.getInfo(id);
        if(entity!=null){
            eveThemeService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 分组列表
     */
    @GetMapping("/getListGroup")
    public R<Map<Integer,List<EveThemeEntity>>> getListGroup(){
        return eveThemeService.getListGroup();
    }

    /*
    mate_attribute_value转eve_theme
     */
    @GetMapping("/themeMate2Eve")
    public R<String> themeMate2Eve(){
        eveThemeService.themeMate2Eve();
        graphThemeService.insertGraphTheme();
        return R.ok();
    }
    
    @GetMapping("/getThemeTree")
    public R<Map<Integer,List<EveThemeEntity>>> getThemeTree(){
        return eveThemeService.getThemeTree();
    }

    @GetMapping("/insertTheme")
    public R<String> insertTheme(){

        return eveThemeService.insertTheme();
    }

    /**
     * 删除byNAME
     */
    @DeleteMapping("/delByName")
    @Transactional
    public R<String> delByName(String theme){
        QueryWrapper<EveTagEntity> eveTagEntityQuery = new QueryWrapper<>();
        eveTagEntityQuery.eq("name",theme);
        eveTagService.remove(eveTagEntityQuery);
        return R.ok(null, "删除成功");
    }
}
