package com.sinosoft.ie.booster.yearbook.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.BadPdfFormatException;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;

/**
 * @version 1.0
 * @projectName：yearbooks
 * @className：PdfUtil.java
 * @description：pdf工具类
 * @author：peas @data：2022/9/14 17:42
 **/
public class PdfUtil {

	public static void pdfToSub(String filePath, String newFile, int from, int end) {
		Document document = null;
		PdfCopy copy = null;
		try {
			PdfReader reader = new PdfReader(filePath);
			// 总页数
			int n = reader.getNumberOfPages();
			if (end == 0) {
				end = n;
			}
			document = new Document(reader.getPageSize(1));
			copy = new PdfCopy(document, new FileOutputStream(newFile));
			document.open();
			for (int j = from; j <= end; j++) {
				document.newPage();
				PdfImportedPage page = copy.getImportedPage(reader, j);
				copy.addPage(page);
			}
			document.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void test(HttpServletResponse response, String path, Integer pageNum)
			throws IOException, DocumentException {
		PdfReader reader = null;
		PdfWriter writer = null;
		// 获取文件输入流
		FileInputStream is = null;
		try (
				// 获取响应输出流
				ServletOutputStream os = response.getOutputStream()) {
			// 截取文件名称
			String fileName = path.substring(path.lastIndexOf("\\"));
			// 文件下载
			response.setHeader("content-disposition",
					"attachment;fileName=" + new String(fileName.getBytes("gb2312"), StandardCharsets.ISO_8859_1));

			reader = new PdfReader("xx" + "\\" + path);

			// 总页数
			int pageCount = reader.getNumberOfPages();

			Document dd = new Document(reader.getPageSize(pageNum));
			writer = PdfWriter.getInstance(dd, os);
			dd.open();
			PdfContentByte cb = writer.getDirectContent();
			dd.newPage();
			cb.addTemplate(writer.getImportedPage(reader, pageNum), 0, 0);
			dd.close();
			writer.close();
		} catch (IOException | DocumentException e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				reader.close();
			}
			if (writer != null) {
				writer.close();
			}
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * <AUTHOR> 把PDF 按页(逐页、单页)拆分(一页一页拆分，一页为一个新PDF文件)
	 * @param path       源PDF路径
	 * @param fileName   源PDF文件名
	 * @param outputPath 拆分后输出的PDF路径
	 */
	public void splitPDFOneByOne(String path, String fileName, String outPath) {
		String sep = java.io.File.separator;
		PdfReader reader = null;
		int numberOfPages = 0;
		try {
			reader = new PdfReader(path + sep + fileName);
			numberOfPages = reader.getNumberOfPages();
			for (int i = 1; i <= numberOfPages; i++) {
				Document document = null;
				PdfCopy copy = null;
				try {
					document = new Document(reader.getPageSize(1));
					String savePath = outPath + sep + fileName.substring(0, fileName.lastIndexOf(".")) + "_" + i
							+ ".pdf";
					copy = new PdfCopy(document, new FileOutputStream(savePath));
					document.open();
					document.newPage();
					PdfImportedPage page = copy.getImportedPage(reader, i);
					copy.addPage(page);
				} finally {
					if (document != null)
						document.close();
					if (copy != null)
						copy.close();
				}
			}
		} catch (FileNotFoundException e) {

		} catch (IOException e) {

		} catch (BadPdfFormatException e) {

		} catch (DocumentException e) {

		} finally {
			if (reader != null)
				reader.close();
		}
	}

	/**
	 * <AUTHOR> 把PDF 按页(逐页、单页)拆分(一页一页拆分，一页为一个新PDF文件)
	 * @param path       源PDF路径
	 * @param outputPath 拆分后输出的PDF路径
	 * @param fileName   新的PDF文件名
	 */
	public static int splitPDFOneByOne1(String path, String outPath, String fileName) {
		String sep = java.io.File.separator;
		PdfReader reader = null;
		int numberOfPages = 0;
		try {
			reader = new PdfReader(path);
			numberOfPages = reader.getNumberOfPages();
			for (int i = 1; i <= numberOfPages; i++) {
				Document document = null;
				PdfCopy copy = null;
				try {
					document = new Document(reader.getPageSize(1));
					String savePath = outPath + sep + fileName + "_" + i + ".pdf";
					copy = new PdfCopy(document, new FileOutputStream(savePath));
					document.open();
					document.newPage();
					PdfImportedPage page = copy.getImportedPage(reader, i);
					copy.addPage(page);
				} finally {
					if (document != null)
						document.close();
					if (copy != null)
						copy.close();
				}
			}
		} catch (FileNotFoundException e) {

		} catch (IOException e) {

		} catch (BadPdfFormatException e) {

		} catch (DocumentException e) {

		} finally {
			if (reader != null)
				reader.close();
		}
		return numberOfPages;
	}

	public static void splitPDF(InputStream inputStream, OutputStream outputStream, int fromPage, int toPage) {
		Document document = new Document();
		try {
			PdfReader inputPDF = new PdfReader(inputStream);
			int totalPages = inputPDF.getNumberOfPages();

			if (fromPage > toPage) {
				fromPage = toPage;
			}
			if (toPage > totalPages) {
				toPage = totalPages;
			}

			PdfWriter writer = PdfWriter.getInstance(document, outputStream);

			document.open();
			PdfContentByte cb = writer.getDirectContent();
			PdfImportedPage page;

			while (fromPage <= toPage) {
				document.newPage();
				page = writer.getImportedPage(inputPDF, fromPage);
				cb.addTemplate(page, 0, 0);
				fromPage++;
			}
			outputStream.flush();
			document.close();
			outputStream.close();

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (document.isOpen())
				document.close();
			try {
				if (outputStream != null)
					outputStream.close();
			} catch (IOException ioe) {
				ioe.printStackTrace();
			}
		}
	}

	/**
	 * 获取pdf的第几页
	 * 
	 * @param in_path    文件输入路径
	 * @param out_path   输出路径
	 * @param pageNumber 第几页
	 */
	public static void splitPDFOneByOne(String in_path, String out_path, int pageNumber) {
		String sep = java.io.File.separator;
		PdfReader reader = null;
		try {
			reader = new PdfReader(in_path);
			Document document = null;
			PdfCopy copy = null;
			try {
				document = new Document(reader.getPageSize(1));
				copy = new PdfCopy(document, new FileOutputStream(out_path));
				document.open();
				document.newPage();
				PdfImportedPage page = copy.getImportedPage(reader, pageNumber);
				copy.addPage(page);
			} finally {
				if (document != null)
					document.close();
				if (copy != null)
					copy.close();
			}
		} catch (FileNotFoundException e) {

		} catch (IOException e) {

		} catch (BadPdfFormatException e) {

		} catch (DocumentException e) {

		} finally {
			if (reader != null)
				reader.close();
		}
	}

	/**
	 * 分页读取pdf文字
	 * @param fileName
	 * @param from
	 * @param end
	 * @return
	 */
	public static Map<Integer,String> readPdfByPage(String fileName) {
		Map<Integer,String> map = new HashMap<>();
		File file = new File(fileName);
		FileInputStream in = null;
		try {
			in = new FileInputStream(fileName);
			// 新建一个PDF解析器对象
			PdfReader reader = new PdfReader(fileName);
			reader.setAppendable(false);
			// 对PDF文件进行解析，获取PDF文档页码
			int size = reader.getNumberOfPages();
			for (int i = 1; i  <= size; i++) {
				// 一页页读取PDF文本
				String pageStr = PdfTextExtractor.getTextFromPage(reader, i);
				map.put(i, pageStr);
			}
			reader.close();
		} catch (Exception e) {
			System.out.println(e);
			e.printStackTrace();
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e1) {
				}
			}
		}
		return map;
	}

	public static void main(String[] args) {
		String path = "C:\\Users\\<USER>\\Desktop\\aaaa.pdf";
		Map<Integer,String> map = readPdfByPage(path);
		
		for(Integer num : map.keySet()) {
			System.out.println(num);
			System.out.println(map.get(num));
			
		}
		
	}
}
