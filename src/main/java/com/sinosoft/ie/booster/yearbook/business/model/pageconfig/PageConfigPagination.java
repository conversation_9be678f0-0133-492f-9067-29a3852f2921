package com.sinosoft.ie.booster.yearbook.business.model.pageconfig;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * PageConfig模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-22 17:46:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class PageConfigPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 标签显示总数
     */
    @ApiModelProperty(value = "标签显示总数")
    private Integer tagTotal;

    /**
     * 单个分类显示总数
     */
    @ApiModelProperty(value = "单个分类显示总数")
    private Integer tagSinglecate;

    /**
     * 多个分类平均数
     */
    @ApiModelProperty(value = "多个分类平均数")
    private Integer tagAverage;

    /**
     * 单位毫秒
     */
    @ApiModelProperty(value = "单位毫秒")
    private Integer rotationSpeed;
}