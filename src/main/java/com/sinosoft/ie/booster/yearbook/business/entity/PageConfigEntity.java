package com.sinosoft.ie.booster.yearbook.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-22 17:46:32
 */
@Data
@TableName("page_config")
@ApiModel(value = "")
public class PageConfigEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标签显示总数
     */
    @ApiModelProperty(value = "标签显示总数")
    private Integer tagTotal;

    /**
     * 单个分类显示总数
     */
    @ApiModelProperty(value = "单个分类显示总数")
    private Integer tagSinglecate;

    /**
     * 词云显示的标签的最小权重
     */
    @ApiModelProperty(value = "多个分类平均数")
    private Integer tagAverage;

    /**
     * 单位毫秒
     */
    @ApiModelProperty(value = "单位毫秒")
    private Integer rotationSpeed;
    
    
    //查询字母显示的标签数量
    private Integer  tagLetter;
    //查询机构显示的标签数量
    private Integer  tagOrg;
    //查询机构标签的轮播速度
    private Integer  tagOrgCarousel;
    //标签权重
    private Integer  tagWeight;
    //字号大小
//    @TableField("tag_font_size")
    @TableField(exist = false)
    private String tagFontSize;
    //词块个数
//    @TableField("tag_font_number")
    @TableField(exist = false)
    private String tagFontNumber;
    
}
