package com.sinosoft.ie.booster.yearbook.graph.service.impl;

import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeEventMapper;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphThemeEventService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * graph_theme_event
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 16:14:57
 */
@Service
public class GraphThemeEventServiceImpl extends ServiceImpl<GraphThemeEventMapper, GraphThemeEventEntity> implements GraphThemeEventService {


    @Override
    public List<GraphThemeEventEntity> getList(GraphThemeEventPagination graphThemeEventPagination){
        QueryWrapper<GraphThemeEventEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(graphThemeEventPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getId,graphThemeEventPagination.getId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getTheme()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getTheme,graphThemeEventPagination.getTheme()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getEventId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getEventId,graphThemeEventPagination.getEventId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getEntityId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getEntityId,graphThemeEventPagination.getEntityId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getEntityName()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getEntityName,graphThemeEventPagination.getEntityName()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getOntoId,graphThemeEventPagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getOntoName()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getOntoName,graphThemeEventPagination.getOntoName()));
        }

        //排序
        if(StrUtil.isEmpty(graphThemeEventPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(GraphThemeEventEntity::getId);
        }else{
            queryWrapper="asc".equals(graphThemeEventPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(graphThemeEventPagination.getSidx()):queryWrapper.orderByDesc(graphThemeEventPagination.getSidx());
        }
        Page<GraphThemeEventEntity> page=new Page<>(graphThemeEventPagination.getCurrentPage(), graphThemeEventPagination.getPageSize());
        IPage<GraphThemeEventEntity> userIPage=this.page(page,queryWrapper);
        return graphThemeEventPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<GraphThemeEventEntity> getTypeList(GraphThemeEventPagination graphThemeEventPagination,String dataType){
        QueryWrapper<GraphThemeEventEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(graphThemeEventPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getId,graphThemeEventPagination.getId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getTheme()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getTheme,graphThemeEventPagination.getTheme()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getEventId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getEventId,graphThemeEventPagination.getEventId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getEntityId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getEntityId,graphThemeEventPagination.getEntityId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getEntityName()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getEntityName,graphThemeEventPagination.getEntityName()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getOntoId,graphThemeEventPagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(graphThemeEventPagination.getOntoName()))){
            queryWrapper.lambda().and(t->t.like(GraphThemeEventEntity::getOntoName,graphThemeEventPagination.getOntoName()));
        }

        //排序
        if(StrUtil.isEmpty(graphThemeEventPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(GraphThemeEventEntity::getId);
        }else{
            queryWrapper="asc".equals(graphThemeEventPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(graphThemeEventPagination.getSidx()):queryWrapper.orderByDesc(graphThemeEventPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<GraphThemeEventEntity> page=new Page<>(graphThemeEventPagination.getCurrentPage(), graphThemeEventPagination.getPageSize());
            IPage<GraphThemeEventEntity> userIPage=this.page(page,queryWrapper);
            return graphThemeEventPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public GraphThemeEventEntity getInfo(Long id){
        QueryWrapper<GraphThemeEventEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(GraphThemeEventEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(GraphThemeEventEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, GraphThemeEventEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(GraphThemeEventEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}