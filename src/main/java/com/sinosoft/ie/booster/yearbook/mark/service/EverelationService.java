package com.sinosoft.ie.booster.yearbook.mark.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.mark.entity.EverelationEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.everelation.EverelationPagination;

import java.util.*;

/**
 *
 * everelation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-02 10:13:41
 */
public interface EverelationService extends IService<EverelationEntity> {

    List<EverelationEntity> getList(EverelationPagination everelationPagination);

    List<EverelationEntity> getTypeList(EverelationPagination everelationPagination,String dataType);



    EverelationEntity getInfo(Long id);

    void delete(EverelationEntity entity);

    void create(EverelationEntity entity);

    boolean update( Long id, EverelationEntity entity);
    
//  子表方法
}
