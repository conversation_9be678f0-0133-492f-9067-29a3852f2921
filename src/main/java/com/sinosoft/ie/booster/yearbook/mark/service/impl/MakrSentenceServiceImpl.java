package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkEntityMapper;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkSentenceMapper;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityRelation;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentenceListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;

import com.sinosoft.ie.booster.yearbook.util.FileUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 *
 * makr_sentence
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-09 14:49:54
 */
@Service
public class MakrSentenceServiceImpl extends ServiceImpl<MarkSentenceMapper, MarkSentenceEntity> implements MakrSentenceService {

    @Autowired
    private ProjectConfig projectConfig;

    @Autowired
    private MarkEntityMapper markEntityMapper;

    @Resource
    private MarkSentenceMapper markSentenceMapper;

    @Override
    public List<MarkSentenceEntity> getList(MarkSentencePagination makrSentencePagination){
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(makrSentencePagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getId,makrSentencePagination.getId()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getFid()))){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getFid,makrSentencePagination.getFid()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getFsort()))){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getFsort,makrSentencePagination.getFsort()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getSentence()))){
            queryWrapper.lambda().and(!StringUtils.isEmpty(makrSentencePagination.getSentence()),  t->t.like(MarkSentenceEntity::getSentence,makrSentencePagination.getSentence()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getSentenceExc()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getSentenceExc,makrSentencePagination.getSentenceExc()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getExecuteData()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getExecuteData,makrSentencePagination.getExecuteData()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getCreateTime()))){
            queryWrapper.lambda().eq(MarkSentenceEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(makrSentencePagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getUpdateTime()))){
            queryWrapper.lambda().eq(MarkSentenceEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(makrSentencePagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getStatus()))){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getStatus,makrSentencePagination.getStatus()));
        }
        if(!"null".equals(String.valueOf(makrSentencePagination.getCorpusId()))){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusId,makrSentencePagination.getCorpusId()));
        }
        if(!"null".equals(String.valueOf(makrSentencePagination.getCorpusVersion()))){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusVersion,makrSentencePagination.getCorpusVersion()));
        }
        //排序
        if(StrUtil.isEmpty(makrSentencePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkSentenceEntity::getId);
        }else{
            queryWrapper="asc".equals(makrSentencePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(makrSentencePagination.getSidx()):queryWrapper.orderByDesc(makrSentencePagination.getSidx());
        }
        Page<MarkSentenceEntity> page=new Page<>(makrSentencePagination.getCurrentPage(), makrSentencePagination.getPageSize());
        IPage<MarkSentenceEntity> userIPage=this.page(page,queryWrapper);
        return makrSentencePagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MarkSentenceEntity> getTypeList(MarkSentencePagination makrSentencePagination,String dataType){
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(makrSentencePagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getId,makrSentencePagination.getId()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getFid()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getFid,makrSentencePagination.getFid()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getFsort()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getFsort,makrSentencePagination.getFsort()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getSentence()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getSentence,makrSentencePagination.getSentence()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getSentenceExc()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getSentenceExc,makrSentencePagination.getSentenceExc()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getExecuteData()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getExecuteData,makrSentencePagination.getExecuteData()));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getCreateTime()))){
            queryWrapper.lambda().eq(MarkSentenceEntity::getCreateTime, DateUtil.stringToDates(DateUtil.daFormat(makrSentencePagination.getCreateTime())));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getUpdateTime()))){
            queryWrapper.lambda().eq(MarkSentenceEntity::getUpdateTime, DateUtil.stringToDates(DateUtil.daFormat(makrSentencePagination.getUpdateTime())));
        }

        if(!"null".equals(String.valueOf(makrSentencePagination.getStatus()))){
            queryWrapper.lambda().and(t->t.like(MarkSentenceEntity::getStatus,makrSentencePagination.getStatus()));
        }

        //排序
        if(StrUtil.isEmpty(makrSentencePagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkSentenceEntity::getId);
        }else{
            queryWrapper="asc".equals(makrSentencePagination.getSort().toLowerCase())?queryWrapper.orderByAsc(makrSentencePagination.getSidx()):queryWrapper.orderByDesc(makrSentencePagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MarkSentenceEntity> page=new Page<>(makrSentencePagination.getCurrentPage(), makrSentencePagination.getPageSize());
            IPage<MarkSentenceEntity> userIPage=this.page(page,queryWrapper);
            return makrSentencePagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MarkSentenceEntity getInfo(Long id){
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MarkSentenceEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MarkSentenceEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MarkSentenceEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MarkSentenceEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
    
    @Override
    public JSONArray exportCorpus(Integer status, Integer startNum, Integer endNum, Long corpusId, Long corpusVersion){
        JSONArray jsonArray = new JSONArray();
        //查询句子
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        if (status != null){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getStatus,status));
        }
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusVersion,corpusVersion));
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusId,corpusId));
        queryWrapper.last("limit "+(startNum-1)+","+ (endNum-startNum+1));
        List<MarkSentenceEntity> sentenceList = this.list(queryWrapper);
        if (sentenceList == null || sentenceList.size() == 0){
            return jsonArray;
        }
        for (MarkSentenceEntity sentence:sentenceList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("createAt",sentence.getCreateTime()== null?"":sentence.getCreateTime());
            jsonObject.set("id",sentence.getId());
            String sentenceStatus = "";
            if (sentence.getStatus() == 1){
                sentenceStatus = "就绪";
            }
            if (sentence.getStatus() == 2){
                sentenceStatus = "处理中";
            }
            if (sentence.getStatus() == 3){
                sentenceStatus = "智能标注已完成";
            }
            if (sentence.getStatus() == 4){
                sentenceStatus = "已完成";
            }
            if (sentence.getStatus() == 5){
                sentenceStatus = "报错";
            }
            jsonObject.set("status",sentenceStatus);
            jsonObject.set("text",sentence.getSentenceExc());
            jsonObject.set("title",sentence.getSentence());
            jsonObject.set("updateAt",sentence.getUpdateTime()== null?"":sentence.getUpdateTime());
            //查询实体
            QueryWrapper<MarkEntityEntity> entityQueryWrapper=new QueryWrapper<>();
            entityQueryWrapper.lambda().and(t->t.eq(MarkEntityEntity::getSentenceid,sentence.getId()));
            List<MarkEntityEntity> markEntityEntities = markEntityMapper.selectList(entityQueryWrapper);
            JSONArray labelArray = new JSONArray();
            if (markEntityEntities == null || markEntityEntities.size() == 0){
                jsonObject.set("label",labelArray);
                jsonArray.add(jsonObject);
                continue;
            }

            for (MarkEntityEntity entity:markEntityEntities) {
                JSONObject entityObject = new JSONObject();
                entityObject.set("tagName",entity.getTagname());
                entityObject.set("text",entity.getText());
                entityObject.set("ontoName",entity.getOntoname());
                entityObject.set("startIndex",entity.getStartindex());
                entityObject.set("endIndex",entity.getEndindex());
                String labType = "";
                if (entity.getLabType() == 1){
                    labType = "实体";
                }
                if (entity.getLabType() == 2){
                    labType = "属性";
                }
                if (entity.getLabType() == 3){
                    labType = "属性值";
                }
                entityObject.set("labType",labType);
                labelArray.add(entityObject);
            }
            jsonObject.set("label",labelArray);
            entityQueryWrapper.select("ontoName,count(ontoName) startindex");
            entityQueryWrapper.last("GROUP BY ontoName");
            List<MarkEntityEntity> ontoNameList = markEntityMapper.selectList(entityQueryWrapper);
            JSONObject ontoNameObject = new JSONObject();
            for (MarkEntityEntity ontoName:ontoNameList) {
                ontoNameObject.set(ontoName.getOntoname(),ontoName.getStartindex());
            }
            jsonObject.set("tagCount",ontoNameObject);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    @Override
    public String makeJson(JSONArray jsonArray) throws IOException {
        String jsonStr = "";
        if (jsonArray != null){
            jsonStr = String.valueOf(jsonArray);
        }
        String uploaderPath = projectConfig.getDownPath();
        String fileName = IdUtil.simpleUUID() + ".json";
        FileUtil.mkdir(new File(uploaderPath));
        FileUtil.writeFile(uploaderPath+File.separator+fileName, jsonStr);
        return fileName;
    }

    @Override
    public void deleteids(String ids){
        this.removeBatchByIds(new ArrayList<>(Arrays.asList(ids.split(","))));
    }

    @Override
    public JSONArray getCorpusStatusChart(Long corpusId, Integer corpusVersion){
        JSONArray jsonArray = new JSONArray();
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.select("status,count(status) fsort");
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusVersion,corpusVersion));
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusId,corpusId));
        queryWrapper.last("GROUP BY status");
        List<MarkSentenceEntity> sentenceList = this.list(queryWrapper);
        if (sentenceList == null || sentenceList.size() == 0){
            return jsonArray;
        }
        for (int i = 1; i < 6; i++) {
            JSONObject jsonObject = new JSONObject();
            int value = 0;
            for (MarkSentenceEntity sentence:sentenceList) {
                if (sentence.getStatus() == i){
                    value = sentence.getFsort();
                    break;
                }
            }
            String sentenceStatus = "";
            if (i == 1){
                sentenceStatus = "就绪";
            }
            if (i == 2){
                sentenceStatus = "处理中";
            }
            if (i == 3){
                sentenceStatus = "智能标注已完成";
            }
            if (i == 4){
                sentenceStatus = "已完成";
            }
            if (i == 5){
                sentenceStatus = "报错";
            }

            jsonObject.set("name",sentenceStatus);
            jsonObject.set("value",value);
            jsonArray.add(jsonObject);
        }

        return jsonArray;
    }

    @Override
    public JSONArray getCorpusTagnameChart(Long corpusId, Integer tag, Integer corpusVersion, Integer status){
        JSONArray jsonArray = new JSONArray();
        //查找句子
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.select("id");
        if (status != null){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getStatus,status));
        }
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusVersion,corpusVersion));
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusId,corpusId));
        List<MarkSentenceEntity> sentenceList = this.list(queryWrapper);
        if (sentenceList == null || sentenceList.size() == 0){
            return jsonArray;
        }
        List<Long> ids = new ArrayList<>();
        for (MarkSentenceEntity sentence:sentenceList) {
            ids.add(sentence.getId());
        }
        //查找对应实体-分组查询
        QueryWrapper<MarkEntityEntity> entityWrapper=new QueryWrapper<>();
        entityWrapper.select("ontoName,count(ontoName) labType");
        entityWrapper.lambda().and(t->t.in(MarkEntityEntity::getSentenceid,ids));
        String tagName = "";
        int valType = 0;
        if (tag == 1){
            //实体
            tagName = "实体";
        }
        if (tag == 2){
            //关系-对象属性
            tagName = "属性";
            valType = 2;
        }
        if (tag == 3){
            //属性-实体属性
            tagName = "属性";
            valType = 1;
        }
        String finalTagName = tagName;
        entityWrapper.lambda().and(t->t.eq(MarkEntityEntity::getTagname, finalTagName));
        //实体不查valType
        if (valType > 0){
            Integer finalValType = valType;
            entityWrapper.lambda().and(t->t.eq(MarkEntityEntity::getValType, finalValType));
        }
        entityWrapper.last("GROUP BY ontoName");
        List<MarkEntityEntity> markEntityEntities = markEntityMapper.selectList(entityWrapper);
        if (markEntityEntities == null || markEntityEntities.size() == 0){
            return jsonArray;
        }
        for (MarkEntityEntity markEntity:markEntityEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("name",markEntity.getOntoname());
            jsonObject.set("value",markEntity.getLabType());
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    @Override
    public R<PageListVO<MarkEntityEntity>> getEntityList(MarkSentencePagination sentencePagination){
//        //查询句子
//        QueryWrapper<MarkSentenceEntity> markSentenceWrapper = new QueryWrapper<>();
//        markSentenceWrapper.eq("corpus_id",corpusId);
//        markSentenceWrapper.eq("corpus_version",corpusVersion);
//        List<MarkSentenceEntity> markSentenceList = markSentenceMapper.selectList(markSentenceWrapper);
//        List<Long> ids = new ArrayList<>();
//        for (MarkSentenceEntity sentence:markSentenceList) {
//            ids.add(sentence.getId());
//        }
//        //查询实体
//        QueryWrapper<MarkEntityEntity> markEntityWrapper = new QueryWrapper<>();
//        markEntityWrapper.in("sentenceId",ids);
//        MarkSentenceEntity markSentenceEntity = new MarkSentenceEntity();
//        markSentenceEntity.setCorpusVersion(corpusVersion);
//        markSentenceEntity.setCorpusId(corpusId);
        sentencePagination.setCurrentPage((sentencePagination.getCurrentPage()-1)*sentencePagination.getPageSize());

        List<MarkEntityEntity> entityList = markEntityMapper.getEntityList(sentencePagination);
        int count = markEntityMapper.getEntityCount(sentencePagination);
        PageListVO<MarkEntityEntity> vo=new PageListVO<>();
        vo.setList(entityList);
        PaginationVO page = new PaginationVO();
        page.setPageSize(sentencePagination.getPageSize());
        page.setCurrentPage(sentencePagination.getCurrentPage());
        page.setTotal(count);
        vo.setPagination(page);
        return R.ok(vo);
    }

    @Override
    public R<PageListVO<MarkEntityRelation>> getEntityRelationList(MarkSentencePagination sentencePagination){
        sentencePagination.setCurrentPage((sentencePagination.getCurrentPage()-1)*sentencePagination.getPageSize());
        List<MarkEntityRelation> entityRelationList = markEntityMapper.getEntityRelationList(sentencePagination);
        int count = markEntityMapper.getEntityRelationCount(sentencePagination);
        PageListVO<MarkEntityRelation> vo=new PageListVO<>();
        vo.setList(entityRelationList);
        PaginationVO page = new PaginationVO();
        page.setPageSize(sentencePagination.getPageSize());
        page.setCurrentPage(sentencePagination.getCurrentPage());
        page.setTotal(count);
        vo.setPagination(page);
        return R.ok(vo);
    }

    @Override
    public R<PageListVO<MarkEntityEntity>> getEntityAttrList(MarkSentencePagination sentencePagination){
        sentencePagination.setCurrentPage((sentencePagination.getCurrentPage()-1)*sentencePagination.getPageSize());
        List<MarkEntityEntity> entityAttrList = markEntityMapper.getEntityAttrList(sentencePagination);
        int count = markEntityMapper.getEntityRelationCount(sentencePagination);
        PageListVO<MarkEntityEntity> vo=new PageListVO<>();
        vo.setList(entityAttrList);
        PaginationVO page = new PaginationVO();
        page.setPageSize(sentencePagination.getPageSize());
        page.setCurrentPage(sentencePagination.getCurrentPage());
        page.setTotal(count);
        vo.setPagination(page);
        return R.ok(vo);
    }
}