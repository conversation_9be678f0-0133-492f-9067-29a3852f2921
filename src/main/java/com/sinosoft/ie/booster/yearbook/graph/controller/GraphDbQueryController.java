package com.sinosoft.ie.booster.yearbook.graph.controller;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.graph.service.GraphDbQueryService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
//@RestController
@Api(tags = "GraphDb")
@RequestMapping("/GraphDb")
public class GraphDbQueryController {

    @Resource
    private GraphDbQueryService graphDbQueryService;

    @GetMapping("/queryRelationForNode")
    public R<List<String>> queryRelationForNode() {
        List<String> nodeRelationModelList = graphDbQueryService.queryAllNodeLabel();
        return R<PERSON>ok(nodeRelationModelList);
    }

}
