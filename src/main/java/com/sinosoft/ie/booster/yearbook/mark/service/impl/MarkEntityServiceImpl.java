package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkEntityMapper;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkEntityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * mark_entity
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-08 16:58:44
 */
@Service
public class MarkEntityServiceImpl extends ServiceImpl<MarkEntityMapper, MarkEntityEntity> implements MarkEntityService {


    @Override
    public List<MarkEntityEntity> getList(MarkEntityPagination markEntityPagination){
        QueryWrapper<MarkEntityEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markEntityPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkEntityEntity::getId,markEntityPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markEntityPagination.getTagname()))){
            queryWrapper.lambda().and(t->t.like(MarkEntityEntity::getTagname,markEntityPagination.getTagname()));
        }

        if(!"null".equals(String.valueOf(markEntityPagination.getSentenceid()))){
            queryWrapper.lambda().and(t->t.like(MarkEntityEntity::getSentenceid,markEntityPagination.getSentenceid()));
        }

        //排序
        if(StrUtil.isEmpty(markEntityPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkEntityEntity::getId);
        }else{
            queryWrapper="asc".equals(markEntityPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markEntityPagination.getSidx()):queryWrapper.orderByDesc(markEntityPagination.getSidx());
        }
        Page<MarkEntityEntity> page=new Page<>(markEntityPagination.getCurrentPage(), markEntityPagination.getPageSize());
        IPage<MarkEntityEntity> userIPage=this.page(page,queryWrapper);
        return markEntityPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MarkEntityEntity> getTypeList(MarkEntityPagination markEntityPagination,String dataType){
        QueryWrapper<MarkEntityEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(markEntityPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MarkEntityEntity::getId,markEntityPagination.getId()));
        }

        if(!"null".equals(String.valueOf(markEntityPagination.getTagname()))){
            queryWrapper.lambda().and(t->t.like(MarkEntityEntity::getTagname,markEntityPagination.getTagname()));
        }


        if(!"null".equals(String.valueOf(markEntityPagination.getSentenceid()))){
            queryWrapper.lambda().and(t->t.like(MarkEntityEntity::getSentenceid,markEntityPagination.getSentenceid()));
        }

        //排序
        if(StrUtil.isEmpty(markEntityPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MarkEntityEntity::getId);
        }else{
            queryWrapper="asc".equals(markEntityPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(markEntityPagination.getSidx()):queryWrapper.orderByDesc(markEntityPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MarkEntityEntity> page=new Page<>(markEntityPagination.getCurrentPage(), markEntityPagination.getPageSize());
            IPage<MarkEntityEntity> userIPage=this.page(page,queryWrapper);
            return markEntityPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MarkEntityEntity getInfo(Long id){
        QueryWrapper<MarkEntityEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MarkEntityEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MarkEntityEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MarkEntityEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MarkEntityEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}