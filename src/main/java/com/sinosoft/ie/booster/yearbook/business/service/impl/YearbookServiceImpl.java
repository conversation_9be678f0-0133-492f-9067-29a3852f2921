package com.sinosoft.ie.booster.yearbook.business.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.DownUtil;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.YearbookMapper;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookListVO;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookPagination;
import com.sinosoft.ie.booster.yearbook.business.service.EveThemeService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.FileDirectoryService;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTagService;
import com.sinosoft.ie.booster.yearbook.business.service.YearbookService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;
import com.sinosoft.ie.booster.yearbook.util.PdfUtil;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 *
 * yearbook
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-16 13:39:57
 */
@Service
public class YearbookServiceImpl extends ServiceImpl<YearbookMapper, YearbookEntity> implements YearbookService {

	@Autowired
	private ProjectConfig projectConfig;
	@Autowired
	private HttpServletResponse response;
	@Autowired
	private FileDirectoryService fileDirectoryService;
	@Autowired
	private EventService eventService;
	@Autowired
	private ResearchTagService researchTagService;
	@Autowired
	private EveThemeService eveThemeService;
	@Autowired
	private YearbookMapper yearbookMapper;
    @Override
    public List<YearbookEntity> getList(YearbookPagination yearbookPagination){
        QueryWrapper<YearbookEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(yearbookPagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(YearbookEntity::getId,yearbookPagination.getId()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getOldName()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getOldName,yearbookPagination.getOldName()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getType()))){
            queryWrapper.lambda().and(t->t.eq(YearbookEntity::getType,yearbookPagination.getType()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getPid()))){
            queryWrapper.lambda().and(t->t.eq(YearbookEntity::getPid,yearbookPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getPageNum()))){
            queryWrapper.lambda().and(t->t.eq(YearbookEntity::getPageNum,yearbookPagination.getPageNum()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.eq(YearbookEntity::getDelFlag,yearbookPagination.getDelFlag()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getBookYear()))){
            queryWrapper.lambda().and(t->t.eq(YearbookEntity::getBookYear,yearbookPagination.getBookYear()));
        }
        queryWrapper.lambda().orderByDesc(YearbookEntity::getBookYear);
//        //排序
//        if(StrUtil.isEmpty(yearbookPagination.getSidx())){
//            queryWrapper.lambda().orderByDesc(YearbookEntity::getId);
//        }else{
//            queryWrapper="asc".equals(yearbookPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(yearbookPagination.getSidx()):queryWrapper.orderByDesc(yearbookPagination.getSidx());
//        }
        Page<YearbookEntity> page=new Page<>(yearbookPagination.getCurrentPage(), yearbookPagination.getPageSize());
        IPage<YearbookEntity> userIPage=this.page(page,queryWrapper);
        return yearbookPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<YearbookEntity> getTypeList(YearbookPagination yearbookPagination,String dataType){
        QueryWrapper<YearbookEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(yearbookPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getId,yearbookPagination.getId()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getOldName()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getOldName,yearbookPagination.getOldName()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getType()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getType,yearbookPagination.getType()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getPid,yearbookPagination.getPid()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getPageNum()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getPageNum,yearbookPagination.getPageNum()));
        }

        if(!"null".equals(String.valueOf(yearbookPagination.getDelFlag()))){
            queryWrapper.lambda().and(t->t.like(YearbookEntity::getDelFlag,yearbookPagination.getDelFlag()));
        }

        //排序
        if(StrUtil.isEmpty(yearbookPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(YearbookEntity::getId);
        }else{
            queryWrapper="asc".equals(yearbookPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(yearbookPagination.getSidx()):queryWrapper.orderByDesc(yearbookPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<YearbookEntity> page=new Page<>(yearbookPagination.getCurrentPage(), yearbookPagination.getPageSize());
            IPage<YearbookEntity> userIPage=this.page(page,queryWrapper);
            return yearbookPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public YearbookEntity getInfo(Long id){
        QueryWrapper<YearbookEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(YearbookEntity::getId,id);
        return this.getOne(queryWrapper);
    }

//    @Override
//    public void create(YearbookEntity entity) throws Exception {
//		String pdfPath = projectConfig.getUploadPath() +  entity.getPath();
//		String path = projectConfig.getUploadPath() +  ProjectConstant.imag_path;
//		String fileName = IdUtil.simpleUUID();
//		int pageCount = Pdf2PicUtil.pdf2Pic(pdfPath, path ,fileName);
//		entity.setBookName(entity.getOldName());
//		entity.setPageNum(pageCount);
//		entity.setPid(0L);
//		this.save(entity);
//		if (pageCount > 0){
//			for (int i = 0; i < pageCount; i++) {
//				String imgName = fileName + i + ".png";
//				YearbookEntity picEntity = new YearbookEntity();
//				picEntity.setPid(entity.getId());
//				picEntity.setPageNum(i+1);
//				picEntity.setPath(ProjectConstant.imag_path + imgName);
//				picEntity.setOldName(entity.getOldName());
//				picEntity.setNewName(imgName);
//				picEntity.setType(1);
//				this.save(picEntity);
//			}
//		}
//    }

    @Override
    public void create(YearbookEntity entity) throws Exception {
		String pdfPath = projectConfig.getUploadPath() +  entity.getPath();
		String path = projectConfig.getUploadPath() +  ProjectConstant.pdf_prefix;
		String fileName = entity.getNewName().replace(".pdf","");
		int pageCount = PdfUtil.splitPDFOneByOne1(pdfPath,path,fileName);
		entity.setBookName(entity.getOldName().replace(".pdf",""));
		entity.setPageNum(pageCount);
		entity.setPid(0L);   
		//fileName + "_" + i + ".pdf"
		this.save(entity);
		if (pageCount > 0){
			for (int i = 1; i <= pageCount; i++) {
				YearbookEntity picEntity = new YearbookEntity();
				picEntity.setPid(entity.getId());
				picEntity.setPageNum(i);
				picEntity.setPath(ProjectConstant.pdf_prefix + fileName + "_" + i + ".pdf");
				picEntity.setOldName(entity.getOldName());
				picEntity.setNewName(fileName + "_" + i + ".pdf");
				picEntity.setType(1);
				this.save(picEntity);
			}
		}
		//将当前的年鉴名称作为目录
		FileDirectoryEntity fdentity = new FileDirectoryEntity();
		fdentity.setName(entity.getBookName());
		fdentity.setPid(0L);
		fdentity.setPageNum(0);
		fdentity.setFileId(entity.getId());
		fileDirectoryService.create(fdentity);
    }
    
    
    @Override
    public boolean update(Long id, YearbookEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }



	@Override
    public void delete(YearbookEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public void previewPdf(String name) throws IOException {
		File file = new File(projectConfig.getUploadPath() + ProjectConstant.pdf_prefix + name);
		System.out.println(projectConfig.getUploadPath() + ProjectConstant.pdf_prefix + name);
		if (!file.exists()) {
			response.sendError(404, "File not found!");
			return;
		}
		DownUtil.dowloadFile(file);
	}
	@Override
	public void  getCurrentPageTagAndTheme(YearbookListVO bookVO) {
		
		List<EveThemeEntity> themeList =  eveThemeService.list();
		List<String> themeStrList = Optional.ofNullable(themeList)
				.orElseGet(Collections::emptyList)
				.stream()
				.filter(Objects::nonNull)
				.filter(x -> x.getDir() == 1)
				.map(EveThemeEntity::getName)
				.collect(Collectors.toList());
		StringBuffer  sb = new StringBuffer();
		StringBuffer  sb_theme = new StringBuffer();
		System.out.println(bookVO.getPid());
		YearbookEntity yearbook = this.getById(bookVO.getPid());
		QueryWrapper<EventEntity>  query = new QueryWrapper<>();
//		query.like("start_time", yearbook.getBookYear());
		query.eq("page_num", bookVO.getPageNum());
		List<EventEntity> list = eventService.list(query);
		Set<String> tagSet =  new HashSet<>();
		Set<String> themeSet =  new HashSet<>();
		for (int i = 0; i < list.size(); i++) {
			String tag = list.get(i).getTag();
			if(!StringUtils.isEmpty(tag)) {
				String[] tags =  tag.split(";");
				for (int j = 0; j < tags.length; j++) {
					tagSet.add(tags[j]);
				}
			}
			String theme =  list.get(i).getTheme();
			if(!StringUtils.isEmpty( theme)) {
				if(themeStrList.contains(theme)) {
					themeSet.add(theme);	
				}
//				List<EveThemeEntity> tlist = themeList.stream().filter(x->x.getName().equals(theme)).collect(Collectors.toList());
//				if(!CollectionUtils.isEmpty(tlist)) {
//					themeSet.add(theme);	
//				}
			}
		}
		if(tagSet.size()!=0) {
			for (String string : tagSet) {
				sb.append(string).append(";");
			}
			bookVO.setTag2(sb.toString());
		}
		if(themeSet.size()!=0) {
			for (String string : themeSet) {
				sb_theme.append(string).append(";");
			}
			bookVO.setTheme(sb_theme.toString());
		}

	}

	@Override
	public YearbookEntity getInfoFid(Long fid) {
		return yearbookMapper.getInfoFid(fid);
	}
}