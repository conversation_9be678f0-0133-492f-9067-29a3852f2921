package com.sinosoft.ie.booster.yearbook.baselib.service;

import java.util.List;
import java.util.Map;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libconcept.LibConceptListVO;

public interface LibEntityServicve {
	
	
	/**
	 * 分页查询实体
	 * @param tion
	 * @param id
	 * @return
	 */
	public List<Object>  queryEntityList(Pagination tion ,Long id);
	/**
	 * 根据本体id查询本体的属性
	 * @param ontoId
	 * @return
	 */
	
	List<LibConceptListVO> getPropertsList(Long ontoId);
	
	/**
	 * 根据实体id查询实体关系
	 */
	Map<String ,Object>queryEntityRelation(Long ontoId,Long entId);
	
}
