package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.*;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibEntityListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibEntityPaginationExportModel;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationOut;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaEntityRelationService;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.job.EsDateHandle;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibEntityPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * lib_entity
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-16 15:45:57
 */
@Service
public class LibEntityServiceImpl extends ServiceImpl<LibEntityMapper, LibEntityEntity> implements LibEntityService {

    @Resource
    private LibEntityMapper libEntityMapper;
    @Resource
    private MetaEntityRelationMapper metaEntityRelationMapper;
    @Resource
    private MateAttributeMapper mateAttributeMapper;
    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;
    @Resource
    private MetaEntityRelationService metaEntityRelationService;
    @Resource
    private EsDateHandle esDateHandle;

    @Override
    public List<LibEntityEntity> getList(LibEntityPagination libEntityPagination){
        QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(libEntityPagination.getId()))){
            queryWrapper.lambda().and(t->t.eq(LibEntityEntity::getId,libEntityPagination.getId()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getName,libEntityPagination.getName()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.eq(LibEntityEntity::getOntoId,libEntityPagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getData()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getData,libEntityPagination.getData()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getState()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getState,libEntityPagination.getState()));
        }

        //排序
        if(StrUtil.isEmpty(libEntityPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(LibEntityEntity::getId);
        }else{
            queryWrapper="asc".equals(libEntityPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(libEntityPagination.getSidx()):queryWrapper.orderByDesc(libEntityPagination.getSidx());
        }
        Page<LibEntityEntity> page=new Page<>(libEntityPagination.getCurrentPage(), libEntityPagination.getPageSize());
        IPage<LibEntityEntity> userIPage=this.page(page,queryWrapper);
        return libEntityPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<LibEntityEntity> getTypeList(LibEntityPagination libEntityPagination,String dataType){
        QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(libEntityPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getId,libEntityPagination.getId()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getName,libEntityPagination.getName()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getOntoId,libEntityPagination.getOntoId()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getData()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getData,libEntityPagination.getData()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getState()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getState,libEntityPagination.getState()));
        }

        //排序
        if(StrUtil.isEmpty(libEntityPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(LibEntityEntity::getId);
        }else{
            queryWrapper="asc".equals(libEntityPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(libEntityPagination.getSidx()):queryWrapper.orderByDesc(libEntityPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<LibEntityEntity> page=new Page<>(libEntityPagination.getCurrentPage(), libEntityPagination.getPageSize());
            IPage<LibEntityEntity> userIPage=this.page(page,queryWrapper);
            return libEntityPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public LibEntityEntity getInfo(Long id){
        QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(LibEntityEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(LibEntityEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, LibEntityEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(LibEntityEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }

    @Override
    public void deleteids(String ids){
        this.removeBatchByIds(new ArrayList<>(Arrays.asList(ids.split(","))));
    }
    //子表方法
    @Override
    public PageListVO<LibEntityListVO> getEntityByonto(LibEntityPagination libEntityPagination){
        QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
        if(!"null".equals(String.valueOf(libEntityPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.eq(LibEntityEntity::getOntoId,libEntityPagination.getOntoId()));
        }
        if(!"null".equals(String.valueOf(libEntityPagination.getName()))){
            queryWrapper.lambda().apply("e.name like '%"+libEntityPagination.getName()+"%'");
        }
        queryWrapper.lambda().orderByDesc(LibEntityEntity::getId);
        Page<LibEntityEntity> page=new Page<>(libEntityPagination.getCurrentPage(), libEntityPagination.getPageSize());
        IPage<LibEntityPaginationExportModel> userIPage=libEntityMapper.getEntityByonto(page,queryWrapper);
        List<LibEntityListVO> listVO=JsonUtil.getJsonToList(userIPage.getRecords(),LibEntityListVO.class);
        PageListVO<LibEntityListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page1= JsonUtil.getJsonToBean(userIPage,PaginationVO.class);
        vo.setPagination(page1);
        page1.setCurrentPage(userIPage.getCurrent());
        page1.setPageSize(userIPage.getSize());
        return vo;
    }

    @Override
    public List<MetaEntityRelationOut> queryEntityRelationByEntityId(Long entityId,Long ontoId){
        List<MetaEntityRelationOut> result = CollectionUtil.newArrayList();
        MetaEntityRelationEntity entity = new MetaEntityRelationEntity();
        entity.setOnto1Id(ontoId);
        entity.setEntity1Id(entityId);
//        QueryWrapper<MetaEntityRelationEntity> queryWrapper=new QueryWrapper<>();
//        if(!"null".equals(String.valueOf(entityId))){
//            queryWrapper.lambda().or(t->t.eq(MetaEntityRelationEntity::getEntity1Id,entityId));
//            queryWrapper.lambda().or(t->t.eq(MetaEntityRelationEntity::getEntity2Id,entityId));
//        }
//        queryWrapper.lambda().orderByDesc(MetaEntityRelationEntity::getId);
//        List<MetaEntityRelationInfoVO> vos = metaEntityRelationMapper.queryEntityRelationByEntityId(queryWrapper);
        List<MetaEntityRelationInfoVO> vos = metaEntityRelationMapper.ontolistNew(entity);
        Map<String, List<MetaEntityRelationInfoVO>> map =
                vos.stream().collect(Collectors.groupingBy(MetaEntityRelationInfoVO::getRelationName));
        map.forEach((k,v)->{
            MetaEntityRelationOut out = new MetaEntityRelationOut();
            out.setOntoRelName(k);
            out.setRelationId(v.get(0).getOntoRelId());
            if (v.get(0).getId() != null){
                out.setChild(v);
            }
            out.setOnto2Id(v.get(0).getOnto2Id());
            out.setVals(v.get(0).getVals());
            result.add(out);
        });
        return result;
    }


    @Override
    public List<LibEntityEntity> getEntityByOntoIdANDName(LibEntityPagination libEntityPagination){
        QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();

        if(!"null".equals(String.valueOf(libEntityPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(LibEntityEntity::getName,libEntityPagination.getName()));
        }

        if(!"null".equals(String.valueOf(libEntityPagination.getOntoId()))){
            queryWrapper.lambda().and(t->t.eq(LibEntityEntity::getOntoId,libEntityPagination.getOntoId()));
        }
        queryWrapper.select("id","name","onto_id");
        //排序
        queryWrapper.lambda().orderByDesc(LibEntityEntity::getId);
        return this.list(queryWrapper);
    }

    @Override
    public LibEntityEntity getByEntityId(Long id){
        QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(LibEntityEntity::getId,id);
        LibEntityEntity one = this.getOne(queryWrapper);
        if (one == null){
            return null;
        }
        QueryWrapper<MateAttributeEntity> mateAttributeQuery = new QueryWrapper<>();
        mateAttributeQuery.lambda().eq(MateAttributeEntity::getOnto,one.getOntoId());
        //2,对象类型-下拉框;3,枚举类型-文本下拉框
        List<Integer> ids = new ArrayList<>();
        ids.add(2);
        ids.add(3);
        Integer [] vals = {7,8};
        mateAttributeQuery.lambda().and(t->t.in(MateAttributeEntity::getType,ids) 
        		.or (t1->t1.in(MateAttributeEntity::getValType,vals)));
        List<MateAttributeEntity> mateAttributeEntities = mateAttributeMapper.selectList(mateAttributeQuery);
        if (mateAttributeEntities == null || mateAttributeEntities.size() == 0){
            return one;
        }
        if (one.getData() == null || one.getData().isEmpty()){
            return one;
        }
        Map<String, Object> stringObjectMap = JsonUtil.stringToMap(one.getData());
        for (MateAttributeEntity mate:mateAttributeEntities) {
            String nameEn = mate.getNameEn();
            System.out.println(nameEn);
            Object o = stringObjectMap.get(nameEn);
            if (o == null){
                continue;
            }
            if (o instanceof Long){
                String value = "";
                if (mate.getType() == 2){
                    //对象类型
                    LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o);
                    if (libEntityEntity != null && libEntityEntity.getName() != null){
                        value = libEntityEntity.getName();
                    }
                }
                if (mate.getType() == 3){
                    //枚举类型
                    MateAttributeValueEntity mateAttributeValueEntity = mateAttributeValueMapper.selectById((Serializable) o);
                    if (mateAttributeValueEntity != null && mateAttributeValueEntity.getValName() != null){
                        value = mateAttributeValueEntity.getValName();
                    }
                }

                JSONObject json = new JSONObject();
                json.set("id",o.toString());
                json.set("value",value);
                stringObjectMap.put(nameEn,json);
            }
            if (o.getClass().getName() == "com.alibaba.fastjson.JSONArray"){
            	cn.hutool.json.JSONArray jsonArray = new JSONArray();
            	if(mate.getValType()==7 || mate.getValType()==8) {
            		for (Object o1 : (com.alibaba.fastjson.JSONArray) o) {
            			jsonArray.set(o1.toString());
            		}
            		stringObjectMap.put(nameEn,jsonArray);
            		continue;
            	}
            	
            	
            	
                for (Object o1 : (com.alibaba.fastjson.JSONArray) o) {
                    String value = "";
                    if (mate.getType() == 2){
                        //对象类型
                        LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o1);
                        if (libEntityEntity != null && libEntityEntity.getName() != null){
                            value = libEntityEntity.getName();
                        }
                    }
                    if (mate.getType() == 3){
                        //枚举类型
                        MateAttributeValueEntity mateAttributeValueEntity = mateAttributeValueMapper.selectById((Serializable) o1);
                        if (mateAttributeValueEntity != null && mateAttributeValueEntity.getValName() != null){
                            value = mateAttributeValueEntity.getValName();
                        }
                    }
                    JSONObject json = new JSONObject();
                    json.set("id",o1.toString());
                    json.set("value",value);
                    jsonArray.set(json);
                }
                stringObjectMap.put(nameEn,jsonArray);
            }
            one.setData(String.valueOf(new JSONObject(stringObjectMap)));
        }
        return one;
    }

    @Override
    public LibEntityEntity createEntityS2L(LibEntityEntity entity){
        if (entity.getData() == null || entity.getData().isEmpty()){
            return entity;
        }
        //处理对象类型-type=2
        QueryWrapper<MateAttributeEntity> mateAttributeWrapper=new QueryWrapper<>();
        mateAttributeWrapper.lambda().and(t->t.eq(MateAttributeEntity::getOnto,entity.getOntoId()));
        mateAttributeWrapper.lambda().and(t->t.eq(MateAttributeEntity::getType,2));
        List<MateAttributeEntity> mateAttributeEntities = mateAttributeMapper.selectList(mateAttributeWrapper);
//        if (mateAttributeEntities == null || mateAttributeEntities.size() == 0){
//            return entity;
//        }
        Map<String, Object> stringObjectMap = JsonUtil.stringToMap(entity.getData());
        for (MateAttributeEntity mate:mateAttributeEntities) {
            String nameEn = mate.getNameEn();
            Object o = stringObjectMap.get(nameEn);
            if (o == null){
                continue;
            }
            if (o.getClass().getName().equals("java.lang.String")){
                stringObjectMap.put(nameEn,Long.valueOf(o.toString()));
            }
            if (o.getClass().getName().equals("com.alibaba.fastjson.JSONArray")){
                JSONArray jsonArray = new JSONArray();
                for (Object o1 : (com.alibaba.fastjson.JSONArray) o) {
                    jsonArray.set(Long.valueOf(o1.toString()));
                }
                stringObjectMap.put(nameEn,jsonArray);
            }
        }
        //处理枚举类型-type=3
        QueryWrapper<MateAttributeEntity> meijuWrapper=new QueryWrapper<>();
        meijuWrapper.lambda().and(t->t.eq(MateAttributeEntity::getOnto,entity.getOntoId()));
        meijuWrapper.lambda().and(t->t.eq(MateAttributeEntity::getType,3));
        List<MateAttributeEntity> meijuEntities = mateAttributeMapper.selectList(meijuWrapper);
        for (MateAttributeEntity mate:meijuEntities) {
            String nameEn = mate.getNameEn();
            System.out.println(nameEn);
            Object o = stringObjectMap.get(nameEn);
            System.out.println(o);
            if (o == null){
                continue;
            }
            System.out.println(o.getClass().getName());
            if (o.getClass().getName().equals("com.alibaba.fastjson.JSONObject")){
                com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) o;
//                System.out.println("id"+jsonObject.get("id"));
//                System.out.println("value"+jsonObject.get("value"));
//                if (jsonObject.get("value") !=null && !jsonObject.getString("value").isEmpty()){
//
//                }
                if (jsonObject.get("id") != null){
                    stringObjectMap.put(nameEn,jsonObject.getLong("id"));
                }
                if (jsonObject.get("value") != null && jsonObject.get("id") == null){
                    //属性值表中新增value
                    String addvalue = jsonObject.getString("value");
                    MateAttributeValueEntity valueEntity = new MateAttributeValueEntity();
                    valueEntity.setAttributeId(mate.getId());
                    valueEntity.setOntoId(mate.getOnto());
                    valueEntity.setValName(addvalue);
                    mateAttributeValueMapper.insert(valueEntity);
                    stringObjectMap.put(nameEn,valueEntity.getId());
                }
            }
            if (o.getClass().getName() == "com.alibaba.fastjson.JSONArray"){
                JSONArray jsonArray = new JSONArray();
                for (Object o1 : (com.alibaba.fastjson.JSONArray) o) {
                    if (o1.getClass().getName().equals("com.alibaba.fastjson.JSONObject")){
                        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) o1;
                        if (jsonObject.get("id") != null){
                            jsonArray.set(jsonObject.getLong("id"));
                        }
                        if (jsonObject.get("value") != null && jsonObject.get("id") == null){
                            //属性值表中新增value
                            String value = jsonObject.getString("value");
                            MateAttributeValueEntity valueEntity = new MateAttributeValueEntity();
                            valueEntity.setAttributeId(mate.getId());
                            valueEntity.setOntoId(mate.getOnto());
                            valueEntity.setValName(value);
                            mateAttributeValueMapper.insert(valueEntity);
                            jsonArray.set(valueEntity.getId());
                        }
                    }
                }
                stringObjectMap.put(nameEn,jsonArray);
            }
        }
        //处理图片类型-val_type=7
        QueryWrapper<MateAttributeEntity> imgWrapper=new QueryWrapper<>();
        imgWrapper.lambda().and(t->t.eq(MateAttributeEntity::getOnto,entity.getOntoId()));
        imgWrapper.lambda().and(t->t.eq(MateAttributeEntity::getValType,7));
        List<MateAttributeEntity> imgEntities = mateAttributeMapper.selectList(imgWrapper);
        for (MateAttributeEntity mate:imgEntities) {
            String nameEn = mate.getNameEn();
            Object o = stringObjectMap.get(nameEn);
            if (o == null){
                continue;
            }
            if (o.getClass().getName().equals("java.lang.String")){
                stringObjectMap.put(nameEn,Long.valueOf(o.toString()));
            }
            if (o.getClass().getName().equals("com.alibaba.fastjson.JSONArray")){
                JSONArray jsonArray = new JSONArray();
                for (Object o1 : (com.alibaba.fastjson.JSONArray) o) {
                    jsonArray.set(Long.valueOf(o1.toString()));
                }
                stringObjectMap.put(nameEn,jsonArray);
            }
        }
        entity.setData(String.valueOf(new JSONObject(stringObjectMap)));
       return entity;
    }

    @Override
    public LibEntityEntity getListId2Name(LibEntityEntity one){
        if (one.getData() == null || one.getData().isEmpty()){
            return one;
        }
        QueryWrapper<MateAttributeEntity> mateAttributeQuery = new QueryWrapper<>();
        mateAttributeQuery.lambda().eq(MateAttributeEntity::getOnto,one.getOntoId());
        //2,对象类型-下拉框;3,枚举类型-文本下拉框
        List<Integer> ids = new ArrayList<>();
        ids.add(2);
        ids.add(3);
        mateAttributeQuery.lambda().and(t->t.in(MateAttributeEntity::getType,ids));
        List<MateAttributeEntity> mateAttributeEntities = mateAttributeMapper.selectList(mateAttributeQuery);
        if (mateAttributeEntities == null || mateAttributeEntities.size() == 0){
            return one;
        }
        Map<String, Object> stringObjectMap = JsonUtil.stringToMap(one.getData());
        for (MateAttributeEntity mate:mateAttributeEntities) {
            String nameEn = mate.getNameEn();
            Object o = stringObjectMap.get(nameEn);
            if (o == null){
                continue;
            }
            if (o.getClass().getName() == "java.lang.Long"){
                String value = "";
                if (mate.getType() == 2){
                    //对象类型
                    LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o);
                    if (libEntityEntity != null && libEntityEntity.getName() != null){
                        value = libEntityEntity.getName();
                    }
                }
                if (mate.getType() == 3){
                    //枚举类型
                    MateAttributeValueEntity mateAttributeValueEntity = mateAttributeValueMapper.selectById((Serializable) o);
                    if (mateAttributeValueEntity != null && mateAttributeValueEntity.getValName() != null){
                        value = mateAttributeValueEntity.getValName();
                    }
                }

//                JSONObject json = new JSONObject();
//                json.set("id",o.toString());
//                json.set("value",value);
                stringObjectMap.put(nameEn,value);
            }
            if (o.getClass().getName() == "com.alibaba.fastjson.JSONArray"){
                String value = "";
                for (Object o1 : (com.alibaba.fastjson.JSONArray) o) {

                    if (mate.getType() == 2){
                        //对象类型
                        LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o1);
                        if (libEntityEntity != null && libEntityEntity.getName() != null){
                            if (!value.isEmpty()){
                                value += ",";
                            }
                            value += libEntityEntity.getName();
                        }
                    }
                    if (mate.getType() == 3){
                        //枚举类型
                        MateAttributeValueEntity mateAttributeValueEntity = mateAttributeValueMapper.selectById((Serializable) o1);
                        if (mateAttributeValueEntity != null && mateAttributeValueEntity.getValName() != null){
                            if (!value.isEmpty()){
                                value += ",";
                            }
                            value += mateAttributeValueEntity.getValName();
                        }
                    }
//                    JSONObject json = new JSONObject();
//                    json.set("id",o1.toString());
//                    json.set("value",value);
//                    jsonArray.set(json);
                }
                stringObjectMap.put(nameEn,value);
            }
            one.setData(String.valueOf(new JSONObject(stringObjectMap)));
        }
        return one;
    }
	@Override
	public List<LibEntityEntity> getList(Long ontoId) {
		QueryWrapper<LibEntityEntity> queryWrapper=new QueryWrapper<>();
		queryWrapper.eq("onto_id", ontoId);
		queryWrapper.select("id","name");
		return this.list(queryWrapper);
	}

    @Override
    public boolean checkEntity(LibEntityEntity entity){
        if (entity.getData() == null || entity.getData().isEmpty()){
            return false;
        }
        //判断必填项
        QueryWrapper<MateAttributeEntity> mateAttributeWrapper=new QueryWrapper<>();
        mateAttributeWrapper.lambda().and(t->t.eq(MateAttributeEntity::getOnto,entity.getOntoId()));
        mateAttributeWrapper.lambda().and(t->t.eq(MateAttributeEntity::getMust,1));
        List<MateAttributeEntity> mateAttributeEntities = mateAttributeMapper.selectList(mateAttributeWrapper);
        Map<String, Object> stringObjectMap = JsonUtil.stringToMap(entity.getData());
        for (MateAttributeEntity mate:mateAttributeEntities) {
            String nameEn = mate.getNameEn();
            Object o = stringObjectMap.get(nameEn);
            if (o == null){
                return false;
            }
        }
        return true;
    }
    /**
     * 保存实体，同时保存实体关系
     */
	@Override
	public void updataEntityAndRealation(LibEntityEntity entity) {
		Long ontoId =  entity.getOntoId();
		if(entity.getData()==null) {
			return;
		}
		Map<String, Object> entMap = JsonUtil.stringToMap(entity.getData());
		List<MateAttributeEntity>  attrList = OntoCache.getAttListByOntoId(ontoId);
		for (int i = 0; i < attrList.size(); i++) {
			if(attrList.get(i).getValType()==YearbookConstant.META_ATTRIBUTE_VAL_OBJECT) {
				MateAttributeEntity attr =  attrList.get(i);
				Object value = entMap.get(attr.getNameEn());
				if(ObjectUtil.isEmpty(value)) {
					continue;
				}
				if(attr.getVals()==1) {
					Long vid =  (Long)value;
					//查询实体
					LibEntityEntity  vent = this.getById(vid);
					// 保存实体和实体的关系
					MetaEntityRelationEntity relation_ent = new MetaEntityRelationEntity();
					relation_ent.setOnto1Id(ontoId);
					relation_ent.setEntity1Id( entity.getId());
					relation_ent.setOnto2Id( vent.getOntoId());
					relation_ent.setEntity2Id(vid);
					relation_ent.setOntoRelId(attr.getRelationId());
					relation_ent.setName(attr.getNameCn());
//					metaEntityRelationService.create(relation_ent);
					checkRelation(relation_ent);
				}
				if(attr.getVals()==2) {
					for (Object o : (com.alibaba.fastjson.JSONArray) value ) {
						Long vid = ( Long)o;
						System.out.println("vid==>"+vid);
						LibEntityEntity  vent = this.getById(vid);
						if(vent==null) {
							continue;
						}
						MetaEntityRelationEntity relation_ent = new MetaEntityRelationEntity();
						relation_ent.setOnto1Id(ontoId);
						relation_ent.setEntity1Id( entity.getId());
						relation_ent.setOnto2Id( vent.getOntoId());
						relation_ent.setEntity2Id(vid);
						relation_ent.setOntoRelId(attr.getRelationId());
						relation_ent.setName(attr.getNameCn());
						//metaEntityRelationService.create(relation_ent);
						checkRelation(relation_ent);
					}
				}
				
			}
		}
//		this.updateById(entity);
	}
	
	
	public void  checkRelation(MetaEntityRelationEntity  relation_ent) {
		QueryWrapper<MetaEntityRelationEntity> query  =  new QueryWrapper<MetaEntityRelationEntity>();
		query.eq("onto1_id", relation_ent.getOnto1Id());
		query.eq("onto2_id", relation_ent.getOnto2Id());
		query.eq("entity1_Id", relation_ent.getEntity1Id());
		query.eq("entity2_Id", relation_ent.getEntity2Id());
		query.eq("onto_rel_id", relation_ent.getOntoRelId());
		long count = metaEntityRelationService.count(query);
		if(count == 0 ) {
			metaEntityRelationService.create(relation_ent);
		}
	}

    @Override
	public void mergeEntity(Long ontoId, Long corpusId){
        //查重复的数据
        List<LibEntityEntity> mergeList = libEntityMapper.getMergeEntity();
        for (LibEntityEntity merge:mergeList) {
            QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
            libEntityWrapper.select("id","data");
            libEntityWrapper.eq("name",merge.getName());
            libEntityWrapper.eq("onto_id",merge.getOntoId());
            libEntityWrapper.eq("corpus_id",merge.getCorpusId());
            List<LibEntityEntity> libEntityList = libEntityMapper.selectList(libEntityWrapper);
            JSONObject jsonObject = new JSONObject();
            QueryWrapper<MateAttributeEntity> mateAttributeWrapper = new QueryWrapper<>();
            mateAttributeWrapper.eq("onto",merge.getOntoId());
            List<MateAttributeEntity> mateAttributeLists = mateAttributeMapper.selectList(mateAttributeWrapper);
            for (MateAttributeEntity mateAttribute:mateAttributeLists) {
                if (mateAttribute.getVals() == 1){
                    //单值
                    for (LibEntityEntity LibEntity:libEntityList) {
                        if (LibEntity.getData() == null){continue;}
                        Map<String, Object> dataMap = JsonUtil.stringToMap(LibEntity.getData());
                        if (dataMap.containsKey(mateAttribute.getNameEn())){
                            jsonObject.set(mateAttribute.getNameEn(),dataMap.get(mateAttribute.getNameEn()));
                        }
                    }
                }
                if (mateAttribute.getVals() == 2){
                    //多值
                    JSONArray jsonArray = new JSONArray();
                    for (LibEntityEntity LibEntity:libEntityList) {
                        if (LibEntity.getData() == null){continue;}
                        Map<String, Object> dataMap = JsonUtil.stringToMap(LibEntity.getData());
                        if (dataMap.containsKey(mateAttribute.getNameEn())){
                            Object o = dataMap.get(mateAttribute.getNameEn());
                            jsonArray.addAll((Collection<?>) o);
                        }
                    }
                    Set set = new HashSet<>();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        set.add(jsonArray.get(i));
                    }
//                    jsonArray = set.toString();
                    jsonObject.set(mateAttribute.getNameEn(),set.toString());
                }
            }
            LibEntityEntity libEntity = new LibEntityEntity();
            libEntity.setName(merge.getName());
            libEntity.setOntoId(merge.getOntoId());
            libEntity.setCorpusId(merge.getCorpusId());
            libEntity.setData(String.valueOf(jsonObject));
            libEntityMapper.insert(libEntity);
        }
    }
}