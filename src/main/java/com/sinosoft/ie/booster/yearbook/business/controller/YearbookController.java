package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.text.DocumentException;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.visualdev.util.FileUtil;
import com.sinosoft.ie.booster.visualdev.util.UpUtil;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookListVO;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookPagination;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookUpForm;
import com.sinosoft.ie.booster.yearbook.business.service.YearbookService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.config.ProjectConstant;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * yearbook
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 13:39:57
 */
@Slf4j
@RestController
@Api(tags = "yearbook")
@RequestMapping("/Yearbook")
public class YearbookController {

	@Autowired
	private ProjectConfig projectConfig;
    @Autowired
    private YearbookService yearbookService;


    /**
     * 列表
     *
     * @param yearbookPagination
     * @return
     */
    @GetMapping
    public R list(YearbookPagination yearbookPagination)throws IOException{
        List<YearbookEntity> list= yearbookService.getList(yearbookPagination);
        //处理id字段转名称，若无需转或者为空可删除
        List<YearbookListVO> listVO=JsonUtil.getJsonToList(list,YearbookListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(yearbookPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param yearbookCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid YearbookCrForm yearbookCrForm) throws Exception {
        YearbookEntity entity=JsonUtil.getJsonToBean(yearbookCrForm, YearbookEntity.class);
        yearbookService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<YearbookInfoVO> info(@PathVariable("id") Long id){
        YearbookEntity entity= yearbookService.getInfo(id);
        YearbookInfoVO vo=JsonUtil.getJsonToBean(entity, YearbookInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid YearbookUpForm yearbookUpForm) throws DataException {
        YearbookEntity entity= yearbookService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(yearbookUpForm, YearbookEntity.class);
            yearbookService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        YearbookEntity entity= yearbookService.getInfo(id);
        if(entity!=null){
            yearbookService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

	/**
	 * 上传文件
	 *
	 * @return
	 */
	@ApiOperation("上传文件" )
	@RequestMapping(method = {RequestMethod.POST}, value = "/upload")
	public R upload(@RequestParam("file") MultipartFile file)  {
		if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
			return R.failed("上传附件操作异常，附件为空，请重新上传");
		}
		String fileType = UpUtil.getFileType(file);
//		//验证类型
//		if (!FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//			return R.failed("上传失败，文件格式不允许上传" );
//		}
		JSONObject json = JSONUtil.createObj();
		try {
			String originalFilename = file.getOriginalFilename();
			String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
			String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
			FileUtil.upFile(file, projectConfig.getUploadPath() +  ProjectConstant.pdf_prefix, fileName);

			json.set("oldName", originalFilename);
			json.set("newName", fileName);
			json.set("path",  ProjectConstant.pdf_prefix + fileName);
		} catch (Exception e) {
			log.error("{}", e);
			return R.failed("上传失败:" + e);
		}
		return R.ok(json.toString(), "上传成功");
	}

	/**
	 * @methodName：previewPdf
	 * @description：预览pdf
	 * @author：peas
	 * @date：2022/9/14 16:51
	 * @param：[id, type]
	 * @return：void
	 **/
	@GetMapping("/upload/pdf/{name}")
	public void previewPdf(@PathVariable("name") String name) throws IOException, DocumentException {
		yearbookService.previewPdf(name);
	}

	/**
	 * 年鉴封面首页列表
	 *
	 * @param yearbookPagination
	 * @return
	 */
	@GetMapping("/coverList")
	public R coverList(YearbookPagination yearbookPagination)throws IOException{
		yearbookPagination.setPid(0L);
		List<YearbookEntity> list= yearbookService.getList(yearbookPagination);
		//处理id字段转名称，若无需转或者为空可删除

		for(YearbookEntity entity:list){
		}
		List<YearbookListVO> listVO=JsonUtil.getJsonToList(list,YearbookListVO.class);
		PageListVO vo=new PageListVO();
		vo.setList(listVO);
		PaginationVO page=JsonUtil.getJsonToBean(yearbookPagination,PaginationVO.class);
		vo.setPagination(page);
		return R.ok(vo);
	}

	/**
	 * 年鉴翻页
	 *
	 * @param pid 年鉴id
	 * @parampageNum  页码
	 * @return
	 */
	@GetMapping("/bookpage")
	@ApiImplicitParams({@ApiImplicitParam(name = "pid", value = "pid", required = true),
			@ApiImplicitParam(name = "pageNum", value = "pageNum", required = true)})
	public R bookpage(YearbookPagination yearbookPagination)throws IOException{
		List<YearbookEntity> list= yearbookService.getList(yearbookPagination);
		if (list.size()>0){
			YearbookListVO listVO=JsonUtil.getJsonToBean(list.get(0),YearbookListVO.class);
			yearbookService.getCurrentPageTagAndTheme( listVO );
			return R.ok(listVO);
		}else {
			return R.failed("查询无数据！");
		}
	}


}
