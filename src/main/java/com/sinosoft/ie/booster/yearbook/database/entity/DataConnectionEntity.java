package com.sinosoft.ie.booster.yearbook.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-06 15:01:22
 */
@Data
@TableName("data_connection")
@ApiModel(value = "")
public class DataConnectionEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 数据集名称
     */
    @ApiModelProperty(value = "数据集名称")
    private String name;

    /**
     * 连接类型：1.mysql;2.postgresql;3.kingbase;4.dm;5.orcle;6.sqlserver
     */
    @ApiModelProperty(value = "连接类型：1.mysql;2.postgresql;3.kingbase;4.dm;5.orcle;6.sqlserver")
    private Integer dbType;

    /**
     * 主机ip
     */
    @ApiModelProperty(value = "主机ip")
    private String ip;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    private Integer port;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbBaseName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String user;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;
}
