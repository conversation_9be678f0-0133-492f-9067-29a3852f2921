package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 标注关系表
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:56
 */
@Data
@TableName("mark_triplet")
@ApiModel(value = "标注关系表")
public class MarkTripletEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long tag1id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long tag2id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long relationid;
    

    /**
     * 关系名称
     */
    @ApiModelProperty(value = "关系名称")
    private String relationname;

    /**
     * 句子id
     */
    @ApiModelProperty(value = "句子id")
    private Long sentenceid;
}
