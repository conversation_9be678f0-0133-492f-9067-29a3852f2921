package com.sinosoft.ie.booster.yearbook.business.model.filedirectory;

import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * FileDirectory模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 14:28:43
 */
@Data
@ApiModel
public class FileDirectoryListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String pid;
    
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Integer zonelevel;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileId;
    
    /**
     * 图片id
     */
    @ApiModelProperty(value = "图片id")
    private String picId;
    
    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;

	/**
	 * 子集
	 */
	@ApiModelProperty(value = "子集")
	private List<FileDirectoryListVO> sonList;
	@ApiModelProperty(value = "默认有子集")
	private boolean hasChildren = true;

}