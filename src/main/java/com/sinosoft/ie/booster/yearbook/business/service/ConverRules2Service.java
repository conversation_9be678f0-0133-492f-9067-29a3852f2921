package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.ConverRules2Entity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2Pagination;
import java.util.*;

/**
 *
 * conver_rules2
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-20 16:32:03
 */
public interface ConverRules2Service extends IService<ConverRules2Entity> {

    List<ConverRules2Entity> getList(ConverRules2Pagination converRules2Pagination);

    List<ConverRules2Entity> getTypeList(ConverRules2Pagination converRules2Pagination,String dataType);



    ConverRules2Entity getInfo(Long id);

    void delete(ConverRules2Entity entity);

    void create(ConverRules2Entity entity);
    
    void create2(List<ConverRules2Entity>  entList );

    boolean update( Long id, ConverRules2Entity entity);
    
//  子表方法
}
