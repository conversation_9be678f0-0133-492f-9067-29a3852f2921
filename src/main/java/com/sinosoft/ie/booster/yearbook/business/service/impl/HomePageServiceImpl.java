package com.sinosoft.ie.booster.yearbook.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.BannerMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.*;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagListVO;
import com.sinosoft.ie.booster.yearbook.business.model.yearbook.YearbookPagination;
import com.sinosoft.ie.booster.yearbook.business.service.*;
import com.sinosoft.ie.booster.yearbook.constant.enums.GlobalEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class HomePageServiceImpl implements HomePageService {

    @Resource
    private YearbookService yearbookService;

    @Resource
    private ResearchTagService researchTagService;

    @Resource
    private EventService eventService;

    @Resource
    private BannerMapper bannerMapper;

    @Resource
    private EventMapper eventMapper;

    @Resource
    private OrganizationService organizationService;

    @Override
    public List<BannerOut> queryBanner() {
        return bannerMapper.queryBanner();
    }

//    @Override
//    public Map<String,List<ResearchTagListVO>> labelRead(String typeList) {
//        return researchTagService.showMapByLegendType(typeList);
//    }

    @Override
    public YearbookCount count() {
        YearbookCount res = new YearbookCount();
        //查询年鉴数量
        Long yearbooks = yearbookService.count(new LambdaQueryWrapper<YearbookEntity>()
                .eq(YearbookEntity::getPid, 0L)
                .eq(YearbookEntity::getDelFlag, GlobalEnum.isDeleteType.NO.getCode()));
        res.setYearbookCount(yearbooks.intValue());
        //查询事件数量
        Long eventCount = eventService.count();
        res.setEventCount(eventCount.intValue());

        //计算人物数量、课题数量、成果数量、机构数量、年鉴图片数量
        LambdaQueryWrapper<EventEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(EventEntity::getPersons, EventEntity::getTask, EventEntity::getAchievements,
                EventEntity::getOrganisation,EventEntity::getImages);
        List<EventEntity> list = eventService.list(queryWrapper);
        int personCount = 0;
        int taskCount = 0;
        int achievementsCount = 0;
        int organisationCount = 0;
        int imagesCount = 0;
        for(EventEntity e : list){
            if(ObjectUtil.isNull(e)){
                continue;
            }
            if(StringUtils.isNotBlank(e.getPersons())){
                personCount = personCount + e.getPersons().split(";").length;
            }
            if(StringUtils.isNotBlank(e.getTask())){
                taskCount = taskCount + e.getTask().split(";").length;
            }
            if(StringUtils.isNotBlank(e.getAchievements())){
                achievementsCount = achievementsCount + e.getAchievements().split(";").length;
            }
            if(StringUtils.isNotBlank(e.getOrganisation())){
                organisationCount = organisationCount + e.getOrganisation().split(";").length;
            }
            if(StringUtils.isNotBlank(e.getImages())){
                imagesCount = imagesCount + e.getImages().split(";").length;
            }
        }
        res.setPersonCount(personCount);
        res.setTaskCount(taskCount);
        res.setResultCount(achievementsCount);
        res.setOrgCount(organisationCount);
        res.setImgCount(imagesCount);
        return res;
    }

    @Override
    public ImportantNews getNews() {
        ImportantNews importantNews = new ImportantNews();
        //查询机构数量
        Long count = organizationService.count(new LambdaQueryWrapper<OrganizationEntity>().eq(OrganizationEntity::getDelFlag, GlobalEnum.isDeleteType.NO.getCode()));
        importantNews.setOrgCount(count.intValue());
        //查询要要事要闻总数
        Integer newsCount = eventMapper.getNewsCount();
        importantNews.setNewsCount(newsCount);
        //查询各个机构的事件数量
        List<OrgEventOut> orgEventList = eventMapper.getOrgEvent();
        importantNews.setOrgEvent(orgEventList);
        //查询要事要闻列表
        LambdaQueryWrapper<EventEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(EventEntity::getTitle, EventEntity::getStartTime);
        queryWrapper.orderByDesc(EventEntity::getStartTime);
        queryWrapper.last("LIMIT 5");
        List<EventEntity> list = eventService.list(queryWrapper);
        List<ImportantNewsInfo> infoList = CollectionUtil.newArrayList();
        list.forEach(e->{
            ImportantNewsInfo info = new ImportantNewsInfo();
            info.setTitle(e.getTitle());
            info.setDate(e.getStartTime());
            infoList.add(info);
        });
        importantNews.setInfoList(infoList);
        return importantNews;
    }

    @Override
    public Object eventVein() {
        List<EventVeinOut> list = eventMapper.getEventVein();
        Map<String, Map<String, List<EventVeinOut>>> collect = list.stream().collect(Collectors.groupingBy(EventVeinOut::getYear))
            .entrySet().stream().collect(Collectors.toMap(
                entry -> entry.getKey(),
                entry -> entry.getValue().stream().collect(Collectors.groupingBy(EventVeinOut::getTheme))
        ));
        //因需求未落实，暂不开发
        return null;
    }
    
	@Override
	public R getEventByTitleYearPage(EventPagination eventPagination) {
		//需查询出当前年鉴id
		Map<String,Long>  result =  new HashMap<>();
		if(StringUtils.isEmpty(eventPagination.getTitle())) {
			YearbookPagination yearbookPagination = new YearbookPagination();
			yearbookPagination.setBookYear(eventPagination.getStartTime());
			//父目录
			List<YearbookEntity>  pList = yearbookService.getList(yearbookPagination);
			if(pList.size()!=0) {
				YearbookPagination yearbookPagination2 = new YearbookPagination();
				yearbookPagination2.setPid(pList.get(0).getId());
				yearbookPagination2.setPageNum(eventPagination.getPageNum());
				List<YearbookEntity>  yearList = yearbookService.getList(yearbookPagination2);
				result.put("year_id", yearList.get(0).getId());
			}
		}else {
			//返回当前事件
			QueryWrapper<EventEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("title", eventPagination.getTitle());
			queryWrapper.eq("page_num",  eventPagination.getPageNum());
			queryWrapper.like("start_time", eventPagination.getStartTime());
			List<EventEntity> list = eventService.list(queryWrapper);
			if(list.size()!=0) {
				result.put("eve_id", list.get(0).getId());
			}
		}
		return R.ok(result);
	}

}
