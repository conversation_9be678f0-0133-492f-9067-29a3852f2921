package com.sinosoft.ie.booster.yearbook.business.model.hotinformation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * HotInformation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-21 17:37:05
 */
@Data
@ApiModel
public class HotInformationCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String theme;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer number;
}