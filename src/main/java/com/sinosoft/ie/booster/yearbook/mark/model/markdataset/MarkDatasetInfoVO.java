package com.sinosoft.ie.booster.yearbook.mark.model.markdataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * MarkDataset模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-11 15:34:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkDatasetInfoVO extends MarkDatasetCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    
    private String oldName;
}