package com.sinosoft.ie.booster.yearbook.util;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;

import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFPicture;
import org.apache.poi.xwpf.usermodel.XWPFPictureData;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFStyles;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;

public class WordUtil {

	/**
	 * 判断是zip文件还是doc或docx文件 读取全部文件
	 * 
	 * @param path
	 * @return
	 * @throws IOException
	 */
	public static String analysisWord1(String path) throws IOException {
		String text = "";
		// 判断后缀是zip格式
		File file = new File(path);
		String par = path.substring(path.length() - 3, path.length());
		if (par.equals("zip")) {
			ZipInputStream zin = new ZipInputStream(new FileInputStream(file), Charset.forName("gbk"));
			ZipFile zf = new ZipFile(path, Charset.forName("GBK"));
			ZipEntry ze = zin.getNextEntry();
			InputStream inputStream = zf.getInputStream(ze);
			text = getText(inputStream);
		} else {
			InputStream inputStream = new FileInputStream(file);
			text = getText(inputStream);
		}
		return text;
	}

	/**
	 * 从流里头读取文本
	 * 
	 * @param inputStream
	 * @return
	 * @throws IOException
	 */
	public static String getText(InputStream inputStream) throws IOException {
		String text = "";
		FileMagic fileMagic = null;
		ByteArrayOutputStream baos = cloneInputStream(inputStream);
		InputStream clone_ins1 = new ByteArrayInputStream(baos.toByteArray());
		BufferedInputStream bufferedInputStream = new BufferedInputStream(clone_ins1);
		InputStream clone_ins2 = new ByteArrayInputStream(baos.toByteArray());
		try {
			fileMagic = FileMagic.valueOf(bufferedInputStream);
		} finally {
			bufferedInputStream.close();
		}
		if (FileMagic.OOXML.equals(fileMagic)) {
			text = readDocx(clone_ins2);
		}
		if (FileMagic.OLE2.equals(fileMagic)) {
			text = readDoc(clone_ins2);
		}
		return text;

	}

	/**
	 * 从doc或docx读取文本
	 * 
	 * @param path
	 * @return
	 * @throws IOException
	 */
	public static String analysisWord(String path) throws IOException {
		String text = "";
		File file = new File(path);
		InputStream inputStream = new FileInputStream(file);
		FileMagic fileMagic = null;
		ByteArrayOutputStream baos = cloneInputStream(inputStream);
		InputStream clone_ins1 = new ByteArrayInputStream(baos.toByteArray());
		BufferedInputStream bufferedInputStream = new BufferedInputStream(clone_ins1);
		InputStream clone_ins2 = new ByteArrayInputStream(baos.toByteArray());
		try {
			fileMagic = FileMagic.valueOf(bufferedInputStream);
		} finally {
			bufferedInputStream.close();
		}
		if (FileMagic.OOXML.equals(fileMagic)) {
			text = readDocx(clone_ins2);
		}
		if (FileMagic.OLE2.equals(fileMagic)) {
			text = readDoc(clone_ins2);
		}
		return text;
	}

	/**
	 * 解析单个文件的压缩包
	 * 
	 * @throws IOException
	 */
	public static String analysisFile(String zipPath) throws IOException {
		String text = "";
		File file = new File(zipPath);
		ZipInputStream zin = new ZipInputStream(new FileInputStream(file), Charset.forName("gbk"));
		ZipFile zf = new ZipFile(zipPath, Charset.forName("GBK"));
		ZipEntry ze = null;
		while ((ze = zin.getNextEntry()) != null) {
			InputStream inputStream = zf.getInputStream(ze);
			FileMagic fileMagic = null;
			ByteArrayOutputStream baos = cloneInputStream(inputStream);
			InputStream clone_ins1 = new ByteArrayInputStream(baos.toByteArray());
			BufferedInputStream bufferedInputStream = new BufferedInputStream(clone_ins1);
			InputStream clone_ins2 = new ByteArrayInputStream(baos.toByteArray());
			try {
				fileMagic = FileMagic.valueOf(bufferedInputStream);
			} finally {
				bufferedInputStream.close();
			}
			if (FileMagic.OOXML.equals(fileMagic)) {
				text = readDocx(clone_ins2);
			}
			if (FileMagic.OLE2.equals(fileMagic)) {
				text = readDoc(clone_ins2);
			}
		}

		return text;
	}

	/**
	 * 解析多个文件的压缩包
	 * 
	 * @throws IOException
	 */
	public static List<String> analysisZipFile(String zipPath) throws IOException {
		List<String> list = new ArrayList<>();
		File file = new File(zipPath);
		ZipInputStream zin = new ZipInputStream(new FileInputStream(file), Charset.forName("gbk"));
		ZipFile zf = new ZipFile(zipPath, Charset.forName("GBK"));
		ZipEntry ze = null;
		while ((ze = zin.getNextEntry()) != null) {
			String text = "";
			System.out.print(ze.getName());
			InputStream inputStream = zf.getInputStream(ze);
			FileMagic fileMagic = null;
			ByteArrayOutputStream baos = cloneInputStream(inputStream);
			InputStream clone_ins1 = new ByteArrayInputStream(baos.toByteArray());
			BufferedInputStream bufferedInputStream = new BufferedInputStream(clone_ins1);
			InputStream clone_ins2 = new ByteArrayInputStream(baos.toByteArray());
			try {
				fileMagic = FileMagic.valueOf(bufferedInputStream);
			} finally {
				bufferedInputStream.close();
			}
			if (FileMagic.OOXML.equals(fileMagic)) {
				text = readDocx(clone_ins2);
			}
			if (FileMagic.OLE2.equals(fileMagic)) {
				text = readDoc(clone_ins2);
			}
			list.add(text);
		}

		return list;
	}

	public static String readDoc(InputStream inputStream) throws IOException {
		StringBuffer sb = new StringBuffer();
		HWPFDocument doc = null;
		doc = new HWPFDocument(inputStream);
		String str = doc.getDocumentText();
		doc.close();
		return str;
	}

	public static String readDocx(InputStream inputStream) throws IOException {
		StringBuffer sb = new StringBuffer();
		XWPFDocument xwpfDocument = null;
		xwpfDocument = new XWPFDocument(inputStream);
		List<XWPFParagraph> paragraphs = xwpfDocument.getParagraphs();
		XWPFStyles xWPFStyles = xwpfDocument.getStyles();

		for (int j = 0; j < paragraphs.size(); j++) {
			XWPFParagraph xWPFParagraph = paragraphs.get(j);
			List<XWPFRun> runlist = xWPFParagraph.getRuns();
			boolean isImg = false;
			// 获取段落内容
			String text = xWPFParagraph.getText();
			sb.append(text);
		}
		xwpfDocument.close();
		return sb.toString();
	}

	/**
	 * 复制流
	 * 
	 * @param input
	 * @return
	 */
	private static ByteArrayOutputStream cloneInputStream(InputStream input) {
		try {
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			byte[] buffer = new byte[1024];
			int len;
			while ((len = input.read(buffer)) > -1) {
				baos.write(buffer, 0, len);
			}
			baos.flush();
			return baos;
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	/**
	 * docx转pdf
	 * @param src
	 * @return
	 */
	public static byte[] docxToPdf(InputStream src) {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		byte[] resBytes = null;
		String result;
		try {
			// pdf文件的尺寸
			Document pdfDocument = new Document(PageSize.A3, 72, 72, 72, 72);
			PdfWriter pdfWriter = PdfWriter.getInstance(pdfDocument, baos);
			XWPFDocument doc = new XWPFDocument(src);
			pdfWriter.setInitialLeading(20);
			java.util.List<XWPFParagraph> plist = doc.getParagraphs();
			pdfWriter.open();
			pdfDocument.open();
			for (int i = 0; i < plist.size(); i++) {
				XWPFParagraph pa = plist.get(i);
				java.util.List<XWPFRun> runs = pa.getRuns();
				for (int j = 0; j < runs.size(); j++) {
					XWPFRun run = runs.get(j);
					java.util.List<XWPFPicture> piclist = run.getEmbeddedPictures();
					Iterator<XWPFPicture> iterator = piclist.iterator();
					while (iterator.hasNext()) {
						XWPFPicture pic = iterator.next();
						XWPFPictureData picdata = pic.getPictureData();
						byte[] bytepic = picdata.getData();
						Image imag = Image.getInstance(bytepic);
						pdfDocument.add(imag);
					}
					// 中文字体的解决
					BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
					Font font = new Font(bf, 11.0f, Font.NORMAL, BaseColor.BLACK);
					String text = run.getText(-1);
					byte[] bs;
					if (text != null) {
						bs = text.getBytes();
						String str = new String(bs);
						Chunk chObj1 = new Chunk(str, font);
						pdfDocument.add(chObj1);
					}
				}
				pdfDocument.add(new Chunk(Chunk.NEWLINE));
			}
			// 需要关闭，不然无法获取到输出流
			pdfDocument.close();
			pdfWriter.close();
			resBytes = baos.toByteArray();
		} catch (Exception e) {
			//log.error("docx转pdf文件异常：{}", e);
		} finally {
			try {
				if (baos != null) {
					baos.close();
				}
			} catch (IOException e) {
				//log.error("docx转pdf关闭io流异常：{}", e);
			}
		}
		return resBytes;
	}

	public static void main(String[] args) throws IOException {
		String path = "C:\\Users\\<USER>\\Desktop\\test.docx";
		InputStream ins = new FileInputStream(path);
		
//		XWPFDocument xwpfDocument = new XWPFDocument(ins);
//		Integer h= (Integer) xwpfDocument.getDocument().getBody().getSectPr().getPgSz().getH();
//		Integer w= (Integer) xwpfDocument.getDocument().getBody().getSectPr().getPgSz().getW();
//		
//		System.out.println(h);
//		System.out.println(w);
//		String txt = WordUtil.analysisWord1(path);
//		System.out.println(txt);
	}

}
