package com.sinosoft.ie.booster.yearbook.business.model.organization;

import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * Organization模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:08:32
 */
@Data
@ApiModel
public class OrganizationListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 
     */
//    @ApiModelProperty(value = "")
//    private String id;
    
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String name;

    /**
     * 机构级别
     */
    @ApiModelProperty(value = "机构级别")
    private Integer zonelevel;

    /**
     * 父机构id
     */
    @ApiModelProperty(value = "父机构id")
    private Long pid;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sortNum;

    /**
     * 图片路径
     */
    @ApiModelProperty(value = "图片路径")
    private String pic;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;
    
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;

	private List<OrganizationEntity> sonList;
    
}