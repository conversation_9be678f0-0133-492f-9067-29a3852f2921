package com.sinosoft.ie.booster.yearbook.baselib.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

import javax.annotation.Resource;

/**
 *
 * meta_onto_relation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-31 16:37:39
 */
@Service
public class MetaOntoRelationServiceImpl extends ServiceImpl<MetaOntoRelationMapper, MetaOntoRelationEntity> implements MetaOntoRelationService {

	@Resource
	private MetaOntoRelationMapper metaOntoRelationMapper;
	@Resource
	private LibConceptService libConceptService;
	
    @Override
    public List<MetaOntoRelationEntity> getList(MetaOntoRelationPagination metaOntoRelationPagination){
        QueryWrapper<MetaOntoRelationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getId,metaOntoRelationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getOnto1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getOnto1Id,metaOntoRelationPagination.getOnto1Id()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getOnto2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getOnto2Id,metaOntoRelationPagination.getOnto2Id()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getName,metaOntoRelationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getPid,metaOntoRelationPagination.getPid()));
        }

        //排序
        if(StrUtil.isEmpty(metaOntoRelationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MetaOntoRelationEntity::getId);
        }else{
            queryWrapper="asc".equals(metaOntoRelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaOntoRelationPagination.getSidx()):queryWrapper.orderByDesc(metaOntoRelationPagination.getSidx());
        }
        Page<MetaOntoRelationEntity> page=new Page<>(metaOntoRelationPagination.getCurrentPage(), metaOntoRelationPagination.getPageSize());
        IPage<MetaOntoRelationEntity> userIPage=this.page(page,queryWrapper);
        return metaOntoRelationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<MetaOntoRelationEntity> getTypeList(MetaOntoRelationPagination metaOntoRelationPagination,String dataType){
        QueryWrapper<MetaOntoRelationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getId,metaOntoRelationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getOnto1Id()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getOnto1Id,metaOntoRelationPagination.getOnto1Id()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getOnto2Id()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getOnto2Id,metaOntoRelationPagination.getOnto2Id()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getName,metaOntoRelationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getPid()))){
            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getPid,metaOntoRelationPagination.getPid()));
        }

        //排序
        if(StrUtil.isEmpty(metaOntoRelationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(MetaOntoRelationEntity::getId);
        }else{
            queryWrapper="asc".equals(metaOntoRelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaOntoRelationPagination.getSidx()):queryWrapper.orderByDesc(metaOntoRelationPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<MetaOntoRelationEntity> page=new Page<>(metaOntoRelationPagination.getCurrentPage(), metaOntoRelationPagination.getPageSize());
            IPage<MetaOntoRelationEntity> userIPage=this.page(page,queryWrapper);
            return metaOntoRelationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public MetaOntoRelationEntity getInfo(Long id){
        QueryWrapper<MetaOntoRelationEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(MetaOntoRelationEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(MetaOntoRelationEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, MetaOntoRelationEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(MetaOntoRelationEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public List<MetaOntoRelationInfoVO> getList1(MetaOntoRelationPagination metaOntoRelationPagination) {
	        QueryWrapper<MetaOntoRelationEntity> queryWrapper=new QueryWrapper<>();
	        //关键字（账户、姓名、手机）
	        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getId()))){
	            queryWrapper.lambda().and(t->t.eq(MetaOntoRelationEntity::getId,metaOntoRelationPagination.getId()));
	        }

	        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getOnto1Id()))){
	            queryWrapper.lambda().and(t->t.eq(MetaOntoRelationEntity::getOnto1Id,metaOntoRelationPagination.getOnto1Id()));
	        }

	        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getOnto2Id()))){
	            queryWrapper.lambda().and(t->t.eq(MetaOntoRelationEntity::getOnto2Id,metaOntoRelationPagination.getOnto2Id()));
	        }

	        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getName()))){
	            queryWrapper.lambda().and(t->t.like(MetaOntoRelationEntity::getName,metaOntoRelationPagination.getName()));
	        }

	        if(!"null".equals(String.valueOf(metaOntoRelationPagination.getPid()))){
	           // queryWrapper.lambda().and(t->t.eq(MetaOntoRelationEntity::getPid,metaOntoRelationPagination.getPid()));
	            queryWrapper.eq(" t.pid ",metaOntoRelationPagination.getPid());
	        }
	        //排序
	        if(StrUtil.isEmpty(metaOntoRelationPagination.getSidx())){
	            queryWrapper.lambda().orderByDesc(MetaOntoRelationEntity::getId);
	        }else{
	            queryWrapper="asc".equals(metaOntoRelationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(metaOntoRelationPagination.getSidx()):queryWrapper.orderByDesc(metaOntoRelationPagination.getSidx());
	        }
            List<MetaOntoRelationInfoVO>  list =  null;
            if(metaOntoRelationPagination.getCurrentPage()==0){
               list = metaOntoRelationMapper.ontolist(queryWrapper);
            }else{
                Page<MetaOntoRelationEntity> page=new Page<>(metaOntoRelationPagination.getCurrentPage(), metaOntoRelationPagination.getPageSize());
                //   IPage<MetaOntoRelationEntity> userIPage=metaEntityRelationMapper.ontolist(page,queryWrapper);
                list = metaOntoRelationMapper.ontolist(page,queryWrapper);
                 metaOntoRelationPagination.setData(page.getRecords(),page.getTotal());
            }
	        return list;

	}
	@Override
	public List<MetaOntoRelationInfoVO> getList2(Long onto_id) {
		List<LibConceptEntity>  ontoList = libConceptService.getAllSubNode(onto_id);
		StringBuffer  stb = new StringBuffer();
		for (int i = 0; i < ontoList.size(); i++) {
			stb.append(ontoList.get(i).getId()).append(",");
		}
		String ids = stb.toString().substring(0,stb.length()-1);
		QueryWrapper<MetaOntoRelationEntity> queryWrapper=new QueryWrapper<>();
		//queryWrapper.last(" id in ( "+ids+" ) ");
		queryWrapper.in("c1.id", ids);
		
		List<MetaOntoRelationInfoVO>  list = metaOntoRelationMapper.ontolist(queryWrapper);
		
		return list;
	}

    @Override
    public List<MetaOntoRelationInfoVO> getListByOnto1Id(Long ontoId) {
        QueryWrapper<MetaOntoRelationEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().and(t->t.eq(MetaOntoRelationEntity::getOnto1Id,ontoId));
        return metaOntoRelationMapper.ontolist(queryWrapper);
    }
}