package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.plugin.excel.vo.ErrorMessage;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.DomainThesaurusEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.DomainThesaurusExcel;
import com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.DomainThesaurusPagination;
import org.springframework.validation.BindingResult;

import java.util.*;

/**
 *
 * domain_thesaurus
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-14 15:44:58
 */
public interface DomainThesaurusService extends IService<DomainThesaurusEntity> {

    List<DomainThesaurusEntity> getList(DomainThesaurusPagination domainThesaurusPagination);

    List<DomainThesaurusEntity> getTypeList(DomainThesaurusPagination domainThesaurusPagination,String dataType);

    DomainThesaurusEntity getInfo(Long id);

    void delete(DomainThesaurusEntity entity);

    void create(DomainThesaurusEntity entity);

    boolean update( Long id, DomainThesaurusEntity entity);

    R<List<ErrorMessage>> importDate(List<DomainThesaurusExcel> excelList, BindingResult bindingResult);

    List<DomainThesaurusExcel> getExportList(String category,String name);
}
