package com.sinosoft.ie.booster.yearbook.database.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.database.entity.DataConnectionEntity;
import com.sinosoft.ie.booster.yearbook.database.model.dataconnection.DataConnectionPagination;

import java.util.*;

/**
 *
 * data_connection
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-06 15:01:22
 */
public interface DataConnectionService extends IService<DataConnectionEntity> {

    List<DataConnectionEntity> getList(DataConnectionPagination dataConnectionPagination);

    List<DataConnectionEntity> getTypeList(DataConnectionPagination dataConnectionPagination,String dataType);



    DataConnectionEntity getInfo(Long id);

    void delete(DataConnectionEntity entity);

    void create(DataConnectionEntity entity);

    boolean update( Long id, DataConnectionEntity entity);
    
//  子表方法
}
