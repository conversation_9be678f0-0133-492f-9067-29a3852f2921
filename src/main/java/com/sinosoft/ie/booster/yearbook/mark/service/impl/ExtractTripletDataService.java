package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;
import com.sinosoft.ie.booster.yearbook.mark.mapper.MarkSentenceMapper;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkDatasetService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkEntityService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkTripletService;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EnableAsync
@Service
public class ExtractTripletDataService {
	@Resource
	private MakrSentenceService sentenceService;
	@Resource
	private ProjectConfig projectConfig;
	@Resource
	private LibConceptService libConceptService;
	@Resource
	private MarkEntityService markEntityService;
	@Resource
	private MarkTripletService markTripletService;
	@Resource
	private MetaOntoRelationService metaOntoRelationService;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	/**
	 * 抽取三元组数据
	 */
	@Transactional
	@Async("taskExecutor")
	public void extractTriplet(MarkSentenceEntity ent) {
		if (ent != null) {
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("content", ent.getSentenceExc());
			paramMap.put("page_num", ent.getFsort());
			HttpResponse res = HttpRequest.post(projectConfig.getTripletUrl()).form(paramMap).execute();
			if(res.getStatus()!=200) {
				log.info("抽取三元组数据返回报错：{}",res.getStatus() );
				return ;
			}
			String result_body = res.body();
			log.info("解析结果--->" + result_body);
			JSONObject jsonObject = JSONUtil.parseObj(result_body);
			Integer code = jsonObject.getInt("code");
			// 状态码正确200；否则500
			if (code.equals(500)) {
				ent.setStatus(YearbookConstant.MARK_STATS_ERROR);
			} else {
				JSONObject result = jsonObject.getJSONObject("result");
				log.info("当前返回的三元组json--->");
				log.info(result.toString());
				JSONArray entList = result.getJSONArray("ent");
				JSONArray relList = result.getJSONArray("rel");

				List<MarkEntityEntity> all_ent = new ArrayList<MarkEntityEntity>();

				// 循环保存实体
				for (int i = 0; i < entList.size(); i++) {
					JSONObject entObj = (JSONObject) entList.get(i);
					MarkEntityEntity markent =  convertJson(entObj);
					//通过本体名称获取本体对象
					LibConceptEntity ontoEnt = OntoCache.getOntoCache(markent.getOntoname());
					markent.setTagId(ontoEnt.getId());
					markent.setSentenceid(ent.getId());
					markEntityService.save(markent);
					all_ent.add(markent);
				}
				// 循环保存关系
				for (int i = 0; i < relList.size(); i++) {
					JSONObject entObj = (JSONObject) relList.get(i);
					String  relationName = entObj.getStr("name");
					JSONObject from_josn = entObj.getJSONObject("from");
					JSONObject to_josn = entObj.getJSONObject("to");
					MarkEntityEntity from_ent =  convertJson(from_josn);
					MarkEntityEntity to_ent =  convertJson(to_josn);
					to_ent.setSentenceid(ent.getId());
					MarkEntityEntity ent_from_ent = getMarkEntity(all_ent,from_ent);
					if(ent_from_ent==null) {
						continue;
					}
					//创建三元组关系
					MarkTripletEntity  triEnt =  new MarkTripletEntity();
					triEnt.setSentenceid(ent.getId());
					triEnt.setTag1id(ent_from_ent.getId());
					triEnt.setRelationname(relationName);
					//如果是实体
					if(to_ent.getLabType()==1) {
						//通过本体名称获取本体对象
						LibConceptEntity ontoEnt = OntoCache.getOntoCache(to_ent.getOntoname());
						to_ent.setTagId(ontoEnt.getId());
						MarkEntityEntity ent_to_ent = getMarkEntity(all_ent,to_ent);
						triEnt.setTag2id(ent_to_ent.getId());
						
						List<MetaOntoRelationEntity>   relationList = OntoCache.getMetaOntoRelationListByOnto1Id(ent_from_ent.getTagId());
						for(MetaOntoRelationEntity relation:relationList) {
							if(relation.getName().equals(relationName)) {
								triEnt.setRelationid(relation.getId());
							}
						}
					}
					//如果是属性
					if(to_ent.getLabType()==2) {
						//获取左本体的所有属性,包括继承自父级的属性
						log.error(ent_from_ent.getText());
						List<MateAttributeEntity>  attrList = OntoCache.getAttListByOntoId(ent_from_ent.getTagId());
						for (MateAttributeEntity attEnt : attrList) {
							//如果在属性集合中找到关系名称则赋值给TagId
							if(relationName.equals(attEnt.getNameCn())) {
								to_ent.setTagId(attEnt.getId());
								triEnt.setRelationid(attEnt.getId());
								//如果属性名称==事件内容
								if(relationName.equals(YearbookConstant.MARK_EVENT_CONTENT)) {
									//保存第一个字
									Integer sidex = to_ent.getStartindex();
									Integer endidx = to_ent.getEndindex();
									to_ent.setStartindex(sidex);
									to_ent.setEndindex(sidex+1);
									markEntityService.save(to_ent);
									triEnt.setTag2id(to_ent.getId());
									markTripletService.save(triEnt);
									//保存最后一个字
									to_ent.setId(null);
									to_ent.setStartindex(endidx-1);
									to_ent.setEndindex(endidx);
									markEntityService.save(to_ent);
									triEnt.setTag2id(to_ent.getId());
									triEnt.setId(null);
									markTripletService.save(triEnt);
								}else {
									markEntityService.save(to_ent);
									triEnt.setTag2id(to_ent.getId());
								}
								break;
							}
						}
					}
					//如果属性==事件内容，则添加完数据了，就continue
					if(relationName.equals(YearbookConstant.MARK_EVENT_CONTENT)) {
						continue;
					}
					//如果是可选择属性值
					if(to_ent.getLabType()==3) {
						//获取左本体的所有属性,包括继承自父级的属性
						List<MateAttributeEntity>  attrList = OntoCache.getAttListByOntoId(ent_from_ent.getTagId());
						for (MateAttributeEntity attEnt : attrList) {
							//如果在属性集合中找到关系名称则赋值TagPid
							if(relationName.equals(attEnt.getNameCn())) {
								to_ent.setTagPid(attEnt.getId());
								String  val_txt =  to_ent.getText();
								//根据属性id查询值
								log.info("attEnt.getId()--->{}",attEnt.getId());
								List<MateAttributeValueEntity>  valList = OntoCache.getAttrValListByAttrId(attEnt.getId());
								for(MateAttributeValueEntity valent : valList) {
									if(val_txt.equals(valent.getValName())) {
										to_ent.setTagId(valent.getId());
										markEntityService.save(to_ent);
										triEnt.setTag2id(to_ent.getId());
										triEnt.setRelationid(attEnt.getId());
										break;
									}
								}
								break;
							}
						}
					}
					//如果是手动录入的属性值
//					if(to_ent.getLabType()==4) {
//						//获取左本体的所有属性,包括继承自父级的属性
//						List<MateAttributeEntity>  attrList = OntoCache.getAttListByOntoId(ent_from_ent.getTagId());
//						for (MateAttributeEntity attEnt : attrList) {
//							//如果在属性集合中找到关系名称则赋值TagPid
//							if(relationName.equals(attEnt.getNameCn())) {
//								to_ent.setTagId(attEnt.getId());
//								markEntityService.save(to_ent);
//								triEnt.setTag2id(to_ent.getId());
//								triEnt.setRelationid(attEnt.getId());
//								break;
//							}
//						}
//					}
					//labtype = 4 也按照 属性枚举值处理
					//页码
					if(to_ent.getLabType()==4) {
						List<MateAttributeEntity>  attrList = OntoCache.getAttListByOntoId(ent_from_ent.getTagId());
						for (MateAttributeEntity attEnt : attrList) {
							if(relationName.equals(attEnt.getNameCn())) {
								String num =  to_ent.getOntoname().split(":")[1];
							//	Integer number = Integer.parseInt(num);
								QueryWrapper<MateAttributeValueEntity> query = new QueryWrapper<MateAttributeValueEntity>();
								query.eq("val_name", num);
								query.eq("attribute_id", attEnt.getId());
								List<MateAttributeValueEntity> list =mateAttributeValueService.list(query);
								MateAttributeValueEntity vent = null ;
								if(list.size()==0) {
									vent =  new MateAttributeValueEntity();
									vent.setAttributeId(attEnt.getId());
									vent.setOntoId(attEnt.getOnto());
									vent.setSchemaId(attEnt.getSchemaId());
									vent.setValName(num);
									mateAttributeValueService.save(vent);
								}else {
									vent =  list.get(0);
								}
								to_ent.setTagId(vent.getId());
								to_ent.setTagPid(attEnt.getId());
								markEntityService.save(to_ent);
								triEnt.setTag2id(to_ent.getId());
								triEnt.setRelationid(attEnt.getId());
								break;
								
							}
						}
						
					}
					
					
					if(triEnt.getRelationid()==null || triEnt.getRelationname()==null ||
							triEnt.getTag1id()==null || triEnt.getTag2id()==null) {
						continue;
					}
					markTripletService.save(triEnt);
				}
			}
			ent.setUpdateTime(LocalDateTime.now());
			ent.setStatus(YearbookConstant.MARK_STATS_EXT_COMPLETED);
			sentenceService.updateById(ent);
		}
	}

	/**
	 * 将json格式数据转成对象
	 * @param entObj
	 */
	public MarkEntityEntity    convertJson(JSONObject entObj) {
		// "实体"或"属性"
		String tagname = entObj.getStr("tagname");
		// 本体名称
		String tagonto = entObj.getStr("tagonto");
		// 抽取的文本词
		String lab_text = entObj.getStr("text");
		//抽取文本索引
		String startidx = entObj.getStr("startidx");
		String endidx = entObj.getStr("endidx");
		// 保存抽取的数据
		MarkEntityEntity markent = new MarkEntityEntity();
		if(YearbookConstant.MARK_ENTITY.equals("tagname")) {
			markent.setLabType(1);
		}
		if(YearbookConstant.MARK_ATTRIBUTE.equals("tagname")) {
			markent.setLabType(2);
		}
		String labType = entObj.getStr("labType");
		markent.setLabType(Integer.parseInt(labType));
		markent.setTagname(tagname);
		markent.setText(lab_text);
		markent.setOntoname(tagonto);
		markent.setStartindex(Integer.parseInt(startidx));
		markent.setEndindex(Integer.parseInt(endidx));
		//查询并保存本体id
		
		if(tagonto.equals("UNKNOWN")) {
			return markent;
		}
		// 从缓存读取当前实体的本体数据
		LibConceptEntity ontoEntity = OntoCache.getOntoCache(tagonto);
		// 如果没有则查询
		if (ontoEntity == null) {
			QueryWrapper<LibConceptEntity> query = new QueryWrapper<LibConceptEntity>();
			query.eq("name", tagonto);
			List<LibConceptEntity> onlist = libConceptService.list(query);
			if (onlist.size() != 0) {
				ontoEntity = onlist.get(0);
				markent.setTagId(ontoEntity.getId());
			}
		}
		return markent;
	}
	
	/**
	 * 判断实体列表中是否包含当前的实体,则返回实体
	 * @param entlist
	 * @param ent
	 * @return
	 */
	public MarkEntityEntity   getMarkEntity(List<MarkEntityEntity>  entlist , MarkEntityEntity ent) {
		MarkEntityEntity  res_ent =  null;
		//循环实体集合
		for (int i = 0; i < entlist.size(); i++) {
			MarkEntityEntity item = entlist.get(i);
			if(item.comparison(ent)) {
				res_ent = item;
				break;
			}
		}
		return res_ent;
	}
	

}
