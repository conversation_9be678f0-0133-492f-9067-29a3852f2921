package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-01 11:34:49
 */
@Data
@TableName("filecontent")
@ApiModel(value = "")
public class FilecontentEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 抽取后的数据
     */
    @ApiModelProperty(value = "抽取后的数据")
    private String contentExc;

    /**
     * 文档类型1word;2pdf
     */
    @ApiModelProperty(value = "文档类型1word;2pdf")
    private Integer type;

    /**
     * 原文件名
     */
    @ApiModelProperty(value = "原文件名")
    private String oldname;

    /**
     * 新文件名
     */
    @ApiModelProperty(value = "新文件名")
    private String newname;
    @ApiModelProperty(value = "状态")
    private Integer status;
    
}
