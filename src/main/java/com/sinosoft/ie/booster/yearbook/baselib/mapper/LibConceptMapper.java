package com.sinosoft.ie.booster.yearbook.baselib.mapper;

import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 *
 * 本体概念类
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-30 09:58:21
 */
@Mapper
public interface LibConceptMapper extends BaseMapper<LibConceptEntity> {
	
	
	@Select(value="select  * from lib_concept t where t.pid in (${ids})")
	public List<LibConceptEntity>  getListByIds(String ids);
	

}
