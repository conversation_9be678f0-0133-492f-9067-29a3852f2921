package com.sinosoft.ie.booster.yearbook.search.service.impl;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.LibMinEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveThemeMapper;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper;
import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeEventMapper;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.search.service.SearchGraphService;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

@Service
public class SearchGraphServiceImpl  implements SearchGraphService{
    
    @Resource
    private MetaEntityRelationMapper metaEntityRelationMapper;
    @Resource
    private LibEntityMapper libEntityMapper;
    @Resource
    private EveThemeMapper eveThemeMapper;
    @Resource
    private GraphThemeEventMapper graphThemeEventMapper;
    @Resource
    private MateAttributeValueMapper mateAttributeValueMapper;
    @Resource
    private ResearchTagMapper researchTagMapper;
	@Resource
	private LibEntityService libEntityService;
	
	
	@Override
	public R humanGraph(Long entId) {
		String entityId =  entId.toString();
        String orgOntoIds = YearbookConstant.GRAPH_HUMAN_STR;
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_VALUE_LIST;
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        LibEntityEntity libEntity = libEntityMapper.selectById(entId);
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",entityId);
        nodesMap.put("name",libEntity.getName());
        nodesMap.put("onto",libEntity.getOntoId());
        nodesMap.put("isMain","1");
        if (libEntity.getOntoId().equals(YearbookConstant.MARK_PER_ONTO_ID)){
            if (libEntity.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
                if(stringObjectMap.get("image_ids")!=null) {
                	 nodesMap.put("image_ids",stringObjectMap.get("image_ids").toString());
                }
            }
        }
        nodesList.add(nodesMap);

        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
//        eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
        eventQuery.and(i -> i.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID));
        eventQuery.and(i -> i.like("json_query(data,'$.person_ids')",entityId).or()
                .like("json_query(data,'$.person_main')",entityId).or()
                .like("json_query(data,'$.person_related')",entityId).or()
                .like("json_query(data,'$.person_cooperation')",entityId).or()
                .like("json_query(data,'$.person_exchange')",entityId).or()
                .like("json_query(data,'$.person_achievements')",entityId));
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        //查询实体节点
        Map<String,Object> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        if(nodesEntityList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        nodesList.addAll(nodesEntityList);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            boolean ontoHave = false;
            for (Map<String, Object> relationMap:linksRelationList) {
                if (relationMap.get("source").toString().equals(entityId) &&
                        relationMap.get("targetOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("source",ontoMap.get("id"));
                    ontoHave = true;
                    continue;
                }
                if (relationMap.get("target").toString().equals(entityId) &&
                        relationMap.get("sourceOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("target",ontoMap.get("id"));
                    ontoHave = true;
                }
            }
            if (!ontoHave){continue;}
            nodesList.add(ontoMap);
            //到本体的links
            Map<String,Object> linksmap = new HashMap<>();
            linksmap.put("source",entityId);
            linksmap.put("target",ontoMap.get("id").toString());
            linksmap.put("value","");
            linksmap.put("eventid","");
            linksList.add(linksmap);
        }
        linksList.addAll(linksRelationList);

        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        retrunMap.put("links",linksList);
        //去掉和中心实体没关联的数据
        List<Map<String,Object>> nodesListNew  =   removeNoRelationEntity(nodesList,nodesOntoList,entityId.toString(),linksList);
        retrunMap.put("nodes",nodesListNew);
        
        
        Map<String,Object>  nodeListMap =  graphDataList(nodesListNew,libEntity);
        retrunMap.put("nodeListMap", nodeListMap);
		return R.ok(retrunMap);
	}

	@Override
	public R orgGraph(Long entId) {
		String entityId =  entId.toString();

        String orgOntoIds = YearbookConstant.GRAPH_ORG_STR;
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",entityId);
        nodesMap.put("name",libEntity.getName());
        nodesMap.put("onto",libEntity.getOntoId());
        nodesMap.put("isMain","1");
        if (libEntity.getOntoId().equals(YearbookConstant.MARK_ORG_ONTO_ID)){
            if (libEntity.getData() != null){
                Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntity.getData());
                nodesMap.put("image_ids",stringObjectMap.get("icon"));
            }
        }
        nodesList.add(nodesMap);

        //查询事件
        QueryWrapper<LibEntityEntity> eventQuery = new QueryWrapper<>();
        eventQuery.select("id");
//        eventQuery.eq("json_value(data,'$.org2_id')",org.getId());
        eventQuery.eq("json_value(data,'$.org_ids')",entityId);
        eventQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        List<LibEntityEntity> eventList = libEntityMapper.selectList(eventQuery);
        if (eventList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        //查询实体节点
        Map<String,String> querymap = new HashMap<>();
        querymap.put("entityId",entityId);
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEventsOrg(querymap);
        if(nodesEntityList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        nodesList.addAll(nodesEntityList);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        eventMap.put("entityId",entityId);
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEventsOrg(eventMap);
//        linksRelationList = linksRelationList.stream().filter(distinctByKey(o -> o.get("target") )).collect(Collectors.toList());
        linksList.addAll(linksRelationList);

        //研究所的集合
        Map<String,Object> orgQuery = new HashMap<>();
        orgQuery.put("ontoId",YearbookConstant.MARK_EVENT_ONTO_ID);
        orgQuery.put("orgId",entityId);
        List<LibEntityEntity> orgList = libEntityMapper.orgGraphszs(orgQuery);
        for (LibEntityEntity org:orgList) {
            Map<String, Object> orgMap = new HashMap<>();
            orgMap.put("entity1Id",entityId);
            orgMap.put("entity2Id",org.getId());
            List<Map<String, Object>> eventRelationlist = metaEntityRelationMapper.aiGraphGetRelationByEventsOrgMain(orgMap);
            if (eventRelationlist.size() == 0){
                continue;
            }
            //研究所的节点
            Map<String, Object> nodeMap = new HashMap<>();
            nodeMap.put("id",org.getId());
            nodeMap.put("name",org.getName());
            nodeMap.put("onto",YearbookConstant.MARK_ORG_ONTO_ID);
            nodeMap.put("image_ids",null);
            nodeMap.put("isMain","1");
            nodesList.add(nodeMap);
            //到研究所的links
//            Map<String,Object> linksmapyjs = new HashMap<>();
//            linksmapyjs.put("source",entityId);
//            linksmapyjs.put("target",org.getId());
//            linksmapyjs.put("value","父子机构");
//            linksmapyjs.put("eventid",eventRelationlist.get(0).get("eventid"));
            linksList.add(eventRelationlist.get(0));
        }

        //long 2 string
        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        //去掉和中心实体没关联的数据
        List<Map<String,Object>> nodesListNew = removeNoRelationEntityOrg(nodesList,entityId,linksList);
        retrunMap.put("nodes",nodesListNew);
        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
		
        
        Map<String,Object>  nodeListMap =  graphDataList(nodesListNew,libEntity);
        retrunMap.put("nodeListMap", nodeListMap);
        
        return R.ok(retrunMap);
    
	}

	@Override
	public R themeGraph(Long entityId) {

        //查询本身节点
        LibEntityEntity libEntity = libEntityMapper.selectById(entityId);
        String theme = libEntity.getName();
       
        String orgOntoIds = YearbookConstant.GRAPH_THEME_STR;
        List<Map<String, Object>> nodesOntoList = YearbookConstant.GRAPH_THEME_LIST;
        Map<String,Object> retrunMap = new HashMap<>();
        List<Map<String,Object>> nodesList = new ArrayList<>();
        List<Map<String,Object>> linksList = new ArrayList<>();
        //查询本身节点
        QueryWrapper<LibEntityEntity> themeQuery = new QueryWrapper<>();
        themeQuery.eq("name",theme);
        themeQuery.eq("onto_id",YearbookConstant.MARK_THEME_ONTO_ID);
        List<LibEntityEntity> libEntityList = libEntityMapper.selectList(themeQuery);
        if (libEntityList.size() == 0){
            //本身节点
            Map<String, Object> nodesMap = new HashMap<>();
            nodesMap.put("id","1");
            nodesMap.put("name",theme);
            nodesMap.put("isMain","1");
            nodesList.add(nodesMap);
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }

        Map<String, Object> nodesMap = new HashMap<>();
        nodesMap.put("id",libEntityList.get(0).getId());
        nodesMap.put("name",theme);
        nodesMap.put("onto",YearbookConstant.MARK_THEME_ONTO_ID);
        nodesMap.put("isMain","1");
        nodesList.add(nodesMap);

        //查询事件
        QueryWrapper<LibEntityEntity> libEntityEntityQuery = new QueryWrapper<>();
        libEntityEntityQuery.eq("onto_id",YearbookConstant.MARK_EVENT_ONTO_ID);
        libEntityEntityQuery.apply("json_value(data,'$.theme')= '"+ theme +"'");
        List<LibEntityEntity> eventList = libEntityMapper.selectList(libEntityEntityQuery);
        if (eventList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        //查询实体节点
        Map<String,Object> querymap = new HashMap<>();
        querymap.put("ontoId",orgOntoIds);
        String eventids = "";
        for (LibEntityEntity event:eventList) {
            if (!eventids.isEmpty()){
                eventids += ",";
            }
            eventids += event.getId();
        }
        querymap.put("eventids",eventids);
        List<Map<String, Object>> nodesEntityList = metaEntityRelationMapper.aiGraphGetEntityByEvents(querymap);
        if(nodesEntityList.size() == 0){
            retrunMap.put("nodes",nodesList);
            retrunMap.put("links",linksList);
            return R.ok(retrunMap);
        }
        nodesList.addAll(nodesEntityList);
        //关系
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("eventids",eventids);
        eventMap.put("ontoId",orgOntoIds);
        List<Map<String, Object>> linksRelationList = metaEntityRelationMapper.aiGraphGetRelationByEvents(eventMap);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            boolean ontoHave = false;
            for (Map<String, Object> relationMap:linksRelationList) {
                if (relationMap.get("source").toString().equals(libEntityList.get(0).getId().toString()) &&
                        relationMap.get("targetOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("source",ontoMap.get("id"));
                    ontoHave = true;
                    continue;
                }
                if (relationMap.get("target").toString().equals(libEntityList.get(0).getId().toString()) &&
                        relationMap.get("sourceOnto").toString().equals(ontoMap.get("id").toString())){
                    relationMap.put("target",ontoMap.get("id"));
                    ontoHave = true;
                }
            }
            if (!ontoHave){continue;}
            nodesList.add(ontoMap);
            //到本体的links
            Map<String,Object> linksmap = new HashMap<>();
            linksmap.put("source",libEntityList.get(0).getId().toString());
            linksmap.put("target",ontoMap.get("id").toString());
            linksmap.put("value","");
            linksmap.put("eventid","");
            linksList.add(linksmap);
        }
        linksList.addAll(linksRelationList);

        //物资
        Map<String, Object> ontoMap = new HashMap<>();
        ontoMap.put("id",YearbookConstant.MARK_ARTICLES_ONTO_ID);
        ontoMap.put("name","物资");
        nodesList.add(ontoMap);
        //到本体的links
        Map<String,Object> linksmap = new HashMap<>();
        linksmap.put("source",libEntityList.get(0).getId().toString());
        linksmap.put("target",YearbookConstant.MARK_ARTICLES_ONTO_ID.toString());
        linksmap.put("value","");
        linksmap.put("eventid","");
        linksList.add(linksmap);
        QueryWrapper<LibEntityEntity> themeEntityQuery = new QueryWrapper<>();
        themeEntityQuery.eq("onto_id",YearbookConstant.MARK_THEME_ONTO_ID);
        themeEntityQuery.eq("name",theme);
        themeEntityQuery.apply("json_value(data,'$.goods') is not null");
        List<LibEntityEntity> themeEntityList = libEntityMapper.selectList(themeEntityQuery);
        int i = 0;
        if (themeEntityList.size()>0){
            Map<String, Object> dataMap = JsonUtil.stringToMap(themeEntityList.get(0).getData());
            if (dataMap.get("goods") == null){return null;}
            if (dataMap.get("goods").toString().isEmpty()){return null;}
                String[] goods = dataMap.get("goods").toString().split(";");
                for (String good:goods) {
                    Map<String, Object> nodesMapgoods = new HashMap<>();
                    nodesMapgoods.put("id",Long.valueOf(YearbookConstant.MARK_ARTICLES_ONTO_ID.toString().substring(5)+i));
                    nodesMapgoods.put("name",good);
                    nodesMapgoods.put("onto",YearbookConstant.MARK_ARTICLES_ONTO_ID);
                    nodesMapgoods.put("eventid","");
                    nodesList.add(nodesMapgoods);
                    //links
                    Map<String,Object> linksmapgoods = new HashMap<>();
                    linksmapgoods.put("source",YearbookConstant.MARK_ARTICLES_ONTO_ID);
                    linksmapgoods.put("target",Long.valueOf(YearbookConstant.MARK_ARTICLES_ONTO_ID.toString().substring(5)+i));
                    linksmapgoods.put("value","");
                    linksmapgoods.put("eventid","");
                    linksList.add(linksmapgoods);
                    i++;
                }
        }

        //经费
        Map<String, Object> ontoMapFunds = new HashMap<>();
        ontoMapFunds.put("id",YearbookConstant.MARK_FUNDS_ONTO_ID.toString());
        ontoMapFunds.put("name","经费");
        nodesList.add(ontoMapFunds);
        //到本体的links
        Map<String,Object> linksmapFunds = new HashMap<>();
        linksmapFunds.put("source",libEntityList.get(0).getId().toString());
        linksmapFunds.put("target",YearbookConstant.MARK_FUNDS_ONTO_ID.toString());
        linksmapFunds.put("value","");
        linksmapFunds.put("eventid","");
        linksList.add(linksmapFunds);
        QueryWrapper<LibEntityEntity> themeEntityQueryFunds = new QueryWrapper<>();
        themeEntityQueryFunds.eq("onto_id",YearbookConstant.MARK_THEME_ONTO_ID);
        themeEntityQueryFunds.eq("name",theme);
        themeEntityQueryFunds.apply("json_value(data,'$.funds_str') is not null");
        List<LibEntityEntity> themeEntityListFunds = libEntityMapper.selectList(themeEntityQueryFunds);
        int iFunds = 0;
        if (themeEntityListFunds.size()>0){
            Map<String, Object> dataMap = JsonUtil.stringToMap(themeEntityListFunds.get(0).getData());
            if (dataMap.get("funds_str") == null){return null;}
            if (dataMap.get("funds_str").toString().isEmpty()){return null;}
//            for (LibEntityEntity event:eventList) {
            String[] goods = dataMap.get("funds_str").toString().split(";");
            for (String good:goods) {
                Map<String, Object> nodesMapgoods = new HashMap<>();
                nodesMapgoods.put("id",Long.valueOf(YearbookConstant.MARK_FUNDS_ONTO_ID.toString().substring(5)+iFunds));
                nodesMapgoods.put("name",good);
                nodesMapgoods.put("onto",YearbookConstant.MARK_FUNDS_ONTO_ID);
                nodesMapgoods.put("eventid","");
                nodesList.add(nodesMapgoods);
                //links
                Map<String,Object> linksmapgoods = new HashMap<>();
                linksmapgoods.put("source",YearbookConstant.MARK_FUNDS_ONTO_ID);
                linksmapgoods.put("target",Long.valueOf(YearbookConstant.MARK_FUNDS_ONTO_ID.toString().substring(5)+iFunds));
                linksmapgoods.put("value","");
                linksmapgoods.put("eventid","");
                linksList.add(linksmapgoods);
                iFunds++;
            }
        }

        linksList = linkLongToStr(linksList);
        nodesList = nodeLongToStr(nodesList);
        linksList = linkOnlyOne(linksList);
        //根据id去重
        nodesList = nodesList.stream().filter(distinctByKey(o -> o.get("id") )).collect(Collectors.toList());
        //去掉和中心实体没关联的数据
        List<Map<String,Object>> nodesListNew = removeNoRelationEntity(nodesList,nodesOntoList,libEntityList.get(0).getId().toString(),linksList);
        retrunMap.put("nodes",nodesListNew)  ;

        for (Map<String, Object> linkrelation:linksList) {
            if (linkrelation.get("pageNumber") == null){continue;}
            MateAttributeValueEntity mateAttributeValue = mateAttributeValueMapper.selectById((Serializable) linkrelation.get("pageNumber"));
            if (mateAttributeValue == null || mateAttributeValue.getValName() == null){
                linkrelation.put("pageNumber","");
            }else {
                linkrelation.put("pageNumber",mateAttributeValue.getValName());
            }
        }
        retrunMap.put("links",linksList);
        
        Map<String,Object>  nodeListMap =  graphDataList(nodesListNew,libEntity);
        retrunMap.put("nodeListMap", nodeListMap);
        
        return R.ok(retrunMap);
    
	}

	
	
	public Map<String, Object>    graphDataList(List<Map<String,Object>> nodesList,LibEntityEntity libEntity) {
		Map<String, Object> retrunMap = new HashMap<>();
		List<Long> ids = new ArrayList<Long>();
		
		List<LibMinEntity> foundlist = new ArrayList<LibMinEntity>();
		List<LibMinEntity> goodslist = new ArrayList<LibMinEntity>();
		for (int i = 0; i < nodesList.size(); i++) {
			Map<String, Object> map = nodesList.get(i);
			Long id = Long.valueOf(map.get("id").toString());
			ids.add(id);
			
			System.out.print(map.toString());
			Object onto_obj =  map.get("onto");
			if(onto_obj!=null) {
				Long onto_id  = Long.valueOf(onto_obj.toString());
				if(onto_id.equals(YearbookConstant.MARK_FUNDS_ONTO_ID)) {
					LibMinEntity ent = new LibMinEntity();
					String name  = String.valueOf(map.get("name").toString());
					ent.setName(name);
					ent.setId(id.toString());
					foundlist.add(ent);
					continue;
				}
				if(onto_id.equals(YearbookConstant.MARK_ARTICLES_ONTO_ID)) {
					LibMinEntity ent = new LibMinEntity();
					String name  = String.valueOf(map.get("name").toString());
					ent.setName(name);
					ent.setId(id.toString());
					goodslist.add(ent);
					continue;
				}
			}
		}
		retrunMap.put( "funds" , foundlist);
		retrunMap.put( "goods" , goodslist);
		
		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.in("id", ids);
		List<LibEntityEntity> list = libEntityService.list(query);

		Map<Long, List<LibEntityEntity>> map = list.stream().collect(Collectors.groupingBy(LibEntityEntity::getOntoId));
		for (Long onto : map.keySet()) {
			List<LibEntityEntity> entList = map.get(onto);
			List<LibMinEntity> relist = new ArrayList<LibMinEntity>();
			
			for (int i = 0; i < entList.size(); i++) {
				LibEntityEntity x  =  entList.get(i);
				
				if(!ObjectUtil.isEmpty(x.getData())) {
					if(!libEntity.getId().equals(x.getId())){
						LibMinEntity ent = new LibMinEntity();
						ent.setId(x.getId().toString());
						ent.setName(x.getName());
						//  如果是机构图谱 显示子机构  是否有图片和机构等级两个字段
						if(libEntity.getOntoId().equals(YearbookConstant.MARK_ORG_ONTO_ID) && onto.equals(YearbookConstant.MARK_ORG_ONTO_ID)) {
							JSONObject json =  JSONUtil.parseObj(x.getData());
							Long pid =  json.getLong("pid");
							if(pid!=null && pid.equals(libEntity.getId())) {
								Long org_img_id = json.getLong("icon"); 
								if(org_img_id!=null) {
									ent.setImg(org_img_id.toString());
									ent.setPortrait(1);
								}else {
									relist.add(ent);
									continue;
								}
							}
							continue;
						}
						if(onto.equals(YearbookConstant.MARK_PER_ONTO_ID)) {
							JSONObject json =  JSONUtil.parseObj(x.getData());
							Object img_obj =  json.get("image_ids");
							if(img_obj!=null) {
								ent.setPortrait(1);
								Long image_id = (Long)img_obj;
								ent.setImg(image_id.toString());
							}
						}
						relist.add(ent);
					}
				}
			}
			retrunMap.put(getEntityCoder(onto.toString()), relist);
		}
		return retrunMap;
	}
	
	
	
	
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
    Set<Long> orgGraphGetEntity(Object object,Set<Long> entityids){
        if (object == null){
            return entityids;
        }
        if (object instanceof Long){
            entityids.add((long) object);
        }
        if (object.getClass().getName() == "com.alibaba.fastjson.JSONArray"){
            JSONArray jsonArray = (JSONArray) object;
            if (jsonArray.size() == 0){
                return entityids;
            }
            for (Object id:jsonArray) {
                entityids.add((long) id);
            }
        }
        return entityids;
    }
	public String getEntityCoder(String id) {
		String code = "";
		switch (id) {
		case "1586970280968970242":
			code = "per";
			break;
		case "1586973763214368769":
			code = "org";
			break;
		case "1655899624481320962":
			code = "theme";
			break;
		case "1586968527091392513":
			code = "result";
			break;
		case "1586999607391420418":
			code = "project";
			break;
		case "1649952061550292993":
			code = "team";
			break;
		case "1625418542513926146":
			code = "prize";
			break;
		case "1604454992306974721":
			code = "funds";
			break;
		case "1604454992306974722":
			code = "goods";
			break;	
		case "16305538085043240981":
			code = "obj";
		case "1623985394850742273":
			code = "active";
			break;
		}
		return code;
	}
	
	public List<Map<String,Object>>  linkLongToStr(List<Map<String,Object>> linksList ){
		for (int i = 0; i < linksList.size(); i++) {
			Map<String,Object>  map =  linksList.get(i);
			Object source = map.get("source");
			if( source!=null &&  source instanceof Long) {
				map.put("source", (source).toString());
			}
			Object target =  map.get("target");
			if(target!=null && target instanceof Long) {
				map.put("target", (target).toString());
			}
			Object eventid =  map.get("eventid");
			if(eventid!=null && eventid instanceof Long) {
				map.put("eventid", (eventid).toString());
			}
		}
		return  linksList;
	}
	
	public List<Map<String,Object>>  nodeLongToStr(List<Map<String,Object>> nodesList ){
		for (int i = 0; i < nodesList.size(); i++) {
			Map<String,Object>  map =  nodesList.get(i);
			Object id = map.get("id");
			if(id!=null && id instanceof Long) {
				map.put("id", (id).toString());
			}
			Object onto = map.get("onto");
			if(onto!=null && onto instanceof Long) {
				map.put("onto", (onto).toString());
			}
			Object image_ids = map.get("image_ids");
			if(image_ids!=null && image_ids instanceof Long) {
				map.put("image_ids", (image_ids).toString());
			}
		}
		return  nodesList;
	}
	
	
	
	public List<Map<String,Object>>   linkOnlyOne(List<Map<String,Object>> linksList ) {
		List<Map<String,Object>> newlinksList = new ArrayList<>();
		Set<String> setList =  new HashSet<>();
		for (int i = 0; i < linksList.size(); i++) {
			Map<String,Object>  map =  linksList.get(i);
			String source =  (String)map.get("source");
			String target =  (String)map.get("target");
			String value =  (String)map.get("value");
			String join = source + target + value;
			boolean  fal = setList.contains(join);
			if(!fal) {
				setList.add(join);
				newlinksList.add(map);
			}
		}
		return newlinksList;
	}
    public List<Map<String,Object>> removeNoRelationEntityOrg(
            List<Map<String,Object>> nodesList,
            String entityId,List<Map<String,Object>> linksList){
        //去掉和中心实体没关联的数据
        Set<String> entityidSet = new HashSet<>();
        entityidSet.add(entityId);
        getEntityNode(entityidSet,entityId,linksList);
        List<Map<String,Object>> nodesListNew = new ArrayList<>();
        for (Map<String,Object> nodeMap:nodesList) {
            for (String entityNode:entityidSet) {
                if (nodeMap.get("id").toString().equals(entityNode)){
                    nodesListNew.add(nodeMap);
                }
            }
        }
        return nodesListNew;
    }
    void getEntityNode(Set<String> entityidSet,String entity,List<Map<String,Object>> linksList){
        for (Map<String,Object> linkMap:linksList) {
            if (linkMap.get("source").toString().equals(entity)){
                if (!entityidSet.contains(linkMap.get("target").toString())){
                    entityidSet.add(linkMap.get("target").toString());
                    getEntityNode(entityidSet,linkMap.get("target").toString(),linksList);
                }
            }
            if (linkMap.get("target").toString().equals(entity)){
                if (!entityidSet.contains(linkMap.get("source").toString())){
                    entityidSet.add(linkMap.get("source").toString());
                    getEntityNode(entityidSet,linkMap.get("source").toString(),linksList);
                }
            }
        }
    }
    
   public  List<Map<String,Object>> removeNoRelationEntity(
            List<Map<String,Object>> nodesList,List<Map<String, Object>> nodesOntoList,
                                                    String entityId,List<Map<String,Object>> linksList){
        //去掉和中心实体没关联的数据
        Set<String> entityidSet = new HashSet<>();
        entityidSet.add(entityId);
        getEntityNode(entityidSet,entityId,linksList);
        for (Map<String, Object> ontoMap:nodesOntoList) {
            entityidSet.add(ontoMap.get("id").toString());
        }
        List<Map<String,Object>> nodesListNew = new ArrayList<>();
        for (Map<String,Object> nodeMap:nodesList) {
            for (String entityNode:entityidSet) {
                if (nodeMap.get("id").toString().equals(entityNode)){
                    nodesListNew.add(nodeMap);
                }
            }
        }
        return nodesListNew;
    }
}
