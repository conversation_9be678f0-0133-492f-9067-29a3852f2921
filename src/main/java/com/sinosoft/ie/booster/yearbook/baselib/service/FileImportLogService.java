package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportLogEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogPagination;
import java.util.*;

/**
 *
 * file_import_log
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-08 16:35:51
 */
public interface FileImportLogService extends IService<FileImportLogEntity> {

    List<FileImportLogEntity> getList(FileImportLogPagination fileImportLogPagination);

    List<FileImportLogEntity> getTypeList(FileImportLogPagination fileImportLogPagination,String dataType);



    FileImportLogEntity getInfo(Long id);

    void delete(FileImportLogEntity entity);

    void create(FileImportLogEntity entity);

    boolean update( Long id, FileImportLogEntity entity);
    
//  子表方法
}
