package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.ReportThemes;
import com.sinosoft.ie.booster.yearbook.business.mapper.ReportThemesMapper;
import com.sinosoft.ie.booster.yearbook.business.service.ReportThemesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class ReportThemesServiceImpl implements ReportThemesService {
    @Resource
    private ReportThemesMapper reportThemesMapper;
    @Override
    public List<ReportThemes> getReportThemesList() {
        List<ReportThemes> reportThemesList = reportThemesMapper.getReportThemesList();
        return reportThemesList;
    }
}
