package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.SubjectEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.subject.SubjectPagination;
import java.util.*;

/**
 *
 * subject
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-26 14:03:56
 */
public interface SubjectService extends IService<SubjectEntity> {

    List<SubjectEntity> getList(SubjectPagination subjectPagination);

    List<SubjectEntity> getTypeList(SubjectPagination subjectPagination,String dataType);



    SubjectEntity getInfo(Long id);

    void delete(SubjectEntity entity);

    void create(SubjectEntity entity);

    boolean update( Long id, SubjectEntity entity);

	List<Map<String,Object>> getListSelect();

//  子表方法
}
