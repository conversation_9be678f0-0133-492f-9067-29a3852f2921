package com.sinosoft.ie.booster.yearbook.business.model.event;

import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 *
 * Event模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-21 18:11:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class EventInfoVO extends EventCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 每月数量
     */
    @ApiModelProperty(value = "每月数量")
    private Integer monthCount;

    @ApiModelProperty(value = "标签数组")
    private List<ResearchTagEntity> tagList;
}