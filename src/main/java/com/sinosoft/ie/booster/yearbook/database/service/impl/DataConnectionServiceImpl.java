package com.sinosoft.ie.booster.yearbook.database.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.database.entity.DataConnectionEntity;
import com.sinosoft.ie.booster.yearbook.database.mapper.DataConnectionMapper;
import com.sinosoft.ie.booster.yearbook.database.model.dataconnection.DataConnectionPagination;
import com.sinosoft.ie.booster.yearbook.database.service.DataConnectionService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * data_connection
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-01-06 15:01:22
 */
@Service
public class DataConnectionServiceImpl extends ServiceImpl<DataConnectionMapper, DataConnectionEntity> implements DataConnectionService {


    @Override
    public List<DataConnectionEntity> getList(DataConnectionPagination dataConnectionPagination){
        QueryWrapper<DataConnectionEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(dataConnectionPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getId,dataConnectionPagination.getId()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getName,dataConnectionPagination.getName()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getDbType()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getDbType,dataConnectionPagination.getDbType()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getIp()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getIp,dataConnectionPagination.getIp()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getPort()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getPort,dataConnectionPagination.getPort()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getDbBaseName()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getDbBaseName,dataConnectionPagination.getDbBaseName()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getUser()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getUser,dataConnectionPagination.getUser()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getPassword()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getPassword,dataConnectionPagination.getPassword()));
        }

        //排序
        if(StrUtil.isEmpty(dataConnectionPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(DataConnectionEntity::getId);
        }else{
            queryWrapper="asc".equals(dataConnectionPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(dataConnectionPagination.getSidx()):queryWrapper.orderByDesc(dataConnectionPagination.getSidx());
        }
        Page<DataConnectionEntity> page=new Page<>(dataConnectionPagination.getCurrentPage(), dataConnectionPagination.getPageSize());
        IPage<DataConnectionEntity> userIPage=this.page(page,queryWrapper);
        return dataConnectionPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<DataConnectionEntity> getTypeList(DataConnectionPagination dataConnectionPagination,String dataType){
        QueryWrapper<DataConnectionEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(dataConnectionPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getId,dataConnectionPagination.getId()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getName,dataConnectionPagination.getName()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getDbType()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getDbType,dataConnectionPagination.getDbType()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getIp()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getIp,dataConnectionPagination.getIp()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getPort()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getPort,dataConnectionPagination.getPort()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getDbBaseName()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getDbBaseName,dataConnectionPagination.getDbBaseName()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getUser()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getUser,dataConnectionPagination.getUser()));
        }

        if(!"null".equals(String.valueOf(dataConnectionPagination.getPassword()))){
            queryWrapper.lambda().and(t->t.like(DataConnectionEntity::getPassword,dataConnectionPagination.getPassword()));
        }

        //排序
        if(StrUtil.isEmpty(dataConnectionPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(DataConnectionEntity::getId);
        }else{
            queryWrapper="asc".equals(dataConnectionPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(dataConnectionPagination.getSidx()):queryWrapper.orderByDesc(dataConnectionPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<DataConnectionEntity> page=new Page<>(dataConnectionPagination.getCurrentPage(), dataConnectionPagination.getPageSize());
            IPage<DataConnectionEntity> userIPage=this.page(page,queryWrapper);
            return dataConnectionPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public DataConnectionEntity getInfo(Long id){
        QueryWrapper<DataConnectionEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(DataConnectionEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataConnectionEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, DataConnectionEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(DataConnectionEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}