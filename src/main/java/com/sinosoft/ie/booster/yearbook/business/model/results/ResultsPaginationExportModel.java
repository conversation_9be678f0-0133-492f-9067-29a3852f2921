package com.sinosoft.ie.booster.yearbook.business.model.results;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Results模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 13:51:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ResultsPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String field;
}