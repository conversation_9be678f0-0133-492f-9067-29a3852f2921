package com.sinosoft.ie.booster.yearbook.mark.service;

import cn.hutool.json.JSONArray;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityRelation;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.IOException;
import java.util.*;

/**
 *
 * makr_sentence
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-09 14:49:54
 */
public interface MakrSentenceService extends IService<MarkSentenceEntity> {

    List<MarkSentenceEntity> getList(MarkSentencePagination makrSentencePagination);

    List<MarkSentenceEntity> getTypeList(MarkSentencePagination makrSentencePagination,String dataType);



    MarkSentenceEntity getInfo(Long id);

    void delete(MarkSentenceEntity entity);

    void create(MarkSentenceEntity entity);

    boolean update( Long id, MarkSentenceEntity entity);

    JSONArray exportCorpus(Integer status, Integer startNum, Integer endNum, Long corpusId, Long corpusVersion);

    String makeJson(JSONArray jsonArray) throws IOException;

    void deleteids(String ids);

    JSONArray getCorpusStatusChart(Long corpusId, Integer corpusVersion);

    JSONArray getCorpusTagnameChart(Long corpusId, Integer tag, Integer corpusVersion, Integer status);

    R<PageListVO<MarkEntityEntity>> getEntityList(MarkSentencePagination sentencePagination);

    R<PageListVO<MarkEntityRelation>> getEntityRelationList(MarkSentencePagination sentencePagination);

    R<PageListVO<MarkEntityEntity>> getEntityAttrList(MarkSentencePagination sentencePagination);
//  子表方法
}
