package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.PageConfigEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.pageconfig.PageConfigPagination;
import java.util.*;

/**
 *
 * page_config
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-22 17:46:32
 */
public interface PageConfigService extends IService<PageConfigEntity> {

    List<PageConfigEntity> getList(PageConfigPagination pageConfigPagination);

    List<PageConfigEntity> getTypeList(PageConfigPagination pageConfigPagination,String dataType);



    PageConfigEntity getInfo(Long id);

    void delete(PageConfigEntity entity);

    void create(PageConfigEntity entity);

    boolean update( Long id, PageConfigEntity entity);
    
//  子表方法
}
