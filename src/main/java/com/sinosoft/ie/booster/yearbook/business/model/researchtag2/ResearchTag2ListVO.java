package com.sinosoft.ie.booster.yearbook.business.model.researchtag2;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * ResearchTag2模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-20 13:38:22
 */
@Data
@ApiModel
public class ResearchTag2ListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String name;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String tagGroup;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private String tagType;

    /**
     * 所属本体
     */
    @ApiModelProperty(value = "所属本体")
    private Long ontoId;

    /**
     * 所属本体属性
     */
    @ApiModelProperty(value = "所属本体属性")
    private String attrCategory;

    /**
     * 所属本体属性枚举值id
     */
    @ApiModelProperty(value = "所属本体属性枚举值id")
    private Long attrValId;
    /**
     * 标签分类英文名
     */
    @ApiModelProperty(value = "标签分类英文名")
    private String tagAttr;


}