package com.sinosoft.ie.booster.yearbook.baselib.controller;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.pig4cloud.plugin.excel.annotation.ResponseExcel;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.visualdev.util.FileUtil;
import com.sinosoft.ie.booster.visualdev.util.UpUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportJobEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaSchemaEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog.FileImportLogExcel;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaschema.MetaSchemaUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.service.CalculateScoreService;
import com.sinosoft.ie.booster.yearbook.baselib.service.FileImportJobService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaSchemaService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * meta_schema
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-02 15:36:32
 */
@Slf4j
@RestController
@Api(tags = "meta_schema")
@RequestMapping("/MetaSchema")
public class MetaSchemaController {
    @Autowired
    private MetaSchemaService metaSchemaService;

    @Resource
    private CalculateScoreService calculateScoreService;

    @Autowired
    private ProjectConfig projectConfig;


    @Resource
    private FileImportJobService fileImportJobService;
    /**
     * 列表
     *
     * @param metaSchemaPagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MetaSchemaListVO>> list(MetaSchemaPagination metaSchemaPagination)throws IOException{
        List<MetaSchemaEntity> list= metaSchemaService.getList(metaSchemaPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MetaSchemaEntity entity:list){
            }
        List<MetaSchemaListVO> listVO=JsonUtil.getJsonToList(list,MetaSchemaListVO.class);
        PageListVO<MetaSchemaListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(metaSchemaPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param metaSchemaCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MetaSchemaCrForm metaSchemaCrForm) throws DataException {
        MetaSchemaEntity entity=JsonUtil.getJsonToBean(metaSchemaCrForm, MetaSchemaEntity.class);
        metaSchemaService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MetaSchemaInfoVO> info(@PathVariable("id") Long id){
        MetaSchemaEntity entity= metaSchemaService.getInfo(id);
        MetaSchemaInfoVO vo=JsonUtil.getJsonToBean(entity, MetaSchemaInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MetaSchemaUpForm metaSchemaUpForm) throws DataException {
        MetaSchemaEntity entity= metaSchemaService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(metaSchemaUpForm, MetaSchemaEntity.class);
            metaSchemaService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MetaSchemaEntity entity= metaSchemaService.getInfo(id);
        if(entity!=null){
            metaSchemaService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }
    
    
    /**
     * 不分页列表
     */
    @GetMapping("/list")
    public R<PageListVO<MetaSchemaListVO>> list1()throws IOException{
        List<MetaSchemaEntity> list= metaSchemaService.list();
        //处理id字段转名称，若无需转或者为空可删除
        List<MetaSchemaListVO> listVO=JsonUtil.getJsonToList(list,MetaSchemaListVO.class);
        PageListVO<MetaSchemaListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        return R.ok(vo);
    }

    /**
     * 上传本体excel文件
     *
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST}, value = "/uploadConceptExcel")
    public R uploadConceptExcel(@RequestParam("file") MultipartFile file,Long schemaId){
        System.out.println(schemaId);
        if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
            return R.failed("上传附件操作异常，附件为空，请重新上传");
        }
        String fileType = UpUtil.getFileType(file);
//        //验证类型
//        if (!com.sinosoft.ie.booster.visualdev.util.FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//            return R.failed("上传失败，文件格式不允许上传" );
//        }
        JSONObject json = JSONUtil.createObj();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
            FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);

            // 调用解析excel方法
            String msg = metaSchemaService.dealConceptFileData(projectConfig.getUploadPath()+File.separator+fileName,schemaId);
            json.set("msg",msg);
        } catch (Exception e) {
            log.error("{}", e);
            return R.failed("上传失败:" + e);
        }
        return R.ok(json.toString(), "上传成功");
    }


    /**
     * 上传数值型excel文件
     *
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST}, value = "/uploadNumberExcel")
    public R uploadNumberExcel(@RequestParam("file") MultipartFile file,Long schemaId){
        if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
            return R.failed("上传附件操作异常，附件为空，请重新上传");
        }
        String fileType = UpUtil.getFileType(file);
//        //验证类型
//        if (!com.sinosoft.ie.booster.visualdev.util.FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//            return R.failed("上传失败，文件格式不允许上传" );
//        }
        JSONObject json = JSONUtil.createObj();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
            FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);

            // 调用解析excel方法
            String msg = metaSchemaService.dealNumberFileData(projectConfig.getUploadPath()+File.separator+fileName,schemaId);
            json.set("msg",msg);
        } catch (Exception e) {
            log.error("{}", e);
            return R.failed("上传失败:" + e);
        }
        return R.ok(json.toString(), "上传成功");
    }

    /**
     * 上传对象型excel文件
     *
     * @return
     */
    @RequestMapping(method = {RequestMethod.POST}, value = "/uploadObjectExcel")
    public R uploadObjectExcel(@RequestParam("file") MultipartFile file,Long schemaId){
        if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
            return R.failed("上传附件操作异常，附件为空，请重新上传");
        }
        String fileType = UpUtil.getFileType(file);
//        //验证类型
//        if (!com.sinosoft.ie.booster.visualdev.util.FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//            return R.failed("上传失败，文件格式不允许上传" );
//        }
        JSONObject json = JSONUtil.createObj();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
            FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);

            // 调用解析excel方法
            String msg = metaSchemaService.dealObjectFileData(projectConfig.getUploadPath()+File.separator+fileName,schemaId);
            json.set("msg",msg);
        } catch (Exception e) {
            log.error("{}", e);
            return R.failed("上传失败:" + e);
        }
        return R.ok(json.toString(), "上传成功");
    }

    /**
     * 模式复用
     */
    @PostMapping("/schemaReuse")
    @Transactional
    public R<String> schemaReuse(@RequestBody @Valid MetaSchemaCrForm metaSchemaCrForm) throws DataException {
        MetaSchemaEntity entity=JsonUtil.getJsonToBean(metaSchemaCrForm, MetaSchemaEntity.class);
        metaSchemaService.create(entity);
        metaSchemaService.schemaReuse(entity);
        return R.ok(null, "新建成功");
    }

    @GetMapping("/getEntityExcel")
    public R<String> getEntityExcel(Long ontoId) throws Exception {
        return R.ok(metaSchemaService.buildExcelDocument(ontoId));
    }

    /**
     * 上传实体excel文件
     */
    @RequestMapping(method = {RequestMethod.POST}, value = "/uploadEntityExcel")
    public R uploadEntityExcel(@RequestParam("file") MultipartFile file,Long ontoId){
        if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
            return R.failed("上传附件操作异常，附件为空，请重新上传");
        }
        String fileType = UpUtil.getFileType(file);
//        //验证类型
//        if (!com.sinosoft.ie.booster.visualdev.util.FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//            return R.failed("上传失败，文件格式不允许上传" );
//        }
        JSONObject json = JSONUtil.createObj();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
            FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);
            //存入知识运维的导入导出-任务表
            FileImportJobEntity fileImportJob = new FileImportJobEntity();
            fileImportJob.setFileName(originalFilename);
            fileImportJob.setFileNewName(fileName);
            fileImportJob.setFileType(fileSuffixName);
            fileImportJob.setFileSize(file.getSize()/1000 + "kb");
            fileImportJob.setJobState(1);
            fileImportJob.setJobType(1);
            fileImportJob.setCreateTime(new Date());
            fileImportJob.setCreateUser(1L);
            fileImportJobService.create(fileImportJob);
            // 调用解析excel方法
            metaSchemaService.dealExcelFileData(projectConfig.getUploadPath()+File.separator+fileName,ontoId,fileImportJob);
            json.set("msg","");
        } catch (Exception e) {
            log.error("{}", e);
            return R.failed("上传失败:" + e);
        }
        return R.ok(json.toString(), "上传成功");
    }

    /**
     * 上传实体关系excel文件
     */
    @RequestMapping(method = {RequestMethod.POST}, value = "/uploadEntityRelationExcel")
    public R uploadEntityRelationExcel(@RequestParam("file") MultipartFile file){
        if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
            return R.failed("上传附件操作异常，附件为空，请重新上传");
        }
        String fileType = UpUtil.getFileType(file);
        //验证类型
//        if (!com.sinosoft.ie.booster.visualdev.util.FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//            return R.failed("上传失败，文件格式不允许上传" );
//        }
        JSONObject json = JSONUtil.createObj();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
            FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);
            //存入知识运维的导入导出-任务表
            FileImportJobEntity fileImportJob = new FileImportJobEntity();
            fileImportJob.setFileName(originalFilename);
            fileImportJob.setFileNewName(fileName);
            fileImportJob.setFileType(fileSuffixName);
            fileImportJob.setFileSize(file.getSize()/1000 + "kb");
            fileImportJob.setJobState(1);
            fileImportJob.setJobType(2);
            fileImportJob.setCreateTime(new Date());
            fileImportJob.setCreateUser(1L);
            fileImportJobService.create(fileImportJob);
            // 调用解析excel方法
            metaSchemaService.dealExcelFileData(projectConfig.getUploadPath()+File.separator+fileName,null,fileImportJob);
            json.set("msg","");
        } catch (Exception e) {
            log.error("{}", e);
            return R.failed("上传失败:" + e);
        }
        return R.ok(json.toString(), "上传成功");
    }

    /**
     * 上传实体同义词excel文件
     */
    @RequestMapping(method = {RequestMethod.POST}, value = "/uploadEntitySynonymExcel")
    public R uploadEntitySynonymExcel(@RequestParam("file") MultipartFile file){
        if (ObjectUtil.isNull(file) || ObjectUtil.isNull(file.getName())) {
            return R.failed("上传附件操作异常，附件为空，请重新上传");
        }
        String fileType = UpUtil.getFileType(file);
        //验证类型
//        if (!com.sinosoft.ie.booster.visualdev.util.FileUtil.fileType(configValueUtil.getAllowUploadFileType(), fileType)) {
//            return R.failed("上传失败，文件格式不允许上传" );
//        }
        JSONObject json = JSONUtil.createObj();
        try {
            String originalFilename = file.getOriginalFilename();
            String fileSuffixName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            String fileName = IdUtil.simpleUUID() + "." + fileSuffixName;
            FileUtil.upFile(file, projectConfig.getUploadPath() , fileName);
            //存入知识运维的导入导出-任务表
            FileImportJobEntity fileImportJob = new FileImportJobEntity();
            fileImportJob.setFileName(originalFilename);
            fileImportJob.setFileNewName(fileName);
            fileImportJob.setFileType(fileSuffixName);
            fileImportJob.setFileSize(file.getSize()/1000 + "kb");
            fileImportJob.setJobState(1);
            fileImportJob.setJobType(3);
            fileImportJob.setCreateTime(new Date());
            fileImportJob.setCreateUser(1L);
            fileImportJobService.create(fileImportJob);
            // 调用解析excel方法
            metaSchemaService.dealExcelFileData(projectConfig.getUploadPath()+File.separator+fileName,null,fileImportJob);
            json.set("msg","");
        } catch (Exception e) {
            log.error("{}", e);
            return R.failed("上传失败:" + e);
        }
        return R.ok(json.toString(), "上传成功");
    }

    @ResponseExcel
    @GetMapping("/fileImportLogExport")
    public List<FileImportLogExcel> fileImportLogExport(long id) {
        return metaSchemaService.fileImportLogExport(id);
    }

    @GetMapping("/test")
    public void test(){
        calculateScoreService.CalculateTagScore();
    }
}
