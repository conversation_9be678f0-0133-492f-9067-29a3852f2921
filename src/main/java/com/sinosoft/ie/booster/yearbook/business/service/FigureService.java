package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.FigureEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigurePagination;
import java.util.*;

/**
 *
 * figure
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-26 11:45:26
 */
public interface FigureService extends IService<FigureEntity> {

    List<FigureEntity> getList(FigurePagination figurePagination);

    List<FigureEntity> getTypeList(FigurePagination figurePagination,String dataType);



    FigureEntity getInfo(Long id);

    void delete(FigureEntity entity);

    void create(FigureEntity entity);

    boolean update( Long id, FigureEntity entity);
    
//  子表方法
}
