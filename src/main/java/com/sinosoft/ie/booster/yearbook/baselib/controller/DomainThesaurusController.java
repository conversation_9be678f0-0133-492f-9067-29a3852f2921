package com.sinosoft.ie.booster.yearbook.baselib.controller;

import cn.hutool.core.io.IoUtil;
import com.pig4cloud.plugin.excel.annotation.RequestExcel;
import com.pig4cloud.plugin.excel.annotation.ResponseExcel;
import com.pig4cloud.plugin.excel.vo.ErrorMessage;
import com.sinosoft.ie.booster.yearbook.baselib.entity.DomainThesaurusEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.DomainThesaurusPagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.*;
import com.sinosoft.ie.booster.yearbook.baselib.service.DomainThesaurusService;
import com.sinosoft.ie.booster.yearbook.constant.enums.GlobalEnum;
import io.swagger.annotations.Api;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 *
 * domain_thesaurus
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-14 15:44:58
 */
@Slf4j
@RestController
@Api(tags = "domain_thesaurus")
@RequestMapping("/DomainThesaurus")
public class DomainThesaurusController {

    @Autowired
    private DomainThesaurusService domainthesaurusservice;

    /**
     * 列表
     *
     * @param domainThesaurusPagination
     * @return
     */
    @GetMapping
    public R list(DomainThesaurusPagination domainThesaurusPagination)throws IOException{
        List<DomainThesaurusEntity> list= domainthesaurusservice.getList(domainThesaurusPagination);
        //处理id字段转名称，若无需转或者为空可删除
        for(DomainThesaurusEntity entity:list){
        }
        List<DomainThesaurusListVO> listVO=JsonUtil.getJsonToList(list,DomainThesaurusListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(domainThesaurusPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param domainThesaurusCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid DomainThesaurusCrForm domainThesaurusCrForm) throws DataException {
        DomainThesaurusEntity entity=JsonUtil.getJsonToBean(domainThesaurusCrForm, DomainThesaurusEntity.class);
        domainthesaurusservice.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<DomainThesaurusInfoVO> info(@PathVariable("id") Long id){
        DomainThesaurusEntity entity= domainthesaurusservice.getInfo(id);
        DomainThesaurusInfoVO vo=JsonUtil.getJsonToBean(entity, DomainThesaurusInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid DomainThesaurusUpForm domainThesaurusUpForm) throws DataException {
        DomainThesaurusEntity entity= domainthesaurusservice.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(domainThesaurusUpForm, DomainThesaurusEntity.class);
            domainthesaurusservice.update(id, entity);
            return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        DomainThesaurusEntity entity = domainthesaurusservice.getInfo(id);
        if(entity!=null){
            entity.setDelFlag(GlobalEnum.isDeleteType.YES.getCode().toString());
            domainthesaurusservice.update(id, entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * @methodName：download
     * @description：模板下载
     * @author：peas
     * @date：2022/11/14 17:58
     * @param：[fileName, response]
     * @return：void
     **/
    @GetMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        String fileName = "domainThesaurus.xlsx";
        ClassPathResource resource = new ClassPathResource("file/"+fileName);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName,StandardCharsets.UTF_8.toString()));
        IoUtil.copy(resource.getInputStream(), response.getOutputStream());
    }

    /**
     * @methodName：importDate
     * @description：导入数据
     * @author：peas
     * @date：2022/11/15 16:31
     * @param：[excelList, bindingResult]
     * @return：com.sinosoft.ie.booster.common.core.util.R<java.util.List<com.pig4cloud.plugin.excel.vo.ErrorMessage>>
     **/
    @PostMapping("/import")
    public R<List<ErrorMessage>> importDate(@RequestExcel List<DomainThesaurusExcel> excelList, BindingResult bindingResult) {
        return domainthesaurusservice.importDate(excelList, bindingResult);
    }

    /**
     * @methodName：export
     * @description：
     * @author：peas
     * @date：2022/11/15 16:46
     * @param：[category]
     * @return：java.util.List<com.sinosoft.ie.booster.yearbook.baselib.model.domainthesaurus.DomainThesaurusExcel>
     **/
    @ResponseExcel
    @GetMapping("/export")
    public List<DomainThesaurusExcel> export(String category,String name) {
        return domainthesaurusservice.getExportList(category,name);
    }

    /**
     * 模板下载
     **/
    @GetMapping("/downloadMoban/{fileName}")
    public void download(HttpServletResponse response,@PathVariable("fileName") String fileName) throws IOException {
//        String fileName = "domainThesaurus.xlsx";
        ClassPathResource resource = new ClassPathResource("file/"+fileName);
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName,StandardCharsets.UTF_8.toString()));
        IoUtil.copy(resource.getInputStream(), response.getOutputStream());
    }
}
