package com.sinosoft.ie.booster.yearbook.baselib.model.fileimportlog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * FileImportLog模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-08 16:35:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class FileImportLogInfoVO extends FileImportLogCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}