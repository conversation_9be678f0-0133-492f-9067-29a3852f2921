package com.sinosoft.ie.booster.yearbook.mark.service;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityPagination;
import java.util.*;

/**
 *
 * mark_entity
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-08 16:58:44
 */
public interface MarkEntityService extends IService<MarkEntityEntity> {

    List<MarkEntityEntity> getList(MarkEntityPagination markEntityPagination);

    List<MarkEntityEntity> getTypeList(MarkEntityPagination markEntityPagination,String dataType);



    MarkEntityEntity getInfo(Long id);

    void delete(MarkEntityEntity entity);

    void create(MarkEntityEntity entity);

    boolean update( Long id, MarkEntityEntity entity);
    
//  子表方法
}
