package com.sinosoft.ie.booster.yearbook.baselib.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeCrForm;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributePagination;
import com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeUpForm;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * mate_attribute
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-16 14:48:56
 */
@Slf4j
@RestController
@Api(tags = "mate_attribute")
@RequestMapping("/MateAttribute")
public class MateAttributeController {
    @Autowired
    private MateAttributeService mateAttributeService;


    /**
     * 列表
     *
     * @param mateAttributePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MateAttributeListVO>> list(MateAttributePagination mateAttributePagination)throws IOException{
        List<MateAttributeEntity> list= mateAttributeService.getList(mateAttributePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MateAttributeEntity entity:list){
            }
        List<MateAttributeListVO> listVO=JsonUtil.getJsonToList(list,MateAttributeListVO.class);
        PageListVO<MateAttributeListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(mateAttributePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param mateAttributeCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MateAttributeCrForm mateAttributeCrForm) throws DataException {
        MateAttributeEntity entity=JsonUtil.getJsonToBean(mateAttributeCrForm, MateAttributeEntity.class);
        mateAttributeService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MateAttributeInfoVO> info(@PathVariable("id") Long id){
        MateAttributeEntity entity= mateAttributeService.getInfo(id);
        MateAttributeInfoVO vo=JsonUtil.getJsonToBean(entity, MateAttributeInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MateAttributeUpForm mateAttributeUpForm) throws DataException {
        MateAttributeEntity entity= mateAttributeService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(mateAttributeUpForm, MateAttributeEntity.class);
            mateAttributeService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MateAttributeEntity entity= mateAttributeService.getInfo(id);
        if(entity!=null){
            mateAttributeService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

    /**
     * 批量添加数值属性
     */
    @PostMapping("/createNumValueList")
    @Transactional
    public R<String> createNumValueList(@RequestBody @Valid List<MateAttributeCrForm> list) throws DataException {
        mateAttributeService.createNumValueList(list);
        return R.ok(null, "新建成功");
    }

    /**
     * 批量添加数值属性
     */
    @PostMapping("/createObjectValueList")
    @Transactional
    public R<String> createObjectValueList(@RequestBody @Valid List<MateAttributeCrForm> list) throws DataException {
        mateAttributeService.createObjectValueList(list);
        return R.ok(null, "新建成功");
    }

    /**
     * 列表-模式工作
     */
    @GetMapping("/getMoShiList")
    public R<PageListVO<MateAttributeListVO>> getMoShiList(MateAttributePagination mateAttributePagination){
        mateAttributePagination.setPid(0L);
        List<MateAttributeEntity> list= mateAttributeService.getList(mateAttributePagination);
        List<MateAttributeListVO> listVO=JsonUtil.getJsonToList(list,MateAttributeListVO.class);
        for(MateAttributeListVO entity:listVO){
            mateAttributeService.getMoShiChild(entity);
        }
        PageListVO<MateAttributeListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(mateAttributePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 批量删除，逗号隔开
     */
    @DeleteMapping("/deleteids")
    @Transactional
    public R<String> deleteids(String ids){
        mateAttributeService.deleteids(ids);
        return R.ok(null, "删除成功");
    }
}
