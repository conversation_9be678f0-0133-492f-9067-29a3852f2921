package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.ResultsEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.ResultsMapper;
import com.sinosoft.ie.booster.yearbook.business.service.ResultsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.business.model.results.ResultsPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * results
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-26 13:51:00
 */
@Service
public class ResultsServiceImpl extends ServiceImpl<ResultsMapper, ResultsEntity> implements ResultsService {


    @Override
    public List<ResultsEntity> getList(ResultsPagination resultsPagination){
        QueryWrapper<ResultsEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(resultsPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(ResultsEntity::getId,resultsPagination.getId()));
        }

        if(!"null".equals(String.valueOf(resultsPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(ResultsEntity::getName,resultsPagination.getName()));
        }

        if(!"null".equals(String.valueOf(resultsPagination.getField()))){
            queryWrapper.lambda().and(t->t.like(ResultsEntity::getField,resultsPagination.getField()));
        }

        //排序
        if(StrUtil.isEmpty(resultsPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(ResultsEntity::getId);
        }else{
            queryWrapper="asc".equals(resultsPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(resultsPagination.getSidx()):queryWrapper.orderByDesc(resultsPagination.getSidx());
        }
        Page<ResultsEntity> page=new Page<>(resultsPagination.getCurrentPage(), resultsPagination.getPageSize());
        IPage<ResultsEntity> userIPage=this.page(page,queryWrapper);
        return resultsPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<ResultsEntity> getTypeList(ResultsPagination resultsPagination,String dataType){
        QueryWrapper<ResultsEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(resultsPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(ResultsEntity::getId,resultsPagination.getId()));
        }

        if(!"null".equals(String.valueOf(resultsPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(ResultsEntity::getName,resultsPagination.getName()));
        }

        if(!"null".equals(String.valueOf(resultsPagination.getField()))){
            queryWrapper.lambda().and(t->t.like(ResultsEntity::getField,resultsPagination.getField()));
        }

        //排序
        if(StrUtil.isEmpty(resultsPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(ResultsEntity::getId);
        }else{
            queryWrapper="asc".equals(resultsPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(resultsPagination.getSidx()):queryWrapper.orderByDesc(resultsPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<ResultsEntity> page=new Page<>(resultsPagination.getCurrentPage(), resultsPagination.getPageSize());
            IPage<ResultsEntity> userIPage=this.page(page,queryWrapper);
            return resultsPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public ResultsEntity getInfo(Long id){
        QueryWrapper<ResultsEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(ResultsEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(ResultsEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, ResultsEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(ResultsEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}