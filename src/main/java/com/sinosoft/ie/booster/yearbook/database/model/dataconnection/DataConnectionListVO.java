package com.sinosoft.ie.booster.yearbook.database.model.dataconnection;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * DataConnection模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-06 15:01:22
 */
@Data
@ApiModel
public class DataConnectionListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 数据集名称
     */
    @ApiModelProperty(value = "数据集名称")
    private String name;

    /**
     * 连接类型：1.mysql;2.postgresql;3.kingbase;4.dm;5.orcle;6.sqlserver
     */
    @ApiModelProperty(value = "连接类型：1.mysql;2.postgresql;3.kingbase;4.dm;5.orcle;6.sqlserver")
    private Integer dbType;

    /**
     * 主机ip
     */
    @ApiModelProperty(value = "主机ip")
    private String ip;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    private Integer port;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称")
    private String dbBaseName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String user;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;

}