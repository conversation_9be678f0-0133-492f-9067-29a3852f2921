package com.sinosoft.ie.booster.yearbook.baselib.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-12-02 15:36:32
 */
@Data
@TableName("meta_schema")
@ApiModel(value = "")
public class MetaSchemaEntity  {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 模式定义名称
     */
    @ApiModelProperty(value = "模式定义名称")
    private String name;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;

    /**
     * 1未提交；2已提交
     */
    @ApiModelProperty(value = "1未提交；2已提交")
    private Integer state;

    /**
     * 模式类型：1实体关系属性抽取；2事件抽取；3文本分类；4机器阅读理解
     */
    @ApiModelProperty(value = "模式类型：1实体关系属性抽取；2事件抽取；3文本分类；4机器阅读理解")
    private Integer schemaType;

    /**
     * 继承的模式定义
     */
    @ApiModelProperty(value = "继承的模式定义")
    private Long extId;
}
