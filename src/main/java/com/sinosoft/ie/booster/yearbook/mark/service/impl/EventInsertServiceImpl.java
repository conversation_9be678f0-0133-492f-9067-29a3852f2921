package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.awt.Event;
import java.io.File;
import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibConceptEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibConceptService;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaEntityRelationService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MetaOntoRelationService;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRules2Entity;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRulesEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.EveThemeEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.UploadfileEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.EveThemeMapper;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventCrForm;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRules2Service;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRulesService;
import com.sinosoft.ie.booster.yearbook.business.service.EveThemeService;
import com.sinosoft.ie.booster.yearbook.business.service.EventService;
import com.sinosoft.ie.booster.yearbook.business.service.FileDirectoryService;
import com.sinosoft.ie.booster.yearbook.business.service.HotInformationService;
import com.sinosoft.ie.booster.yearbook.business.service.OrganizationService;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTag2Service;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTagService;
import com.sinosoft.ie.booster.yearbook.business.service.UploadfileService;
import com.sinosoft.ie.booster.yearbook.config.ProjectConfig;
import com.sinosoft.ie.booster.yearbook.mark.cache.OntoCache;
import com.sinosoft.ie.booster.yearbook.mark.constant.YearbookConstant;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity;
import com.sinosoft.ie.booster.yearbook.mark.service.EventInsertService;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkEntityService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkTripletService;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileAppender;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class EventInsertServiceImpl implements EventInsertService {

	@Resource
	private LibEntityMapper libEntityMapper;
	@Autowired
	private LibEntityService libEntityService;
	@Resource
	private EventService eventService;
	@Autowired
	private OrganizationService organizationService;
	@Autowired
	private MateAttributeService mateAttributeService;
	@Resource
	private MateAttributeValueService mateAttributeValueService;
	@Resource
	private ResearchTagService researchTagService;
	@Resource
	private HotInformationService hotInformationService;
	@Resource
	private EveThemeMapper eveThemeMapper;
	@Resource
	private EveThemeService eveThemeService;
	@Resource
	private UploadfileService uploadfileService;
	@Resource
	private ProjectConfig projectConfig;
	@Resource
	private MetaOntoRelationService metaOntoRelationService;

	Map<String, List<ConverRulesEntity>> rulesMap = new HashedMap();
	Map<String, List<ConverRules2Entity>> rulesMap2 = new HashedMap();
	@Autowired
	private ConverRulesService converRulesService;
	@Autowired
	private ConverRules2Service converRules2Service;
	@Autowired
	private MetaEntityRelationService metaEntityRelationService;
	@Autowired
	private LibConceptService libConceptService;
	@Autowired
	private FileDirectoryService fileDirectoryService;
	@Autowired
	private MakrSentenceService makrSentenceService;
	@Autowired
	private MarkEntityService markEntityService;
	@Autowired
	private MarkTripletService markTripletService;
	@Autowired
	private ResearchTag2Service researchTag2Service;
	@Autowired
	private EventWeight eventWeight;
	/**
	 * 数据转化中要用到的一些常量数据
	 */
//	private final String category = ",政治工作,管理保障,综合协调,科研管理,领导决策,科研力量,科研组织,科研课题,科研成果,科研经费,";
//	private final String meeting = ",会议,领导讲话,获奖,评议,讲课,汇报,联谊,观摩,学术年会,";
	/******** 事件的十个属性 *************/
	private static List<String> tagList = new ArrayList<String>();
	private static Map<String, String> tagMap = new HashMap<String, String>();
	static {
		tagList.add("political_work");
		tagList.add("security_manage");
		tagList.add("comprehensive_coordination");
		tagList.add("scientific_manage");
		tagList.add("decision_making");
		tagList.add("scientific_strength");
		tagList.add("scientific_environment");
		tagList.add("scientific_subject");
		tagList.add("scientific_result");
		tagList.add("scientific_funding");

		tagMap.put("政治工作", "political_work");
		tagMap.put("管理保障", "security_manage");
		tagMap.put("综合协调", "comprehensive_coordination");
		tagMap.put("科研管理", "scientific_manage");
		tagMap.put("领导决策", "decision_making");
		tagMap.put("科研力量", "scientific_strength");
		tagMap.put("科研环境", "scientific_environment");
		tagMap.put("科研课题", "scientific_subject");
		tagMap.put("科研成果", "scientific_result");
		tagMap.put("科研经费", "scientific_funding");

	}
	/***** <事件的对象属性英文名称,对象的[类别属性]英文名称> **********/
//	private static Map<String,String> objAttrList = new HashedMap<String, String>();
//	static {
//		objAttrList.put("place_id", "category_id");
//		objAttrList.put("person_ids", "category_Id");
//		objAttrList.put("org_ids", "category_Id");
//		objAttrList.put("object_ids", "category_Id");
//		
//	}

	// 显示的机构字段
	String org_arr[] = { "org_ids", "org2_id","org_main", "org_cooperation", "relation_zuzhi"};
	// 不显示的机构字段
	//String not_show_org[] =  {  "org_related",  "org_external", "org_achievement" };
	//显示的人物
	String person_arr[] = { "person_ids", "person_main", "person_cooperation","person_achievements" };
	//不显示的人物
	//String not_person_arr[] = { "person_ids",  "person_related",  "person_exchange"};
	/**
	 * LibEventEntity转EventEntity改表结构后
	 */
	public R lib2eventNewTable(EventEntity even) {

		LibEntityEntity libEntityEntity = libEntityMapper.selectById(even.getId());
		if (libEntityEntity == null) {
			return R.failed("数据不存在=>" + libEntityEntity.getId());
		}
		String tit = libEntityEntity.getName();
		String data = libEntityEntity.getData();
		Map<String, Object> dataMap = JsonUtil.stringToMap(data);

		// 查询数据库，如果有同一事件则覆盖
		EventEntity eventEntity = null;
		QueryWrapper<EventEntity> eve_query = new QueryWrapper<EventEntity>();
		eve_query.eq("title", libEntityEntity.getName());
		List<EventEntity> list = eventService.list(eve_query);
		if (list.size() == 0) {
			eventEntity = new EventEntity();
		} else {
			eventEntity = list.get(0);
		}

		LibConceptEntity conEnt = OntoCache.getOntoCache(YearbookConstant.MARK_EVENT);
		List<MateAttributeEntity> attrList = OntoCache.getAttListByOntoId(conEnt.getId());
		Map<String, MateAttributeEntity> map = attrList.stream()
				.collect(Collectors.toMap(MateAttributeEntity::getNameEn, MateAttributeEntity -> MateAttributeEntity));

		Object start_time = dataMap.get("start_time");
		if (start_time == null || isValidDate(start_time.toString()) == false) {
			return R.failed(tit + "==>【start_time】数据不正确:" + start_time);
		}
		// convertValue(dataMap,map.get("")
		eventEntity.setFid(even.getFid());
		eventEntity.setTitle(libEntityEntity.getName());
		String start_time_val = (String) convertValue(dataMap, map.get("start_time"));
		eventEntity.setStartTime(start_time_val);
		dataMap.put("start_time_val", start_time_val);
		eventEntity.setEndTime((String) convertValue(dataMap, map.get("stop_time")));
		eventEntity.setVenue((String) convertValue(dataMap, map.get("place_id")));
		eventEntity.setStatus((String) convertValue(dataMap, map.get("event_status")));
		eventEntity.setArticles((String) convertValue(dataMap, map.get("object_ids")));
		eventEntity.setTriggerss((String) convertValue(dataMap, map.get("trigger_word")));
		eventEntity.setCategory((String) convertValue(dataMap, map.get("event_lab_id")));
		eventEntity.setContent((String) convertValue(dataMap, map.get("content")));
		// eventEntity.setNote((String) convertValue(dataMap,
		// map.get("event_overview")));

		eventEntity.setPerson_main((String) convertValue(dataMap, map.get("person_main")));
//		eventEntity.setOrg_main((String) convertValue(dataMap, map.get("org_main")));

		Object imgs = dataMap.get("image_ids");
		if (!ObjectUtil.isEmpty(imgs)) {
			if (!(imgs instanceof Collection)) {
				return R.failed(tit + "==>【image_ids】数据类型不正确");
			}
			JSONArray imgarr = (JSONArray) imgs;
			Object id = imgarr.get(0);
			eventEntity.setImages(String.valueOf(id));
		} else {
			eventEntity.setImages(String.valueOf(projectConfig.getImgRandom()));
		}

		String themeVal = (String) convertValue(dataMap, map.get("theme"));
		if (!StringUtils.isEmpty(themeVal)) {
			// 6个科研是1；管理类是2
			String eventLabId = (String) convertValue(dataMap, map.get("event_lab_id"));
			eventEntity.setTag(eventLabId);
			eventEntity.setTheme(themeVal);
			dataMap.put("themeVal", themeVal);
			// 判断表里有没有
			QueryWrapper<EveThemeEntity> queryEveTheme = new QueryWrapper<>();
			queryEveTheme.lambda().and(t -> t.eq(EveThemeEntity::getName, themeVal));
			if (eveThemeMapper.selectCount(queryEveTheme) == 0) {
				EveThemeEntity eveTheme = new EveThemeEntity();
				eveTheme.setName(themeVal);
				eveTheme.setCategory1(2);
				eveThemeMapper.insert(eveTheme);
			}
			log.info("保存主题至主题表");
		}

		Object dir_obj = dataMap.get("directory");
		String dir_name = "";
		if(!ObjectUtil.isEmpty(dir_obj)) {
			dir_name = ((String)dir_obj).trim();
		}
		String pageNum = (String) convertValue(dataMap, map.get("pageNum"));
		Integer pageNumber =  Integer.parseInt(pageNum);
		if (!StringUtils.isEmpty(pageNum)) {
			eventEntity.setPageNum(pageNumber);
			// 查询当前年鉴所有目录
			QueryWrapper<FileDirectoryEntity> fdquery = new QueryWrapper<FileDirectoryEntity>();
			List<String>  ords =  new ArrayList<>();
			ords.add("page_num");
			ords.add("id");
			fdquery.eq("file_id", even.getFid());
			fdquery.orderByAsc(ords);
			List<FileDirectoryEntity> fdlistt = fileDirectoryService.list(fdquery);

			// 从目录倒序查询id
			Long fdid = 0L;
			for (int i = fdlistt.size() - 1; i >= 0; i--) {
				if (fdlistt.get(i).getPageNum() .equals(pageNumber)  || fdlistt.get(i).getPageNum() < pageNumber) {
					//如果目录名称和事件的目录名称一致，则确定id
					//如果不一致，则取前一个值
					if(dir_name.equals(fdlistt.get(i).getName().trim())) {
						fdid = fdlistt.get(i).getId();
					}else {
						fdid = fdlistt.get(i-1).getId();
					}
					break;
				}
			}
			// 目录
			eventEntity.setFdid(fdid);
		}

		// 十个 【科研 + 管理】 属性 同步录入到research_tag表
		StringBuffer tagSb = new StringBuffer();
		for (int i = 0; i < tagList.size(); i++) {
			String attr = tagList.get(i);
			String tag = (String) convertValue(dataMap, map.get(attr));
			if (!StringUtils.isEmpty(tag)) {
				tagSb.append(tag).append(";");
			}

		}
		if (rulesMap.size() == 0) {
			List<ConverRulesEntity> rulesList = converRulesService.list();
			rulesMap = rulesList.stream().collect(Collectors.groupingBy(t -> t.getType()));
		}

		if (rulesMap2.size() == 0) {
			List<ConverRules2Entity> rulesList2 = converRules2Service.list();
			rulesMap2 = rulesList2.stream().collect(Collectors.groupingBy(t -> t.getType()));
		}

		// 会议;成果;课题；经费,奖项
		eventEntity.setMeeting((String) convertValue(dataMap, map.get("relevant_meeting")));
		eventEntity.setAchievements((String) convertValue(dataMap, map.get("achievement")));
		eventEntity.setTask((String) convertValue(dataMap, map.get("research_project")));
		eventEntity.setPrize((String) convertValue(dataMap, map.get("prize")));

		// 设置人物
		Set<String> perSet = new HashSet<>();
		for (int i = 0; i < person_arr.length; i++) {
			String per_attr_name = person_arr[i];
			JSONArray perjson = new JSONArray();
			MateAttributeEntity attr = getAttrEntity(per_attr_name);
			Object value = dataMap.get(per_attr_name);
			eventEntity.setPersons(joinSet(perSet));
			if (ObjectUtil.isEmpty(value)) {
				continue;
			}
			if (attr.getVals() == 1) {
				if ((value instanceof Collection)) {
					return R.failed(tit + "==>【" + per_attr_name + "】数据类型不对:" + value);
				}
				perjson.add(value);
			}
			if (attr.getVals() == 2) {
				if (!(value instanceof Collection)) {
					return R.failed(tit + "==>【" + per_attr_name + "】数据类型不对:" + value);
				}
				perjson = (JSONArray) value;
			}
			for (int j = 0; j < perjson.size(); j++) {
				Long obj = (Long) perjson.get(j);
				LibEntityEntity lib_ent = libEntityMapper.selectById(obj);
				if (lib_ent != null) {
					perSet.add(lib_ent.getName());
				}
			}
		}

		// 设置机构
		Set<String> orgSet = new HashSet<>();
		for (int i = 0; i < org_arr.length; i++) {
			String org_attr_name = org_arr[i];
			MateAttributeEntity attr = getAttrEntity(org_attr_name);
			JSONArray orgjson = new JSONArray();
			Object value = dataMap.get(org_attr_name);
			if (ObjectUtil.isEmpty(value)) {
				continue;
			}
			if (attr.getVals() == 1) {
				if ((value instanceof Collection)) {
					return R.failed(tit + "==>【" + org_attr_name + "】数据类型不对:" + value);
				}
				orgjson.add(value);
			}
			if (attr.getVals() == 2) {
				if (!(value instanceof Collection)) {
					return R.failed(tit + "==>【" + org_attr_name + "】数据类型不对:" + value);
				}
				orgjson = (JSONArray) value;
			}
			for (int j = 0; j < orgjson.size(); j++) {
				Long obj = (Long) orgjson.get(j);
				LibEntityEntity lib_ent = libEntityMapper.selectById(obj);
				if (lib_ent != null && lib_ent.getName() != null) {
					QueryWrapper<OrganizationEntity> query = new QueryWrapper<OrganizationEntity>();
					query.eq("name", lib_ent.getName());
					query.or().like("note", lib_ent.getName());
					List<OrganizationEntity> orgList = organizationService.list(query);
					if (orgList.size() != 0) {
						if ("org_ids".equals(org_attr_name)) {
							eventEntity.setOrgid(String.valueOf(orgList.get(0).getId()));
						}
					}
					orgSet.add(lib_ent.getName());
				}
			}
		}
		// 显示的机构
		eventEntity.setOrganisation(joinSet(orgSet));
		
		
		eventEntity.setTag((String) convertValue(dataMap, map.get("tag")));
		// 事件标签
		eventEntity.setCategory((String) convertValue(dataMap, map.get("event_lab_id")));

		eventEntity.setPoliticalWork(coverRules2(eventEntity.getPoliticalWork(), "political_work", dataMap));
		eventEntity.setSecurityManage(coverRules2(eventEntity.getSecurityManage(), "security_manage", dataMap));
		eventEntity.setComprehensiveCoordination(
				coverRules2(eventEntity.getComprehensiveCoordination(), "comprehensive_coordination", dataMap));
		eventEntity.setScientificManage(coverRules2(eventEntity.getScientificManage(), "scientific_manage", dataMap));
		eventEntity.setDecisionMaking(coverRules2(eventEntity.getDecisionMaking(), "decision_making", dataMap));
		eventEntity.setScientificStrength(
				coverRules2(eventEntity.getScientificStrength(), "scientific_strength", dataMap));
		eventEntity.setScientificOrganization(
				coverRules2(eventEntity.getScientificOrganization(), "scientific_environment", dataMap));
		eventEntity
				.setScientificSubject(coverRules2(eventEntity.getScientificSubject(), "scientific_subject", dataMap));
		eventEntity.setScientificResult(coverRules2(eventEntity.getScientificResult(), "scientific_result", dataMap));
		eventEntity
				.setScientificFunding(coverRules2(eventEntity.getScientificFunding(), "scientific_funding", dataMap));
		// 相关批示
		eventEntity.setInstructions((String) convertValue(dataMap, map.get("instruction")));

		Integer score = eventWeight.start(libEntityEntity);
		eventEntity.setWeight(score);
		// 计算tag2字段
		// researchTag2Service.eventGetTag2_2(eventEntity, libEntityEntity);

		Set<String> tag2all = new HashSet<>();
		for (int i = 0; i < tagList.size(); i++) {
			Set<String> tag2s = researchTag2Service.event_tag2_3(tagList.get(i), dataMap);
			tag2all.addAll(tag2s);
		}
		Set<String> pishi = pishi(eventEntity);
		tag2all.addAll(pishi);
		eventEntity.setTag2(joinSet(tag2all));

		eventService.saveOrUpdate(eventEntity);
		return R.ok("转化成功");
	}

	public Set<String> pishi(EventEntity event) {
		Set<String> tag2s = new HashSet<>();
		// 批示字段
		String inst = event.getInstructions();
		if (!StringUtils.isEmpty(inst)) {
			String[] insts = inst.split(";");
			for (int i = 0; i < insts.length; i++) {
				QueryWrapper<LibEntityEntity> libEntityWrapper = new QueryWrapper<>();
				libEntityWrapper.eq("name", insts[i]);
				List<LibEntityEntity> entList = libEntityMapper.selectList(libEntityWrapper);
				if (entList.size() != 0) {
					String data = entList.get(0).getData();
					if (ObjectUtil.isEmpty(data)) {
						continue;
					}
					Map<String, Object> dataMap = JsonUtil.stringToMap(data);
					Object value = dataMap.get("category_id");
					if (ObjectUtil.isEmpty(value)) {
						continue;
					}
					if (value instanceof Collection) {
						for (Object obj : (JSONArray) value) {
							Long iid = null;
							if (obj instanceof Long) {
								iid = (Long) obj;
							}
							MateAttributeValueEntity valEntity = mateAttributeValueService.getById(iid);
							if (valEntity != null) {
								tag2s.add(valEntity.getValName());
							}
						}
					} else {
						Long iid = (Long) value;
						MateAttributeValueEntity valEntity = mateAttributeValueService.getById(iid);
						if (valEntity != null) {
							tag2s.add(valEntity.getValName());
						}
					}

				}
			}
		}
		return tag2s;
	}

	public String joinSet(Set<String> names) {
		StringBuffer sb = new StringBuffer();
		for (String string : names) {
			sb.append(string).append(";");
		}
		return sb.toString();
	}

	public Object convertValue(Map<String, Object> stringObjectMap, MateAttributeEntity attrEnt) {
		if (attrEnt == null) {
			return null;
		}
		Object value = stringObjectMap.get(attrEnt.getNameEn());
		if (value == null) {
			return null;
		}

		Integer vt = attrEnt.getValType();
		// 先判断是否是枚举值属性
		if (attrEnt.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT)) {
			String dataVal = "";
			if (attrEnt.getVals() == 1) {
				Long cid = null;
				if (value instanceof Long) {
					cid = (Long) value;
				}
				if (value instanceof String) {
					cid = Long.parseLong(String.valueOf(value));
				}
				MateAttributeValueEntity attVal = mateAttributeValueService.getById(cid);

				dataVal = attVal.getValName();
			}
			if (attrEnt.getVals() == 2) {
				StringBuffer vals = new StringBuffer();
				for (Object o1 : (JSONArray) value) {
					Long cid = null;
					if (o1 instanceof Long) {
						cid = (Long) o1;
					}
					if (o1 instanceof String) {
						cid = Long.parseLong(String.valueOf(o1));
					}
					MateAttributeValueEntity attVal = mateAttributeValueService.getById(cid);
					vals.append(attVal.getValName()).append(";");
					dataVal = vals.toString();
				}
			}
			return dataVal;
		}
		// 是否下下拉框 对象属性
		if (attrEnt.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_SELECT)) {
			if (attrEnt.getVals() == 1) {
				return getEntityNameOne(value);
			}
			if (attrEnt.getVals() == 2) {
				return getEntityNameMore(value);
			}
		}
		if (vt.equals(YearbookConstant.META_ATTRIBUTE_VAL_NUMBER) || vt.equals(YearbookConstant.META_ATTRIBUTE_VAL_STR)
				|| vt.equals(YearbookConstant.META_ATTRIBUTE_VAL_FLOAT)
				|| vt.equals(YearbookConstant.META_ATTRIBUTE_VAL_DATA)
				|| vt.equals(YearbookConstant.META_ATTRIBUTE_VAL_TIME)
				|| vt.equals(YearbookConstant.META_ATTRIBUTE_VAL_IMAGE)) {
			String dataVal = "";
			if (attrEnt.getVals() == 1) {
				dataVal = object2string(value);
			}
			if (attrEnt.getVals() == 2) {
				StringBuffer vals = new StringBuffer();
				for (Object o1 : (JSONArray) value) {
					vals.append(o1).append(";");
					dataVal = vals.toString();
				}
			}
			return dataVal;
		}
		return null;
	}

	public String object2string(Object o) {
		return o == null ? null : o.toString();
	}

	public static boolean isValidDate(String str) {
		boolean convertSuccess = true;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		try {
			format.setLenient(false);
			format.parse(str);
		} catch (ParseException e) {
			convertSuccess = false;
		}
		return convertSuccess;
	}

	public String getEntityNameOne(Object o) {
		String name = "";
		if (o == null) {
			return name;
		}
		if (o.toString().isEmpty()) {
			return name;
		}
		LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o);
		if (libEntityEntity == null) {
			return name;
		}
		return libEntityEntity.getName();
	}

	public String getEntityNameMore(Object o) {
		String name = "";
		if (o == null) {
			return name;
		}
		if (o.toString().isEmpty()) {
			return name;
		}
		for (Object o1 : (JSONArray) o) {
			LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o1);
			if (libEntityEntity != null && libEntityEntity.getName() != null) {
				if (!name.isEmpty()) {
					name += ";";
				}
				name += libEntityEntity.getName();
			}
		}
		return name;
	}

	/**
	 * 根据属性值查询当前属性的评分
	 */
	public Integer getWeightByVal(MateAttributeEntity attrEnt, Map dataMap) {
		Integer score = 0;
		if (attrEnt == null) {
			return 0;
		}
		Object value = dataMap.get(attrEnt.getNameEn());
		if (value == null) {
			return 0;
		}
		if (value instanceof Collection) {
			int size = ((Collection) value).size();
			if (size == 0) {
				return score;
			}
		}
		if (attrEnt.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_SELECT)) {
			if (attrEnt.getVals() == 1) {
				LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) value);
				if (libEntityEntity != null) {
					score += queryScore(libEntityEntity);
				}
			} else {
				for (Object o1 : (JSONArray) value) {
					LibEntityEntity libEntityEntity = libEntityMapper.selectById((Serializable) o1);
					if (libEntityEntity != null) {
						score += queryScore(libEntityEntity);
					}
				}
			}
		}
		if (attrEnt.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT)) {
			if (attrEnt.getVals() == 1) {
				Long cid = (Long) value;
				MateAttributeValueEntity valEntity = mateAttributeValueService.getById(cid);
				score = valEntity.getScore();

			} else {
				for (Object o1 : (JSONArray) value) {
					Long cid = null;
					if (value instanceof Long) {
						cid = (Long) value;
					}
					if (value instanceof String) {
						cid = Long.parseLong((String) o1);
					}
					if (cid != null) {
						MateAttributeValueEntity valEntity = mateAttributeValueService.getById(cid);
						score += valEntity.getScore();
					}
				}
			}
		}
		return score;
	}

	public Integer queryScore(LibEntityEntity libEntityEntity) {
		Integer score = 0;
		Map<String, Object> stringObjectMap = JsonUtil.stringToMap(libEntityEntity.getData());
		if (stringObjectMap == null) {
			return score;
		}
		Object oid = stringObjectMap.get("category_id");
		if (oid != null) {
			Long cid = (Long) oid;
			MateAttributeValueEntity valEntity = mateAttributeValueService.getById(cid);
			score = valEntity.getScore();
		}
		return score;
	}

	/**
	 * 事件脉络中的十个属性转化规则
	 * 
	 */
	public String coverRules(String attrName, Map dataMap) {
		StringBuffer vals = new StringBuffer();
		List<MateAttributeEntity> list = OntoCache.getAttListByOntoId(YearbookConstant.MARK_EVENT_ONTO_ID);
		List<ConverRulesEntity> ruleList = rulesMap.get(attrName);
		if (ruleList == null) {
			return null;
		}
		for (int i = 0; i < ruleList.size(); i++) {
			ConverRulesEntity rules = ruleList.get(i);
			Long ontoId = rules.getOntoId();

			for (int j = 0; j < list.size(); j++) {
				MateAttributeEntity attr = list.get(j);
				// 如果是对象类型 并且 指向本体和当前规则一致
				if (attr.getValType() == YearbookConstant.META_ATTRIBUTE_VAL_OBJECT && ontoId.equals(attr.getPoint())) {
					Object obj = dataMap.get(attr.getNameEn());
					// log.error("当前属性{},当前值{}", attr.getNameEn(), obj);
					if (obj == null) {
						continue;
					}
					if (attr.getVals() == 1) {
						// 查询 该实体对象
						vals = joinName(vals, obj, rules);
					}
					if (attr.getVals() == 2) {
						if (!(obj instanceof Collection)) {
							throw new DataException("【" + attr.getNameEn() + "】数据类型不对:" + obj);
						}
						for (Object o1 : (JSONArray) obj) {
							// 查询 该实体对象
							vals = joinName(vals, o1, rules);
						}
					}
				}
			}
		}
		return vals.toString();
	}

	/**
	 * 根据 属性规则转化
	 * 
	 * @param value    现有的值
	 * @param attrName 十大要素
	 * @param dataMap  事件数据
	 * @return
	 */
	public String coverRules2(String value, String attrName, Map dataMap) {
		StringBuffer vals = new StringBuffer();
		Set<String> varSet = new HashSet<>();
		List<MateAttributeEntity> list = OntoCache.getAttListByOntoId(YearbookConstant.MARK_EVENT_ONTO_ID);
		Map<String, MateAttributeEntity> map = list.stream()
				.collect(Collectors.toMap(MateAttributeEntity::getNameEn, MateAttributeEntity -> MateAttributeEntity));
		List<ConverRules2Entity> rulelist = rulesMap2.get(attrName);
		if (rulelist == null) {
			return "";
		}
		for (int i = 0; i < rulelist.size(); i++) {
			String attr_name = rulelist.get(i).getAttrName();
			Object obj = dataMap.get(attr_name);
			if (obj == null) {
				continue;
			}
			MateAttributeEntity attr = map.get(attr_name);
			if (attr.getVals() == 1) {
				JSONArray jsarry = new JSONArray();
				jsarry.add(obj);
				obj = jsarry;
			}
			if (attr.getVals() == 2) {
				if (!(obj instanceof Collection)) {
					throw new DataException("【" + attr.getNameEn() + "】数据类型不对:" + obj);
				}
			}
			for (Object ob : (JSONArray) obj) {
				// 查询 该实体对象
				if (attr.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_SELECT)) {
					Long entId = (Long) ob;
					LibEntityEntity libEntityEntity = libEntityMapper.selectById(entId);
					vals.append(libEntityEntity.getName()).append(";");
					varSet.add(libEntityEntity.getName());
				}
				if (attr.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT)) {
					Long entId = (Long) ob;
					MateAttributeValueEntity valueEntity = mateAttributeValueService.getById(entId);
					vals.append(valueEntity.getValName()).append(";");
					varSet.add(valueEntity.getValName());
				}
				if (attr.getType().equals(YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT)) {
					vals.append((String) ob).append(";");
					varSet.add((String) ob);
				}
			}
		}
		return   joinSet(varSet);
		//return vals.toString();
	}

	public StringBuffer joinName(StringBuffer vals, Object obj, ConverRulesEntity rules) {
		log.info("当前查询的实体id：{}", (Long) obj);
		Long entId = (Long) obj;
		LibEntityEntity libEntityEntity = libEntityMapper.selectById(entId);
		if (libEntityEntity != null && libEntityEntity.getData() != null) {
			Map<String, Object> entDataMap = JsonUtil.stringToMap(libEntityEntity.getData());
			Object catId = entDataMap.get("category_id");
			if (catId != null) {
				if (catId instanceof Collection) {
					for (Object id : (JSONArray) catId) {
						Long valCatId = (Long) id;
						if (rules.getCategoryId().equals(valCatId)) {
							vals.append(libEntityEntity.getName()).append(";");
							break;
						}
					}
				} else {
					Long valCatId = (Long) catId;
					if (rules.getCategoryId().equals(valCatId)) {
						vals.append(libEntityEntity.getName()).append(";");
					}
				}
			}
		}
		return vals;
	}

	/**
	 * 批量转化数据
	 */
	@Override
	public R lib2eventNewTable2(EventCrForm even) {

		List<String> sIds = even.getIds();
		for (int i = 0; i < sIds.size(); i++) {
			Long sid = Long.parseLong(sIds.get(i));
			try {
				EventEntity eventEntity = new EventEntity();
				eventEntity.setId(sid);
				eventEntity.setFid(even.getFid());
				R resule = lib2eventNewTable(eventEntity);
				if (resule.getCode() == 1) {
					log.error(resule.getMsg());
				}
			} catch (NumberFormatException e) {
				log.error("id=>{}的事件转化error", sid, e);
			}
		}
		return R.ok();
	}
	
	public  R  lib2eventNewTable3(EventCrForm even) {
		ExecutorService executor = ExecutorBuilder.create()
				.setCorePoolSize(10)
				.setMaxPoolSize(10)
				.setWorkQueue(new LinkedBlockingQueue<>(3000)).build();
		
		QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
		query.select("id");
		List<LibEntityEntity> list = libEntityMapper.selectList(query);
		
		for (int i = 0; i < list.size(); i++) {
			EventEntity eventEntity = new EventEntity();
			eventEntity.setId(list.get(i).getId());
			eventEntity.setFid(even.getFid());
			executor.execute(new Runnable(){
				@Override
				public void run() {
					R resule = lib2eventNewTable(eventEntity);
					System.out.println(eventEntity.getId()+"==============>计算完成"+resule.getMsg());
				}});
		}
		return R.ok();
	}
	
	/**
	 * 主题，所在机构这两个字段都从实体库中查询 数据转化时再添加至主题表和机构表
	 */
	/**
	 *
	 */
	@Transactional
	@Override
	public R reloveExcel(Long id) {

		Long corpusId = projectConfig.getCorpuId();
		Integer version = projectConfig.getCorpuVer();

		UploadfileEntity file = uploadfileService.getById(id);
		if (file == null) {
			return R.failed("请上传文件");
		}
		// 刷新缓存
		// initSys.queryData();
		// 读取文件
		String file_path = projectConfig.getUploadPath() + File.separator + file.getPath();
		ExcelReader reader = ExcelUtil.getReader(file_path);
		List<List<Object>> readAll = reader.read();
		MarkInfoServiceImpl markInfo = new MarkInfoServiceImpl();
		List<MarkSentenceEntity> sentenceList = new ArrayList<MarkSentenceEntity>();
		// 查询全部的本体关系,用onto1,onto2为双键
		List<MetaOntoRelationEntity> ontoRelationList = metaOntoRelationService.list();

		for (int i = 2; i < readAll.size(); i++) {
			try {
				// 先保存句子
				MarkSentenceEntity sentenceEntity = new MarkSentenceEntity();
				sentenceEntity.setCreateTime(LocalDateTime.now());
				sentenceEntity.setStatus(4);
				sentenceEntity.setFid(id);
				sentenceEntity.setId(IdWorker.getId());
				sentenceEntity.setCorpusId(corpusId);
				sentenceEntity.setCorpusVersion(version);
				// 每个句子中的标注数据
				List<MarkEntityEntity> markEntityList = new ArrayList<MarkEntityEntity>();
				List<MarkTripletEntity> markTripletList = new ArrayList<MarkTripletEntity>();
				// 实体关系集合
				List<MetaEntityRelationEntity> relationList = new ArrayList<MetaEntityRelationEntity>();
				List<Object> list = readAll.get(i);
				int null_cell_size = 40 - list.size();
				if (list.size() != 40) {
					for (int j = 0; j < null_cell_size; j++) {
						list.add("");
					}
				}
				LibEntityEntity eveEntity = new LibEntityEntity();
				eveEntity.setCorpusId(projectConfig.getCorpuId());
				// 提前创建事件id
				Long eveEnt_id = IdWorker.getId();
				eveEntity.setId(eveEnt_id);
				JSONObject json = JSONUtil.createObj();
				// 实体关系集合
				json.set("relationList", relationList);
				// 关系:实体 集合，有@的数据在这个集合中查询关系
				List<RelationEntity> relname_entity = new ArrayList<>();
				json.set("relname_entity", relname_entity);
				// 事件id
				json.set("eveEnt_id", eveEnt_id);
				// 事件内容
				String content = (String) list.get(4);
				json.set("content", content);
				json.set("event_overview", content);
				sentenceEntity.setSentenceExc(content);
				EventUtil util = new EventUtil(markEntityList, markTripletList, id, sentenceEntity.getId(), content);
				util.putEveEnt_id(eveEnt_id);
				util.impOntoRelations(ontoRelationList);
				// 事件标题
				String title = (String) list.get(3);
				QueryWrapper<LibEntityEntity> query_title = new QueryWrapper<LibEntityEntity>();
				query_title.eq("name", title);
		 		query_title.eq("onto_id", YearbookConstant.MARK_EVENT_ONTO_ID);
				List<LibEntityEntity> titlelist = libEntityService.list(query_title);
				if (titlelist.size() != 0) {
					eveEntity = titlelist.get(0);
					eveEnt_id = eveEntity.getId();
					json.set("eveEnt_id", eveEnt_id);
				}

				json.set("title", title);
				eveEntity.setName(title);
				sentenceEntity.setSentence(title);
				MarkEntityEntity entity = new MarkEntityEntity();
				entity.setId(IdWorker.getId());
				entity.setLabType(1);
				entity.setTagId(id);
				entity.setText(title);
				entity.setSentenceid(sentenceEntity.getId());
				Integer start_idx = 1;
				entity.setStartindex(start_idx);
				entity.setEndindex(start_idx + title.length());
				util.addEvent(entity);

				// 事件内容
				util.addEntityContentToList();

				// 院
				LibEntityEntity orgEntity = null;
				MateAttributeEntity attrEntity = getAttrEntity("org_ids");
				String str_1 = getObjectVal(list.get(1));
				if(!StringUtils.isEmpty(str_1)) {
					QueryWrapper<LibEntityEntity> query = new QueryWrapper<LibEntityEntity>();
					query.eq("name", str_1);
					query.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
					query.or().like("synonym_word", str_1);
					List<LibEntityEntity> orgList = libEntityService.list(query);
					orgEntity = new LibEntityEntity();
					if (orgList.size() == 0) {
						orgEntity.setName(str_1);
						JSONObject org_obj = JSONUtil.createObj();
						org_obj.set("name", str_1);
						orgEntity.setData(org_obj.toString());
						orgEntity.setSynonymWord(str_1 + ";");
						orgEntity.setOntoId(attrEntity.getPoint());
						libEntityService.save(orgEntity);
					} else {
						orgEntity = orgList.get(0);
					}
					json.set("org_ids", orgEntity.getId());
					util.addEntityToList(str_1, "org_ids");
					MetaEntityRelationEntity orgrel = util.createRelation(orgEntity);
					if (orgrel != null) {
						relationList.add(orgrel);
					}
				}

				

				// 机构所在所
				LibEntityEntity org2Entity = null;
				String str_2 = getObjectVal(list.get(2));
				if(!StringUtils.isEmpty(str_2)) {
					MateAttributeEntity org2_attr_entity = getAttrEntity("org2_id");
					QueryWrapper<LibEntityEntity> org2_query = new QueryWrapper<LibEntityEntity>();
					org2_query.eq("name", str_2);
					org2_query.eq("onto_id", YearbookConstant.MARK_ORG_ONTO_ID);
					List<LibEntityEntity> org2List = libEntityService.list(org2_query);
					org2Entity = new LibEntityEntity();
					if (org2List.size() == 0) {
						org2Entity.setName(str_2);
						JSONObject org_obj = JSONUtil.createObj();
						org_obj.set("name", str_2);
						org_obj.set("pid", orgEntity.getId());
						org2Entity.setData(org_obj.toString());
						org2Entity.setSynonymWord(str_2 + ";");
						org2Entity.setOntoId(org2_attr_entity.getPoint());
						libEntityService.save(org2Entity);
					} else {
						org2Entity = org2List.get(0);
					}
					json.set("org2_id", org2Entity.getId());

					MetaEntityRelationEntity org2rel = util.createRelation(org2Entity);
					if (org2rel != null) {
						relationList.add(org2rel);
					}
				}
				
				if(orgEntity!=null && org2Entity!=null) {
					// 院和所关系-->pid
					QueryWrapper<MetaEntityRelationEntity> org_pid_query = new QueryWrapper<MetaEntityRelationEntity>();
					org_pid_query.eq("entity1_Id", orgEntity.getId());
					org_pid_query.eq("entity2_Id", org2Entity.getId());
					List<MetaEntityRelationEntity> org_rel_list = metaEntityRelationService.list(org_pid_query);
					if(org_rel_list.size()==0) {
						QueryWrapper<MateAttributeEntity> pid_attr = new QueryWrapper<MateAttributeEntity>();
						pid_attr.eq("name_en", "pid");
						List<MateAttributeEntity> pidList = mateAttributeService.list(pid_attr);
						MateAttributeEntity pid_att =  pidList.get(0);
						JSONObject org_obj = JSONUtil.createObj();
						org_obj.set("name", org2Entity.getName());
						org_obj.set("pid", orgEntity.getId());
						org2Entity.setData(org_obj.toString());
						libEntityService.updateById(org2Entity);
						
						MetaEntityRelationEntity org_rela =  new MetaEntityRelationEntity();
						org_rela.setOnto1Id(orgEntity.getOntoId());
						org_rela.setOnto2Id(org2Entity.getOntoId());
						org_rela.setEntity1Id(orgEntity.getId());
						org_rela.setEntity2Id(org2Entity.getId());
						org_rela.setOntoRelId(pid_att.getRelationId());
						org_rela.setEventid(eveEnt_id);
						metaEntityRelationService.save(org_rela);
					}
				}
				//机构  ---->人员,项目,成果   建立关系 用【所】的id，如果没有[所],用【院】id
				Long  org_id = null;
				if(org2Entity!=null) {
					org_id = org2Entity.getId();
				}
				if(org2Entity==null && orgEntity!=null ) {
					org_id = orgEntity.getId();
				}
				

				String times = getObjectVal(list.get(5));
				String[] timesarr = times.split(";");
				if (timesarr.length > 1) {
					String starTime = markInfo.convertTimeStr(timesarr[0]);
					json.set("start_time", starTime);
					if (timesarr.length == 2) {
						String entTime = markInfo.convertTimeStr(timesarr[1]);
						json.set("stop_time", entTime);
					}
					String y = starTime.substring(0, 4);
				}

				setJsonData(list.get(6), "", "learder_decision", json);
				util.addEntityToList(list.get(6), "learder_decision");

				setJsonData(list.get(7), "内部人员", "person_main", json);
				setJsonData(list.get(8), "内部人员", "person_main", json);
				util.addEntityToList(list.get(7), "person_main");
				util.addEntityToList(list.get(8), "person_main");

				/**
				 * 主导人物 和  所在院  建立关系
				 * person_main   和   org_ids   相关机构  
				 *  人员（1586970280968970242） 关系( 1667078893755981825)相关机构   ---------机构（1586973763214368769）
				 */
				cn.hutool.json.JSONArray person_main_arry = (cn.hutool.json.JSONArray)json.get("person_main");
				if(person_main_arry !=null  &&  person_main_arry.size()>0   && orgEntity!=null ) {
					for (int k = 2; k < person_main_arry.size(); k++) {
						Long per_id =  person_main_arry.getLong(k);

							MetaEntityRelationEntity rel_ent =  new MetaEntityRelationEntity();
							rel_ent.setEntity1Id(per_id);
							rel_ent.setOnto1Id(1586970280968970242L);
							rel_ent.setEntity2Id(orgEntity.getId());
							rel_ent.setOnto2Id(1586973763214368769L);
							rel_ent.setOntoRelId(1667078893755981825L);
							rel_ent.setName("相关关系");  
							rel_ent.setEventid(eveEnt_id);
							relationList.add(rel_ent);
						
					}
				}

				setJsonData(list.get(9), "内部人员", "person_related", json);
				setJsonData(list.get(14), "外部人员", "person_related", json);
				util.addEntityToList(list.get(9), "person_related");
				util.addEntityToList(list.get(14), "person_related");

				setJsonData(list.get(11), "外部人员", "person_cooperation", json);
				util.addEntityToList(list.get(11), "person_cooperation");

				setJsonData(list.get(12), "外部人员", "person_exchange", json);
				util.addEntityToList((String) list.get(12), "person_exchange");

				setJsonData(list.get(13), "外部人员", "person_achievements", json);
				util.addEntityToList((String) list.get(13), "person_achievements");

				// 内部外部人员-批示
				instructionHadle(list.get(10), json);
				instructionHadle(list.get(15), json);

				setJsonData(list.get(16), "内部机构", "org_main", json);
				util.addEntityToList((String) list.get(16), "org_main");
					
				
				// setJsonData(list.get(2),"内部机构","org_related",json);
				setJsonData(list.get(17), "内部机构", "org_related", json);
				setJsonData(list.get(21), "外部机构", "org_related", json);
				util.addEntityToList((String) list.get(2), "org_related");
				util.addEntityToList((String) list.get(17), "org_related");
				util.addEntityToList((String) list.get(21), "org_related");

				setJsonData(list.get(18), "外部机构", "org_cooperation", json);
				util.addEntityToList((String) list.get(18), "org_cooperation");

				setJsonData(list.get(19), "外部机构", "org_external", json);
				util.addEntityToList((String) list.get(19), "org_external");

				setJsonData(list.get(20), "外部机构", "org_achievement", json);
				util.addEntityToList(list.get(20), "org_achievement");

				// 工作类型 , 标签查阅使用10要素
				setJsonData(list.get(23), "", "event_lab_id", json);
				util.addValEntityToList((String) list.get(23), "event_lab_id");
				// [工作属性],[工作位置]不导入

				// [环境]同时导入机构和物品
				String str_org = getObjectVal(list.get(26));
				if (!StringUtils.isEmpty(str_org)) {
					String[] str_24_arr = str_org.split(";");
					for (int j = 0; j < str_24_arr.length; j++) {
						String val_text = str_24_arr[j];
						String cate = getCategory(val_text);
						Assert.notNull(cate, "26列缺少双#号类型");
						if (cate.equals("国家重点实验室") || cate.equals("国防科技重点实验室")) {
							setJsonData(val_text, "机构", "org_work_eve", json);
							util.addEntityToList(val_text, "org_work_eve");
						} else {
							setJsonData(val_text, "物品", "obj_work_eve", json);
							util.addEntityToList(val_text, "obj_work_eve");
						}
					}
				}
				// [活动]同时导入 相关课题，相关活动
				String str_sub = getObjectVal(list.get(27));
				if (!StringUtils.isEmpty(str_sub)) {
					String[] str_sub_arr = str_sub.split(";");
					for (int j = 0; j < str_sub_arr.length; j++) {
						String cate = getCategory(str_sub_arr[j]);
						Assert.notNull(cate, "27列缺少双#号类型");
						if (cate.equals("项目")) {
							setJsonData(str_sub_arr[j], "项目", "research_project", json);
							util.addEntityToList(str_sub_arr[j], "research_project");
						} else {
							setJsonData(str_sub_arr[j], "", "operation", json);
							util.addEntityToList(str_sub_arr[j], "operation");
						}
					}
				}
				// 成果
				String str_res = getObjectVal(list.get(28));
				if (!StringUtils.isEmpty(str_res)) {
					String[] str_res_arr = str_res.split(";");
					for (int j = 0; j < str_res_arr.length; j++) {
						setJsonData(str_res_arr[j], "", "achievement", json);
						util.addEntityToList(str_res_arr[j], "achievement");
					}
				}
				if (!ObjectUtil.isEmpty(list.get(29)) && (!"null".equals(list.get(29)))) {
					if (list.get(29) instanceof String) {
						String str_funds = getObjectVal(list.get(29));
						setJsonData(str_funds, "", "funds", json);
						util.addEntityToList(str_funds, "funds");
					} else {
						Long str_funds = (Long) (list.get(29));
						setJsonData(String.valueOf(str_funds), "", "funds", json);
						util.addEntityToList(String.valueOf(str_funds), "funds");
					}
				}

				setJsonData(list.get(30), "", "work_eff", json);
				util.addEntityToList(list.get(30), "work_eff");

				setJsonData(list.get(31), "", "affected_area", json);
				util.addEntityToList(list.get(31), "affected_area");

				setJsonData(list.get(33), "", "affected_org", json);
				util.addEntityToList(list.get(33), "affected_org");

				setJsonData(list.get(32), "", "affected_population", json);
				util.addEntityToList(list.get(32), "affected_population");

				setJsonData(list.get(34), "奖项", "prize", json);
				util.addEntityToList((String) list.get(34), "prize");

				String str_tag = getObjectVal(list.get(35));
				String str_tag1 = "";
				if (!StringUtils.isEmpty(str_tag)) {
					if (str_tag.contains(":")) {
						String[] str_arr = str_tag.split(";");
						for (int j = 0; j < str_arr.length; j++) {
							String[] vals = str_arr[j].split(":");
							str_tag1 = str_tag1 + vals[1] + ";";
						}
					} else {
						str_tag1 = str_tag;
					}
					setJsonData(str_tag1, "", "tag", json);
					util.addEntityToList(list.get(35), "tag");
				}
				MateAttributeEntity tag_attr = getAttrEntity("tag");
				Object obj = json.get("tag");
				cn.hutool.json.JSONArray   tagarr = JSONUtil.createArray();
				if(tag_attr.getVals()==1) {
					String [] tags =  ((String)obj).split(";");
					for (int j = 0; j < tags.length; j++) {
						tagarr.add(tags[j]);
					}  
				}
				if(tag_attr.getVals()==2) {
					tagarr =  (cn.hutool.json.JSONArray )obj;
				}
				List<MetaEntityRelationEntity>  tagRelationList = new ArrayList<MetaEntityRelationEntity>();
				for (int j = 0; j < tagarr.size(); j++) {
					LibEntityEntity tagEntity = new LibEntityEntity();
					tagEntity.setOntoId(YearbookConstant.MARK_TAG_ONTO_ID);
					tagEntity.setName((String)tagarr.get(j));
					libEntityService.create(tagEntity);
					// 成果
					cn.hutool.json.JSONArray achievement = (cn.hutool.json.JSONArray)json.get("achievement");
					if(achievement!=null  &&  achievement.size()>0) {
						for (int k = 0; k < achievement.size(); k++) {
							Long a_id =  achievement.getLong(k);
							MetaEntityRelationEntity result_theme =  new MetaEntityRelationEntity();
							result_theme.setEntity2Id(a_id);
							result_theme.setOnto2Id(YearbookConstant.MARK_RESULT_ONTO_ID);
							result_theme.setEntity1Id(tagEntity.getId());
							result_theme.setOnto1Id(YearbookConstant.MARK_TAG_ONTO_ID);
							result_theme.setOntoRelId(1671422936971554818L);
							result_theme.setName("相关成果");  
							result_theme.setEventid(eveEnt_id);
							tagRelationList.add(result_theme);
						}
					}
					// 项目
					cn.hutool.json.JSONArray research_project = (cn.hutool.json.JSONArray)json.get("research_project");
					if(research_project!=null  &&  research_project.size()>0) {
						for (int k = 0; k < research_project.size(); k++) {
							Long a_id =  research_project.getLong(k);
							MetaEntityRelationEntity project_theme =  new MetaEntityRelationEntity();
							project_theme.setEntity2Id(a_id);
							project_theme.setOnto2Id(YearbookConstant.MARK_PROJECT_ONTO_ID);
							project_theme.setEntity1Id(tagEntity.getId());
							project_theme.setOnto1Id(YearbookConstant.MARK_TAG_ONTO_ID);
							project_theme.setOntoRelId(1671423203167252481L);
							project_theme.setName("相关项目");  
							project_theme.setEventid(eveEnt_id);
							tagRelationList.add(project_theme);
						}
					}
					// 奖项
					cn.hutool.json.JSONArray prize = (cn.hutool.json.JSONArray)json.get("prize");
					if(prize!=null  &&  prize.size()>0) {
						for (int k = 0; k < prize.size(); k++) {
							Long a_id =  prize.getLong(k);
							MetaEntityRelationEntity prize_theme =  new MetaEntityRelationEntity();
							prize_theme.setEntity2Id(a_id);
							prize_theme.setOnto2Id(YearbookConstant.MARK_AWARD_ONTO_ID);
							prize_theme.setEntity1Id(tagEntity.getId());
							prize_theme.setOnto1Id(YearbookConstant.MARK_TAG_ONTO_ID);
							prize_theme.setOntoRelId(1671423147575947266L);
							prize_theme.setName("相关奖项");  
							prize_theme.setEventid(eveEnt_id);
							tagRelationList.add(prize_theme);
						}
					}
					//组织
					Long zuzhi = json.getLong("relation_zuzhi");
					if(zuzhi!=null  ) { 
							MetaEntityRelationEntity zuzhi_theme =  new MetaEntityRelationEntity();
							zuzhi_theme.setEntity2Id(zuzhi);
							zuzhi_theme.setOnto2Id(YearbookConstant.MARK_TEAM_ONTO_ID);
							zuzhi_theme.setEntity1Id(tagEntity.getId());
							zuzhi_theme.setOnto1Id(YearbookConstant.MARK_TAG_ONTO_ID);
							zuzhi_theme.setOntoRelId(1671423647771865090L);
							zuzhi_theme.setName("相关组织");  
							zuzhi_theme.setEventid(eveEnt_id);
							tagRelationList.add(zuzhi_theme);
						}
				}
				
				
				try {
					Object page_obj = list.get(36);
					if (ObjectUtil.isEmpty(page_obj)) {
						throw new DataException(StrUtil.format("第{}行页码没有数据", i + 1));
					}
					String pageNumber = "";
					if (page_obj instanceof Long) {
						Long page = (Long) page_obj;
						pageNumber = String.valueOf(page);
					} else {
						pageNumber = (String) page_obj;
					}
					setJsonData(pageNumber, "", "pageNum", json);

					sentenceEntity.setFsort(Integer.parseInt(pageNumber));
					sentenceList.add(sentenceEntity);
				} catch (Exception e) {
					log.error("pageNum error", e);
					throw new DataException(StrUtil.format("第{}行页码数据转化异常", i + 1));
				}

				String img_name = getObjectVal(list.get(37));
				if (!(StringUtils.isEmpty(img_name))) {
					String[] img_path_arry = img_name.split(";");
					cn.hutool.json.JSONArray img_json_arry = JSONUtil.createArray();
					for (int j = 0; j < img_path_arry.length; j++) {
						String img_path = projectConfig.getExcel_imgpath() + File.separator + img_path_arry[j];
						if (!FileUtil.exist(img_path)) {
							throw new DataException(StrUtil.format("{}文件不存在", img_path));
						}
						UploadfileEntity up = new UploadfileEntity();
						up.setOldName(img_path_arry[j]);
						String suffix = FileUtil.getSuffix(img_path_arry[j]);
						String newName = IdUtil.simpleUUID() + "." + suffix;
						up.setNewName(newName);
						FileUtil.copy(img_path, projectConfig.getUploadPath() + File.separator + newName, false);
						up.setPath(newName);
						up.setCreateTime(LocalDateTime.now());
						uploadfileService.save(up);
						img_json_arry.add(up.getId());
					}
					json.set("image_ids", img_json_arry);
				}

				String str_theme = getObjectVal(list.get(38));
				if (str_theme.contains(";")) {
					throw new DataException(StrUtil.format("第{}行主题数据异常->{}", i + 1, str_theme));
				}
				
				LibEntityEntity themeEntity = null;
				// 主题
				MateAttributeEntity attr = getAttrEntity("theme");
				if (attr.getType() != 1) {
					throw new DataException(StrUtil.format("[theme]应该设置成文本类型"));
				}
				String theme_text = getObjectVal(list.get(38));
				QueryWrapper<EveThemeEntity> theme_query = new QueryWrapper<EveThemeEntity>();
				theme_query.eq("name", theme_text);
				List themelist = eveThemeService.list(theme_query);
				if (themelist.size() == 0) {
					EveThemeEntity ent = new EveThemeEntity();
					ent.setName(theme_text);
					ent.setCategory1(1);
					eveThemeService.save(ent);
				}
				QueryWrapper<LibEntityEntity> themeQuery = new QueryWrapper<LibEntityEntity>();
				themeQuery.eq("name", theme_text);
				themeQuery.eq("onto_id", YearbookConstant.MARK_THEME_ONTO_ID);
				List<LibEntityEntity>  themeList = libEntityService.list(themeQuery);
				if(themeList.size()==0) {
					themeEntity = new LibEntityEntity();
					themeEntity.setName(theme_text);
					themeEntity.setOntoId(YearbookConstant.MARK_THEME_ONTO_ID);
					libEntityService.create(themeEntity);
				}else {
					themeEntity = themeList.get(0);
				}
				
				json.set("theme", theme_text);
				util.addEntityToList((String) list.get(38), "theme");
				
				List<MetaEntityRelationEntity>  themeAndOrgrelationList = new ArrayList<MetaEntityRelationEntity>();
				//主题  --相关-->成果，项目，组织，奖项  
				if(!StringUtils.isEmpty(theme_text)) {
					// 成果
					cn.hutool.json.JSONArray achievement = (cn.hutool.json.JSONArray)json.get("achievement");
					if(achievement!=null  &&  achievement.size()>0) {
						for (int k = 0; k < achievement.size(); k++) {
							Long a_id =  achievement.getLong(k);
							MetaEntityRelationEntity result_theme =  new MetaEntityRelationEntity();
							result_theme.setEntity2Id(a_id);
							result_theme.setOnto2Id(YearbookConstant.MARK_RESULT_ONTO_ID);
							result_theme.setEntity1Id(themeEntity.getId());
							result_theme.setOnto1Id(YearbookConstant.MARK_THEME_ONTO_ID);
							result_theme.setOntoRelId(1671422936971554818L);
							result_theme.setName("相关成果");  
							result_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(result_theme);
						}
					}
					// 项目
					cn.hutool.json.JSONArray research_project = (cn.hutool.json.JSONArray)json.get("research_project");
					if(research_project!=null  &&  research_project.size()>0) {
						for (int k = 0; k < research_project.size(); k++) {
							Long a_id =  research_project.getLong(k);
							MetaEntityRelationEntity project_theme =  new MetaEntityRelationEntity();
							project_theme.setEntity2Id(a_id);
							project_theme.setOnto2Id(YearbookConstant.MARK_PROJECT_ONTO_ID);
							project_theme.setEntity1Id(themeEntity.getId());
							project_theme.setOnto1Id(YearbookConstant.MARK_THEME_ONTO_ID);
							project_theme.setOntoRelId(1671423203167252481L);
							project_theme.setName("相关项目");  
							project_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(project_theme);
						}
					}
					// 奖项
					cn.hutool.json.JSONArray prize = (cn.hutool.json.JSONArray)json.get("prize");
					if(prize!=null  &&  prize.size()>0) {
						for (int k = 0; k < prize.size(); k++) {
							Long a_id =  prize.getLong(k);
							MetaEntityRelationEntity prize_theme =  new MetaEntityRelationEntity();
							prize_theme.setEntity2Id(a_id);
							prize_theme.setOnto2Id(YearbookConstant.MARK_AWARD_ONTO_ID);
							prize_theme.setEntity1Id(themeEntity.getId());
							prize_theme.setOnto1Id(YearbookConstant.MARK_THEME_ONTO_ID);
							prize_theme.setOntoRelId(1671423147575947266L);
							prize_theme.setName("相关奖项");  
							prize_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(prize_theme);
						}
					}
					//组织
					Long zuzhi = json.getLong("relation_zuzhi");
					if(zuzhi!=null  ) { 
							MetaEntityRelationEntity zuzhi_theme =  new MetaEntityRelationEntity();
							zuzhi_theme.setEntity2Id(zuzhi);
							zuzhi_theme.setOnto2Id(YearbookConstant.MARK_TEAM_ONTO_ID);
							zuzhi_theme.setEntity1Id(themeEntity.getId());
							zuzhi_theme.setOnto1Id(YearbookConstant.MARK_THEME_ONTO_ID);
							zuzhi_theme.setOntoRelId(1671423647771865090L);
							zuzhi_theme.setName("相关组织");  
							zuzhi_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(zuzhi_theme);
						}
					
				}
				//院所  --相关-->成果，项目，奖项
				if(org_id!=null) {
					// 成果
					cn.hutool.json.JSONArray achievement = (cn.hutool.json.JSONArray)json.get("achievement");
					if(achievement!=null  &&  achievement.size()>0) {
						for (int k = 0; k < achievement.size(); k++) {
							Long a_id =  achievement.getLong(k);
							MetaEntityRelationEntity result_theme =  new MetaEntityRelationEntity();
							result_theme.setEntity2Id(a_id);
							result_theme.setOnto2Id(YearbookConstant.MARK_RESULT_ONTO_ID);
							result_theme.setEntity1Id(org_id);
							result_theme.setOnto1Id(YearbookConstant.MARK_ORG_ONTO_ID);
							result_theme.setOntoRelId(1671422840888438785L);
							result_theme.setName("相关成果");  
							result_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(result_theme);
						}
					}
					// 项目
					cn.hutool.json.JSONArray research_project = (cn.hutool.json.JSONArray)json.get("research_project");
					if(research_project!=null  &&  research_project.size()>0) {
						for (int k = 0; k < research_project.size(); k++) {
							Long a_id =  research_project.getLong(k);
							MetaEntityRelationEntity project_theme =  new MetaEntityRelationEntity();
							project_theme.setEntity2Id(a_id);
							project_theme.setOnto2Id(YearbookConstant.MARK_PROJECT_ONTO_ID);
							project_theme.setEntity1Id(org_id);
							project_theme.setOnto1Id(YearbookConstant.MARK_ORG_ONTO_ID);
							project_theme.setOntoRelId(1671423029648896002L);
							project_theme.setName("相关项目");  
							project_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(project_theme);
						}
					}
					// 奖项
					cn.hutool.json.JSONArray prize = (cn.hutool.json.JSONArray)json.get("prize");
					if(prize!=null  &&  prize.size()>0) {
						for (int k = 0; k < prize.size(); k++) {
							Long a_id =  prize.getLong(k);
							MetaEntityRelationEntity prize_theme =  new MetaEntityRelationEntity();
							prize_theme.setEntity2Id(a_id);
							prize_theme.setOnto2Id(YearbookConstant.MARK_AWARD_ONTO_ID);
							prize_theme.setEntity1Id(org_id);
							prize_theme.setOnto1Id(YearbookConstant.MARK_ORG_ONTO_ID);
							prize_theme.setOntoRelId(1671423114784878594L);
							prize_theme.setName("相关奖项");  
							prize_theme.setEventid(eveEnt_id);
							themeAndOrgrelationList.add(prize_theme);
						}
					}
				}
				setJsonData(list.get(39), "", "object_ids", json);
				util.addEntityToList((String) list.get(39), "object_ids");
				//当前事件所在目录名称
				String  dir_name = getObjectVal(list.get(40));
				json.set("directory", dir_name)	;
				
				json.remove("relname_entity");
				json.remove("eveEnt_id");
				cn.hutool.json.JSONArray relationarry = (cn.hutool.json.JSONArray) json.get("relationList");
				relationList = JSONUtil.toList(relationarry, MetaEntityRelationEntity.class);
				json.remove("relationList");
				eveEntity.setData(json.toString());
				eveEntity.setOntoId(YearbookConstant.MARK_EVENT_ONTO_ID);
				libEntityService.saveOrUpdate(eveEntity);
				
				relationList.addAll(tagRelationList);
				relationList.addAll(themeAndOrgrelationList);
				metaEntityRelationService.saveBatch(relationList);

				markEntityService.saveBatch(util.getEntitleList());
				markTripletService.saveBatch(util.getRelation());
			} catch (Exception e) {
				log.error("转成实体功能，第{}行数据error=>", i + 1, e);
			}
		}
		return R.ok();
	}

	public Long text_sele(MateAttributeEntity attrEntity, String val) {
		List<MateAttributeValueEntity> attrList = OntoCache.getAttrValListByAttrId(attrEntity.getId());
		MateAttributeValueEntity valent = attrList.stream().filter(x -> x.getValName().equals(val)).findAny()
				.orElse(null);
		if (valent != null) {
			return valent.getId();
		} else {
			return null;
		}
	}

	private Map<String, MateAttributeEntity> attr_Map = new HashedMap<>();

	/**
	 * 根据属性名称和事件本体id获取属性对象
	 * 
	 * @param attr_name
	 */
	public MateAttributeEntity getAttrEntity(String attr_name) {
		if (attr_Map.get(attr_name) != null) {
			return attr_Map.get(attr_name);
		}
		QueryWrapper<MateAttributeEntity> query = new QueryWrapper<MateAttributeEntity>();
		query.lambda().and(t -> t.eq(MateAttributeEntity::getNameEn, attr_name));
		query.lambda().and(t -> t.eq(MateAttributeEntity::getOnto, YearbookConstant.MARK_EVENT_ONTO_ID));
		List<MateAttributeEntity> attrList = mateAttributeService.list(query);
		attr_Map.put(attr_name, attrList.get(0));
		return attrList.get(0);
	}

	/**
	 * 根据属性名称查询关系
	 * 
	 * @param attr_name
	 * @return
	 */
	public MetaOntoRelationEntity getRelation(String attr_name) {
		MateAttributeEntity attrEntity = getAttrEntity(attr_name);
		MetaOntoRelationEntity relationEntity = null;
		Long relationId = attrEntity.getRelationId();
		relationEntity = metaOntoRelationService.getById(relationId);
		return relationEntity;
	}

	/**
	 * 解析Object返回字符串
	 * 
	 * @param obj
	 * @return
	 */
	public String getObjectVal(Object obj) {
		String str = (String) obj;
		if (!StringUtils.isEmpty(str)) {
			str = str.replace("；", ";");
		} else {
			str = "";
		}
		return str;
	}

	public boolean isNotNull(Object obj) {
		boolean fal = false;
		if (obj instanceof String) {
			String str = (String) obj;
			if ((StringUtils.isEmpty(str) || "null".equals(str))) {
				return fal;
			}
		}
		if (obj instanceof JSONObject) {
			JSONObject json = (JSONObject) obj;
			if (json.size() == 0) {
				return fal;
			}
		}
		return true;
	}

	/**
	 * 
	 * @param cell      原始数据
	 * @param categor   分类
	 * @param attr_name 属性名
	 * @param json      json
	 */
	public void setJsonData(Object cell, String categor, String attr_name, JSONObject json) throws Exception {

		String text = getObjectVal(cell);
		if (!isNotNull(text)) {
			return;
		}
		MateAttributeEntity attr = getAttrEntity(attr_name);
		// 文本类型
		if (attr.getType() == YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT) {
			if (attr.getVals() == 1) {
				json.set(attr_name, text);
			}
			if (attr.getVals() == 2) {
				cn.hutool.json.JSONArray jsonarry = JSONUtil.createArray();
				String[] texts = text.split(";");
				for (int i = 0; i < texts.length; i++) {
					jsonarry.add(texts[i]);
				}
				if (!ObjectUtil.isEmpty(json.get(attr_name))) {
					cn.hutool.json.JSONArray arr = (cn.hutool.json.JSONArray) json.get(attr_name);
					jsonarry.addAll(arr);
				}
				json.set(attr_name, jsonarry);
			}
		}
		// 对象类型
		if (attr.getType() == YearbookConstant.META_ATTRIBUTE_FROM_TPYE_SELECT) {
			Long eveEnt_id = json.getLong("eveEnt_id");
			cn.hutool.json.JSONArray jsonarry = getTypeEq2(json, eveEnt_id, cell, categor, attr);

		}
		// 枚举类型
		if (attr.getType() == YearbookConstant.META_ATTRIBUTE_FROM_TPYE_TEXT_SELECT) {
			cn.hutool.json.JSONArray jsonarry = getTypeEq3(cell, attr);
			if (!isNotNull(jsonarry)) {
				return;
			}
			if (attr.getVals() == 1) {
				json.set(attr_name, jsonarry.get(0));
			} else {
				if (!ObjectUtil.isEmpty(json.get(attr_name))) {
					cn.hutool.json.JSONArray arr = (cn.hutool.json.JSONArray) json.get(attr_name);
					jsonarry.addAll(arr);
				}
				json.set(attr_name, jsonarry);
			}
		}
	}

	/**
	 * 关系名称:#分类名称#当前列的实体名称@实体1名称
	 * 
	 * @param json
	 * @param eveEnt_id
	 * @param obj
	 * @param categor
	 * @param attrEntity
	 * @return
	 */
	public cn.hutool.json.JSONArray getTypeEq2(JSONObject json, Long eveEnt_id, Object obj, String categor,
			MateAttributeEntity current_attrEntity) {
		//判断函数的入口
		String current_attr_name =  current_attrEntity.getNameEn();
		//当前属性要保存的json
		cn.hutool.json.JSONArray jsonarry = JSONUtil.createArray();
		//相关组织，相关项目保存的json
		cn.hutool.json.JSONArray jsonarry1 = JSONUtil.createArray();
		MateAttributeEntity  attrEntity1 =  null;
		
		String str = getObjectVal(obj);
		cn.hutool.json.JSONArray relationarry = (cn.hutool.json.JSONArray) json.get("relationList");
		;
		List<MetaEntityRelationEntity> relationList = JSONUtil.toList(relationarry, MetaEntityRelationEntity.class);

		cn.hutool.json.JSONArray array = (cn.hutool.json.JSONArray) json.get("relname_entity");

		List<RelationEntity> relname_entity = JSONUtil.toList(array, RelationEntity.class);
//		List<RelationEntity>   relname_entity = JSONUtil.toList(json.get("relname_entity").toString(), RelationEntity.class);
		if (StringUtils.isEmpty(str)) {
			return jsonarry;
		}
		String[] str_arry = str.replace("；", ";").split(";");

		
		// 多个@符号，分隔后的数组
		String entity1Arry[] = null;
		// 把实体id保存到jsonarry
		for (int i = 0; i < str_arry.length; i++) {
			String split_text = str_arry[i];
			
			MateAttributeEntity attrEntity = current_attrEntity;
			// categor用数据表示，在lambda表达式中使用
			String[] cates = new String[1];
			cates[0] = categor;
			// 获取当前属性对象信息
			Long onto_id = attrEntity.getPoint();
			// 当前列的实体名;关系名称;如果有@符号,实体1名称
			String name;
			String relat_name[] = new String[1];
			// 不包含@#:
			name = split_text.replace("：", ":");
			// 如果有：，则前面的是关系名称
			if (name.contains(":")) {
				String[] name_arry = name.split(":");
				relat_name[0] = name_arry[0];
				name = name_arry[1];
			}
			// 用正则获取分类名称
			if (name.contains("#")) {
				String categor1 = getCategory(name);
				cates[0] = categor1;
				name = name.replaceAll("#.*?#", "");
			}
			// 如果包含@，则在前面保存到关系集合查找关系
			if (name.contains("@")) {
				entity1Arry = name.split("@");
				name = entity1Arry[0];
			}
			name = name.trim();
			if (!StringUtils.isEmpty(relat_name[0])) {
				relat_name[0] = relat_name[0].trim();
			}
			cates[0] = cates[0].trim();
			if (StringUtils.isEmpty(cates[0])) {
				throw new DataException(StrUtil.format("当前数据[{}]缺少分类", name));
			}
			//默认是1，表示用的是当前列属性；如果是团队或项目则为2
			int    isOther = 1;
			if ("团队".equals(cates[0])) {
				onto_id = YearbookConstant.MARK_TEAM_ONTO_ID;
				//显示的机构    //这两个字段的数据放入"relation_zuzhi"
				if( current_attr_name.equals("org_main") ||  current_attr_name.equals("org_cooperation") ) {
					attrEntity = getAttrEntity("relation_zuzhi");
					attrEntity1 = attrEntity;
				}else {
				//不显示的机构
					attrEntity = getAttrEntity("relation_zuzhi_no");
					attrEntity1 = attrEntity;
				}
				isOther = 2 ;
			}
			if ("项目".equals(cates[0])) {
				onto_id = YearbookConstant.MARK_PROJECT_ONTO_ID;
				attrEntity = getAttrEntity("research_project");
				attrEntity1 = attrEntity;
				isOther = 2 ;
			}
			// 当前属性指向本体的“category_id”属性
			List<MateAttributeEntity> attrList = OntoCache.getAttListByOntoId(onto_id);
			MateAttributeEntity attr_cate = attrList.stream().filter(x -> x.getNameEn().equals("category_id")).findAny()
					.orElse(null);

			LibConceptEntity onto = libConceptService.getById(onto_id);
			Assert.notNull(onto, StrUtil.format("当前本体[{}]没有category_id属性", onto.getName()));

			Long[] ontoIds = { onto_id };
			// 根据categor 查询 “category_id”属性的值
			List<MateAttributeValueEntity> attrValList = OntoCache.getAttrValListByAttrId(attr_cate.getId());
			MateAttributeValueEntity valEntity = attrValList.stream()
					.filter(x -> (x.getValName().equals(cates[0]) && x.getOntoId().equals(ontoIds[0]))).findAny()
					.orElse(null);
			Assert.notNull(valEntity, StrUtil.format("当前本体[{}]的category_id属性缺少该值[{}]", onto.getName(), cates[0]));
			// 保存实体和属性
			QueryWrapper<LibEntityEntity>  query = new QueryWrapper<LibEntityEntity>();
			query.eq("name", name);
			query.eq("onto_id",onto_id);
			List<LibEntityEntity> nameList = libEntityMapper.selectList(query);
			LibEntityEntity entity = null;
			cn.hutool.json.JSONObject data = JSONUtil.createObj();
			if (nameList.size() == 0) {
				entity = new LibEntityEntity();
				entity.setName(name);
				data.set("category_id", valEntity.getId());
				data.set("name", name);
				entity.setData(data.toString());
				entity.setOntoId(attrEntity.getPoint());
				libEntityService.create(entity);
			} else {
				entity = nameList.get(0);
				if (!StringUtils.isEmpty(entity.getData())) {
					data = JSONUtil.parseObj(entity.getData());
				} else {
					data = JSONUtil.createObj();
				}
				data.set("category_id", valEntity.getId());
				String new_data = data.toString();
				entity.setData(new_data);
			}
			if(isOther ==1 ) {
				if(!jsonarry.contains(entity.getId())){
					jsonarry.add(entity.getId());	
				}
			}else {
				if(!jsonarry1.contains(entity.getId())){
					jsonarry1.add(entity.getId());	
				}
			}
			
			// 保存原始数据上的关系:实体
			if (split_text.contains(":")) {

				List<MetaOntoRelationEntity> relationEntityList = OntoCache.getMetaOntoRelationListByOnto1Id(onto_id);
				if (ObjectUtil.isEmpty(relationEntityList)) {
					throw new DataException(StrUtil.format("本体1[{}]没有关系数据", onto_id));
				}
				// 当前本体的关系，可能有多个同名关系
				List<MetaOntoRelationEntity> relations = relationEntityList.stream()
						.filter(x -> x.getName().equals(relat_name[0])).collect(Collectors.toList());
				if (relations.size() == 0) {
					throw new DataException(StrUtil.format("本体[{}]没有[{}]关系", onto.getName(), relat_name));
				}
				for (int j = 0; j < relations.size(); j++) {
					MetaOntoRelationEntity relation = relations.get(j);
					RelationEntity entityRelation = new RelationEntity();
					entityRelation.setEntityId(entity.getId());
					entityRelation.setEntity1(name);
					entityRelation.setOnto1(onto_id);
					entityRelation.setRelationName(relat_name[0]);
					entityRelation.setRelationId(relation.getId());
					entityRelation.setOnto2(relation.getOnto2Id());
					relname_entity.add(entityRelation);
				}
			}
			// 如果@后面有数据
			// 当前列为本体2，@后面是实体1的名称，根据这两个条件查询前面列的数据
			if (split_text.contains("@")) {
				for (int j = 1; j < entity1Arry.length; j++) {
					String entity1 = entity1Arry[j].trim();
					RelationEntity en = relataionHandle(relname_entity, onto_id, entity1);
					Assert.notNull(en, StrUtil.format("没有找到[@{}]--[{}]本体的关系", entity1, onto_id));
					MetaEntityRelationEntity entRelation = new MetaEntityRelationEntity();
					entRelation.setEntity1Id(en.getEntityId());
					entRelation.setEntity2Id(entity.getId());
					entRelation.setOnto1Id(en.getOnto1());
					entRelation.setOnto2Id(onto_id);
					entRelation.setName(en.getRelationName());
					entRelation.setOntoRelId(en.getRelationId());
					entRelation.setEventid(eveEnt_id);
					relationList.add(entRelation);
				}
			}
		}
		json.set("relname_entity", relname_entity);
		MetaOntoRelationEntity relation = metaOntoRelationService.getById(current_attrEntity.getRelationId());
		//处理	 jsonarry 的数据
		// 创建实体关系 并 保存至 relationList
		for (int i = 0; i < jsonarry.size(); i++) {
			MetaEntityRelationEntity entRelation = new MetaEntityRelationEntity();
			entRelation.setEntity1Id(eveEnt_id);
			entRelation.setEntity2Id(jsonarry.getLong(i));
			entRelation.setOnto1Id(YearbookConstant.MARK_EVENT_ONTO_ID);
			entRelation.setOnto2Id(current_attrEntity.getPoint());
			entRelation.setName(relation.getName());
			entRelation.setOntoRelId(relation.getId());
			entRelation.setEventid(eveEnt_id);
			relationList.add(entRelation);
		}
		
		//当前数据存入json中
		String attr_name = current_attrEntity.getNameEn();
		if (!isNotNull(jsonarry)) {
			return jsonarry;
		}
		if (current_attrEntity.getVals() == 1) {
			json.set(current_attrEntity.getNameEn(), jsonarry.get(0));
		} else {
			if (!ObjectUtil.isEmpty(json.get(attr_name))) {
				cn.hutool.json.JSONArray arr = (cn.hutool.json.JSONArray) json.get(attr_name);
				for(int i=0;i<arr.size();i++) {
					if(!jsonarry.contains(arr.get(i))) {
						jsonarry.add(arr.get(i));
					}
				}
			}
			json.set(attr_name, jsonarry);
		}
		//处理	 jsonarry1 的数据   于 上面的代码逻辑一致
		if(attrEntity1!=null) {
			for (int i = 0; i < jsonarry1.size(); i++) {
				MetaEntityRelationEntity entRelation = new MetaEntityRelationEntity();
				entRelation.setEntity1Id(eveEnt_id);
				entRelation.setEntity2Id(jsonarry1.getLong(i));
				entRelation.setOnto1Id(YearbookConstant.MARK_EVENT_ONTO_ID);
				entRelation.setOnto2Id(attrEntity1.getPoint());
				entRelation.setName(relation.getName());
				entRelation.setOntoRelId(relation.getId());
				entRelation.setEventid(eveEnt_id);
				relationList.add(entRelation);
			}
			attr_name = attrEntity1.getNameEn();
			if (attrEntity1.getVals() == 1) {
				json.set(attrEntity1.getNameEn(), jsonarry1.get(0));
			}
			else {
				if (!ObjectUtil.isEmpty(json.get(attr_name))) {
					cn.hutool.json.JSONArray arr = (cn.hutool.json.JSONArray) json.get(attr_name);
					jsonarry1.addAll(arr);
				}
				json.set(attr_name, jsonarry1);
			}
		}
		json.set("relationList", relationList);
		return jsonarry;
	}

	/**
	 * 枚举数据
	 */
	public cn.hutool.json.JSONArray getTypeEq3(Object obj, MateAttributeEntity attr) {
		String str = getObjectVal(obj);
		String[] str_arry = str.split(";");
		// 获取当前属性对象
		cn.hutool.json.JSONArray jsonarry = JSONUtil.createArray();
		for (int i = 0; i < str_arry.length; i++) {
			String val = str_arry[i];
			Long entId = text_sele(attr, val);
			Assert.notNull(entId, "本体[" + attr.getOnto() + "]的" + attr.getNameEn() + "属性没有[{" + val + "}]枚举值");
			jsonarry.add(entId);
		}
		return jsonarry;
	}

	/**
	 * 处理[批示数据]
	 * 
	 * @param cell
	 * @param categor
	 * @param json
	 */
	public void instructionHadle(Object cell, JSONObject json) {
		String text = getObjectVal(cell);
		if (!isNotNull(text)) {
			return;
		}
		cn.hutool.json.JSONArray instru_arry = JSONUtil.createArray();

		String attr_name = "instruction";
		MateAttributeEntity attr = getAttrEntity(attr_name);

		String[] texts = text.split(";");
		for (int i = 0; i < texts.length; i++) {

			String leve = getCategory(texts[i]);
			String name = texts[i].replaceAll("#.*?#", "");

			// cn.hutool.json.JSONArray arry = getTypeEq2(leve, attr);
			Long instruction_onto_id = attr.getPoint();
			List<MateAttributeEntity> attrList = OntoCache.getAttListByOntoId(instruction_onto_id);
			MateAttributeEntity instruction_cate = attrList.stream().filter(x -> x.getNameEn().equals("category_id"))
					.findAny().orElse(null);
			Assert.notNull(instruction_cate, StrUtil.format("当前本体[{}]没有category_id属性", instruction_onto_id));
			List<MateAttributeValueEntity> attrValList = OntoCache.getAttrValListByAttrId(instruction_cate.getId());
			MateAttributeValueEntity cate_value = attrValList.stream().filter(x -> x.getValName().equals(leve))
					.findAny().orElse(null);
			Assert.notNull(instruction_cate, StrUtil.format("当前属性[{}]没有[{}]值", instruction_cate.getNameEn(), leve));

			QueryWrapper<LibEntityEntity> persion_query = new QueryWrapper<LibEntityEntity>();
			persion_query.eq("name", name);
			List<LibEntityEntity> pers = libEntityService.list(persion_query);
			LibEntityEntity per_ent = null;
			if (pers.size() == 0) {
				per_ent = new LibEntityEntity();
				per_ent.setName(name);
				libEntityService.save(per_ent);
			} else {
				per_ent = pers.get(0);
			}

			String ins_name = name + "批示";
			QueryWrapper<LibEntityEntity> query_persion = new QueryWrapper<LibEntityEntity>();
			query_persion.eq("name", ins_name);
			List<LibEntityEntity> nameList = libEntityService.list(query_persion);
			LibEntityEntity entity = null;
			cn.hutool.json.JSONObject data = JSONUtil.createObj();
			if (nameList.size() == 0) {
				entity = new LibEntityEntity();
				entity.setName(ins_name);
				data.set("name", ins_name);
				data.set("category_id", cate_value.getId());
				data.set("instruction_person", per_ent.getId());
				entity.setData(data.toString());
				entity.setOntoId(instruction_onto_id);
				libEntityService.create(entity);
			} else {
				entity = nameList.get(0);
			}
			instru_arry.add(entity.getId());
		}
		if (!ObjectUtil.isEmpty(json.get(attr_name))) {
			cn.hutool.json.JSONArray arr = (cn.hutool.json.JSONArray) json.get(attr_name);
			instru_arry.addAll(arr);
		}
		json.set(attr_name, instru_arry);
	}

	/**
	 * 
	 * @param list      集合
	 * @param onto2     本体2id
	 * @param enti1name 实体1名称
	 * @return
	 */
	public RelationEntity relataionHandle(List<RelationEntity> list, Long onto2, String enti1name) {
		RelationEntity entity = list.stream()
				.filter(x -> x.getEntity1().equals(enti1name) && x.getOnto2().equals(onto2)).findAny().orElse(null);
		return entity;
	}

//	
//	public void  getPar(String text) {
//		Object [] all =  new Object [4];
//		
//		// 当前列的实体名;关系名称;如果有@符号,实体1名称
//		String name, relat_name categor;
//		// 多个@符号，分隔后的数组
//		String entity1Arry[] = null;
//		
//		//不包含@#:
//		name = text.replace("：", ":");
//		//如果有：，则前面的是关系名称
//		if (name.contains(":") ) {
//			String[] name_arry = name.split(":");
//			relat_name = name_arry[0];
//			name = name_arry[1];
//		}
//		//用正则获取分类名称
//		if(name.contains("#")) {
//			categor = getCategory(name);
//			cates[0] = categor;
//			name = name.replaceAll("#.*?#", "");
//		}
//		// 如果包含@，则在前面保存到关系集合查找关系
//		if(name.contains("@")) {
//			entity1Arry = name.split("@");
//			name = entity1Arry[0];
//		}
//	}

	/**
	 * 提取两个#中间的分类名称
	 * 
	 * @param str
	 * @return
	 */
	public String getCategory(String str) {
		Pattern pattern = Pattern.compile("#(.*?)#");
		Matcher matcher = pattern.matcher(str);
		String result = null;
		// 输出所有匹配的结果
		while (matcher.find()) {
			result = matcher.group(1);
		}
		return result;
	}

	@Override
	public R checkDataBase() {
		String error_data = System.getProperty("user.dir") + File.separator + "error_data.log";
		File file = new File(error_data);
		FileUtil.del(file);
		FileAppender appender = new FileAppender(file, 100, true);

		String error_onto_data = System.getProperty("user.dir") + File.separator + "error_onto_data.log";
		File ontofile = new File(error_onto_data);
		FileUtil.del(ontofile);
		FileAppender ontoappender = new FileAppender(ontofile, 100, true);

		long count = libEntityService.count();
		int pages = 1;
		if (count > 100) {
			pages = (int) (count / 100 + 1);
		} else {
			pages = 1;
		}
		for (int i = 1; i <= pages; i++) {
			Page<LibEntityEntity> page = new Page<>(i, 100);
			IPage<LibEntityEntity> useIPage = libEntityService.page(page);
			List<LibEntityEntity> entList = useIPage.getRecords();

			for (int k = 0; k < entList.size(); k++) {
				LibEntityEntity x = entList.get(k);
				if (ObjectUtil.isEmpty(x.getOntoId())) {
					appender.append("[" + x.getId() + "]缺少本体id;");
					continue;
				}
				if (ObjectUtil.isEmpty(x.getData())) {
					continue;
				}

				if (ObjectUtil.isEmpty(OntoCache.ontoIdMap.get(x.getOntoId()))) {
					continue;
				}
				Map<String, Object> dataMap = JsonUtil.stringToMap(x.getData());
				List<MateAttributeEntity> attrList = OntoCache.getAttListByOntoId(x.getOntoId());
				for (int j = 0; j < attrList.size(); j++) {
					MateAttributeEntity attr = attrList.get(j);
					if ((attr.getType() == 2 && attr.getValType() != 4)
							|| (attr.getType() != 2 && attr.getValType() == 4)) {
						ontoappender.append("[" + x.getOntoId() + "]本体的[" + attr.getNameEn() + "]属性type和valType不匹配！");
						continue;
					}
					Object obj = dataMap.get(attr.getNameEn());
					if (ObjectUtil.isEmpty(obj)) {
						continue;
					}
					if (attr.getVals() == 2 && !(obj instanceof Collection)) {
						appender.append("[" + x.getId() + "]实体的[" + attr.getNameEn() + "]的值是多个值，但元数据是单值");
						continue;
					}
					if (attr.getVals() == 1 && (obj instanceof Collection)) {
						appender.append("[" + x.getId() + "]实体的[" + attr.getNameEn() + "]的值是单个值，但元数据是多值");
						continue;
					}
					if (attr.getVals() == 1) {
						if ((attr.getType() == 2 || attr.getType() == 3 || attr.getValType() == 4
								|| attr.getValType() == 7 || attr.getValType() == 8) && (obj instanceof String)) {
							appender.append("[" + x.getId() + "]实体的[" + attr.getNameEn() + "]的值应是对象id,但现在是字符串类型");
							continue;
						}
						if (attr.getValType() == 1 && (obj instanceof String)) {
							appender.append("[" + x.getId() + "]实体的[" + attr.getNameEn() + "]的值应是整形数据,但现在是字符串类型");
							continue;
						}
						if (attr.getValType() == 3 && (obj instanceof String)) {
							appender.append("[" + x.getId() + "]实体的[" + attr.getNameEn() + "]的值应是浮点型数据,但现在是字符串类型");
							continue;
						}
					}
					if (attr.getVals() == 2) {
						JSONArray array = (com.alibaba.fastjson.JSONArray) obj;
						Object obj1 = array.get(0);
						if ((attr.getType() == 2 || attr.getType() == 3 || attr.getValType() == 4
								|| attr.getValType() == 7 || attr.getValType() == 8) && (obj instanceof String)) {
							appender.append("[" + x.getId() + "]实体的[" + attr.getNameEn() + "]的值应是对象id,但现在是字符串类型");
							continue;
						}

					}
				}
			}
		}
		appender.append("检查完成");
		appender.flush();
		ontoappender.append("检查完成");
		ontoappender.flush();

		return R.ok("正在后台计算，请稍候");
	}

}

@Data
class RelationEntity {
	Long onto1;
	Long entityId;
	String entity1;
	Long onto2;
	String entity2;
	Long relationId;
	String relationName;
}
