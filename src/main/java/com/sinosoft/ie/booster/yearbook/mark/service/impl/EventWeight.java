package com.sinosoft.ie.booster.yearbook.mark.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import com.sinosoft.ie.booster.yearbook.baselib.service.MateAttributeValueService;

import cn.hutool.core.util.ObjectUtil;

@Service
public class EventWeight {

	@Autowired
	private LibEntityService libEntityService;
	@Resource
	private MateAttributeValueService mateAttributeValueService;

	public Integer start(LibEntityEntity libEntityEntity) {
		Integer weight = 0;
		String data = libEntityEntity.getData();
		Map<String, Object> dataMap = JsonUtil.stringToMap(data);
		if(ObjectUtil.isEmpty(dataMap)) {
			return weight;
		}
		// 这三个对象的类型的分数累加  //  这个属于枚举值 "affected_area"
		String attrs[] = { "prize", "instruction" };
		for (int i = 0; i < attrs.length; i++) {
			Object attr_val = dataMap.get(attrs[i]);
			if (!ObjectUtil.isEmpty(attr_val)) {
				JSONArray prizes = (JSONArray) attr_val;
				List<Integer> scores = new ArrayList<>();
				for (Object obj : (JSONArray) prizes) {
					Long id = (Long) obj;
					LibEntityEntity entity = queryEntity(id);
					if(ObjectUtil.isEmpty(entity.getData())) {
						continue;
					}
					Map<String, Object> cateMap = JsonUtil.stringToMap(entity.getData());
					Object cate_obj = cateMap.get("category_id");
					if(ObjectUtil.isEmpty(cate_obj)) {
						continue;
					}
					Long cate_id = (Long) cate_obj;
					Integer score = queryScore(cate_id);
					scores.add(score);
				}
				Integer score = getMaxScore(scores);
				weight += score;
			}
		}
		
		Object affected_area_obj = dataMap.get("affected_area");
		if (!ObjectUtil.isEmpty(affected_area_obj)) {
			JSONArray affected_area = (JSONArray) affected_area_obj;
			List<Integer> scores = new ArrayList<>();
			for (Object obj : (JSONArray) affected_area) {
				Long id = (Long) obj;
				Integer score = queryScore(id);
				scores.add(score);
			}
			Integer score = getMaxScore(scores);
			weight += score;
		}
		// 四个属性中的所有人 从三个分类中取最大分即可
		// 技术等级 部职别 高层次专家
		// person_main
		// 相关批示的人员 的等级
		// person_cooperation
		//
		// 先获取四个属性中的所有人员
		//批示中的人员
		Set<Long> persionIds = new HashSet();
		Object instructions = dataMap.get("instruction");
		if(instructions!=null) {
			for (Object obj : (JSONArray) instructions) {
				Long inst_id = (Long) obj;
				LibEntityEntity entity = queryEntity(inst_id);
				if(ObjectUtil.isEmpty(entity.getData())) {
					continue;
				}
				Map<String, Object> instrData = JsonUtil.stringToMap(entity.getData());
				Long per_id = (Long) instrData.get("instruction_person");
				persionIds.add(per_id);
			}
		}
		//其他三个属性的人员
		String per_attrs[] = { "person_main", "person_cooperation", "person_achievements" };
		for (int i = 0; i < per_attrs.length; i++) {
			Object attr_val = dataMap.get(per_attrs[i]);
			if (!ObjectUtil.isEmpty(attr_val)) {
				for (Object obj : (JSONArray) attr_val) {
					Long id = (Long) obj;
					persionIds.add(id);
				}
			}
		}
		//循环获取全部人员中的三个属性分数
		String cate_attr[] = { "skill_job_id", "job_id", "expert_type_id" };
		List<Integer> scores = new ArrayList<>();
		for (Long perId : persionIds) {
			LibEntityEntity entity = queryEntity(perId);
			if(ObjectUtil.isEmpty(entity.getData())) {
				continue;
			}
			Map<String, Object> per_dataMap = JsonUtil.stringToMap(entity.getData());
			for (int j = 0; j < cate_attr.length; j++) {
				Object cate_obj = per_dataMap.get(cate_attr[j]);
				if (ObjectUtil.isEmpty(cate_obj)) {
					continue;
				}
				if (cate_obj instanceof Collection) {
					for (Object idobj : (JSONArray) cate_obj) {
						Long cateId = (Long) idobj;
						Integer score = queryScore(cateId);
						scores.add(score);
					}
				} else {
					Long cateId = (Long) cate_obj;
					Integer score = queryScore(cateId);
					scores.add(score);
				}
			}
		}
		
		Integer  per_score = getMaxScore(scores);
		
		return weight+per_score;
	}

	// 实体，实体类型的分数
	Map<Long, LibEntityEntity> scoreMap = new HashMap<Long, LibEntityEntity>();

	public LibEntityEntity queryEntity(Long entityId) {
		LibEntityEntity entity = scoreMap.get(entityId);
		if (entity == null) {
			entity = libEntityService.getById(entityId);
		}
		return entity;
	}

	// 分类id,分类权重
	Map<Long, Integer> cateMap = new HashMap<Long, Integer>();

	public Integer queryScore(Long cateId) {
		Integer score = cateMap.get(cateId);
		if (score == null) {
			MateAttributeValueEntity valueEntity = mateAttributeValueService.getById(cateId);
			score = valueEntity.getScore();
			cateMap.put(cateId, score);
		}
		return score;
	}

	public Integer getMaxScore(List<Integer> scores) {
		Integer max = scores.stream().max(Comparator.comparing(x -> x)).orElse(0);
		return max;
	}

	
	
	
	
}
