package com.sinosoft.ie.booster.yearbook.mark.mapper;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityRelation;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 * mark_entity
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-08 16:58:44
 */
@Mapper
public interface MarkEntityMapper extends BaseMapper<MarkEntityEntity> {

    @Select(value="SELECT m.*,c.name tagOntoName FROM mark_entity m " +
            "LEFT JOIN lib_concept c on c.id = m.tagOnto " +
            "where m.sentenceId = #{id}")
    public List<MarkEntityListVO> queryByWapper(Long id);

    List<MarkEntityEntity> getEntityList(MarkSentencePagination sentence);

    List<MarkEntityRelation> getEntityRelationList(MarkSentencePagination sentence);

    List<MarkEntityEntity> getEntityAttrList(MarkSentencePagination sentence);

    int getEntityCount(MarkSentencePagination sentence);
    int getEntityRelationCount(MarkSentencePagination sentence);
    int getEntityAttrCount(MarkSentencePagination sentence);
}
