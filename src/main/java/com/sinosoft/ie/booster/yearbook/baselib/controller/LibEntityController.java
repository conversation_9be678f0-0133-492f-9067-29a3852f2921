package com.sinosoft.ie.booster.yearbook.baselib.controller;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;
import javax.swing.text.html.parser.Entity;
import javax.validation.Valid;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.baselib.model.libentity.*;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationOut;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.Pagination;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.service.LibEntityServicve;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Api(tags = "实体库查询")
@RequestMapping("/LibEntity")
public class LibEntityController {
	

	@Autowired
	private LibEntityService libEntityService;

	/**
	 * 列表
	 */
	@GetMapping
	public R<PageListVO<LibEntityListVO>> list(LibEntityPagination libEntityPagination)throws IOException {
		List<LibEntityEntity> list= libEntityService.getList(libEntityPagination);
		//处理id字段转名称，若无需转或者为空可删除

		for(LibEntityEntity entity:list){
		}
		List<LibEntityListVO> listVO=JsonUtil.getJsonToList(list,LibEntityListVO.class);
		PageListVO<LibEntityListVO> vo=new PageListVO<>();
		vo.setList(listVO);
		PaginationVO page=JsonUtil.getJsonToBean(libEntityPagination,PaginationVO.class);
		vo.setPagination(page);
		return R.ok(vo);
	}

	/**
	 * 创建
	 *
	 * @param libEntityCrForm
	 * @return
	 */
	@PostMapping
	@Transactional
	public R<String> create(@RequestBody @Valid LibEntityCrForm libEntityCrForm) throws DataException {
		LibEntityEntity entity=JsonUtil.getJsonToBean(libEntityCrForm, LibEntityEntity.class);
		libEntityService.create(entity);
		return R.ok(null, "新建成功");
	}


	/**
	 * 信息
	 *
	 * @param id
	 * @return
	 */
	@GetMapping("/{id}")
	public R<LibEntityInfoVO> info(@PathVariable("id") Long id){
		LibEntityEntity entity= libEntityService.getInfo(id);
		LibEntityInfoVO vo=JsonUtil.getJsonToBean(entity, LibEntityInfoVO.class);
		return R.ok(vo);
	}



	/**
	 * 更新
	 *
	 * @param id
	 * @return
	 */
	@PutMapping("/{id}")
	@Transactional
	public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid LibEntityUpForm libEntityUpForm) throws DataException {
		LibEntityEntity entity= libEntityService.getInfo(id);
		if(entity!=null){
			entity=JsonUtil.getJsonToBean(libEntityUpForm, LibEntityEntity.class);
			libEntityService.update(id, entity);
			return R.ok(null, "更新成功");
		}else{
			return R.failed("更新失败，数据不存在");
		}
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	@DeleteMapping("/{id}")
	@Transactional
	public R<String> delete(@PathVariable("id") Long id){
		LibEntityEntity entity= libEntityService.getInfo(id);
		if(entity!=null){
			libEntityService.delete(entity);
		}
		return R.ok(null, "删除成功");
	}

	

	/**
	 * 根据本体id和关系name查询实体
	 */
	@GetMapping("/getEntityByonto")
	public R<PageListVO<LibEntityListVO>> getEntityByonto(LibEntityPagination libEntityPagination)throws IOException {
		return R.ok(libEntityService.getEntityByonto(libEntityPagination));
	}

	/**
	 * 根据实体id查询实体关系列表
	 */
	@GetMapping("/getEntityRelationByEntityId")
	public R<List<MetaEntityRelationOut>> getEntityRelationByEntityId(Long entityId,Long ontoId)throws IOException {
		return R.ok(libEntityService.queryEntityRelationByEntityId(entityId,ontoId));
	}

	/**
	 * 删除多个
	 */
	@GetMapping("/deleteids")
	@Transactional
	public R<String> deleteids(String ids){
		libEntityService.deleteids(ids);
		return R.ok(null, "删除成功");
	}

	/**
	 * 根据属性的本体id和实体name查询实体
	 */
	@GetMapping("/getEntityByOntoIdANDName")
	public R<List<LibEntityListVO>> getEntityByOntoIdANDName(LibEntityPagination libEntityPagination)throws IOException {
		List<LibEntityEntity> list= libEntityService.getEntityByOntoIdANDName(libEntityPagination);
		List<LibEntityListVO> listVO=JsonUtil.getJsonToList(list,LibEntityListVO.class);
		return R.ok(listVO);
	}

	/**
	 * 根据实体id查询实体
	 * @param id
	 * @return
	 */
	@GetMapping("/getByEntityId/{id}")
	public R<LibEntityInfoVO> getByEntityId(@PathVariable("id") Long id){
		LibEntityEntity entity= libEntityService.getByEntityId(id);
		LibEntityInfoVO vo=JsonUtil.getJsonToBean(entity, LibEntityInfoVO.class);
		return R.ok(vo);
	}

	/**
	 * 创建,对象string转long
	 */
	@PostMapping("/createEntityS2L")
	@Transactional
	public R<String> createEntityS2L(@RequestBody @Valid LibEntityCrForm libEntityCrForm) throws DataException {
		LibEntityEntity entity=JsonUtil.getJsonToBean(libEntityCrForm, LibEntityEntity.class);
		//校验必填项
		if (libEntityService.checkEntity(entity)){
			//id转long型
			libEntityService.createEntityS2L(entity);
			//保存实体关系
			libEntityService.updataEntityAndRealation(entity);
			//保存实体
			libEntityService.create(entity);
			return R.ok(null, "新建成功");
		}else {
			return R.failed("必填项不能为空！");
		}

	}

	/**
	 * 更新,对象string转long
	 */
	@PutMapping("/updateEntityS2L/{id}")
	@Transactional
	public R<String> updateEntityS2L(@PathVariable("id") Long id,@RequestBody @Valid LibEntityUpForm libEntityUpForm) throws DataException {
		LibEntityEntity entity= libEntityService.getInfo(id);
		if(entity!=null){
			entity=JsonUtil.getJsonToBean(libEntityUpForm, LibEntityEntity.class);
			//校验必填项
			if (libEntityService.checkEntity(entity)){
				//id转long型
				libEntityService.createEntityS2L(entity);
				//保存实体关系
				libEntityService.updataEntityAndRealation(entity);
				//修改实体
				libEntityService.update(id, entity);
				return R.ok(null, "更新成功");
			}else {
				return R.failed("更新失败，必填项不能为空！");
			}
		}else{
			return R.failed("更新失败，数据不存在");
		}
	}

	/**
	 * 列表-id2name
	 */
	@GetMapping("/getListId2Name")
	public R<PageListVO<LibEntityListVO>> getListId2Name(LibEntityPagination libEntityPagination)throws IOException {
		List<LibEntityEntity> list= libEntityService.getList(libEntityPagination);
		//处理id字段转名称，若无需转或者为空可删除
		for (LibEntityEntity entity:list) {
			libEntityService.getListId2Name(entity);
		}
		for(LibEntityEntity entity:list){
		}
		List<LibEntityListVO> listVO=JsonUtil.getJsonToList(list,LibEntityListVO.class);
		PageListVO<LibEntityListVO> vo=new PageListVO<>();
		vo.setList(listVO);
		PaginationVO page=JsonUtil.getJsonToBean(libEntityPagination,PaginationVO.class);
		vo.setPagination(page);
		return R.ok(vo);
	}
	
	
	@GetMapping("/getListByOntoId")
	public R   getList( Long ontoId){
		return R.ok(libEntityService.getList(ontoId));
	}

	@GetMapping("/mergeEntity")
	public R mergeEntity(Long ontoId,Long corpusId){
		libEntityService.mergeEntity(ontoId,corpusId);
		return R.ok();
	}
}
