package com.sinosoft.ie.booster.yearbook.business.model.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Organization模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-14 10:08:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class OrganizationUpForm extends OrganizationCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}