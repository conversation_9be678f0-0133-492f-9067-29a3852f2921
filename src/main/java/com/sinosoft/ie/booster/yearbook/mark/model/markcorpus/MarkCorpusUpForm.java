package com.sinosoft.ie.booster.yearbook.mark.model.markcorpus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MarkCorpus模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-24 17:45:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkCorpusUpForm extends MarkCorpusCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}