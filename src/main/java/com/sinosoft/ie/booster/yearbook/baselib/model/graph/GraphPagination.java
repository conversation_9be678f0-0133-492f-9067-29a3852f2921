package com.sinosoft.ie.booster.yearbook.baselib.model.graph;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * Graph模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-31 13:39:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class GraphPagination extends Pagination {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;

    /**
     * 状态：
     */
    @ApiModelProperty(value = "状态：")
    private Integer status;
}