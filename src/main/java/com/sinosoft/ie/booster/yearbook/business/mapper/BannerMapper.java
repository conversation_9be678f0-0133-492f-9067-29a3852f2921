package com.sinosoft.ie.booster.yearbook.business.mapper;

import com.sinosoft.ie.booster.yearbook.business.entity.BannerEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinosoft.ie.booster.yearbook.business.model.homePage.BannerOut;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * banner
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-11-10 13:51:42
 */
@Mapper
public interface BannerMapper extends BaseMapper<BannerEntity> {

    List<BannerOut> queryBanner();
}
