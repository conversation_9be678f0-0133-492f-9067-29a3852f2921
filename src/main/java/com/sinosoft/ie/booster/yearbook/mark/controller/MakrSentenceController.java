package com.sinosoft.ie.booster.yearbook.mark.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusListVO;
import com.sinosoft.ie.booster.yearbook.mark.model.markcorpus.MarkCorpusPagination;
import com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityRelation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;

import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.mark.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;

import com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentenceCrForm;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentenceInfoVO;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePaginationExportModel;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentenceUpForm;
import com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentenceListVO;
import com.sinosoft.ie.booster.yearbook.mark.service.EventInsertService;
import com.sinosoft.ie.booster.yearbook.mark.service.MakrSentenceService;
import com.sinosoft.ie.booster.yearbook.mark.service.MarkTripletService;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * makr_sentence
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-09 14:49:54
 */
@Slf4j
@RestController
@Api(tags = "makr_sentence")
@RequestMapping("/MakrSentence")
public class MakrSentenceController {
    @Autowired
    private MakrSentenceService makrSentenceService;
    @Resource
    private EventInsertService eventInsertService;
    /**
     * 列表
     *
     * @param makrSentencePagination
     * @return
     */
    @GetMapping
    public R<PageListVO<MarkSentenceListVO>> list(MarkSentencePagination makrSentencePagination)throws IOException{
        List<MarkSentenceEntity> list= makrSentenceService.getList(makrSentencePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(MarkSentenceEntity entity:list){
            }
        List<MarkSentenceListVO> listVO=JsonUtil.getJsonToList(list,MarkSentenceListVO.class);
        PageListVO<MarkSentenceListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(makrSentencePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param makrSentenceCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R<String> create(@RequestBody @Valid MarkSentenceCrForm makrSentenceCrForm) throws DataException {
        MarkSentenceEntity entity=JsonUtil.getJsonToBean(makrSentenceCrForm, MarkSentenceEntity.class);
        makrSentenceService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<MarkSentenceInfoVO> info(@PathVariable("id") Long id){
        MarkSentenceEntity entity= makrSentenceService.getInfo(id);
        MarkSentenceInfoVO vo=JsonUtil.getJsonToBean(entity, MarkSentenceInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R<String> update(@PathVariable("id") Long id,@RequestBody @Valid MarkSentenceUpForm makrSentenceUpForm) throws DataException {
        MarkSentenceEntity entity= makrSentenceService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(makrSentenceUpForm, MarkSentenceEntity.class);
            makrSentenceService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R<String> delete(@PathVariable("id") Long id){
        MarkSentenceEntity entity= makrSentenceService.getInfo(id);
        if(entity!=null){
            makrSentenceService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }



    /**
     * 查询数量
     */
    @GetMapping("/count")
    public R<Long> count(MarkSentenceCrForm markSentenceCrForm){
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        if (markSentenceCrForm.getStatus() != null){
            queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getStatus,markSentenceCrForm.getStatus()));
        }
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusVersion,markSentenceCrForm.getCorpusVersion()));
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getCorpusId,markSentenceCrForm.getCorpusId()));
        long count = makrSentenceService.count(queryWrapper);
        return R.ok(count);
    }


    /**
     * 导出语料
     */
    @GetMapping("/exportCorpus")
    public R<String> exportCorpus(
            Integer status, Integer startNum, Integer endNum, Long corpusId,Long corpusVersion)throws IOException{
        JSONArray markSentenceEntities = makrSentenceService.exportCorpus(status, startNum, endNum, corpusId, corpusVersion);
        return R.ok(makrSentenceService.makeJson(markSentenceEntities));
    }

    /**
     * 语料总览-详情-table

     */
    @GetMapping("/getListCorpus")
    public R<Map<String,Object>> getListCorpus(MarkSentencePagination makrSentencePagination)throws IOException{
        Map<String,Object> map = new HashMap<>();
        makrSentencePagination.setSidx("id");
        makrSentencePagination.setSort("asc");
        
        List<MarkSentenceEntity> list= makrSentenceService.getList(makrSentencePagination);
        //处理id字段转名称，若无需转或者为空可删除


        List<MarkSentenceListVO> listVO=JsonUtil.getJsonToList(list,MarkSentenceListVO.class);
        PageListVO<MarkSentenceListVO> vo=new PageListVO<>();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(makrSentencePagination,PaginationVO.class);
        vo.setPagination(page);
        map.put("vo",vo);
        QueryWrapper<MarkSentenceEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().and(t->t.eq(MarkSentenceEntity::getStatus,4));
        queryWrapper.lambda().and(!ObjectUtils.isEmpty(makrSentencePagination.getSentence()), t->t.like(MarkSentenceEntity::getSentence,makrSentencePagination.getSentence()));
        queryWrapper.lambda().and(!ObjectUtils.isEmpty(makrSentencePagination.getCorpusId()),t->t.eq(MarkSentenceEntity::getCorpusVersion,makrSentencePagination.getCorpusVersion()));
        queryWrapper.lambda().and(!ObjectUtils.isEmpty(makrSentencePagination.getCorpusId()),t->t.eq(MarkSentenceEntity::getCorpusId,makrSentencePagination.getCorpusId()));
        long count = makrSentenceService.count(queryWrapper);
        map.put("num",count + "/" +page.getTotal());
        return R.ok(map);
    }

    /**
     * 删除多个
     */
    @DeleteMapping("/deleteids")
    @Transactional
    public R<String> deleteids(String ids){
        makrSentenceService.deleteids(ids);
        return R.ok(null, "删除成功");
    }

    /**
     * 语料统计-状态饼图
     */
    @GetMapping("/getCorpusStatusChart")
    public R<JSONArray> getCorpusStatusChart(Long corpusId,Integer corpusVersion){
        return R.ok(makrSentenceService.getCorpusStatusChart(corpusId, corpusVersion));
    }

    /**
     * 语料统计-实体关系属性饼图
     */
    @GetMapping("/getCorpusTagnameChart")
    public R<JSONArray> getCorpusTagnameChart(Long corpusId,Integer tag,Integer corpusVersion,Integer status){
        return R.ok(makrSentenceService.getCorpusTagnameChart(corpusId, tag, corpusVersion, status));
    }

    /**
     * 语料统计-实体列表
     */
    @GetMapping("/getEntityList")
    public R<PageListVO<MarkEntityEntity>> getEntityList(MarkSentencePagination sentencePagination){
        return makrSentenceService.getEntityList(sentencePagination);
    }

    /**
     * 语料统计-实体-关系列表
     */
    @GetMapping("/getEntityRelationList")
    public R<PageListVO<MarkEntityRelation>> getEntityRelationList(MarkSentencePagination sentencePagination){
        return makrSentenceService.getEntityRelationList(sentencePagination);
    }

    /**
     * 语料统计-实体属性列表
     */
    @GetMapping("/getEntityAttrList")
    public R<PageListVO<MarkEntityEntity>> getEntityAttrList(MarkSentencePagination sentencePagination){
        return makrSentenceService.getEntityAttrList(sentencePagination);
    }
}
