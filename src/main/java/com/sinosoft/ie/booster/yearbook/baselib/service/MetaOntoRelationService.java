package com.sinosoft.ie.booster.yearbook.baselib.service;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO;
import com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationPagination;
import java.util.*;

/**
 *
 * meta_onto_relation
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-10-31 16:37:39
 */
public interface MetaOntoRelationService extends IService<MetaOntoRelationEntity> {

    List<MetaOntoRelationEntity> getList(MetaOntoRelationPagination metaOntoRelationPagination);

    List<MetaOntoRelationEntity> getTypeList(MetaOntoRelationPagination metaOntoRelationPagination,String dataType);



    MetaOntoRelationEntity getInfo(Long id);

    void delete(MetaOntoRelationEntity entity);

    void create(MetaOntoRelationEntity entity);

    boolean update( Long id, MetaOntoRelationEntity entity);
    
//  子表方法
    
    List<MetaOntoRelationInfoVO> getList1(MetaOntoRelationPagination metaOntoRelationPagination);
    /*
     * 根据本体id查询所有子节点的关系
     */
    List<MetaOntoRelationInfoVO>   getList2(Long onto_id);

    List<MetaOntoRelationInfoVO> getListByOnto1Id(Long ontoId);
}
