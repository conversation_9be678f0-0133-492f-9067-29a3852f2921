package com.sinosoft.ie.booster.yearbook.business.model.filedirectory;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * FileDirectory模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-16 14:28:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class FileDirectoryPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long pid;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private Integer delFlag;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;
}