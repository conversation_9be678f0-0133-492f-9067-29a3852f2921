package com.sinosoft.ie.booster.yearbook.search.model;

import lombok.Data;

@Data
public class EsQueryPagination {
  
	/**
     * 当前页
     */
    private int pageNum=1;
    /**
     * 每页的数量
     */
    private int pageSize=10;
    /**
     * 当前页的数量
     */
    private int size;
    /**
     * 当前页面第一个元素在数据库中的行号
     */
    private int startRow;
    /**
     * 当前页面最后一个元素在数据库中的行号
     */
    private int endRow;
    /**
     * 总页数
     */
    private int pages;
    /**
     * 总记录数
     */
    protected long total;
	/**
	 * 首页条目数量
	 */
	private int  firstPageSize = 10;
	/**
	 * 从第几条开始查询
	 */
	private int start_idx = 0;
	/**
	 * 查询的关键词
	 */
	private String str;
	/**
	 * 查询的字段
	 */
	public String[] attrs;
	/**
	 * 排序字段
	 */
	public String[]  sort_fields;
	
	public void  setTotal(long size) {
		this.total = size;
		this.pages = (int)(size % pageSize > 0 ? size / pageSize + 1 : size / pageSize);
		if(this.pageNum>1) {
			this.start_idx = (this.pageNum-1)*this.pageSize;
		}
	}
	public void  setEventTotal(long size) {
		this.total = size;
		if(this.firstPageSize==10) {
			this.pages = (int)(size % pageSize > 0 ? size / pageSize + 1 : size / pageSize);
		}
		else {
			int count = (int)size-this.firstPageSize;
			this.pages = (int)(count % pageSize > 0 ? count / pageSize + 1 : count / pageSize)+1;
		}
		
		this.start_idx =  this.getFirstPageSize()+(this.pageNum-2)*this.pageSize ;
		//this.start_idx = (this.pageNum-1)*this.pageSize - (10-this.getFirstPageSize());
	}
	
	
	public int getStart_idx() {

		return start_idx;
	}
	
	private Long ontoId;
	//事件用到的字段
	private String org_name;
	private String year;
	//事件排序的字段    start_time/sort
	private String  sort_attr;
	//查询接口类型，1首页查询接口；2人物频道查询接口 ;3.没有图片时查询其他的
	private Integer per_type = 1;
}
