package com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MetaOntoRelation模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-10-31 16:37:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MetaOntoRelationPaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 本体1的id
     */
    @ApiModelProperty(value = "本体1的id")
    private Long onto1Id;

    /**
     * 本体2的id
     */
    @ApiModelProperty(value = "本体2的id")
    private Long onto2Id;

    /**
     * 关系名称
     */
    @ApiModelProperty(value = "关系名称")
    private String name;

    /**
     * 关系的父id
     */
    @ApiModelProperty(value = "关系的父id")
    private Long pid;
    
    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;
}