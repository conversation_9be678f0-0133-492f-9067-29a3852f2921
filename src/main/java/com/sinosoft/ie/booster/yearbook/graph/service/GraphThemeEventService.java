package com.sinosoft.ie.booster.yearbook.graph.service;

import com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.graph.model.graphthemeevent.GraphThemeEventPagination;
import java.util.*;

/**
 *
 * graph_theme_event
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-05-08 16:14:57
 */
public interface GraphThemeEventService extends IService<GraphThemeEventEntity> {

    List<GraphThemeEventEntity> getList(GraphThemeEventPagination graphThemeEventPagination);

    List<GraphThemeEventEntity> getTypeList(GraphThemeEventPagination graphThemeEventPagination,String dataType);



    GraphThemeEventEntity getInfo(Long id);

    void delete(GraphThemeEventEntity entity);

    void create(GraphThemeEventEntity entity);

    boolean update( Long id, GraphThemeEventEntity entity);
    
//  子表方法
}
