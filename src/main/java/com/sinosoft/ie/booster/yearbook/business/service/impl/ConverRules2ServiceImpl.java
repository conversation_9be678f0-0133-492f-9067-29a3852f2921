package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ConverRules2Entity;
import com.sinosoft.ie.booster.yearbook.business.mapper.ConverRules2Mapper;
import com.sinosoft.ie.booster.yearbook.business.service.ConverRules2Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.yearbook.business.model.converrules2.ConverRules2Pagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * conver_rules2
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-20 16:32:03
 */
@Service
public class ConverRules2ServiceImpl extends ServiceImpl<ConverRules2Mapper, ConverRules2Entity> implements ConverRules2Service {


    @Override
    public List<ConverRules2Entity> getList(ConverRules2Pagination converRules2Pagination){
        QueryWrapper<ConverRules2Entity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(converRules2Pagination.getId()))){
            queryWrapper.lambda().and(t->t.like(ConverRules2Entity::getId,converRules2Pagination.getId()));
        }

        if(!"null".equals(String.valueOf(converRules2Pagination.getType()))){
            queryWrapper.lambda().and(t->t.like(ConverRules2Entity::getType,converRules2Pagination.getType()));
        }

        if(!"null".equals(String.valueOf(converRules2Pagination.getAttrName()))){
            queryWrapper.lambda().and(t->t.like(ConverRules2Entity::getAttrName,converRules2Pagination.getAttrName()));
        }

        //排序
        if(StrUtil.isEmpty(converRules2Pagination.getSidx())){
            queryWrapper.lambda().orderByDesc(ConverRules2Entity::getId);
        }else{
            queryWrapper="asc".equals(converRules2Pagination.getSort().toLowerCase())?queryWrapper.orderByAsc(converRules2Pagination.getSidx()):queryWrapper.orderByDesc(converRules2Pagination.getSidx());
        }
        if(converRules2Pagination.getCurrentPage()==0) {
        	List<ConverRules2Entity> list  = this.list(queryWrapper);
        	return list;
        }
        Page<ConverRules2Entity> page=new Page<>(converRules2Pagination.getCurrentPage(), converRules2Pagination.getPageSize());
        IPage<ConverRules2Entity> userIPage=this.page(page,queryWrapper);
        return converRules2Pagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<ConverRules2Entity> getTypeList(ConverRules2Pagination converRules2Pagination,String dataType){
        QueryWrapper<ConverRules2Entity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(converRules2Pagination.getId()))){
            queryWrapper.lambda().and(t->t.like(ConverRules2Entity::getId,converRules2Pagination.getId()));
        }

        if(!"null".equals(String.valueOf(converRules2Pagination.getType()))){
            queryWrapper.lambda().and(t->t.like(ConverRules2Entity::getType,converRules2Pagination.getType()));
        }

        if(!"null".equals(String.valueOf(converRules2Pagination.getAttrName()))){
            queryWrapper.lambda().and(t->t.like(ConverRules2Entity::getAttrName,converRules2Pagination.getAttrName()));
        }

        //排序
        if(StrUtil.isEmpty(converRules2Pagination.getSidx())){
            queryWrapper.lambda().orderByDesc(ConverRules2Entity::getId);
        }else{
            queryWrapper="asc".equals(converRules2Pagination.getSort().toLowerCase())?queryWrapper.orderByAsc(converRules2Pagination.getSidx()):queryWrapper.orderByDesc(converRules2Pagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<ConverRules2Entity> page=new Page<>(converRules2Pagination.getCurrentPage(), converRules2Pagination.getPageSize());
            IPage<ConverRules2Entity> userIPage=this.page(page,queryWrapper);
            return converRules2Pagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public ConverRules2Entity getInfo(Long id){
        QueryWrapper<ConverRules2Entity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(ConverRules2Entity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(ConverRules2Entity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, ConverRules2Entity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(ConverRules2Entity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
	@Override
	public void create2(List<ConverRules2Entity> entList) {
		this.saveBatch(entList);
	}
}