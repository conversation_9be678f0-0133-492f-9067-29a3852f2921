package com.sinosoft.ie.booster.yearbook.business.service.impl;

import com.sinosoft.ie.booster.yearbook.business.entity.HotInformationEntity;
import com.sinosoft.ie.booster.yearbook.business.mapper.HotInformationMapper;
import com.sinosoft.ie.booster.yearbook.business.service.HotInformationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationPagination;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import cn.hutool.core.util.StrUtil;
import java.util.*;

/**
 *
 * hot_information
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-21 17:37:05
 */
@Service
public class HotInformationServiceImpl extends ServiceImpl<HotInformationMapper, HotInformationEntity> implements HotInformationService {


    @Override
    public List<HotInformationEntity> getList(HotInformationPagination hotInformationPagination){
        QueryWrapper<HotInformationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(hotInformationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getId,hotInformationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getName,hotInformationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getTheme()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getTheme,hotInformationPagination.getTheme()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getYear()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getYear,hotInformationPagination.getYear()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getNumber()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getNumber,hotInformationPagination.getNumber()));
        }

        //排序
        if(StrUtil.isEmpty(hotInformationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(HotInformationEntity::getNumber);
        }else{
            queryWrapper="asc".equals(hotInformationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(hotInformationPagination.getSidx()):queryWrapper.orderByDesc(hotInformationPagination.getSidx());
        }
        Page<HotInformationEntity> page=new Page<>(hotInformationPagination.getCurrentPage(), hotInformationPagination.getPageSize());
        IPage<HotInformationEntity> userIPage=this.page(page,queryWrapper);
        return hotInformationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }
    @Override
    public List<HotInformationEntity> getTypeList(HotInformationPagination hotInformationPagination,String dataType){
        QueryWrapper<HotInformationEntity> queryWrapper=new QueryWrapper<>();
        //关键字（账户、姓名、手机）
        if(!"null".equals(String.valueOf(hotInformationPagination.getId()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getId,hotInformationPagination.getId()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getName()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getName,hotInformationPagination.getName()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getTheme()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getTheme,hotInformationPagination.getTheme()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getYear()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getYear,hotInformationPagination.getYear()));
        }

        if(!"null".equals(String.valueOf(hotInformationPagination.getNumber()))){
            queryWrapper.lambda().and(t->t.like(HotInformationEntity::getNumber,hotInformationPagination.getNumber()));
        }

        //排序
        if(StrUtil.isEmpty(hotInformationPagination.getSidx())){
            queryWrapper.lambda().orderByDesc(HotInformationEntity::getId);
        }else{
            queryWrapper="asc".equals(hotInformationPagination.getSort().toLowerCase())?queryWrapper.orderByAsc(hotInformationPagination.getSidx()):queryWrapper.orderByDesc(hotInformationPagination.getSidx());
        }
        if("0".equals(dataType)){
            Page<HotInformationEntity> page=new Page<>(hotInformationPagination.getCurrentPage(), hotInformationPagination.getPageSize());
            IPage<HotInformationEntity> userIPage=this.page(page,queryWrapper);
            return hotInformationPagination.setData(userIPage.getRecords(),userIPage.getTotal());
        }else{
            return this.list(queryWrapper);
        }
    }

    @Override
    public HotInformationEntity getInfo(Long id){
        QueryWrapper<HotInformationEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.lambda().eq(HotInformationEntity::getId,id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(HotInformationEntity entity){
        this.save(entity);
    }

    @Override
    public boolean update(Long id, HotInformationEntity entity){
        entity.setId(id);
        return this.updateById(entity);
    }
    @Override
    public void delete(HotInformationEntity entity){
        if(entity!=null){
            this.removeById(entity.getId());
        }
    }
    //子表方法
}