package com.sinosoft.ie.booster.yearbook.business.model.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * Banner模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-10 13:51:42
 */
@Data
@ApiModel(value = "Banner模型")
public class BannerListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 年鉴id
     */
    @ApiModelProperty(value = "年鉴id")
    private String yearbookId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private String sort;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;

}