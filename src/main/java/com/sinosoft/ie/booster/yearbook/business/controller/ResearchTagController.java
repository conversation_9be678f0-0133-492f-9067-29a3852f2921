package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.File;
import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagListVO;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagPagination;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag.ResearchTagUpForm;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTagService;

import cn.hutool.core.io.file.FileReader;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 *
 * research_tag
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-15 10:49:22
 */
@Slf4j
@RestController
@Api(tags = "research_tag")
@RequestMapping("/ResearchTag")
public class ResearchTagController {
    @Autowired
    private ResearchTagService researchTagService;


    /**
     * 列表
     *
     * @param researchTagPagination
     * @return
     */
    @GetMapping
    public R list(ResearchTagPagination researchTagPagination)throws IOException{
        List<ResearchTagEntity> list= researchTagService.getList(researchTagPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(ResearchTagEntity entity:list){
            }
        List<ResearchTagListVO> listVO=JsonUtil.getJsonToList(list,ResearchTagListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(researchTagPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param researchTagCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid ResearchTagCrForm researchTagCrForm) throws DataException {
        ResearchTagEntity entity=JsonUtil.getJsonToBean(researchTagCrForm, ResearchTagEntity.class);
        researchTagService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<ResearchTagInfoVO> info(@PathVariable("id") Long id){
        ResearchTagEntity entity= researchTagService.getInfo(id);
        ResearchTagInfoVO vo=JsonUtil.getJsonToBean(entity, ResearchTagInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid ResearchTagUpForm researchTagUpForm) throws DataException {
        ResearchTagEntity entity= researchTagService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(researchTagUpForm, ResearchTagEntity.class);
            researchTagService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        ResearchTagEntity entity= researchTagService.getInfo(id);
        if(entity!=null){
            researchTagService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

	@GetMapping("/showMapByLegendType")
	@Transactional
	public R showMapByLegendType(String legendTypeList){
		return R.ok(researchTagService.showMapByLegendType(legendTypeList));
	}

    @GetMapping("/showMapByLegendTypeYear")
    @Transactional
    public R showMapByLegendTypeYear(String legendTypeList,String year,int currentPage){
        return R.ok(researchTagService.showMapByLegendTypeYear(legendTypeList,year,currentPage));
    }
    
    @GetMapping("/wordCloud")
    @Transactional
    public R showMapByLegendTypeYear2(String legendTypeList,String year,String initial,String orgId){
    	StopWatch stopWatch = new StopWatch();
    	stopWatch.start();
    	R r =researchTagService.wordCloud(legendTypeList, year,initial,orgId);
    	stopWatch.stop();
    	double uptime = stopWatch.getTotalTimeMillis() / 1000.0;
    	log.info("wordCloud该接口查询时间为===>"+stopWatch.prettyPrint()+" ===> "+uptime);
        return r;
    }
    @GetMapping("/computWeight")
    public R  computWeight() throws BadHanyuPinyinOutputFormatCombination {
    	researchTagService.computWeight();
    	return R.ok();
    }
    /**
     * 标签查询 查询标签
     * @param tag
     * @param year
     * @return
     */
    @GetMapping("/searchTag")
    public R  searchTag(String tag,Integer year) {
    	List list = researchTagService.searchTag(tag,year);
    	return R.ok(list);
    }
    @GetMapping("/readDom")
    public  R  readDom() {
    	StopWatch stopWatch = new StopWatch();
    	stopWatch.start();
    	String path = "D:\\NProject\\project\\booster-yearbook-biz\\dom.txt";
    	File f=  new File(path);
    	FileReader  read =  new FileReader(f);
    	String  str = read.readString();
    	stopWatch.stop();
    	double uptime = stopWatch.getTotalTimeMillis() / 1000.0;
    	log.info("readDom该接口查询时间为===>"+stopWatch.prettyPrint()+" ===> "+uptime);
    	return R.ok(str);
    	
    }
    
    
    
}
