package com.sinosoft.ie.booster.yearbook.mark.model.markdataset;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.math.BigDecimal;

/**
 *
 * MarkDataset模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-11 15:34:34
 */
@Data
@ApiModel
public class MarkDatasetCrForm  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 数据集名称或文件名称
     */
    @NotEmpty
    @Length(max = 200)
    @ApiModelProperty(value = "数据集名称或文件名称")
    private String name;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private Long pid;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Long fid;

    /**
     * 状态：默认1，未处理；2已处理；3处理失败
     */
    @ApiModelProperty(value = "状态：默认1，未处理；2已处理；3处理失败")
    private Integer stat;
    
    @ApiModelProperty(value = "开始页码")
    private Integer startPage;
}