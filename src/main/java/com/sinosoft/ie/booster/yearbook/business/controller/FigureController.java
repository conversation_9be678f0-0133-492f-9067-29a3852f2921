package com.sinosoft.ie.booster.yearbook.business.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.model.DownloadVO;
import com.sinosoft.ie.booster.visualdev.util.*;
import com.sinosoft.ie.booster.common.core.exception.DataException;
import org.springframework.transaction.annotation.Transactional;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigurePaginationExportModel;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigurePagination;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigureCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigureInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigureListVO;
import com.sinosoft.ie.booster.yearbook.business.model.figure.FigureUpForm;
import com.sinosoft.ie.booster.yearbook.business.entity.*;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.model.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sinosoft.ie.booster.yearbook.business.entity.FigureEntity;
import com.sinosoft.ie.booster.yearbook.business.service.FigureService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 *
 * figure
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-26 11:45:26
 */
@Slf4j
@RestController
@Api(tags = "figure")
@RequestMapping("/Figure")
public class FigureController {
    @Autowired
    private FigureService figureService;


    /**
     * 列表
     *
     * @param figurePagination
     * @return
     */
    @GetMapping
    public R list(FigurePagination figurePagination)throws IOException{
        List<FigureEntity> list= figureService.getList(figurePagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(FigureEntity entity:list){
            }
        List<FigureListVO> listVO=JsonUtil.getJsonToList(list,FigureListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(figurePagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param figureCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid FigureCrForm figureCrForm) throws DataException {
        FigureEntity entity=JsonUtil.getJsonToBean(figureCrForm, FigureEntity.class);
        figureService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<FigureInfoVO> info(@PathVariable("id") Long id){
        FigureEntity entity= figureService.getInfo(id);
        FigureInfoVO vo=JsonUtil.getJsonToBean(entity, FigureInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid FigureUpForm figureUpForm) throws DataException {
        FigureEntity entity= figureService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(figureUpForm, FigureEntity.class);
            figureService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        FigureEntity entity= figureService.getInfo(id);
        if(entity!=null){
            figureService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
