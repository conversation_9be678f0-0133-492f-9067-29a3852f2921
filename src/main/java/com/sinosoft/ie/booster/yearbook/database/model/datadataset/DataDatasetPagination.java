package com.sinosoft.ie.booster.yearbook.database.model.datadataset;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * DataDataset模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-06 15:00:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class DataDatasetPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 数据集名称
     */
    @ApiModelProperty(value = "数据集名称")
    private String name;

    /**
     * 数据表
     */
    @ApiModelProperty(value = "数据表")
    private String tableName;

    /**
     * 数据来源：1.本地表；2.远程表；3.同步表
     */
    @ApiModelProperty(value = "数据来源：1.本地表；2.远程表；3.同步表")
    private Integer source;

    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private Long schemaId;

    /**
     * 数据库连接id
     */
    @ApiModelProperty(value = "数据库连接id")
    private Long dataConnectId;

    /**
     * 数据集id
     */
    @ApiModelProperty(value = "数据集id")
    private Long dataId;
}