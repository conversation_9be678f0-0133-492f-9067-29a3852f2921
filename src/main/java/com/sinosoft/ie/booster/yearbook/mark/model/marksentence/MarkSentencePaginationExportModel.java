package com.sinosoft.ie.booster.yearbook.mark.model.marksentence;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MakrSentence模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-09 14:49:54
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkSentencePaginationExportModel extends Pagination {

    private String selectKey;

    private String json;

    private String dataType;


    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 文档id
     */
    @ApiModelProperty(value = "文档id")
    private Long fid;

    /**
     * 在文档中的顺序
     */
    @ApiModelProperty(value = "在文档中的顺序")
    private Long fsort;

    /**
     * 一句话
     */
    @ApiModelProperty(value = "一句话")
    private String sentence;

    /**
     * 修改后的句子
     */
    @ApiModelProperty(value = "修改后的句子")
    private String sentenceExc;

    /**
     * 抽取的数据
     */
    @ApiModelProperty(value = "抽取的数据")
    private String executeData;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long updateTime;

    /**
     * 1未抽取；2已抽取；3抽取报错
     */
    @ApiModelProperty(value = "1未抽取；2已抽取；3抽取报错")
    private Integer status;

    /**
     * 语料id
     */
    @ApiModelProperty(value = "语料id")
    private Long corpusId;
    /**
     * 语料版本
     */
    @ApiModelProperty(value = "语料版本")
    private Integer corpusVersion;
}