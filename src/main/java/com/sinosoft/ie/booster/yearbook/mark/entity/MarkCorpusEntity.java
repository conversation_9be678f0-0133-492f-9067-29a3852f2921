package com.sinosoft.ie.booster.yearbook.mark.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-24 17:45:08
 */
@Data
@TableName("mark_corpus")
@ApiModel(value = "")
public class MarkCorpusEntity  {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 语料名称
     */
    @ApiModelProperty(value = "语料名称")
    private String name;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    private Long pid;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private Long fid;

    /**
     * 状态：默认1，未处理；2已处理；3处理失败
     */
    @ApiModelProperty(value = "状态：默认1，未处理；2已处理；3处理失败")
    private Integer stat;

    /**
     * 开始页码
     */
    @ApiModelProperty(value = "开始页码")
    private Integer startPage;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer version;

    /**
     * 标注类型:实体;关系;属性;篇章；句子
     */
    @ApiModelProperty(value = "标注类型:实体;关系;属性;篇章；句子")
    private String annotaionType;

    /**
     * 模式类型：1实体关系属性抽取；2事件抽取；3文本分类；4机器阅读理解
     */
    @ApiModelProperty(value = "模式类型：1实体关系属性抽取；2事件抽取；3文本分类；4机器阅读理解")
    private Integer schemaType;

    /**
     * 模式定义
     */
    @ApiModelProperty(value = "模式定义")
    private Long schemaId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String fileType;

    /**
     * 导入方式；0文件导入
     */
    @ApiModelProperty(value = "导入方式；0文件导入")
    private Long exportType;

    /**
     * 语料粒度0不划分1按句子划分2按段落划分
     */
    @ApiModelProperty(value = "语料粒度0不划分1按句子划分2按段落划分")
    private Integer granularity;

    /**
     * 删除标识：1删除，0正常
     */
    @ApiModelProperty(value = "删除标识：1删除，0正常")
    private String delFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改者
     */
    @ApiModelProperty(value = "修改者")
    private String updateBy;

    /**
     *继承版本
     */
    @ApiModelProperty(value = "继承版本")
    private Integer pversion;
}
