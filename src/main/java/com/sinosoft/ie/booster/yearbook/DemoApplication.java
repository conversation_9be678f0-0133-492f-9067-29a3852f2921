package com.sinosoft.ie.booster.yearbook;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import cn.easyes.starter.register.EsMapperScan;


/**
 * <AUTHOR>
 *
 * 项目启动类
 */
@EnableScheduling
@ComponentScan("com.sinosoft")	
@SpringBootApplication
@EsMapperScan("com.sinosoft.ie.booster.yearbook.search.mapper")
public class DemoApplication {

	public static void main(String[] args) {
		SpringApplication.run(DemoApplication.class, args);
	}
	//123
}
