package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;
import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.exception.DataException;
import com.sinosoft.ie.booster.common.core.model.PageListVO;
import com.sinosoft.ie.booster.common.core.model.PaginationVO;
import com.sinosoft.ie.booster.common.core.util.JsonUtil;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.HotInformationEntity;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationCrForm;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationInfoVO;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationListVO;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationPagination;
import com.sinosoft.ie.booster.yearbook.business.model.hotinformation.HotInformationUpForm;
import com.sinosoft.ie.booster.yearbook.business.service.HotInformationService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * hot_information
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-09-21 17:37:05
 */
@Slf4j
@RestController
@Api(tags = "hot_information")
@RequestMapping("/HotInformation")
public class HotInformationController {
    @Autowired
    private HotInformationService hotInformationService;


    /**
     * 列表
     *
     * @param hotInformationPagination
     * @return
     */
    @GetMapping
    public R list(HotInformationPagination hotInformationPagination)throws IOException{
        List<HotInformationEntity> list= hotInformationService.getList(hotInformationPagination);
        //处理id字段转名称，若无需转或者为空可删除

    for(HotInformationEntity entity:list){
            }
        List<HotInformationListVO> listVO=JsonUtil.getJsonToList(list,HotInformationListVO.class);
        PageListVO vo=new PageListVO();
        vo.setList(listVO);
        PaginationVO page=JsonUtil.getJsonToBean(hotInformationPagination,PaginationVO.class);
        vo.setPagination(page);
        return R.ok(vo);
    }

    /**
     * 创建
     *
     * @param hotInformationCrForm
     * @return
     */
    @PostMapping
    @Transactional
    public R create(@RequestBody @Valid HotInformationCrForm hotInformationCrForm) throws DataException {
        HotInformationEntity entity=JsonUtil.getJsonToBean(hotInformationCrForm, HotInformationEntity.class);
        hotInformationService.create(entity);
        return R.ok(null, "新建成功");
}


    /**
    * 信息
    *
    * @param id
    * @return
    */
    @GetMapping("/{id}")
    public R<HotInformationInfoVO> info(@PathVariable("id") Long id){
        HotInformationEntity entity= hotInformationService.getInfo(id);
        HotInformationInfoVO vo=JsonUtil.getJsonToBean(entity, HotInformationInfoVO.class);
        return R.ok(vo);
    }



   /**
    * 更新
    *
    * @param id
    * @return
    */
    @PutMapping("/{id}")
    @Transactional
    public R update(@PathVariable("id") Long id,@RequestBody @Valid HotInformationUpForm hotInformationUpForm) throws DataException {
        HotInformationEntity entity= hotInformationService.getInfo(id);
        if(entity!=null){
            entity=JsonUtil.getJsonToBean(hotInformationUpForm, HotInformationEntity.class);
            hotInformationService.update(id, entity);
        return R.ok(null, "更新成功");
        }else{
            return R.failed("更新失败，数据不存在");
        }
    }

   /**
    * 删除
    *
    * @param id
    * @return
    */
    @DeleteMapping("/{id}")
    @Transactional
    public R delete(@PathVariable("id") Long id){
        HotInformationEntity entity= hotInformationService.getInfo(id);
        if(entity!=null){
            hotInformationService.delete(entity);
        }
        return R.ok(null, "删除成功");
    }

}
