package com.sinosoft.ie.booster.yearbook.mark.model.markentity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * MarkEntity模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-26 14:30:05
 */
@Data
@ApiModel
public class MarkEntityRelation {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "关系名称")
    private String relationName;

    @ApiModelProperty(value = "句子id")
    private String sentenceid;

    @ApiModelProperty(value = "实体名称")
    private String text;
    @ApiModelProperty(value = "实体标签")
    private String ontoName;

    @ApiModelProperty(value = "实体名称1")
    private String text1;
    @ApiModelProperty(value = "实体标签1")
    private String ontoName1;
}