package com.sinosoft.ie.booster.yearbook.mark.model.marktriplet;

import com.sinosoft.ie.booster.common.core.model.Pagination;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MarkTriplet模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-08 16:58:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkTripletPagination extends Pagination {

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long tag1id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long tag2id;

    /**
     * 
     */
    @ApiModelProperty(value = "")
    private Long relationid;
}