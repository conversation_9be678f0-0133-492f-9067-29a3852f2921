package com.sinosoft.ie.booster.yearbook.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DataSetPieEnum {
    LOCALTB(1,"本地表"),
    REMOTETB(2,"远程表"),
    SYNCHTB(3,"同步表");
    private int value;
    private String name;

    public static String getName(int num) {
        for (DataSetPieEnum dataSetPieEnum : DataSetPieEnum.values()) {
            if (dataSetPieEnum.value == num) return dataSetPieEnum.name;
        }
       return  null;
    }

}
