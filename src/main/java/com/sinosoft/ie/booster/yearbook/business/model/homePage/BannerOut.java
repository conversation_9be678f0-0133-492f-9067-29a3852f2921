package com.sinosoft.ie.booster.yearbook.business.model.homePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：BannerOut.java
 * @description：首页轮播图返回类
 * @author：peas
     * @data：2022/11/9 18:09
 **/
@Data
@ApiModel(discriminator = "首页轮播图返回类")
public class BannerOut {

    /**
     * 年鉴id
     */
    @ApiModelProperty(value = "年鉴id")
    private Long yearbookId;

    /**
     * 年鉴名称
     */
    @ApiModelProperty(value = "年鉴名称")
    private String bookName;

    /**
     * 封面图片
     */
    @ApiModelProperty(value = "封面图片")
    private String coverPic;



}
