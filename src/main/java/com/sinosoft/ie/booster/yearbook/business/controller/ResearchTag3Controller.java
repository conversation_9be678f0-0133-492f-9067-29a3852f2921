package com.sinosoft.ie.booster.yearbook.business.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTag3Entity;
import com.sinosoft.ie.booster.yearbook.business.service.ResearchTag3Service;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Api(tags = "research_tag3")
@RequestMapping("/ResearchTag3")
public class ResearchTag3Controller {
	
	@Resource
	private ResearchTag3Service researchTag3Service;
	
	@GetMapping("/computTag3")
	public R  computTag3() {
		researchTag3Service.compuit();
		return R.ok();
	}
	
	public R  getTagBydir(Long id) {
		QueryWrapper<ResearchTag3Entity> query = new QueryWrapper<ResearchTag3Entity>();
		query.eq("dir_id", id);
		List  list= researchTag3Service.list(query);
		return R.ok(list);
	}
	

}
