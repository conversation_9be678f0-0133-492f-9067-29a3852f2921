package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryListVO;
import com.sinosoft.ie.booster.yearbook.business.model.filedirectory.FileDirectoryPagination;
import java.util.*;

/**
 *
 * file_directory
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2022-09-16 14:28:43
 */
public interface FileDirectoryService extends IService<FileDirectoryEntity> {

    List<FileDirectoryEntity> getList(FileDirectoryPagination fileDirectoryPagination);

    List<FileDirectoryEntity> getTypeList(FileDirectoryPagination fileDirectoryPagination,String dataType);



    FileDirectoryEntity getInfo(Long id);

    void delete(FileDirectoryEntity entity);

    void create(FileDirectoryEntity entity);

    boolean update( Long id, FileDirectoryEntity entity);

    Map<String,Object>  showDirTree(Long fid);
    
    List<FileDirectoryListVO> showDirTree();
    
    Map<String,Object>  dirInfo(Long id);
    
    Map<String,Object>  dirInfo(Integer year, Integer pageNumber);
}
