package com.sinosoft.ie.booster.yearbook.mark.model.marksentence;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * MakrSentence模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2022-11-09 14:49:54
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class MarkSentenceUpForm extends MarkSentenceCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}