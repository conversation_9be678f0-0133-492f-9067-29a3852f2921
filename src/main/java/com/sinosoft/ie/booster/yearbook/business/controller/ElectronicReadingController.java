package com.sinosoft.ie.booster.yearbook.business.controller;

import java.io.IOException;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination;
import com.sinosoft.ie.booster.yearbook.business.service.ElectronicReadService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * 电子阅读
 * <AUTHOR>
 *
 *  从要事要闻，事件脉络跳转到电子阅读，高亮标题
 *  从标签查阅跳转到电子阅读，高亮标题（黄色），高亮标签（红色）
 *  点击首页人名跳转电子阅读高亮人名
 */
@Slf4j
@RestController
@Api(tags = "electronicReading")
@RequestMapping("/electronicReading")
public class ElectronicReadingController {
	
	@Resource
	private ElectronicReadService electronicReadService;
	
	/**
	 * 跳转至电子阅读高亮title   
	 * 选中关键词 高亮
	 * @param eventPagination
	 * @param id  事件id
	 * @param tag  关键词，多个用分号隔开
	 * @return
	 * @throws IOException 
	 */
	@GetMapping
	public R    highlight(EventPagination eventPagination) throws IOException {
		 return electronicReadService.generateHighlight(eventPagination);
	}
	/**
	 * 首页点击人名跳转
	 * @param year
	 * @param pageNumber
	 * @param name
	 * @return
	 * @throws IOException
	 */
	@GetMapping("/highlightContext")
	public R    highlightPerson(Integer year, Integer pageNumber,String name) throws IOException {
		 return electronicReadService.highlightContext(year, pageNumber, name);
	}
	
	/**
	 * 点击关键词按钮 高亮
	 * @param year
	 * @param id	pdf文件id
	 * @param tag   高亮词
	 * @return
	 * @throws IOException
	 */
	@GetMapping("/highlightTag")
	public R    highlightPerson(Long id, String tag) throws IOException {
		 return electronicReadService.highlightContext(id, tag);
	}

	
	@GetMapping("/esTohighlight")
	public R    esTohighlight(Integer year, Integer pageNumber,Long  entity_id) throws IOException {
		 return electronicReadService.esTohighlight(year,pageNumber,entity_id);
	}
	
	
}
