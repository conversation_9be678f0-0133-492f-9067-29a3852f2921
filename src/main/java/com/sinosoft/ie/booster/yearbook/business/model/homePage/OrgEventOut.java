package com.sinosoft.ie.booster.yearbook.business.model.homePage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @projectName：booster-yearbook-biz
 * @className：OrgEventOut.java
 * @description：机构事件统计返回类
 * @author：peas
 * @data：2022/11/11 13:37
 **/
@Data
public class OrgEventOut implements Serializable {

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String name;

    /**
     * 日期
     */
    @ApiModelProperty(value = "事件数量")
    private Integer count;
}
