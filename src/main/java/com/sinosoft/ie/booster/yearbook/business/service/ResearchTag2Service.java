package com.sinosoft.ie.booster.yearbook.business.service;

import com.sinosoft.ie.booster.common.core.util.R;
import com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.EventEntity;
import com.sinosoft.ie.booster.yearbook.business.entity.ResearchTag2Entity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2CrForm;
import com.sinosoft.ie.booster.yearbook.business.model.researchtag2.ResearchTag2Pagination;
import java.util.*;

/**
 *
 * research_tag2
 * 版本： V1.0.0
 * 作者： booster开发平台组
 * 日期： 2023-02-20 13:38:22
 */
public interface ResearchTag2Service extends IService<ResearchTag2Entity> {

    List<ResearchTag2Entity> getList(ResearchTag2Pagination researchTag2Pagination);

    List<ResearchTag2Entity> getTypeList(ResearchTag2Pagination researchTag2Pagination,String dataType);



    ResearchTag2Entity getInfo(Long id);

    void delete(ResearchTag2Entity entity);

    void create(ResearchTag2Entity entity);

    boolean update( Long id, ResearchTag2Entity entity);

    void createList(ResearchTag2CrForm researchTag2CrForm);

    R<Map<String, Object>> showTree();

    R event2tag2();

    void eventGetTag2(EventEntity event);
    
    void eventGetTag2_2(EventEntity event,LibEntityEntity libEntityEntity);
    
    Set<String> event_tag2_3(String attrName, Map dataMap);

//  子表方法

}
