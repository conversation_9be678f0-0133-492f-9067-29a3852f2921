package com.sinosoft.ie.booster.yearbook.business.model.pageconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 *
 * PageConfig模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-02-22 17:46:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class PageConfigInfoVO extends PageConfigCrForm{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
}