package com.sinosoft.ie.booster.yearbook.baselib.model.graph;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * Graph模型
 * @版本： V1.0.0
 * @作者： booster开发平台组
 * @日期： 2023-01-31 13:39:55
 */
@Data
@ApiModel
public class GraphListVO{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 模式id
     */
    @ApiModelProperty(value = "模式id")
    private String schemaId;
    
    /**
     * 状态：
     */
    @ApiModelProperty(value = "状态：")
    private Integer status;

}