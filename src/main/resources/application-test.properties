spring.datasource.url = jdbc\:dm\://yearbook-db\:5236?logLevel=all&logDir=D:\\DBA\\dm8\\jdbclog"
spring.datasource.username = yearbook
spring.datasource.password = dm_123456
spring.datasource.driver-class-name = dm.jdbc.driver.DmDriver
spring.datasource.druid.initialSize=3


thread-size=8
thread-max=20

upload_path=D:\\data\\upload
down_path=D:\\data\\down

excel_imgpath = D:\\data\\upload


corpuId = 1602150720744779778
corpuVer = 1
def_img_id = 1


#æ¯å¦å¯ç¨ee
easy-es.enable=true
#eså°å
easy-es.address=yearbook-es:9200
easy-es.global-config.process-index-mode=manual
#æ¯å¦æ¯åå¸å¼
easy-es.global-config.distributed=false
#æ¯å¦æå°dsl
easy-es.global-config.print-dsl=false