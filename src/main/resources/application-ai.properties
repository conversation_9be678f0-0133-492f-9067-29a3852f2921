#spring.datasource.url = jdbc\:mysql\://localhost\:3306/booster_yearbooks?useUnicode\=true&characterEncoding\=utf-8&useSSL\=false&serverTimezone\=Asia/Shanghai
spring.datasource.url = jdbc\:mysql\://localhost\:3306/military?useUnicode\=true&characterEncoding\=utf-8&useSSL\=false&serverTimezone\=Asia/Shanghai
#spring.datasource.url = jdbc\:mysql\://*************\:13306/booster_yearbooks?useUnicode\=true&characterEncoding\=utf-8&useSSL\=false&serverTimezone\=Asia/Shanghai
spring.datasource.username = root
spring.datasource.password = root
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.druid.initialSize=3


thread-size=8
thread-max=20

upload_path=D:\\data\\upload
down_path=D:\\data\\down

excel_imgpath = D:\\data\\upload


corpuId = 1602150720744779778
corpuVer = 1
def_img_id = 1

pdf_in_path=D:\\data
pdf_out_paht=D:\\data


#æ¯å¦å¯ç¨ee
easy-es.enable=true
#eså°å
easy-es.address=***************:9200
easy-es.global-config.process-index-mode=manual
#æ¯å¦æ¯åå¸å¼
easy-es.global-config.distributed=false
#æ¯å¦æå°dsl
easy-es.global-config.print-dsl=true

