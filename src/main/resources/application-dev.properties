

spring.datasource.url = jdbc\:mysql\://************\:33310/booster_yearbooks?useUnicode\=true&characterEncoding\=utf-8&useSSL\=false&serverTimezone\=Asia/Shanghai
#spring.datasource.url = jdbc\:mysql\://42.192.148.85\:13306/booster_yearbooks?useUnicode\=true&characterEncoding\=utf-8&useSSL\=false&serverTimezone\=Asia/Shanghai
spring.datasource.username = root
spring.datasource.password = zkrxxgc
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.druid.initialSize=3


thread-size=8
thread-max=20

upload_path=D:\\data\\11024
down_path=D:\\data\\down

excel_imgpath = D:\\data\\upload


corpuId = 1602150720744779778
corpuVer = 1
def_img_id = 1;2;3

#æ¯å¦å¯ç¨ee
easy-es.enable=true
#eså°å
easy-es.address=************:39200
easy-es.username=elastic
easy-es.password=elasticzkr
easy-es.global-config.process-index-mode=manual
#æ¯å¦æ¯åå¸å¼
easy-es.global-config.distributed=false
#æ¯å¦æå°dsl
easy-es.global-config.print-dsl=true
