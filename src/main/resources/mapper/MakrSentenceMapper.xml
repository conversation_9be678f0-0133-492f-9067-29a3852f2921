<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.mark.mapper.MarkSentenceMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.mark.entity.MarkSentenceEntity">
                    <id column="id" property="id" />
                    <result column="fid" property="fid" />
                    <result column="fsort" property="fsort" />
                    <result column="sentence" property="sentence" />
                    <result column="sentence_exc" property="sentenceExc" />
                    <result column="execute_data" property="executeData" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
                    <result column="status" property="status" />
                    <result column="corpus_id" property="corpusId" />
                    <result column="corpus_version" property="corpusVersion" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, fid, fsort, sentence, sentence_exc, execute_data, create_time, update_time, status,corpus_id,corpus_version
        </sql>
        
        <select id="nextEntity"  resultMap="BaseResultMap"   parameterType="Integer" >
       		 select *  from mark_sentence t  where t.id > #{id}  limit 1
        </select>
        
</mapper>
