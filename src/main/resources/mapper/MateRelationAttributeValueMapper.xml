<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.MateRelationAttributeValueMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeValueEntity">
                    <id column="id" property="id" />
                    <result column="attribute_id" property="attributeId" />
                    <result column="val_name" property="valName" />
                    <result column="pid" property="pid" />
                    <result column="onto_id" property="ontoId" />
                    <result column="relation_id" property="relationId" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, attribute_id, val_name, pid, onto_id, relation_id
        </sql>
</mapper>
