<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.FileImportJobMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportJobEntity">
                    <id column="id" property="id" />
                    <result column="file_name" property="fileName" />
                    <result column="file_type" property="fileType" />
                    <result column="file_size" property="fileSize" />
                    <result column="job_type" property="jobType" />
                    <result column="create_time" property="createTime" />
                    <result column="job_state" property="jobState" />
                    <result column="update_time" property="updateTime" />
                    <result column="create_user" property="createUser" />
                    <result column="file_new_name" property="fileNewName" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, file_name, file_type, file_size, job_type, create_time, job_state, update_time, create_user,file_new_name
        </sql>
</mapper>
