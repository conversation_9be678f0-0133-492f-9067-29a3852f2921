<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.GraphMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.GraphEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="schema_id" property="schemaId" />
                    <result column="status" property="status" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, schema_id, status
        </sql>
</mapper>
