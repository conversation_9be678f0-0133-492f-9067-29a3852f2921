<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaEntityRelationMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity">
                    <id column="id" property="id" />
                    <result column="onto1_id" property="onto1Id" />
                    <result column="onto2_id" property="onto2Id" />
                    <result column="name" property="name" />
                    <result column="entity1_Id" property="entity1Id" />
                    <result column="entity2_Id" property="entity2Id" />
                    <result column="onto_rel_id" property="ontoRelId" />
                    <result column="state" property="state" />
                    <result column="confidence" property="confidence" />
                    <result column="weight" property="weight" />
                    <result column="start_time" property="startTime" />
                    <result column="eventid" property="eventid" />
        </resultMap>

    <resultMap id="BaseResultMap1" type="com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationInfoVO">
        <id column="id" property="id" />
        <result column="onto1_id" property="onto1Id" />
        <result column="onto2_id" property="onto2Id" />
        <result column="name" property="name" />
        <result column="entity1_Id" property="entity1Id" />
        <result column="entity2_Id" property="entity2Id" />
        <result column="onto_rel_id" property="ontoRelId" />
        <result column="state" property="state" />
        <result column="confidence" property="confidence" />
        <result column="weight" property="weight" />
        <result column="start_time" property="startTime" />

        <result column="relationName" property="relationName" />
        <result column="onto1Name" property="onto1Name" />
        <result column="onto2Name" property="onto2Name" />
        <result column="entity1Name" property="entity1Name" />
        <result column="entity2Name" property="entity2Name" />
        <result column="vals" property="vals" />
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.sinosoft.ie.booster.yearbook.baselib.model.metaentityrelation.MetaEntityRelationGraph">
        <id column="id" property="id" />
        <result column="name" property="name" />
    </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, onto1_id, onto2_id, name, entity1_Id, entity2_Id, onto_rel_id, state, confidence, weight, start_time
        </sql>

    <select id="ontolistNew"  resultMap="BaseResultMap1" parameterType="com.sinosoft.ie.booster.yearbook.baselib.entity.MetaEntityRelationEntity"  >
        SELECT
            a.vals,
            m.id AS onto_rel_id,
            m.onto2_id,
            m.NAME AS relationName,
            t.*,
            c1.NAME AS onto1Name,
            c2.NAME AS onto2Name,
            e1.NAME entity1Name,
            e2.NAME entity2Name
        FROM
            meta_onto_relation m
                LEFT JOIN ( SELECT * FROM meta_entity_relation WHERE entity1_id = #{entity1Id}) t ON t.onto_rel_id = m.id
                LEFT JOIN lib_concept c1 ON t.onto1_id = c1.id
                LEFT JOIN lib_concept c2 ON t.onto2_id = c2.id
                LEFT JOIN lib_entity e1 ON t.entity1_Id = e1.id
                LEFT JOIN lib_entity e2 ON t.entity2_Id = e2.id
                LEFT JOIN mate_attribute a ON m.id = a.relation_id
        where
            m.onto1_id = #{onto1Id}
          AND a.vals IS NOT NULL
        ORDER BY
            id DESC
    </select>

    <select id="entityIdList"  resultMap="BaseResultMap2" parameterType="Map"  >
        SELECT
            le.id,
            le.`name`
        FROM
            `meta_entity_relation` mer
                LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
        WHERE
            (entity1_Id = #{entityId}
           OR
            entity2_Id = #{entityId})
        <if test="ontoRelId != null and ontoRelId != ''" >
            AND mer.onto_rel_id = #{ontoRelId}
        </if>


        union

        SELECT
            le.id,
            le.`name`
        FROM
            `meta_entity_relation` mer
                LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
        WHERE
            (entity1_Id = #{entityId}
                OR
             entity2_Id = #{entityId})
        <if test="ontoRelId != null and ontoRelId != ''" >
            AND mer.onto_rel_id = #{ontoRelId}
        </if>
    </select>

    <select id="entityIdListOnto"  resultMap="BaseResultMap2" parameterType="Map"  >
        SELECT
            le.id,
            le.`name`
        FROM
            `meta_entity_relation` mer
                LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
        WHERE
            ( onto1_id = #{ontoId} OR onto2_id = #{ontoId} )
          AND le.id IS NOT NULL

        UNION

        SELECT
            le.id,
            le.`name`
        FROM
            `meta_entity_relation` mer
                LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
        WHERE
            ( onto1_id = #{ontoId} OR onto2_id = #{ontoId} )
          AND le.id IS NOT NULL
    </select>
    <select id="aiGraphGetOntoByEntityId" resultType="java.util.Map">
        SELECT onto2_id AS id,lc.name  FROM meta_entity_relation mer
                                                LEFT JOIN lib_concept lc ON mer.onto2_id = lc.id
        WHERE
            entity1_Id = #{entityId}
        GROUP BY onto2_id

        UNION ALL

        SELECT onto1_id AS id,lc.name  FROM meta_entity_relation mer
                                                LEFT JOIN lib_concept lc ON mer.onto1_id = lc.id
        WHERE
            entity2_Id = #{entityId}
        GROUP BY onto1_id
    </select>
    
        <resultMap id="relationMap" type="java.util.Map">
	        <result column="id" property="id" />
	        <result column="name" property="name" />
	        <result column="onto" property="onto" />
	        <result column="eventid" property="eventid" />
            <result column="image_ids" property="image_ids" />
            <result column="time" property="time" />
        </resultMap>
    
    <select id="aiGraphGetEntityByEntityIdAndOntoid" resultMap="relationMap" parameterType="Map">
        SELECT DISTINCT(entity1_Id) AS "id" , le.name as "name", onto1_id AS "onto", eventid as "eventid", json_value(le.data,'$.image_ids')image_ids FROM meta_entity_relation mer
                                                   LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
        left join lib_entity le1 on mer.eventid = le1.id
        WHERE
        entity2_Id = #{entityId}
          and entity1_Id is not null
          and le.name is not null
        <if test="ontoId != null and ontoId != ''" >
            AND onto1_id = #{ontoId}
        </if>
        <if test="startTime != null and endTime != null and startTime != '' and endTime != ''">
            and json_value(le1.data,'$.start_time') <![CDATA[ <= ]]> #{endTime}
            and json_value(le1.data,'$.start_time') <![CDATA[ >= ]]> #{startTime}
        </if>
        UNION ALL

        SELECT DISTINCT(entity2_Id) AS id , le.name , onto2_id AS onto, eventid as eventid, json_value(le.data,'$.image_ids')image_ids FROM meta_entity_relation mer
                                                   LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
        left join lib_entity le1 on mer.eventid = le1.id
        WHERE
        entity1_Id = #{entityId}
        and entity2_Id is not null
        and le.name is not null
        <if test="ontoId != null and ontoId != ''" >
            AND onto2_id = #{ontoId}
        </if>
        <if test="startTime != null and endTime != null and startTime != '' and endTime != ''">
            and json_value(le1.data,'$.start_time') <![CDATA[ <= ]]> #{endTime}
            and json_value(le1.data,'$.start_time') <![CDATA[ >= ]]> #{startTime}
        </if>
    </select>
    
        <resultMap id="relationMap2" type="java.util.Map">
	        <result column="source" property="source" />
	        <result column="target" property="target" />
	        <result column="value" property="value" />
            <result column="eventid" property="eventid" />
            <result column="start_time" property="start_time" />
            <result column="title" property="title" />
            <result column="event_overview" property="event_overview" />
            <result column="pageNumber" property="pageNumber" />
            <result column="sourceOnto" property="sourceOnto" />
            <result column="targetOnto" property="targetOnto" />
        </resultMap>
    <select id="aiGraphGetRelationByEntityIdAndOntoid" resultMap="relationMap2" parameterType="Map">
        SELECT #{ontoId} AS source,entity1_Id AS target,meta_entity_relation.name AS value,eventid,
               json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
               json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
        FROM meta_entity_relation
                 left join lib_entity le on le.id = eventid
        WHERE
            onto1_id = #{ontoId}
          AND entity2_Id = #{entityId}
            and entity1_Id is not null and entity2_Id is not null
        UNION ALL

        SELECT #{ontoId} AS source,entity2_Id AS target,meta_entity_relation.name AS value,eventid,
               json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
               json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
        FROM meta_entity_relation
                 left join lib_entity le on le.id = eventid
        WHERE
            onto2_id = #{ontoId}
          AND entity1_Id = #{entityId}
          and entity1_Id is not null and entity2_Id is not null
    </select>

    <select id="aiGraphGetRelationByEntityId" resultMap="relationMap2" parameterType="Map">
        SELECT entity1_Id AS source,entity2_Id AS target,mer.name AS value,eventid,
               le1.onto_id as sourceOnto,le2.onto_id as targetOnto,
               json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
               json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
            (entity2_Id = #{entityId} AND onto1_id = #{ontoId} and entity1_Id is not null and mer.name is not null)
           or (entity1_Id = #{entityId} AND onto2_id = #{ontoId} and entity2_Id is not null and mer.name is not null)

    </select>
    <resultMap id="relationMap1" type="java.util.Map">
        <result column="time" property="time" />
        <result column="num" property="num" />
    </resultMap>
    <select id="aiGraphGetHumanTimeChartByEntityIdAndOntoid" resultMap="relationMap1" parameterType="Map">
        SELECT LEFT(json_value(le1.data,'$.start_time'),7) as time,count(1) as num FROM meta_entity_relation mer
        left join lib_entity le1 on mer.eventid = le1.id
        WHERE (entity2_Id = #{entityId} AND onto1_id = #{ontoId} and entity1_Id is not null)
           or (entity1_Id = #{entityId} AND onto2_id = #{ontoId} and entity2_Id is not null)
        group by (json_value(le1.data,'$.start_time'),7)
    </select>

    <resultMap id="relationMap3" type="java.util.Map">
        <result column="sourceId" property="sourceId" />
        <result column="sourceName" property="sourceName" />
        <result column="sourceOnto" property="sourceOnto" />
        <result column="targetId" property="targetId" />
        <result column="targetName" property="targetName" />
        <result column="targetOnto" property="targetOnto" />
        <result column="eventid" property="eventid" />
        <result column="startTime" property="startTime" />
        <result column="content" property="content" />
    </resultMap>
    <select id="humanGraphMoreRelationTime" resultMap="relationMap3" parameterType="Map">
        SELECT entity1_Id AS sourceId ,entity2_Id as targetId,le2.name as targetName,le2.onto_id as targetOnto,
               le.name as sourceName, onto1_id AS sourceOnto,
               json_value(le1.data,'$.start_time') as startTime,json_value(le1.data,'$.content')as content FROM meta_entity_relation mer
                 LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
                 left join lib_entity le1 on mer.eventid = le1.id
                 LEFT JOIN lib_entity le2 ON mer.entity2_Id = le2.id
        WHERE entity2_Id = #{entityId} and entity1_Id is not null AND onto1_id = #{ontoId}
        UNION ALL
        SELECT entity2_Id AS targetId ,entity1_Id as sourceId,le2.name as sourceName,le2.onto_id as sourceOnto,
               le.name as targetName, onto2_id AS targetOnto,
               json_value(le1.data,'$.start_time') as start_time,json_value(le1.data,'$.content')as content FROM meta_entity_relation mer
                 LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
                 left join lib_entity le1 on mer.eventid = le1.id
                 LEFT JOIN lib_entity le2 ON mer.entity1_Id = le2.id
        WHERE entity1_Id = #{entityId} and entity2_Id is not null AND onto2_id = #{ontoId}
    </select>

    <select id="aiGraphGetEntityByEvents" resultMap="relationMap" parameterType="Map">
        select DISTINCT(entity1_Id) AS "id" , le.name as "name", onto1_id AS "onto", eventid as "eventid",
                       json_value(le.data,'$.image_ids')image_ids,
                       LEFT(json_value(le1.data,'$.start_time'),7) as time from meta_entity_relation mer
       LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
       left join lib_entity le1 on mer.eventid = le1.id
        where
            entity1_Id is not null and entity2_Id is not null
          and le.name is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
        union
        select DISTINCT(entity2_Id) AS "id" , le.name as "name", onto2_id AS "onto", eventid as "eventid",
                       json_value(le.data,'$.image_ids')image_ids,
                       LEFT(json_value(le1.data,'$.start_time'),7) as time from meta_entity_relation mer
       LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
       left join lib_entity le1 on mer.eventid = le1.id
        where
            entity1_Id is not null and entity2_Id is not null
          and le.name is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
    </select>

    <select id="aiGraphGetRelationByEvents" resultMap="relationMap2" parameterType="Map">
        SELECT entity1_Id AS source,entity2_Id AS target,mer.name AS value,eventid,
               le1.onto_id as sourceOnto,le2.onto_id as targetOnto,
               json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
               json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
             entity1_Id is not null and entity2_Id is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
    </select>
    <select id="aiGraphGetRelationTimeByEvents" resultMap="relationMap3" parameterType="Map">
        SELECT entity1_Id AS sourceId,le1.name as sourceName,le1.onto_id as sourceOnto,
               entity2_Id AS targetId,le2.name as targetName,le2.onto_id as targetOnto,eventid,
               json_value(le.data,'$.start_time') as startTime,json_value(le.data,'$.content') as content
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
            entity1_Id is not null and entity2_Id is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
        order by startTime desc
    </select>
    <select id="aiGraphGetOrgTimeChartByEvents" resultMap="relationMap1" parameterType="Map">
        select time,count(1) as num from (
      select DISTINCT(entity1_Id) AS "id" ,
                     LEFT(json_value(le1.data,'$.start_time'),7) as time from meta_entity_relation mer
                   LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
                   left join lib_entity le1 on mer.eventid = le1.id
      where
          entity1_Id is not null and entity2_Id is not null
        and le.name is not null
        and eventid in(${eventids})
        and mer.onto1_id = #{ontoId}
        and mer.onto2_id in(${ontoIds})
      union
      select DISTINCT(entity2_Id) AS "id" ,
                     LEFT(json_value(le1.data,'$.start_time'),7) as time  from meta_entity_relation mer
                    LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
                    left join lib_entity le1 on mer.eventid = le1.id
      where
          entity1_Id is not null and entity2_Id is not null
        and le.name is not null
        and eventid in(${eventids})
        and mer.onto1_id in(${ontoIds})
        and mer.onto2_id = #{ontoId}
                                  )
        group by time
    </select>

    <select id="aiGraphGetEntityByEventsOrg" resultMap="relationMap" parameterType="Map">
        select DISTINCT(entity1_Id) AS "id" , le.name as "name", onto1_id AS "onto", eventid as "eventid",
                       json_value(le.data,'$.image_ids')image_ids,
                       LEFT(json_value(le1.data,'$.start_time'),7) as time from meta_entity_relation mer
                                                                           LEFT JOIN lib_entity le ON mer.entity1_Id = le.id
                                                                           left join lib_entity le1 on mer.eventid = le1.id
        where
            entity1_Id is not null and entity2_Id is not null
          and le.name is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
          and entity1_Id != #{entityId}
          and entity2_Id != #{entityId}
        union
        select DISTINCT(entity2_Id) AS "id" , le.name as "name", onto2_id AS "onto", eventid as "eventid",
                       json_value(le.data,'$.image_ids')image_ids,
                       LEFT(json_value(le1.data,'$.start_time'),7) as time from meta_entity_relation mer
                                                                           LEFT JOIN lib_entity le ON mer.entity2_Id = le.id
                                                                           left join lib_entity le1 on mer.eventid = le1.id
        where
            entity1_Id is not null and entity2_Id is not null
          and le.name is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
          and entity1_Id != #{entityId}
          and entity2_Id != #{entityId}
    </select>

    <select id="aiGraphGetRelationByEventsOrg" resultMap="relationMap2" parameterType="Map">
        SELECT entity1_Id AS source,entity2_Id AS target,mer.name AS value,eventid,
               le1.onto_id as sourceOnto,le2.onto_id as targetOnto,
               json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
               json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
            entity1_Id is not null and entity2_Id is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
          and entity1_Id != #{entityId}
          and entity2_Id != #{entityId}
    </select>
    <select id="aiGraphGetRelationTimeByEventsOrg" resultMap="relationMap3" parameterType="Map">
        SELECT entity1_Id AS sourceId,le1.name as sourceName,le1.onto_id as sourceOnto,
               entity2_Id AS targetId,le2.name as targetName,le2.onto_id as targetOnto,eventid,
               json_value(le.data,'$.start_time') as startTime,json_value(le.data,'$.content') as content
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
            entity1_Id is not null and entity2_Id is not null
          and eventid in(${eventids})
          and mer.onto1_id in(${ontoId})
          and mer.onto2_id in(${ontoId})
          and entity1_Id != #{entityId}
          and entity2_Id != #{entityId}
        order by startTime desc
    </select>
    <select id="aiGraphGetRelationTimeByEventsOrgMain" resultMap="relationMap3" parameterType="Map">
        SELECT entity1_Id AS sourceId,le1.name as sourceName,le1.onto_id as sourceOnto,
               entity2_Id AS targetId,le2.name as targetName,le2.onto_id as targetOnto,eventid,
               json_value(le.data,'$.start_time') as startTime,json_value(le.data,'$.content') as content
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
            (entity1_Id = #{entity1Id} and entity2_Id = #{entity2Id})
          or (entity1_Id = #{entity2Id} and entity2_Id = #{entity1Id})
        order by startTime desc
    </select>
    <select id="aiGraphGetRelationByEventsOrgMain" resultMap="relationMap2" parameterType="Map">
        SELECT entity1_Id AS source,entity2_Id AS target,mer.name AS value,eventid,
               le1.onto_id as sourceOnto,le2.onto_id as targetOnto,
               json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
               json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
        FROM meta_entity_relation mer
                 left join lib_entity le on le.id = eventid
                 left join lib_entity le1 on le1.id = entity1_Id
                 left join lib_entity le2 on le2.id = entity2_Id
        WHERE
            (entity1_Id = #{entity1Id} and entity2_Id = #{entity2Id})
           or (entity1_Id = #{entity2Id} and entity2_Id = #{entity1Id})
    </select>
</mapper>
