<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.domainThesaurus.mapper.DomainThesaurusMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.DomainThesaurusEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="category" property="category" />
                    <result column="pos" property="pos" />
                    <result column="synonym" property="synonym" />
                    <result column="status" property="status" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_by" property="createBy" />
                    <result column="create_time" property="createTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, category, pos, synonym, status, del_flag, create_by, create_time, update_by, update_time
        </sql>
</mapper>
