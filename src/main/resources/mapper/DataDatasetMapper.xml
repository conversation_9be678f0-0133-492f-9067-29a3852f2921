<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.database.mapper.DataDatasetMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.database.entity.DataDatasetEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="table_name" property="tableName" />
                    <result column="source" property="source" />
                    <result column="schema_id" property="schemaId" />
                    <result column="data_connect_id" property="dataConnectId" />
                    <result column="data_id" property="dataId" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, table, source, schema_id, data_connect_id, data_id
        </sql>

        <select id="getTableName" resultType="string">
            select table_name from INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA='booster_yearbooks'
        </select>

        <select id="getTableNum" resultType="integer" parameterType="string">
            SELECT count(1) FROM ${tableName}
        </select>
</mapper>
