<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.ConverRulesMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.ConverRulesEntity">
                    <id column="id" property="id" />
                    <result column="onto_id" property="ontoId" />
                    <result column="onto_name" property="ontoName" />
                    <result column="category_id" property="categoryId" />
                    <result column="category_val" property="categoryVal" />
                    <result column="type" property="type" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, onto_id, onto_name, category_id, category_val, type
        </sql>
</mapper>
