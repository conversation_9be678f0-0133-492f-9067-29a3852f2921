<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.BannerMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.BannerEntity">
                    <id column="id" property="id" />
                    <result column="yearbook_id" property="yearbookId" />
                    <result column="sort" property="sort" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, yearbook_id, sort, del_flag, create_time, create_by, update_time, update_by
        </sql>

        <select id="queryBanner" resultType="com.sinosoft.ie.booster.yearbook.business.model.homePage.BannerOut">
            SELECT
                b.yearbook_id,
                y.book_name,
                y.cover_pic
            FROM
                banner b
                INNER JOIN yearbook y ON b.yearbook_id = y.id
            WHERE
                b.del_flag = 0
            ORDER BY b.sort ASC
        </select>
</mapper>
