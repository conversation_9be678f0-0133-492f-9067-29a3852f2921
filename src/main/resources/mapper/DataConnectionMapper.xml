<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.database.mapper.DataConnectionMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.database.entity.DataConnectionEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="db_type" property="dbType" />
                    <result column="ip" property="ip" />
                    <result column="port" property="port" />
                    <result column="db_base_name" property="dbBaseName" />
                    <result column="user" property="user" />
                    <result column="password" property="password" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, db_type, ip, port, db_base_name, user, password
        </sql>
</mapper>
