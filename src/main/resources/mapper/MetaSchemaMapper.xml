<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaSchemaMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.MetaSchemaEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="state" property="state" />
                    <result column="schema_type" property="schemaType" />
                    <result column="ext_id" property="extId" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, del_flag, create_time, create_by, update_time, update_by, state, schema_type, ext_id
        </sql>
</mapper>
