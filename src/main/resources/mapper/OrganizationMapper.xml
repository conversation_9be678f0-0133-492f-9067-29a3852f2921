<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.OrganizationMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.OrganizationEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="zonelevel" property="zonelevel" />
                    <result column="pid" property="pid" />
                    <result column="sort_num" property="sortNum" />
                    <result column="pic" property="pic" />
                    <result column="note" property="note" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, zonelevel, pid, sort_num, pic, note, del_flag, create_time, create_by, update_time, update_by
        </sql>
</mapper>
