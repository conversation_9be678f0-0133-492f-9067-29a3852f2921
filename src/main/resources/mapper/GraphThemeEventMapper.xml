<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeEventMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEventEntity">
                    <id column="id" property="id" />
                    <result column="theme" property="theme" />
                    <result column="event_id" property="eventId" />
                    <result column="entity_id" property="entityId" />
                    <result column="entity_name" property="entityName" />
                    <result column="onto_id" property="ontoId" />
                    <result column="onto_name" property="ontoName" />
        </resultMap>
    <resultMap id="relationMap2" type="java.util.Map">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="data" property="data" />
        <result column="onto" property="onto" />
        <result column="eventid" property="eventid" />
        <result column="start_time" property="start_time" />
        <result column="title" property="title" />
        <result column="event_overview" property="event_overview" />
        <result column="pageNumber" property="pageNumber" />
        <result column="image_ids" property="image_ids" />
    </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, theme, event_id, entity_id, entity_name, onto_id, onto_name
        </sql>
    <resultMap id="relationMap3" type="java.util.Map">
        <result column="sourceName" property="sourceName" />
        <result column="targetId" property="targetId" />
        <result column="targetName" property="targetName" />
        <result column="targetOnto" property="targetOnto" />
        <result column="startTime" property="startTime" />
        <result column="targetOntoName" property="targetOntoName" />
        <result column="content" property="content" />
    </resultMap>
    <select id="themeGraphRelationTime" resultMap="relationMap3">
        select #{theme} as sourceName,gte.entity_id as targetId,gte.entity_name as targetName,
               gte.onto_id as targetOnto,gte.onto_name as targetOntoName,
               json_value(data,'$.start_time') as startTime ,
               json_value(data,'$.content') as content from graph_theme_event gte
        left join lib_entity e on gte.event_id = e.id
        where gte.theme =  #{theme}
        order by startTime asc
    </select>

    <select id="themeGraphRelationTimeChart" resultType="java.util.Map">
        select gte.*,e.start_time,e.content from graph_theme_event gte
                                                     left join event e on gte.event_id = e.id
        where gte.theme =  #{theme}
    </select>

    <select id="themeGraphRelation" resultMap="relationMap2">
        select distinct(entity_id) id,gte.entity_name as name,json_value(le1.data,'$.image_ids') as image_ids,#{onto} onto,event_id as eventid,
        json_value(le.data,'$.start_time') as start_time,json_value(le.data,'$.title') as title,
        json_value(le.data,'$.event_overview')as event_overview,json_value(le.data,'$.pageNum')as pageNumber
                       from graph_theme_event gte
        left join lib_entity le on le.id = gte.event_id
        left join lib_entity le1 on le1.id = gte.entity_id
        where gte.onto_id = #{onto} and theme = #{theme}
          <if test="startTime != null and endTime != null and startTime != '' and endTime != ''">
              and json_value(data,'$.start_time') <![CDATA[ <= ]]> #{endTime}
              and json_value(data,'$.start_time') <![CDATA[ >= ]]> #{startTime}
          </if>
    </select>
    <resultMap id="relationMap4" type="java.util.Map">
        <result column="time" property="time" />
        <result column="num" property="num" />
    </resultMap>
    <select id="themeGraphRelationTimeChartNew" resultMap="relationMap4">
        SELECT LEFT(json_value(le.data,'$.start_time'),7) as time,count(1) as num FROM graph_theme_event gte
        left join lib_entity le on event_id = le.id
        WHERE (theme = #{theme} AND gte.onto_id = #{onto})
        group by (json_value(le.data,'$.start_time'),7)
    </select>
</mapper>
