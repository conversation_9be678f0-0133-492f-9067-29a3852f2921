<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.ReportDetailsMapper" >

    <select id="getReportDetails" resultType="com.sinosoft.ie.booster.yearbook.business.entity.ReportDetails">
        select d.detail_content,d.sort_order from report_details d
        join report_themes t on d.theme_id = t.id
        where t.theme_name = #{theme}
        order by d.sort_order
    </select>
</mapper>