<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.MetaOntoRelationEntity">
                    <id column="id" property="id" />
                    <result column="onto1_id" property="onto1Id" />
                    <result column="onto2_id" property="onto2Id" />
                    <result column="name" property="name" />
                    <result column="pid" property="pid" />
        </resultMap>
        
       <resultMap id="BaseResultMap1" type="com.sinosoft.ie.booster.yearbook.baselib.model.metaontorelation.MetaOntoRelationInfoVO">
           <id column="id" property="id" />
           <result column="onto1_id" property="onto1Id" />
           <result column="onto2_id" property="onto2Id" />
           <result column="ontoName1" property="ontoName1" />
           <result column="ontoName2" property="ontoName2" />
           <result column="name" property="name" />
           <result column="pid" property="pid" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, onto1_id, onto2_id, name, pid
        </sql>
        
        
        <select id="ontolist"  resultMap="BaseResultMap1"   >
        select t.*,c1.name as ontoName1 ,c2.name as ontoName2 from meta_onto_relation t 
		left join lib_concept c1 on t.onto1_id =  c1.id
		left join lib_concept c2 on t.onto2_id =  c2.id
		${ew.customSqlSegment}
        </select>
        
        
    <sql id="MySqlPaginationSuffix">
    <if test="startNum != null and startNum >= 0 and pageSize != null and pageSize > 0">
      <![CDATA[  limit #{startNum},#{pageSize} ]]>
    </if>
  </sql>
        
</mapper>
