<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.mark.mapper.MarkTripletMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.mark.entity.MarkTripletEntity">
                    <id column="id" property="id" />
                    <result column="tag1Id" property="tag1Id" />
                    <result column="tag2Id" property="tag2Id" />
                    <result column="relationId" property="relationId" />
                    <result column="relationName" property="relationName" />
                    <result column="sentenceId" property="sentenceId" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, tag1Id, tag2Id, relationId, relationName, sentenceId
        </sql>
</mapper>
