<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.YearbookMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity">
                    <id column="id" property="id" />
                    <result column="new_name" property="newName" />
                    <result column="old_name" property="oldName" />
                    <result column="path" property="path" />
                    <result column="type" property="type" />
                    <result column="pid" property="pid" />
                    <result column="page_num" property="pageNum" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, new_name, old_name, path, type, pid, page_num, del_flag, create_time, create_by, update_time, update_by
        </sql>
    <select id="getInfoFid" resultType="com.sinosoft.ie.booster.yearbook.business.entity.YearbookEntity"
            parameterType="java.lang.Long">
        SELECT
        <include refid="Base_Column_List" />
        FROM yearbook
        WHERE fid = #{fid}
    </select>
</mapper>
