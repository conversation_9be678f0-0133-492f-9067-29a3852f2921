<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.EventMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.EventEntity">
                    <id column="id" property="id" />
                    <result column="title" property="title" />
                    <result column="start_time" property="startTime" />
                    <result column="end_time" property="endTime" />
                    <result column="venue" property="venue" />
                    <result column="status" property="status" />
                    <result column="persons" property="persons" />
                    <result column="articles" property="articles" />
                    <result column="organisation" property="organisation" />
                    <result column="triggerss" property="triggerss" />
                    <result column="multi_relations" property="multiRelations" />
                    <result column="frequency" property="frequency" />
                    <result column="images" property="images" />
                    <result column="category" property="category" />
                    <result column="content" property="content" />
                    <result column="note" property="note" />
                    <result column="orgid" property="orgid" />
                    <result column="tag" property="tag" />
					<result column="tag2" property="tag2" />
                    <result column="achievements" property="achievements" />
                    <result column="meeting" property="meeting" />
                    <result column="fid" property="fid" />
                    <result column="page_num" property="pageNum" />
                    <result column="theme" property="theme" />
					<result column="political_work" property="politicalWork" />
					<result column="security_manage" property="securityManage" />
					<result column="comprehensive_coordination" property="comprehensiveCoordination" />
					<result column="scientific_manage" property="scientificManage" />
					<result column="scientific_strength" property="scientificStrength" />
					<result column="scientific_organization" property="scientificOrganization" />
					<result column="scientific_subject" property="scientificSubject" />
					<result column="scientific_result" property="scientificResult" />
					<result column="weight" property="weight" />
					<result column="decision_making" property="decisionMaking" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, title, start_time, end_time, venue, status, persons, articles, organisation, triggerss, multi_relations, frequency, images, category, content, note, orgid, tag, achievements, meeting, fid, page_num, theme
                    ,political_work,security_manage,comprehensive_coordination,scientific_manage,decision_making,scientific_strength,scientific_organization,scientific_subject,scientific_result,weight
        </sql>
	<sql id="MySqlPaginationSuffix">
		<if test="currentPage != null and currentPage >= 0 and pageSize != null and pageSize > 0">
			<![CDATA[  limit #{currentPage},#{pageSize} ]]>
		</if>
	</sql>

	<select id="queryAll" resultMap="BaseResultMap"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		select
		tag
		from event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			1=1
			<if test="persons != null">
				AND persons = #{persons}
			</if>
			<if test="organisation != null">
				AND organisation = #{organisation}
			</if>
			<if test="achievements != null">
				AND achievements = #{achievements}
			</if>
			<if test="category != null">
				AND category = #{category}
			</if>
			<if test="task != null">
				AND task = #{task}
			</if>
			<if test="tag != null and tag != ''">
				AND ${tag}
			</if>
			<if test="year != null and year != ''">
				AND start_time LIKE '%${year}%'
			</if>
			<if test="startTime != null and startTime != ''">
				AND ${startTime}
			</if>
			<if test="content != null and content != ''">
				AND (content LIKE '%${content}%' or title LIKE '%${content}%' OR  (';'||tag||';' ) like '%;${content};%')
			</if>
			<if test="fdids != null and fdids != ''">
				AND fdid in (${fdids})
			</if>
			<if test="fid != null">
				AND fid = #{fid}
			</if>
			<if test="fdid != null and fdid != ''">
				AND fdid in (select t.cid
				from (
				SELECT t1.id,t6.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				LEFT JOIN file_directory t6
				ON t5.id = t6.pid
				union
				SELECT t1.id,t5.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				union
				SELECT t1.id,t4.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				union
				SELECT t1.id,t3.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				union
				SELECT t1.id,t2.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				union
				SELECT t1.id,t1.id as cid
				FROM file_directory t1) t
				where t.id = #{fdid} and t.cid is not null)
			</if>

			<if test="researchTag2Sql != null and researchTag2Sql != ''">
				${researchTag2Sql}
			</if>
		</trim>
	</select>
	<select id="queryAllCount" resultType="java.lang.Integer"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		select
		count(1)
		from event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			1=1
			<if test="persons != null">
				AND persons = #{persons}
			</if>
			<if test="organisation != null">
				AND organisation = #{organisation}
			</if>
			<if test="achievements != null">
				AND achievements = #{achievements}
			</if>
<!--			<if test="category != null">-->
<!--				AND category = #{category}-->
<!--			</if>-->
			<if test="sqlstr != null">
				AND  ${sqlstr}
			</if>
			<if test="task != null">
				AND task = #{task}
			</if>
<!--			<if test="tag != null and tag != ''">-->
<!--				AND ${tag}-->
<!--			</if>-->
			<if test="year != null and year != ''">
				AND start_time LIKE '%${year}%'
			</if>
			<if test="startTime != null and startTime != ''">
				AND ${startTime}
			</if>
			<if test="content != null and content != ''">
				AND (content LIKE '%${content}%' or title LIKE '%${content}%' OR (';'||tag||';' ) like '%;${content};%')
			</if>
			<if test="fdids != null and fdids != ''">
				AND fdid in (${fdids})
			</if>
			<if test="fid != null">
				AND fid = #{fid}
			</if>
			<if test="fdid != null and fdid != ''">
				AND fdid in (select t.cid
				from (
				SELECT t1.id,t6.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				LEFT JOIN file_directory t6
				ON t5.id = t6.pid
				union
				SELECT t1.id,t5.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				union
				SELECT t1.id,t4.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				union
				SELECT t1.id,t3.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				union
				SELECT t1.id,t2.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				union
				SELECT t1.id,t1.id as cid
				FROM file_directory t1) t
				where t.id = #{fdid} and t.cid is not null)
			</if>

<!--			<if test="researchTag2Sql != null and researchTag2Sql != ''">-->
<!--				${researchTag2Sql}-->
<!--			</if>-->
			<if test="tagsql != null and tagsql != ''">
				AND (${tagsql})
			</if>
		</trim>
	</select>
	<select id="queryTagCount" resultType="java.lang.Integer"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		select
		count(1)
		from event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			<if test="persons != null">
				AND persons = #{persons}
			</if>
			<if test="organisation != null">
				AND organisation = #{organisation}
			</if>
			<if test="achievements != null">
				AND achievements = #{achievements}
			</if>
<!--			<if test="category != null">-->
<!--				AND category = #{category}-->
<!--			</if>-->
			<if test="sqlstr != null">
				AND  ${sqlstr}
			</if>
			<if test="task != null">
				AND task = #{task}
			</if>
			<if test="tag != null and tag != ''">
				AND ${tag}
			</if>
			<if test="year != null and year != ''">
				AND start_time LIKE '%${year}%'
			</if>
			<if test="startTime != null  and startTime != ''">
				AND ${startTime}
			</if>
			<if test="content != null and content != ''">
				AND (content LIKE '%${content}%' or title LIKE '%${content}%' OR (';'||tag||';' ) like '%;${content};%')
			</if>
			<if test="fdids != null and fdids != ''">
				AND fdid in (${fdids})
			</if>
			<if test="fid != null">
				AND fid = #{fid}
			</if>
			<if test="fdid != null and fdid != ''">
				AND fdid in (select t.cid
				from (
				SELECT t1.id,t6.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				LEFT JOIN file_directory t6
				ON t5.id = t6.pid
				union
				SELECT t1.id,t5.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				union
				SELECT t1.id,t4.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				union
				SELECT t1.id,t3.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				union
				SELECT t1.id,t2.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				union
				SELECT t1.id,t1.id as cid
				FROM file_directory t1) t
				where t.id = #{fdid} and t.cid is not null)
			</if>

<!--			<if test="researchTag2Sql != null and researchTag2Sql != ''">-->
<!--				${researchTag2Sql}-->
<!--			</if>-->
<!--			<if test="tagsql != null and tagsql != ''">-->
<!--				AND (${tagsql})-->
<!--			</if>-->
		</trim>
	</select>
	<select id="pdidCount" resultType="Long"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		SELECT DISTINCT(fdid) FROM event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			<if test="persons != null">
				AND persons = #{persons}
			</if>
			<if test="organisation != null">
				AND organisation = #{organisation}
			</if>
			<if test="achievements != null">
				AND achievements = #{achievements}
			</if>
			<if test="category != null">
				AND category = #{category}
			</if>
			<if test="task != null">
				AND task = #{task}
			</if>
			<if test="tag != null  and tag != ''">
				AND ${tag}
			</if>
			<if test="year != null and year != ''">
				AND start_time LIKE '%${year}%'
			</if>
			<if test="startTime != null  and startTime != ''">
				AND ${startTime}
			</if>
			<if test="content != null and content != ''">
				AND (content LIKE '%${content}%' or title LIKE '%${content}%' OR (';'||tag||';' ) like '%;${content};%')
			</if>
			<if test="fdids != null and fdids != ''">
				AND fdid in (${fdids})
			</if>
		</trim>
	</select>
	<select id="mapData" resultType="java.util.Map"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		SELECT
			city AS name,
			COUNT( city ) AS value
		FROM
			event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			<if test="theme != null  and theme != ''">
				AND theme = #{theme}
			</if>
			<if test="startTime != null  and startTime != ''">
				AND start_time LIKE '${startTime}%'
			</if>
		</trim>
		GROUP BY
			city
	</select>

	<select id="getTimeSelect" resultType="com.sinosoft.ie.booster.yearbook.business.entity.EventEntity">
		SELECT MIN(start_time) as start_time,MAX(start_time) as end_time FROM event
	</select>

	<select id="getNewsCount" resultType="java.lang.Integer">
		SELECT
			sum(t.count)
		FROM
			organization o
			INNER JOIN (SELECT count(1) AS count, e.orgid FROM event e GROUP BY e.orgid) t ON o.id = t.orgid
		WHERE
			o.del_flag = 0
	</select>

	<select id="getOrgEvent" resultType="com.sinosoft.ie.booster.yearbook.business.model.homePage.OrgEventOut">
		SELECT
			o.name,
			IFNULL(t.count,0) AS count
		FROM
			organization o
			LEFT JOIN (SELECT count(1) AS count, e.orgid FROM event e GROUP BY e.orgid) t ON o.id = t.orgid
		WHERE
			o.del_flag = 0
	</select>

	<select id="getEventVein" resultType="com.sinosoft.ie.booster.yearbook.business.model.homePage.EventVeinOut">
		SELECT
			DATE_FORMAT(e.start_time, '%c') AS year,
			e.tag,
			e.theme
		FROM
			EVENT e
		WHERE
			DATE_FORMAT(e.start_time, '%Y') = YEAR(CURDATE())-2
		ORDER BY e.start_time ASC
	</select>
	<select id="queryAllPage" resultMap="BaseResultMap"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		select
		<include refid="Base_Column_List" />
		from  event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			1=1
			<if test="persons != null">
				AND persons = #{persons}
			</if>
			<if test="organisation != null">
				AND organisation = #{organisation}
			</if>
			<if test="achievements != null">
				AND achievements = #{achievements}
			</if>
			<if test="sqlstr != null">
				AND  ${sqlstr}
			</if>
			<if test="task != null">
				AND task = #{task}
			</if>
			<if test="year != null and year != ''">
				AND start_time LIKE '%${year}%'
			</if>
			<if test="startTime != null and startTime != ''">
				AND start_time LIKE '%${startTime}%'
			</if>
			<if test="content != null and content != ''">
				AND (content LIKE '%${content}%' or title LIKE '%${content}%' OR (';'||tag||';' ) like '%;${content};%')
			</if>
			<if test="fdids != null and fdids != ''">
				AND fdid in (${fdids})
			</if>
			<if test="fid != null">
				AND fid = #{fid}
			</if>
			<if test="fdid != null and fdid != ''">
				AND fdid in (select t.cid
				from (
				SELECT t1.id,t6.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				LEFT JOIN file_directory t6
				ON t5.id = t6.pid
				union
				SELECT t1.id,t5.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				union
				SELECT t1.id,t4.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				union
				SELECT t1.id,t3.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				union
				SELECT t1.id,t2.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				union
				SELECT t1.id,t1.id as cid
				FROM file_directory t1) t
				where t.id = #{fdid} and t.cid is not null)
			</if>
			<if test="tagsql != null and tagsql != ''">
				AND (${tagsql})
			</if>
		</trim>
		order by start_time ASC
		<include refid="MySqlPaginationSuffix" />
	</select>
	<select id="queryAllAll" resultMap="BaseResultMap"
			parameterType="com.sinosoft.ie.booster.yearbook.business.model.event.EventPagination">
		select
		id,tag,tag2
		from event
		<trim prefix="WHERE" prefixOverrides="AND|OR">
			1=1
			<if test="persons != null">
				AND persons = #{persons}
			</if>
			<if test="organisation != null">
				AND organisation = #{organisation}
			</if>
			<if test="achievements != null">
				AND achievements = #{achievements}
			</if>
			<if test="sqlstr != null">
				AND  ${sqlstr}
			</if>
			<if test="task != null">
				AND task = #{task}
			</if>
			<if test="year != null and year != ''">
				AND start_time LIKE '%${year}%'
			</if>
			<if test="startTime != null and startTime != ''">
				AND ${startTime}
			</if>
			<if test="content != null and content != ''">
				AND (content LIKE '%${content}%' or title LIKE '%${content}%' OR (';'||tag||';' ) like '%;${content};%')
			</if>
			<if test="fdids != null and fdids != ''">
				AND fdid in (${fdids})
			</if>
			<if test="fid != null">
				AND fid = #{fid}
			</if>
			<if test="fdid != null and fdid != ''">
				AND fdid in (select t.cid
				from (
				SELECT t1.id,t6.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				LEFT JOIN file_directory t6
				ON t5.id = t6.pid
				union
				SELECT t1.id,t5.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				LEFT JOIN file_directory t5
				ON t4.id = t5.pid
				union
				SELECT t1.id,t4.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				LEFT JOIN file_directory t4
				ON t3.id = t4.pid
				union
				SELECT t1.id,t3.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				LEFT JOIN file_directory t3
				ON t2.id = t3.pid
				union
				SELECT t1.id,t2.id as cid
				FROM file_directory t1
				LEFT JOIN file_directory t2
				ON t1.id = t2.pid
				union
				SELECT t1.id,t1.id as cid
				FROM file_directory t1) t
				where t.id = #{fdid} and t.cid is not null)
			</if>
			<if test="tagsql != null and tagsql != ''">
				AND (${tagsql})
			</if>
		</trim>
	</select>
    <select id="monthlyStats" resultType="java.lang.Integer">
		WITH months AS (
			SELECT 1 AS month_num UNION ALL
			SELECT 2 UNION ALL
			SELECT 3 UNION ALL
			SELECT 4 UNION ALL
			SELECT 5 UNION ALL
			SELECT 6 UNION ALL
			SELECT 7 UNION ALL
			SELECT 8 UNION ALL
			SELECT 9 UNION ALL
			SELECT 10 UNION ALL
			SELECT 11 UNION ALL
			SELECT 12
		)
		SELECT
			COALESCE(e.event_count, 0) AS event_count
		FROM
			months m
				LEFT JOIN (
				SELECT
					MONTH(STR_TO_DATE(start_time, '%Y-%m-%d')) AS month_num,
					COUNT(*) AS event_count
				FROM
					event
				WHERE
					start_time IS NOT NULL
				  AND STR_TO_DATE(start_time, '%Y-%m-%d') IS NOT NULL
				GROUP BY
					MONTH(STR_TO_DATE(start_time, '%Y-%m-%d'))
			) e ON m.month_num = e.month_num
		ORDER BY
			m.month_num;
	</select>
	<select id="getMonthlyStats" resultType="java.util.Map">
		SELECT 
			MONTH(start_time) AS month,
			COUNT(CASE WHEN category IN ('科研活动', '综合协调', '政治工作') THEN 1 END) AS event_count,
			COUNT(CASE WHEN category IN ('科研力量', '领导决策')  THEN 1 END) AS project_count,
				COUNT(CASE WHEN scientific_result IS NOT NULL AND scientific_result != '' THEN 1 END) AS result_count,
			COUNT(CASE WHEN prize IS NOT NULL AND prize != '' THEN 1 END) AS prize_count
		FROM event 
		WHERE YEAR(start_time) = YEAR(CURDATE())
		GROUP BY MONTH(start_time)
		ORDER BY month
	</select>
</mapper>
