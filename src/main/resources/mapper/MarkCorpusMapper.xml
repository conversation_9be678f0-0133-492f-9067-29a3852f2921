<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.mark.mapper.MarkCorpusMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.mark.entity.MarkCorpusEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="pid" property="pid" />
                    <result column="fid" property="fid" />
                    <result column="stat" property="stat" />
                    <result column="start_page" property="startPage" />
                    <result column="version" property="version" />
                    <result column="annotaion_type" property="annotaionType" />
                    <result column="schema_type" property="schemaType" />
                    <result column="schema_id" property="schemaId" />
                    <result column="file_type" property="fileType" />
                    <result column="export_type" property="exportType" />
                    <result column="granularity" property="granularity" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
                    <result column="pversion" property="pversion" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, pid, fid, stat, start_page, version, annotaion_type, schema_type, schema_id, file_type, export_type, granularity, del_flag, create_time, create_by, update_time, update_by,pversion
        </sql>
</mapper>
