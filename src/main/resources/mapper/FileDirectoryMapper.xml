<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.FileDirectoryMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.FileDirectoryEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="pid" property="pid" />
                    <result column="page_num" property="pageNum" />
                    <result column="zonelevel" property="zonelevel" />
                    <result column="sort_num" property="sortNum" />
                    <result column="file_id" property="fileId" />
                    <result column="pic_id" property="picId" />
                    <result column="del_flag" property="delFlag" />
                    <result column="create_time" property="createTime" />
                    <result column="create_by" property="createBy" />
                    <result column="update_time" property="updateTime" />
                    <result column="update_by" property="updateBy" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, pid, page_num, zonelevel, sort_num, file_id, pic_id, del_flag, create_time, create_by, update_time, update_by
        </sql>
</mapper>
