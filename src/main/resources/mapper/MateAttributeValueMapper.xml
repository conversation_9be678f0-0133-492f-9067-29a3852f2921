<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.MateAttributeValueMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.MateAttributeValueEntity">
                    <id column="id" property="id" />
                    <result column="attribute_id" property="attributeId" />
                    <result column="val_name" property="valName" />
                    <result column="pid" property="pid" />
                    <result column="onto_id" property="ontoId" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, attribute_id, val_name, pid, onto_id
        </sql>

    <select id="getAttributeValueList" resultType="com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO" parameterType="java.util.List">
        SELECT
            b.id,b.pid,b.val_name AS name_cn,a.name_en,a.val_type,a.must,a.onto,a.type,b.attribute_id
        FROM
            ( SELECT * FROM mate_attribute WHERE onto = #{ontoId} AND name_cn IN ( '事件标签', '主题', '类别', '类别II' ) ) a
                INNER JOIN mate_attribute_value b ON a.id = b.attribute_id
    </select>

    <select id="getEntityInsertValue" resultType="com.sinosoft.ie.booster.yearbook.baselib.model.mateattribute.MateAttributeListVO" parameterType="long">
        SELECT a.*,av.val_name,av.pid FROM mate_attribute a
                                               LEFT JOIN mate_attribute_value av ON av.attribute_id = a.id
        WHERE a.onto = #{ontoId}
    </select>
</mapper>
