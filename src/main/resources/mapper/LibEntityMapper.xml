<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.LibEntityMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="onto_id" property="ontoId" />
                    <result column="data" property="data" />
                    <result column="state" property="state" />
                    <result column="score" property="score" />
                    <result  column="synonym_word" property="synonymWord" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, onto_id, data, state,synonym_word
        </sql>

        <select id="getMergeEntity" resultMap="BaseResultMap">
            SELECT * FROM (SELECT
                               id,
                               NAME,
                               onto_id,
                               corpus_id,
                               synonym_word,
                               COUNT( 1 ) AS score
                           FROM
                               yearbook.lib_entity
                           WHERE
                               corpus_id IS NOT NULL
                           GROUP BY
                               NAME,
                               onto_id,
                               corpus_id)a
            WHERE a.score > 1
        </select>

    <select id="getSynonymLike" resultMap="BaseResultMap" parameterType="string">
        SELECT
            *
        FROM
            yearbook.lib_entity
        WHERE
          (';'||synonym_word || ';')
            LIKE "%;${synonym};%"
    </select>
    
    
    <select id="getByNameOrSynonymLike" resultMap="BaseResultMap" 
    parameterType="com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity"
    >
        SELECT
            *
        FROM
            yearbook.lib_entity
        WHERE
            name = #{name}  or 
           (';'||synonym_word || ';') LIKE '%;${synonymWord};%'
    </select>
    <select id="orgGraphszs" resultType="com.sinosoft.ie.booster.yearbook.baselib.entity.LibEntityEntity">
        select distinct(le1.id),le1.name from lib_entity le
        left join lib_entity le1 on le1.id = json_value(le.data,'$.org2_id')
        where
            le1.name is not null and le1.name != ''
           and le.onto_id = #{ontoId}
          and  json_value(le.data,'$.org_ids')= #{orgId}
          and json_value(le.data,'$.org2_id') is not null
    </select>

    <resultMap id="BaseResultMap1" type="map">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="pic" property="pic" />
        <result column="orgId" property="orgId" />
        <result column="score" property="score" />
    </resultMap>
    <select id="orgPersonList" resultMap="BaseResultMap1" parameterType="map">
        SELECT id,name,json_value(data,'$.image_ids') as pic,json_value(data,'$.org_id') as orgId,score FROM lib_entity
        WHERE onto_id = #{ontoId}  and json_value(data,'$.org_id') in (${orgId})
                and json_value(data,'$.image_ids') is not null
        order by score asc
    </select>

</mapper>
