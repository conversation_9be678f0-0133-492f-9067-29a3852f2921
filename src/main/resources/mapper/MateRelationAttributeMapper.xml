<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.MateRelationAttributeMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.MateRelationAttributeEntity">
                    <id column="id" property="id" />
                    <result column="name_en" property="nameEn" />
                    <result column="name_cn" property="nameCn" />
                    <result column="val_type" property="valType" />
                    <result column="must" property="must" />
                    <result column="onto" property="onto" />
                    <result column="type" property="type" />
                    <result column="vals" property="vals" />
                    <result column="relation_id" property="relationId" />
                    <result column="socre" property="socre" />
                    <result column="unit" property="unit" />
                    <result column="synonym" property="synonym" />
                    <result column="point" property="point" />
                    <result column="pid" property="pid" />
                    <result column="towards" property="towards" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name_en, name_cn, val_type, must, onto, type, vals, relation_id, socre, unit, synonym, point, pid, towards
        </sql>
</mapper>
