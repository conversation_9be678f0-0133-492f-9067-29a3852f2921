<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.baselib.mapper.FileImportLogMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.baselib.entity.FileImportLogEntity">
                    <id column="id" property="id" />
                    <result column="job_id" property="jobId" />
                    <result column="row_num" property="rowNum" />
                    <result column="name" property="name" />
                    <result column="job_type" property="jobType" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, job_id, row_num, name, job_type
        </sql>
</mapper>
