<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.mark.mapper.MarkEntityMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.mark.entity.MarkEntityEntity">
                    <id column="id" property="id" />
                    <result column="tagName" property="tagname" />
                    <result column="tag_id" property="tagId" />
                    <result column="tag_pid" property="tagPid" />
                    <result column="text" property="text" />
                    <result column="sentenceId" property="sentenceid" />
                    <result column="ontoName" property="ontoname" />
                    <result column="startIndex" property="startindex" />
                    <result column="endIndex" property="endindex" />
                    <result column="lab_type" property="labType" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, tagName, tag_id, tag_pid, text, sentenceId, ontoName, startIndex, endIndex, lab_type
        </sql>
        <sql id="MySqlPaginationSuffix">
            <if test="currentPage != null and currentPage >= 0 and pageSize != null and pageSize > 0">
                <![CDATA[  limit #{currentPage},#{pageSize} ]]>
            </if>
        </sql>
        <select id="getEntityList" resultMap="BaseResultMap" parameterType="com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination">
            SELECT
                id,
            text,
            sentenceId,
            ontoName
            FROM
                mark_entity
            WHERE
                    sentenceId IN (
                    SELECT id FROM mark_sentence WHERE (corpus_id = #{corpusId} AND corpus_version = #{corpusVersion})
                )
              AND tagName = '实体'
            ORDER BY id ASC
            <include refid="MySqlPaginationSuffix" />
        </select>

        <select id="getEntityRelationList" resultType="com.sinosoft.ie.booster.yearbook.mark.model.markentity.MarkEntityRelation"
                parameterType="com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination">
            SELECT
            mt.id,
            mt.relationName,
            mt.sentenceId,
            me.text,
            me.ontoName,
            me1.text as text1,
            me1.ontoName as ontoName1
            FROM
            mark_triplet mt
            LEFT JOIN mark_entity me ON me.id = mt.tag1Id
            LEFT JOIN mark_entity me1 ON me1.id = mt.tag2Id
            WHERE
            mt.sentenceId IN (
            SELECT id FROM mark_sentence WHERE (corpus_id = #{corpusId} AND corpus_version = #{corpusVersion})
            )
            ORDER BY id ASC
            <include refid="MySqlPaginationSuffix" />
        </select>

        <select id="getEntityAttrList" resultMap="BaseResultMap" parameterType="com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination">
            SELECT
            id,
            text,
            sentenceId,
            ontoName
            FROM
            mark_entity
            WHERE
            sentenceId IN (
            SELECT id FROM mark_sentence WHERE (corpus_id = #{corpusId} AND corpus_version = #{corpusVersion})
            )
            AND tagName = '属性'
            ORDER BY id ASC
            <include refid="MySqlPaginationSuffix" />
        </select>

    <select id="getEntityCount" resultType="integer" parameterType="com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination">
        SELECT
        count(1)
        FROM
        mark_entity
        WHERE
        sentenceId IN (
        SELECT id FROM mark_sentence WHERE (corpus_id = #{corpusId} AND corpus_version = #{corpusVersion})
        )
        AND tagName = '实体'
    </select>

    <select id="getEntityRelationCount" resultType="integer"
            parameterType="com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination">
        SELECT
        count(1)
        FROM
        mark_triplet mt
        LEFT JOIN mark_entity me ON me.id = mt.tag1Id
        LEFT JOIN mark_entity me1 ON me1.id = mt.tag2Id
        WHERE
        mt.sentenceId IN (
        SELECT id FROM mark_sentence WHERE (corpus_id = #{corpusId} AND corpus_version = #{corpusVersion})
        )
    </select>

    <select id="getEntityAttrCount" resultType="integer" parameterType="com.sinosoft.ie.booster.yearbook.mark.model.marksentence.MarkSentencePagination">
        SELECT
        count(1)
        FROM
        mark_entity
        WHERE
        sentenceId IN (
        SELECT id FROM mark_sentence WHERE (corpus_id = #{corpusId} AND corpus_version = #{corpusVersion})
        )
        AND tagName = '属性'
    </select>
</mapper>
