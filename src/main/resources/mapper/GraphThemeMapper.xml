<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.graph.mapper.GraphThemeMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.graph.entity.GraphThemeEntity">
                    <id column="id" property="id" />
                    <result column="theme" property="theme" />
                    <result column="persons" property="persons" />
                    <result column="team" property="team" />
                    <result column="achievements" property="achievements" />
                    <result column="articles" property="articles" />
                    <result column="prize" property="prize" />
                    <result column="project" property="project" />
                    <result column="funding" property="funding" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, theme, persons, team, achievements, articles, prize, project, funding
        </sql>
</mapper>
