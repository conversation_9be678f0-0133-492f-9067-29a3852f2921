<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.mark.mapper.FilecontentMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.mark.entity.FilecontentEntity">
                    <id column="id" property="id" />
                    <result column="title" property="title" />
                    <result column="content" property="content" />
                    <result column="content_exc" property="contentExc" />
                    <result column="type" property="type" />
                    <result column="oldname" property="oldname" />
                    <result column="newname" property="newname" />
                    <result column="status" property="status" />
                    
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, title, content, content_exc, type, oldname, newname,status
        </sql>
</mapper>
