<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinosoft.ie.booster.yearbook.business.mapper.ResearchTagMapper">
        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.sinosoft.ie.booster.yearbook.business.entity.ResearchTagEntity">
                    <id column="id" property="id" />
                    <result column="name" property="name" />
                    <result column="zonelevel" property="zonelevel" />
                    <result column="legend_type" property="legendType" />
                    <result column="organization" property="organization" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
                id, name, zonelevel, legend_type, organization
        </sql>
	<select id="selectLegendTypeStr" resultType="java.lang.String">
		SELECT DISTINCT(legend_type) FROM `research_tag`;
	</select>
    <select id="selectLegendTypeStrYear" resultType="java.lang.String" parameterType="string">
        SELECT DISTINCT(legend_type) FROM `research_tag`
        <if test="year != null and year != ''">
            where year = #{year}
        </if>
    </select>
    <select id="selectByLegendTypeNoYear" resultMap="BaseResultMap" parameterType="string">
        SELECT DISTINCT
            id,name,legend_type,SUM(zonelevel) AS zonelevel
        FROM
            `research_tag`
        WHERE
            legend_type = #{legendType}
        GROUP BY `name`
        ORDER BY zonelevel desc
        LIMIT ${offset},${upset}
    </select>
</mapper>
