spring.datasource.url = jdbc\:mysql\://42.192.148.85\:13306/booster_yearbooks?useUnicode\=true&characterEncoding\=utf-8&useSSL\=false&serverTimezone\=Asia/Shanghai
spring.datasource.username = root
spring.datasource.password = 123456
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.druid.initialSize=3

spring.neo4j.uri=bolt://booster-neo4j:7687
spring.neo4j.authentication.username=neo4j
spring.neo4j.authentication.password=root


server.servlet.context-path=/yearbook
upload_path=/sinosoft/uploadfile
down_path=/sinosoft/downfile
excel_imgpath = /sinosoft/uploadfile

corpuId = 1602150720744779778
corpuVer = 1
def_img_id = 1