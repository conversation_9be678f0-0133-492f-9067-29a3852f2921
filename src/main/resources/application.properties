spring.profiles.active=dm
#spring.profiles.include=es
server.port=8889
server.servlet.context-path=/yearbook
spring.servlet.multipart.max-file-size=1000MB
spring.servlet.multipart.max-request-size=1000MB
logging.config= classpath:logback-spring.xml

mybatis-plus.configuration.log-impl= org.apache.ibatis.logging.stdout.StdOutImpl
# MyBatis??????
mybatis-plus.configuration.default-statement-timeout=30
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.call-setters-on-nulls=true
# ??MyBatis????????
mybatis-plus.configuration.default-fetch-size=100
mybatis-plus.configuration.default-result-set-type=forward_only


extract_ip=*************:5000

thread-size=8
thread-max=20

#åç¹ç»å½
app-id=cpk.csse.sznj
client_secret=745d03ad46504b96ab4fa64499cd9676
sso-url=apigateway.jkxxzx.csse


# actuator çæ§éç½®
#management.server.port=8080
management.endpoints.web.exposure.include=*
management.endpoints.web.exposure.base-path=/actuator
management.endpoint.httptrace.enabled=true
management.endpoint.shutdown.enabled=true
management.endpoint.health.show-details=always

spring.resources.static-locations=file:D:\\sinosoft-project\\NProject\\project\\dist

