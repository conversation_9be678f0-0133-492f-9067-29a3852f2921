#spring.datasource.url=jdbc:dm://**********:5236?logLevel=all&logDir=D:\\DBA\\dm8\\jdbclog
#spring.datasource.username = DXYY-JSDJ
#spring.datasource.password = password01
spring.datasource.url = **********************************************************************************************************************
spring.datasource.username = root
spring.datasource.password = root
spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
#spring.datasource.driver-class-name = dm.jdbc.driver.DmDriver
spring.datasource.druid.initialSize=3

# Database charset configuration for Druid
spring.datasource.druid.connection-init-sqls=SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
spring.datasource.druid.init-connection-sqls=SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci


thread-size=8
thread-max=20

upload_path=D:\\data\\upload
down_path=D:\\data\\down

excel_imgpath = D:\\data\\upload


corpuId = 1602150720744779778
corpuVer = 1
def_img_id = 1

pdf_in_path=D:\\data
pdf_out_paht=D:\\data


#æ¯å¦å¯ç¨ee
easy-es.enable=true
#eså°å
#easy-es.address=***************:9200
easy-es.address=**********:9200
easy-es.username=elastic
easy-es.password=123456
easy-es.global-config.process-index-mode=manual
#æ¯å¦æ¯åå¸å¼
easy-es.global-config.distributed=false
#æ¯å¦æå°dsl
easy-es.global-config.print-dsl=true

