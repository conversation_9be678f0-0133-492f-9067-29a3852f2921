2025-07-08 09:09:25,916 [] [background-preinit]  INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-08 09:09:25,916 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 15852 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-08 09:09:25,928 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-08 09:09:26,079 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-08 09:09:26,079 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-08 09:09:29,533 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-08 09:09:29,549 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-08 09:09:29,550 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-08 09:09:29,551 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-08 09:09:29,748 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-08 09:09:29,749 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 3668 ms
2025-07-08 09:09:31,346 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 09:09:31,347 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 09:09:34,183 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-08 09:09:34,479 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-08 09:09:34,480 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-08 09:09:34,480 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-08 09:09:34,620 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 09:09:34,624 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 09:09:36,240 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-08 09:09:36,242 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-08 09:09:36,268 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 09:09:36,486 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-08 09:09:36,790 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-08 09:09:37,012 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8889"]
2025-07-08 09:09:37,034 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-08 09:09:37,036 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-08 09:09:37,040 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-08 09:09:37,255 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-08 09:09:37,546 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-08 09:09:37,579 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-08 09:09:37,589 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-08 09:09:37,596 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-08 09:09:37,598 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-08 09:09:37,606 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-08 09:09:37,624 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-08 09:09:37,633 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-08 09:09:37,640 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-08 09:09:37,642 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-08 09:09:37,646 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-08 09:09:37,658 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-08 09:09:37,664 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-08 09:09:37,667 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-08 09:09:37,670 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-08 09:09:37,675 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-08 09:09:37,683 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-08 09:09:37,686 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-08 09:09:37,694 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-08 09:09:37,700 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-08 09:09:37,706 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-08 09:09:37,736 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-08 09:09:37,744 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-08 09:09:37,753 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-08 09:09:37,756 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-08 09:09:37,764 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-08 09:09:37,814 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-08 09:09:37,823 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-08 09:09:37,829 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-08 09:09:37,835 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-08 09:09:37,843 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-08 09:09:37,858 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-08 09:09:37,863 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-08 09:09:37,867 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-08 09:09:37,869 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-08 09:09:37,874 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-08 09:09:37,894 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-08 09:09:37,902 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-08 09:09:37,906 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-08 09:09:37,908 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-08 09:09:37,913 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-08 09:09:37,925 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-08 09:09:37,929 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-08 09:09:37,932 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-08 09:09:37,933 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-08 09:09:37,937 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-08 09:09:37,941 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-08 09:09:37,943 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-08 09:09:37,951 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-08 09:09:37,955 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-08 09:09:37,961 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-08 09:09:37,965 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-08 09:09:37,966 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-08 09:09:37,974 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-08 09:09:37,977 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-08 09:09:37,980 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-08 09:09:37,985 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-08 09:09:38,009 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-08 09:09:38,015 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-08 09:09:38,020 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-08 09:09:38,022 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-08 09:09:38,028 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-08 09:09:38,082 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 13.117 seconds (JVM running for 17.203)
2025-07-08 09:09:38,305 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-1 - Starting...
2025-07-08 09:09:38,671 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-1 - Start completed.
2025-07-08 09:09:54,750 [] [http-nio-8889-exec-5]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 09:09:54,751 [] [http-nio-8889-exec-5]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:525 - Initializing Servlet 'dispatcherServlet'
2025-07-08 09:09:54,755 [] [http-nio-8889-exec-5]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:547 - Completed initialization in 3 ms
2025-07-08 09:09:54,951 [144ce16e-db06-4c6a-85b7-b0b5d86cbb85] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:12:33,254 [1d3f1d98-65d8-452a-b7eb-33c25aac8758] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 35476600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
035476600  100%  
 ===> 0.035
2025-07-08 09:12:33,255 [ad0de1e8-c4c0-4947-ae97-6169df07b036] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 36311800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
036311800  100%  
 ===> 0.036
2025-07-08 09:26:22,444 [fa97e045-10e2-4b7d-b2cc-b48f5bba5c3b] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 7852100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
007852100  100%  
 ===> 0.007
2025-07-08 09:26:23,801 [7d9ba45e-9eff-4250-bac8-0146181d92ce] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3388100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003388100  100%  
 ===> 0.003
2025-07-08 09:26:31,313 [21a055f3-f4c1-4095-9225-a9dbaf487443] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:26:44,717 [199d2e84-e7fc-4dba-9c7f-9b8fb6d8f6aa] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4007800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004007800  100%  
 ===> 0.004
2025-07-08 09:26:46,073 [f4fffbd9-8f29-44ad-ad5c-03d1759858f3] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3841400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003841400  100%  
 ===> 0.003
2025-07-08 09:26:47,742 [f3c5d687-37a1-4e46-b758-11a1a4a9e54e] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4609400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004609400  100%  
 ===> 0.004
2025-07-08 09:26:48,002 [b2fa6027-d866-42d2-b902-479165923047] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3384600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003384600  100%  
 ===> 0.003
2025-07-08 09:26:49,066 [40e48831-1626-4983-8256-525e8ca5f542] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:26:55,012 [771a0eb0-c591-4ec2-97ff-8556c7ec8cc3] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.service.impl.UploadfileServiceImpl] UploadfileServiceImpl.java:87 - id====1
2025-07-08 09:26:56,949 [b9bef3c6-6a33-4bc1-a0d4-fcef5864f2ad] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:27:09,251 [9daea0b7-7913-4438-b729-75a1153be3b0] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3474100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003474100  100%  
 ===> 0.003
2025-07-08 09:27:09,522 [a8d1a955-c4cc-49dd-99de-1517597528d8] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 10840400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
010840400  100%  
 ===> 0.01
2025-07-08 09:27:14,261 [a176569e-56a9-4b7e-a99f-4a06e2ad48f6] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:27:21,087 [11ce40be-e49a-4c7d-85f9-5dcf3485daea] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:27:45,692 [f8b33d14-cf05-4e5b-8b8a-a826dfdf6190] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/46b7430ca96c428bb2bd38eb543546c8.pdf
2025-07-08 09:27:45,693 [f8b33d14-cf05-4e5b-8b8a-a826dfdf6190] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/259cc214cc4b42f7b1366a5a26faaea2_1_25.pdf D:\data\upload\/upload/pdf/46b7430ca96c428bb2bd38eb543546c8.pdf 2025年度科技创新大会 null_tag
2025-07-08 09:28:23,294 [5095f79c-41d8-4cd3-a367-ad7013f33948] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5061200 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005061200  100%  
 ===> 0.005
2025-07-08 09:28:23,538 [a0b2acce-5ece-4f65-8067-24219e93ab83] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4810600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004810600  100%  
 ===> 0.004
2025-07-08 09:28:28,799 [2d0e1af1-5db0-4d54-906b-28ba4ae9e2b8] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/1d90f39c56be41f5bf36b41458470c66.pdf
2025-07-08 09:28:28,800 [2d0e1af1-5db0-4d54-906b-28ba4ae9e2b8] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/1d90f39c56be41f5bf36b41458470c66.pdf 数字孪生城市试点 null_tag
2025-07-08 09:28:35,073 [1eaec300-9e8d-4b39-b3b8-996c377ee277] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:29:08,749 [1573ab5a-95a2-4d74-ad9b-3def3635ab00] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 9158700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
009158700  100%  
 ===> 0.009
2025-07-08 09:29:09,007 [611599e8-2c1b-4641-a321-4b955586524d] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5941900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005941900  100%  
 ===> 0.005
2025-07-08 09:29:19,804 [d159b4bc-13ca-49b2-9fb6-76ea88fa0599] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/da13e4d8ceaa492383be206f994a15ac.pdf
2025-07-08 09:29:19,804 [d159b4bc-13ca-49b2-9fb6-76ea88fa0599] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/da13e4d8ceaa492383be206f994a15ac.pdf 数字孪生城市试点 null_tag
2025-07-08 09:29:25,740 [4f02b2e0-3499-42ac-828e-1f4f2f368207] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5162100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005162100  100%  
 ===> 0.005
2025-07-08 09:29:27,122 [996caa65-a94a-4b5e-93a7-18c847318fef] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3960700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003960700  100%  
 ===> 0.003
2025-07-08 09:29:32,175 [ff26bc13-ee4f-48b0-8c51-a9251ee443d1] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3862200 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003862200  100%  
 ===> 0.003
2025-07-08 09:29:36,362 [19b9522d-fb7d-4a48-a580-9377839f5898] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/54197c38e75d41c1b4af9e81e4b016e3.pdf
2025-07-08 09:29:36,363 [19b9522d-fb7d-4a48-a580-9377839f5898] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/54197c38e75d41c1b4af9e81e4b016e3.pdf 数字孪生城市试点 null_tag
2025-07-08 09:30:28,009 [0e469cc7-a673-48de-bc35-86fd46b7a8da] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/8da9b14ac43c415eab317da15a27d300.pdf
2025-07-08 09:30:28,010 [0e469cc7-a673-48de-bc35-86fd46b7a8da] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/8da9b14ac43c415eab317da15a27d300.pdf 数字孪生城市试点 null_tag
2025-07-08 09:30:34,606 [109a17c8-47f5-4747-a72d-00a2357bb726] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3519500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003519500  100%  
 ===> 0.003
2025-07-08 09:30:40,606 [7e00c94b-5a8a-4de1-9c4c-5a2c644202c4] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3993500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003993500  100%  
 ===> 0.003
2025-07-08 09:30:46,908 [e777bb48-5663-4d31-ad2b-2f11d248ba00] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3392400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003392400  100%  
 ===> 0.003
2025-07-08 09:30:52,597 [ab0350c5-e43b-4be4-92c1-f67b3ac52005] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3225100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003225100  100%  
 ===> 0.003
2025-07-08 09:30:58,908 [a8450f2f-3c72-4146-bdc6-16c86b088e6c] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2948300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002948300  100%  
 ===> 0.002
2025-07-08 09:31:00,797 [2907dfc8-7753-47e3-92ef-623026849418] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4203900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004203900  100%  
 ===> 0.004
2025-07-08 09:31:02,684 [0fb34598-c5e8-40d7-b131-b6bd80f51a82] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4506500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004506500  100%  
 ===> 0.004
2025-07-08 09:31:04,375 [bde31230-72c7-4ea0-88ce-55a6ef0c2a16] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4498100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004498100  100%  
 ===> 0.004
2025-07-08 09:31:04,508 [98f1a28d-131e-42a0-95c3-4cbb46edcfa7] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4518500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004518500  100%  
 ===> 0.004
2025-07-08 09:31:24,988 [bd8f8b7e-673a-4f52-a302-d95214c2c835] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2507700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002507700  100%  
 ===> 0.002
2025-07-08 09:31:26,267 [bc9e0594-6e07-4d1b-9419-b3a12a20300b] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2264900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002264900  100%  
 ===> 0.002
2025-07-08 09:31:37,563 [dad7d79e-fd11-4af8-b423-3970a10e4e5d] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5340500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005340500  100%  
 ===> 0.005
2025-07-08 09:31:37,815 [2f00a960-6d99-4ede-b03a-40b7618e2cbd] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4528600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004528600  100%  
 ===> 0.004
2025-07-08 09:31:43,589 [2fa6466c-4632-4019-b10b-568e5b6f5c64] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2678600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002678600  100%  
 ===> 0.002
2025-07-08 09:31:44,050 [603f58d6-18db-4273-afd5-7a63ff520913] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2384300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002384300  100%  
 ===> 0.002
2025-07-08 09:31:47,833 [d33377b6-bc42-4446-be3f-59cba5d3ab46] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3964700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003964700  100%  
 ===> 0.003
2025-07-08 09:31:49,174 [8c7f87d5-bacd-41a9-bef0-e88379076ba1] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2429600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002429600  100%  
 ===> 0.002
2025-07-08 09:31:52,514 [9d9777f7-2f86-4467-9e2e-5edddcd5c0e3] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2882100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002882100  100%  
 ===> 0.002
2025-07-08 09:31:52,895 [de7d8a07-fb6a-4f7c-9a0f-a9ccc31c2133] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2715100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002715100  100%  
 ===> 0.002
2025-07-08 09:32:02,394 [6f6ae263-4e66-4053-bc9e-965a90acdf26] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2863800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002863800  100%  
 ===> 0.002
2025-07-08 09:32:09,648 [224852ad-8bd9-4728-8566-d93b5af9d36d] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2240400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002240400  100%  
 ===> 0.002
2025-07-08 09:32:15,005 [17930adf-4315-49bb-9878-317dda52e09a] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2856400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002856400  100%  
 ===> 0.002
2025-07-08 09:32:20,584 [a3455e3c-a507-4770-b108-1cd9bb0cd7d0] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2383000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002383000  100%  
 ===> 0.002
2025-07-08 09:32:25,402 [f61cb3e9-1173-4a2c-b824-32c0d00a3c1b] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2877900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002877900  100%  
 ===> 0.002
2025-07-08 09:32:25,609 [5c6984dc-faee-457d-bd3f-c3518f2a28eb] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2453800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002453800  100%  
 ===> 0.002
2025-07-08 09:32:29,634 [ee475ed6-f86c-468e-b899-1970280f32ed] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2826900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002826900  100%  
 ===> 0.002
2025-07-08 09:32:29,918 [8f42596d-937f-449b-a9a8-23ac1d2177c8] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3080900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003080900  100%  
 ===> 0.003
2025-07-08 09:32:35,416 [5afe154a-e740-4382-ac4c-0cf96e902f19] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3467300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003467300  100%  
 ===> 0.003
2025-07-08 09:32:35,771 [3130eafc-c0df-4245-89d3-de4c1796126c] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2601800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002601800  100%  
 ===> 0.002
2025-07-08 09:32:41,602 [5a1666eb-9bdf-460d-b9da-6e8500e8dcdf] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2371900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002371900  100%  
 ===> 0.002
2025-07-08 09:32:47,897 [a4612dc5-236b-4e4e-be5f-261e0a90c285] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2664700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002664700  100%  
 ===> 0.002
2025-07-08 09:32:51,371 [16b4b02e-aa27-4897-847b-e28d18b0e29c] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2482500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002482500  100%  
 ===> 0.002
2025-07-08 09:32:51,628 [44f461be-1718-431a-a2e4-165d91279cc4] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3775600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003775600  100%  
 ===> 0.003
2025-07-08 09:32:57,171 [c61afe76-eb02-43d1-bb3d-039f3cfb4185] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2288400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002288400  100%  
 ===> 0.002
2025-07-08 09:32:57,788 [4933dbf7-1dc9-4210-9c83-2b3a65b61b85] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2622900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002622900  100%  
 ===> 0.002
2025-07-08 09:33:03,587 [704d845e-787f-4ba8-803e-948e215b28fe] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2296800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002296800  100%  
 ===> 0.002
2025-07-08 09:33:04,219 [ebf31c93-692f-4401-ae16-bf244a34b75b] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2721300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002721300  100%  
 ===> 0.002
2025-07-08 09:33:08,886 [eedb12f1-f91a-43c3-abbb-bccd0e1a4283] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2673900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002673900  100%  
 ===> 0.002
2025-07-08 09:33:09,011 [3188ae88-2883-4b9e-ab59-d24340f9b3ee] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2610000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002610000  100%  
 ===> 0.002
2025-07-08 09:33:19,089 [0b01dec2-bace-486c-befb-97fc2de30057] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2726600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002726600  100%  
 ===> 0.002
2025-07-08 09:33:19,738 [1febcf93-f0c5-4e93-ba2b-a25828159525] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2791100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002791100  100%  
 ===> 0.002
2025-07-08 09:33:34,484 [b069d6f6-d00d-4c5c-951f-7ffe0bc14a97] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2827000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002827000  100%  
 ===> 0.002
2025-07-08 09:33:36,149 [5f463644-aa37-4ac3-a7f2-0ff2ae77176a] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2537500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002537500  100%  
 ===> 0.002
2025-07-08 09:33:39,603 [6fd7a1eb-368d-4911-bc9e-2d3d26edb99f] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/a8079b6d5ebb412c9ca45034086772ee.pdf
2025-07-08 09:33:39,603 [6fd7a1eb-368d-4911-bc9e-2d3d26edb99f] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/a8079b6d5ebb412c9ca45034086772ee.pdf 数字孪生城市试点 null_tag
2025-07-08 09:35:51,096 [7f6713dd-ed9d-4951-801a-bdfb70e72c45] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/913ac3287a97451bbd1722f73df3d928.pdf
2025-07-08 09:35:51,096 [7f6713dd-ed9d-4951-801a-bdfb70e72c45] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/913ac3287a97451bbd1722f73df3d928.pdf 数字孪生城市试点 null_tag
2025-07-08 09:38:25,542 [d4082ec3-9c5c-4d7e-851d-b06c88be12c3] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/ef208da52c82472391c5c211c8f65e6c.pdf
2025-07-08 09:38:25,543 [d4082ec3-9c5c-4d7e-851d-b06c88be12c3] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/ef208da52c82472391c5c211c8f65e6c.pdf 数字孪生城市试点 null_tag
2025-07-08 09:40:13,601 [77658785-92c6-43d4-bc9f-c3ffb6d6b83e] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 09:40:20,203 [a378d876-8372-4b7f-8d80-c05959709ac8] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4989000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004989000  100%  
 ===> 0.004
2025-07-08 09:40:21,613 [c9091a25-7389-4314-8f9c-ab5ef975c384] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 6988400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
006988400  100%  
 ===> 0.006
2025-07-08 09:40:24,011 [1667c447-2ad8-4364-9eb8-afdc62c81772] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/f0bb19abdbca4804be8bd0a34d8ff70c.pdf
2025-07-08 09:40:24,012 [1667c447-2ad8-4364-9eb8-afdc62c81772] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/f0bb19abdbca4804be8bd0a34d8ff70c.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:40:28,800 [9104c3ab-1821-48cd-9839-0d851fe73eed] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/dcdf305ba4d249ee90baa4566303313e.pdf
2025-07-08 09:40:28,800 [9104c3ab-1821-48cd-9839-0d851fe73eed] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/dcdf305ba4d249ee90baa4566303313e.pdf 数据中心绿色节能改造 数据中心,节能,改造
2025-07-08 09:40:29,790 [5ba7d570-2b3f-477e-83a6-15c1a461aab7] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/0740bc0bd50b4e1aa57b5573ce41e73e.pdf
2025-07-08 09:40:29,790 [5ba7d570-2b3f-477e-83a6-15c1a461aab7] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/0740bc0bd50b4e1aa57b5573ce41e73e.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:40:30,684 [da314c94-e851-4d0b-ac8f-3f491c870fb1] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/d3bc5e8321a441fc96fb5127c15082a8.pdf
2025-07-08 09:40:30,686 [da314c94-e851-4d0b-ac8f-3f491c870fb1] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/d3bc5e8321a441fc96fb5127c15082a8.pdf 数据中心绿色节能改造 数据中心,节能,改造
2025-07-08 09:40:31,654 [3cb7e6d3-ac0f-44cd-9252-8f512f100e59] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/237ab73745a74aecb563d27b9a013981.pdf
2025-07-08 09:40:31,656 [3cb7e6d3-ac0f-44cd-9252-8f512f100e59] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/237ab73745a74aecb563d27b9a013981.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:41:29,437 [8a8f3b3e-51a8-4575-9b2a-b0ba86579404] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 6658700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
006658700  100%  
 ===> 0.006
2025-07-08 09:41:29,577 [ce545521-910e-4848-b1c1-0b7ea4329d8e] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5558800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005558800  100%  
 ===> 0.005
2025-07-08 09:41:35,212 [d4a5826a-d167-4738-997d-64deb1f96ae1] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/d725749ed95743c6a8556a9ddc13383c.pdf
2025-07-08 09:41:35,212 [d4a5826a-d167-4738-997d-64deb1f96ae1] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/d725749ed95743c6a8556a9ddc13383c.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:41:49,756 [42d35a74-1fea-4035-8cb7-36889ed613a8] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/7b7bc84a77984c91934977d6a3de7956.pdf
2025-07-08 09:41:49,757 [42d35a74-1fea-4035-8cb7-36889ed613a8] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/7b7bc84a77984c91934977d6a3de7956.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:42:08,457 [1aa7bf9e-7aad-4ab0-ba82-771a36eeedd0] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 6303100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
006303100  100%  
 ===> 0.006
2025-07-08 09:42:08,599 [31b78dde-dd0f-4e0e-9b88-cea510bbf9e5] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5376400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005376400  100%  
 ===> 0.005
2025-07-08 09:42:13,970 [a75d4a6a-b942-4f98-8906-0b5111fcc45a] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5019000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005019000  100%  
 ===> 0.005
2025-07-08 09:42:19,019 [33f192ce-f844-42a6-af6e-50fd04298582] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4694100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004694100  100%  
 ===> 0.004
2025-07-08 09:42:24,367 [cd8287e0-48d6-4fbd-86c0-6bdaffff8c58] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4791700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004791700  100%  
 ===> 0.004
2025-07-08 09:42:26,660 [bc035b43-f484-4073-a742-c0b165b9f744] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/dd67501b26ba4ea090fe465b7c887ab8.pdf
2025-07-08 09:42:26,661 [bc035b43-f484-4073-a742-c0b165b9f744] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/259cc214cc4b42f7b1366a5a26faaea2_1_25.pdf D:\data\upload\/upload/pdf/dd67501b26ba4ea090fe465b7c887ab8.pdf 2025年度科技创新大会 null_tag
2025-07-08 09:45:22,703 [0d9dc63c-782e-42b6-9203-8fce5dbcdc04] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/4a9f6561d13d425591849894b6318e3f.pdf
2025-07-08 09:45:22,703 [0d9dc63c-782e-42b6-9203-8fce5dbcdc04] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/259cc214cc4b42f7b1366a5a26faaea2_1_25.pdf D:\data\upload\/upload/pdf/4a9f6561d13d425591849894b6318e3f.pdf 2025年度科技创新大会 null_tag
2025-07-08 09:45:50,665 [d16e1aec-2f6f-4fc1-beb1-a80d3bbe176f] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/d3d41faacc2943e5b26488e16a14ed72.pdf
2025-07-08 09:45:50,666 [d16e1aec-2f6f-4fc1-beb1-a80d3bbe176f] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/259cc214cc4b42f7b1366a5a26faaea2_1_25.pdf D:\data\upload\/upload/pdf/d3d41faacc2943e5b26488e16a14ed72.pdf 2025年度科技创新大会 null_tag
2025-07-08 09:49:20,846 [f8885c45-aa87-4810-bfd8-3d9d7af9c2e8] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 6530000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
006530000  100%  
 ===> 0.006
2025-07-08 09:49:21,188 [5c3bb6c4-6959-4d06-a2b6-ec8708344fb6] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 6374000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
006374000  100%  
 ===> 0.006
2025-07-08 09:49:26,122 [6969d322-c0a3-4f82-b62f-c8ae68729347] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5373200 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005373200  100%  
 ===> 0.005
2025-07-08 09:49:27,642 [239a4c34-c7fe-46c9-9929-8ffa353b7f0d] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 7782400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
007782400  100%  
 ===> 0.007
2025-07-08 09:53:54,241 [28d9b10f-876d-4f92-8eb5-1ec0b51f3442] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2822200 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002822200  100%  
 ===> 0.002
2025-07-08 09:53:55,555 [4f78ebfc-d4ea-40e8-882e-e4c6a2249a38] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3996900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003996900  100%  
 ===> 0.003
2025-07-08 09:53:57,135 [5cabee49-7711-45e5-9ed9-058f2777517a] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/6ccaeb02951347ec82153ead26048b11.pdf
2025-07-08 09:53:57,135 [5cabee49-7711-45e5-9ed9-058f2777517a] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/6ccaeb02951347ec82153ead26048b11.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:54:01,542 [a34e5160-acc1-4153-892e-f92704dae6e8] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2075400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002075400  100%  
 ===> 0.002
2025-07-08 09:57:43,586 [fea3bf17-127b-4846-a7e0-94ba307b8cb1] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3695700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003695700  100%  
 ===> 0.003
2025-07-08 09:57:43,696 [5967b912-feea-497f-80b0-160c88b3169e] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2678400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002678400  100%  
 ===> 0.002
2025-07-08 09:57:46,535 [b520f8c2-fca8-436c-b067-f113a0c7659c] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/cc8466198be54ff7af8a990200f3ed35.pdf
2025-07-08 09:57:46,535 [b520f8c2-fca8-436c-b067-f113a0c7659c] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/cc8466198be54ff7af8a990200f3ed35.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:59:44,156 [ce93fc5a-b004-408a-9923-82c8f3091eff] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/2911279d0a9c41a8b75bafc172bf3918.pdf
2025-07-08 09:59:44,156 [ce93fc5a-b004-408a-9923-82c8f3091eff] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/2911279d0a9c41a8b75bafc172bf3918.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:59:45,653 [442059ba-9550-47dd-9256-84b4865930ad] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/fd18cb4727cb4a07b71219948f4fc96c.pdf
2025-07-08 09:59:45,653 [442059ba-9550-47dd-9256-84b4865930ad] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/fd18cb4727cb4a07b71219948f4fc96c.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:59:53,785 [77f85905-ad2e-4fc0-84d1-8ace34a7c9e8] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/d6a24fc945e44b358b5784c011d12016.pdf
2025-07-08 09:59:53,786 [77f85905-ad2e-4fc0-84d1-8ace34a7c9e8] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/d6a24fc945e44b358b5784c011d12016.pdf 数据中心绿色节能改造 null_tag
2025-07-08 09:59:57,562 [a298bb6e-4361-45d0-8f50-f15fc6d31f90] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/c361bdf7e1ce406a956dec51d5de2e06.pdf
2025-07-08 09:59:57,562 [a298bb6e-4361-45d0-8f50-f15fc6d31f90] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/c361bdf7e1ce406a956dec51d5de2e06.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:00:02,097 [665f9f28-ae71-467c-bc91-a99d56b69717] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/6cc13de7d00c4b32a86dc0e15ae787f6.pdf
2025-07-08 10:00:02,098 [665f9f28-ae71-467c-bc91-a99d56b69717] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/6cc13de7d00c4b32a86dc0e15ae787f6.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:00:05,915 [fc15886b-2f2f-443d-be79-0df830b17f38] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/41e9e6130f0b4859ac490439195aaaa5.pdf
2025-07-08 10:00:05,915 [fc15886b-2f2f-443d-be79-0df830b17f38] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/41e9e6130f0b4859ac490439195aaaa5.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:00:37,866 [7b074f0e-7fab-4503-8be8-cb2cbe404908] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/aa5445f5c7f74f68ac56521f4d3e0a62.pdf
2025-07-08 10:00:37,866 [7b074f0e-7fab-4503-8be8-cb2cbe404908] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/aa5445f5c7f74f68ac56521f4d3e0a62.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:00:43,559 [463dfc31-9e7f-4e69-99a8-27f6dbcf0ce4] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/1bfe956a2a9a4a219547d9a44543e684.pdf
2025-07-08 10:00:43,559 [463dfc31-9e7f-4e69-99a8-27f6dbcf0ce4] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/1bfe956a2a9a4a219547d9a44543e684.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:00:49,281 [e67f7916-553c-4311-9abf-33f24b15a126] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/7cd92d0c01d34a58bb81390c505a4dfd.pdf
2025-07-08 10:00:49,282 [e67f7916-553c-4311-9abf-33f24b15a126] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/7cd92d0c01d34a58bb81390c505a4dfd.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:01:04,877 [b20bd7a2-017a-454f-8f1a-bf6daf5d1e0a] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2331700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002331700  100%  
 ===> 0.002
2025-07-08 10:01:06,245 [fc0f35d0-25e2-4119-8be4-cc722b4d2ecd] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3311100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003311100  100%  
 ===> 0.003
2025-07-08 10:01:11,258 [0bca8b36-46af-49f9-b12c-51dfb171f152] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/61d3121ed07e42c58000a316c3858f91.pdf
2025-07-08 10:01:11,259 [0bca8b36-46af-49f9-b12c-51dfb171f152] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/61d3121ed07e42c58000a316c3858f91.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:10:35,132 [388dd528-34ab-44cc-9362-e368d95ce248] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/1179604df8cc4ee2a7490558ae0f17cb.pdf
2025-07-08 10:10:35,134 [388dd528-34ab-44cc-9362-e368d95ce248] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/1179604df8cc4ee2a7490558ae0f17cb.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:10:37,664 [39073970-66fb-4e6c-963f-34067173d699] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 12123100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
012123100  100%  
 ===> 0.012
2025-07-08 10:10:38,011 [cd157098-21fa-42fa-9f31-c15be95fbcc0] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 9074000 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
009074000  100%  
 ===> 0.009
2025-07-08 10:10:41,626 [530c38e4-0393-4265-98c4-53312b6c85bb] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 9733300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
009733300  100%  
 ===> 0.009
2025-07-08 10:10:41,953 [b331664e-8172-4e2d-a103-440a52731863] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 8483400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
008483400  100%  
 ===> 0.008
2025-07-08 10:10:45,095 [] [Thread-27]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskScheduler'
2025-07-08 10:10:45,106 [] [Thread-27]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskExecutor'
2025-07-08 10:10:45,127 [] [Thread-27]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:350 - HikariPool-1 - Shutdown initiated...
2025-07-08 10:10:45,136 [] [Thread-27]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:352 - HikariPool-1 - Shutdown completed.
2025-07-08 10:10:45,971 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 15852 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-08 10:10:45,971 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-08 10:10:47,643 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-08 10:10:47,643 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-08 10:10:47,644 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-08 10:10:47,644 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-08 10:10:47,725 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-08 10:10:47,726 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 1746 ms
2025-07-08 10:10:48,788 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:10:48,789 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:10:51,618 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-08 10:10:51,726 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-08 10:10:51,727 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-08 10:10:51,728 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-08 10:10:51,765 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:10:51,775 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:10:53,290 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-08 10:10:53,290 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-08 10:10:53,304 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 10:10:53,407 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-08 10:10:53,514 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-08 10:10:53,583 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8889"]
2025-07-08 10:10:53,586 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-08 10:10:53,588 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-08 10:10:53,590 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-08 10:10:53,692 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-08 10:10:53,733 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-08 10:10:53,748 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-08 10:10:53,753 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-08 10:10:53,756 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-08 10:10:53,757 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-08 10:10:53,760 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-08 10:10:53,770 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-08 10:10:53,773 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-08 10:10:53,777 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-08 10:10:53,779 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-08 10:10:53,781 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-08 10:10:53,790 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-08 10:10:53,794 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-08 10:10:53,796 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-08 10:10:53,797 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-08 10:10:53,798 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-08 10:10:53,803 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-08 10:10:53,806 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-08 10:10:53,812 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-08 10:10:53,815 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-08 10:10:53,820 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-08 10:10:53,836 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-08 10:10:53,840 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-08 10:10:53,845 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-08 10:10:53,847 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-08 10:10:53,851 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-08 10:10:53,877 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-08 10:10:53,882 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-08 10:10:53,888 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-08 10:10:53,891 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-08 10:10:53,896 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-08 10:10:53,907 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-08 10:10:53,910 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-08 10:10:53,914 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-08 10:10:53,915 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-08 10:10:53,917 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-08 10:10:53,958 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-08 10:10:53,964 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-08 10:10:53,967 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-08 10:10:53,969 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-08 10:10:53,973 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-08 10:10:53,984 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-08 10:10:53,987 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-08 10:10:53,990 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-08 10:10:53,990 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-08 10:10:53,993 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-08 10:10:53,997 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-08 10:10:53,998 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-08 10:10:54,005 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-08 10:10:54,009 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-08 10:10:54,013 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-08 10:10:54,017 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-08 10:10:54,018 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-08 10:10:54,024 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-08 10:10:54,027 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-08 10:10:54,029 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-08 10:10:54,032 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-08 10:10:54,038 [] [http-nio-8889-exec-1]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 10:10:54,040 [] [http-nio-8889-exec-1]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:525 - Initializing Servlet 'dispatcherServlet'
2025-07-08 10:10:54,044 [] [http-nio-8889-exec-1]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:547 - Completed initialization in 4 ms
2025-07-08 10:10:54,057 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-08 10:10:54,063 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-08 10:10:54,067 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-08 10:10:54,069 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-08 10:10:54,073 [2bedb811-1eda-4b6d-8cae-f973444dca3c] [http-nio-8889-exec-1]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-2 - Starting...
2025-07-08 10:10:54,075 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-08 10:10:54,083 [2bedb811-1eda-4b6d-8cae-f973444dca3c] [http-nio-8889-exec-1]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-2 - Start completed.
2025-07-08 10:10:54,087 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 8.209 seconds (JVM running for 3691.723)
2025-07-08 10:10:54,147 [] [restartedMain]  INFO  [o.s.b.d.a.ConditionEvaluationDeltaLoggingListener] ConditionEvaluationDeltaLoggingListener.java:63 - Condition evaluation unchanged
2025-07-08 10:10:55,966 [374f3c20-6830-4dc1-9d8f-8b81d9946bb4] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.HomePagePersonController] HomePagePersonController.java:38 - 首页获取人物接口查询时间为===>StopWatch '': running time = 0 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
 ===> 0.0
2025-07-08 10:10:57,359 [d2b37e34-9df7-4103-a4f7-ab3c5f05ff3b] [http-nio-8889-exec-4]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 26526600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
026526600  100%  
 ===> 0.026
2025-07-08 10:10:58,769 [4ad5812c-090b-4e9b-9dd2-86035864d4d6] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3973600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003973600  100%  
 ===> 0.003
2025-07-08 10:12:04,669 [] [Thread-57]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskScheduler'
2025-07-08 10:12:04,672 [] [Thread-57]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskExecutor'
2025-07-08 10:12:04,683 [] [Thread-57]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:350 - HikariPool-2 - Shutdown initiated...
2025-07-08 10:12:04,690 [] [Thread-57]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:352 - HikariPool-2 - Shutdown completed.
2025-07-08 10:12:05,295 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 15852 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-08 10:12:05,296 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-08 10:12:09,405 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-08 10:12:09,407 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-08 10:12:09,408 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-08 10:12:09,409 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-08 10:12:09,660 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-08 10:12:09,660 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 4351 ms
2025-07-08 10:12:12,420 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:12:12,424 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:12:16,712 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-08 10:12:16,766 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-08 10:12:16,767 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-08 10:12:16,769 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-08 10:12:16,895 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:12:16,935 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:12:20,411 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-08 10:12:20,412 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-08 10:12:20,434 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 10:12:20,608 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-08 10:12:20,915 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-08 10:12:21,286 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-08 10:12:21,289 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-08 10:12:21,291 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-08 10:12:21,556 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-08 10:12:21,687 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-08 10:12:21,726 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-08 10:12:21,740 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-08 10:12:21,750 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-08 10:12:21,752 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-08 10:12:21,762 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-08 10:12:21,815 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-08 10:12:21,822 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-08 10:12:21,829 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-08 10:12:21,831 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-08 10:12:21,837 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-08 10:12:21,854 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-08 10:12:21,862 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-08 10:12:21,868 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-08 10:12:21,872 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-08 10:12:21,879 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-08 10:12:21,891 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-08 10:12:21,893 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-08 10:12:21,909 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-08 10:12:21,917 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-08 10:12:21,925 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-08 10:12:21,970 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-08 10:12:21,992 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-08 10:12:22,009 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-08 10:12:22,033 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-08 10:12:22,041 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-08 10:12:22,096 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-08 10:12:22,106 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-08 10:12:22,115 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-08 10:12:22,122 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-08 10:12:22,132 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-08 10:12:22,152 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-08 10:12:22,156 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-08 10:12:22,163 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-08 10:12:22,165 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-08 10:12:22,170 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-08 10:12:22,202 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-08 10:12:22,224 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-08 10:12:22,243 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-08 10:12:22,245 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-08 10:12:22,254 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-08 10:12:22,277 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-08 10:12:22,286 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-08 10:12:22,296 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-08 10:12:22,301 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-08 10:12:22,310 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-08 10:12:22,322 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-08 10:12:22,326 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-08 10:12:22,338 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-08 10:12:22,346 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-08 10:12:22,356 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-08 10:12:22,368 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-08 10:12:22,371 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-08 10:12:22,403 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-08 10:12:22,412 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-08 10:12:22,442 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-08 10:12:22,448 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-08 10:12:22,505 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-08 10:12:22,517 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-08 10:12:22,531 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-08 10:12:22,537 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-08 10:12:22,556 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-08 10:12:22,597 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 17.451 seconds (JVM running for 3780.233)
2025-07-08 10:12:22,618 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-3 - Starting...
2025-07-08 10:12:22,637 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-3 - Start completed.
2025-07-08 10:12:22,726 [] [restartedMain]  INFO  [o.s.b.d.a.ConditionEvaluationDeltaLoggingListener] ConditionEvaluationDeltaLoggingListener.java:63 - Condition evaluation unchanged
2025-07-08 10:12:24,164 [] [SpringContextShutdownHook]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskScheduler'
2025-07-08 10:12:24,166 [] [SpringContextShutdownHook]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskExecutor'
2025-07-08 10:12:24,174 [] [SpringContextShutdownHook]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:350 - HikariPool-3 - Shutdown initiated...
2025-07-08 10:12:24,179 [] [SpringContextShutdownHook]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:352 - HikariPool-3 - Shutdown completed.
2025-07-08 10:12:34,515 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 31996 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-08 10:12:34,521 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-08 10:12:34,528 [] [background-preinit]  INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-08 10:12:34,655 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-08 10:12:34,656 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-08 10:12:39,039 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-08 10:12:39,069 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-08 10:12:39,070 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-08 10:12:39,071 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-08 10:12:39,418 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-08 10:12:39,418 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 4760 ms
2025-07-08 10:12:41,621 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:12:41,624 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:12:48,076 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-08 10:12:48,443 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-08 10:12:48,444 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-08 10:12:48,444 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-08 10:12:48,671 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:12:48,683 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:12:51,010 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-08 10:12:51,013 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-08 10:12:51,050 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 10:12:51,334 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-08 10:12:51,867 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-08 10:12:52,202 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8889"]
2025-07-08 10:12:52,235 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-08 10:12:52,249 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-08 10:12:52,270 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-08 10:12:53,286 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-08 10:12:53,889 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-08 10:12:53,968 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-08 10:12:53,987 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-08 10:12:54,005 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-08 10:12:54,011 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-08 10:12:54,026 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-08 10:12:54,067 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-08 10:12:54,089 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-08 10:12:54,110 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-08 10:12:54,114 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-08 10:12:54,127 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-08 10:12:54,155 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-08 10:12:54,167 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-08 10:12:54,180 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-08 10:12:54,183 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-08 10:12:54,189 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-08 10:12:54,219 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-08 10:12:54,232 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-08 10:12:54,266 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-08 10:12:54,300 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-08 10:12:54,320 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-08 10:12:54,391 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-08 10:12:54,415 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-08 10:12:54,435 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-08 10:12:54,500 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-08 10:12:54,570 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-08 10:12:55,038 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-08 10:12:55,120 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-08 10:12:55,151 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-08 10:12:55,191 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-08 10:12:55,262 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-08 10:12:55,378 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-08 10:12:55,394 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-08 10:12:55,405 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-08 10:12:55,409 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-08 10:12:55,419 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-08 10:12:55,529 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-08 10:12:55,573 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-08 10:12:55,589 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-08 10:12:55,594 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-08 10:12:55,612 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-08 10:12:55,849 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-08 10:12:55,870 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-08 10:12:55,879 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-08 10:12:55,886 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-08 10:12:55,971 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-08 10:12:56,016 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-08 10:12:56,032 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-08 10:12:56,069 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-08 10:12:56,079 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-08 10:12:56,098 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-08 10:12:56,110 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-08 10:12:56,121 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-08 10:12:56,142 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-08 10:12:56,152 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-08 10:12:56,162 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-08 10:12:56,171 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-08 10:12:56,237 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-08 10:12:56,249 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-08 10:12:56,262 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-08 10:12:56,269 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-08 10:12:56,310 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-08 10:12:56,458 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 22.789 seconds (JVM running for 27.679)
2025-07-08 10:12:57,397 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-1 - Starting...
2025-07-08 10:12:58,241 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-1 - Start completed.
2025-07-08 10:13:24,997 [] [http-nio-8889-exec-1]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 10:13:24,998 [] [http-nio-8889-exec-1]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:525 - Initializing Servlet 'dispatcherServlet'
2025-07-08 10:13:25,000 [] [http-nio-8889-exec-1]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:547 - Completed initialization in 1 ms
2025-07-08 10:13:49,493 [] [HikariPool-1 housekeeper]  WARN  [com.zaxxer.hikari.pool.HikariPool] HikariPool.java:787 - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=51s138ms964µs).
2025-07-08 10:14:00,443 [] [SpringContextShutdownHook]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskScheduler'
2025-07-08 10:14:00,446 [] [SpringContextShutdownHook]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskExecutor'
2025-07-08 10:14:00,467 [] [SpringContextShutdownHook]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:350 - HikariPool-1 - Shutdown initiated...
2025-07-08 10:14:00,547 [] [SpringContextShutdownHook]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:352 - HikariPool-1 - Shutdown completed.
2025-07-08 10:14:17,963 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 34716 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-08 10:14:17,970 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-08 10:14:17,972 [] [background-preinit]  INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-08 10:14:18,114 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-08 10:14:18,114 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-08 10:14:22,420 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-08 10:14:22,445 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-08 10:14:22,447 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-08 10:14:22,447 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-08 10:14:22,730 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-08 10:14:22,731 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 4615 ms
2025-07-08 10:14:24,552 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:14:24,553 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:14:27,516 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-08 10:14:27,801 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-08 10:14:27,802 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-08 10:14:27,802 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-08 10:14:27,951 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:14:27,956 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:14:29,649 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-08 10:14:29,650 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-08 10:14:29,676 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 10:14:29,934 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-08 10:14:30,273 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-08 10:14:30,493 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8889"]
2025-07-08 10:14:30,514 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-08 10:14:30,516 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-08 10:14:30,520 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-08 10:14:30,763 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-08 10:14:31,045 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-08 10:14:31,076 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-08 10:14:31,083 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-08 10:14:31,090 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-08 10:14:31,093 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-08 10:14:31,100 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-08 10:14:31,116 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-08 10:14:31,123 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-08 10:14:31,126 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-08 10:14:31,127 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-08 10:14:31,130 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-08 10:14:31,140 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-08 10:14:31,144 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-08 10:14:31,147 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-08 10:14:31,149 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-08 10:14:31,152 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-08 10:14:31,158 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-08 10:14:31,163 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-08 10:14:31,170 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-08 10:14:31,177 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-08 10:14:31,185 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-08 10:14:31,207 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-08 10:14:31,212 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-08 10:14:31,221 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-08 10:14:31,224 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-08 10:14:31,230 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-08 10:14:31,261 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-08 10:14:31,266 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-08 10:14:31,272 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-08 10:14:31,277 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-08 10:14:31,286 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-08 10:14:31,303 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-08 10:14:31,309 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-08 10:14:31,315 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-08 10:14:31,318 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-08 10:14:31,325 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-08 10:14:31,342 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-08 10:14:31,349 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-08 10:14:31,354 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-08 10:14:31,356 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-08 10:14:31,360 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-08 10:14:31,372 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-08 10:14:31,377 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-08 10:14:31,380 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-08 10:14:31,381 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-08 10:14:31,385 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-08 10:14:31,393 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-08 10:14:31,396 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-08 10:14:31,403 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-08 10:14:31,408 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-08 10:14:31,414 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-08 10:14:31,417 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-08 10:14:31,419 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-08 10:14:31,427 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-08 10:14:31,430 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-08 10:14:31,433 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-08 10:14:31,436 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-08 10:14:31,456 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-08 10:14:31,461 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-08 10:14:31,465 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-08 10:14:31,466 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-08 10:14:31,472 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-08 10:14:31,515 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 14.485 seconds (JVM running for 18.174)
2025-07-08 10:14:31,770 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-1 - Starting...
2025-07-08 10:14:32,216 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-1 - Start completed.
2025-07-08 10:15:12,249 [] [http-nio-8889-exec-5]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 10:15:12,249 [] [http-nio-8889-exec-5]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:525 - Initializing Servlet 'dispatcherServlet'
2025-07-08 10:15:12,253 [] [http-nio-8889-exec-5]  INFO  [org.springframework.web.servlet.DispatcherServlet] FrameworkServlet.java:547 - Completed initialization in 4 ms
2025-07-08 10:15:32,878 [af7b10fd-e7ed-45b8-9dda-b8e4cf26ee25] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 24860600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
024860600  100%  
 ===> 0.024
2025-07-08 10:15:34,267 [509e746a-7609-4610-961e-37e9c862aa55] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4120900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004120900  100%  
 ===> 0.004
2025-07-08 10:15:38,935 [c5ef94ff-3614-4000-929e-95ce6e80690b] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4959400 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004959400  100%  
 ===> 0.004
2025-07-08 10:15:40,287 [490778b6-ca17-4705-9ae6-6fa50585b452] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4958900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004958900  100%  
 ===> 0.004
2025-07-08 10:15:46,379 [9c120924-fcd4-4ce7-b250-4600497d1b35] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/5c8f42a5584d440a803fb930cc872bf0.pdf
2025-07-08 10:15:46,379 [9c120924-fcd4-4ce7-b250-4600497d1b35] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/5c8f42a5584d440a803fb930cc872bf0.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:42:07,420 [1ebf7b9b-eb05-4e0d-8f20-b9598bcd7530] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 8310800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
008310800  100%  
 ===> 0.008
2025-07-08 10:42:07,760 [54005539-5256-4811-9883-f33f29bccb8a] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 7493500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
007493500  100%  
 ===> 0.007
2025-07-08 10:42:12,849 [7a59c11a-f996-4840-bed7-8e7299b6a2bf] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/6f4745a0a7a9460d8b163b596d38d056.pdf
2025-07-08 10:42:12,850 [7a59c11a-f996-4840-bed7-8e7299b6a2bf] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/6f4745a0a7a9460d8b163b596d38d056.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:42:18,166 [6aad884c-4474-4538-a8a4-5bb8015c965f] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3023100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003023100  100%  
 ===> 0.003
2025-07-08 10:42:18,848 [c22c2bc0-6270-46ae-9c9c-5f83aeca3d88] [http-nio-8889-exec-5]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 4933300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
004933300  100%  
 ===> 0.004
2025-07-08 10:42:24,195 [fd745efa-fbf4-44aa-b994-aa3cf6c473e6] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 8931500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
008931500  100%  
 ===> 0.008
2025-07-08 10:42:29,237 [5b471139-7239-4818-99b6-e6c766992a9f] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 2974900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
002974900  100%  
 ===> 0.002
2025-07-08 10:42:34,565 [127c9770-fe4e-409e-8d9a-7f9af459bd0e] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3058100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003058100  100%  
 ===> 0.003
2025-07-08 10:42:39,600 [e88bb1de-8263-442f-b88f-3a41284cc738] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 6962300 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
006962300  100%  
 ===> 0.006
2025-07-08 10:42:42,789 [e82288a2-bf47-4ca5-b902-f9d35ea26095] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/19b91705b44c463da2f3e2eafeab4e76.pdf
2025-07-08 10:42:42,790 [e82288a2-bf47-4ca5-b902-f9d35ea26095] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/19b91705b44c463da2f3e2eafeab4e76.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:42:53,934 [bec88b54-42df-4ab3-8e5b-d1038782912e] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/1ad42714b73b40629a694100286745a5.pdf
2025-07-08 10:42:53,934 [bec88b54-42df-4ab3-8e5b-d1038782912e] [http-nio-8889-exec-10]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/68989cc635914d63bdfe50ebafaee424_4_49.pdf D:\data\upload\/upload/pdf/1ad42714b73b40629a694100286745a5.pdf 数据中心绿色节能改造 null_tag
2025-07-08 10:43:03,750 [a9c3b3e4-f70c-45b0-a99e-d66da9cf3b05] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5945800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005945800  100%  
 ===> 0.005
2025-07-08 10:43:04,005 [c4ef6f4f-8162-45d9-b07d-3e6b0076505e] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5822100 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005822100  100%  
 ===> 0.005
2025-07-08 10:43:09,875 [6c105a8c-91fe-4748-8484-d407c94cf6ce] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 7082900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
007082900  100%  
 ===> 0.007
2025-07-08 10:43:10,113 [befeaae2-74e6-451b-aa0b-189d177fb160] [http-nio-8889-exec-3]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5723900 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005723900  100%  
 ===> 0.005
2025-07-08 10:43:13,092 [3a0b6bfd-1f82-491e-ada3-6a57039e3748] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/f871f3b11d44494dbb4454566acfa442.pdf
2025-07-08 10:43:13,093 [3a0b6bfd-1f82-491e-ada3-6a57039e3748] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/f871f3b11d44494dbb4454566acfa442.pdf 数字孪生城市试点 null_tag
2025-07-08 10:43:24,170 [6e373d65-991c-4cea-b752-45e93934d38c] [http-nio-8889-exec-7]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5398500 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005398500  100%  
 ===> 0.005
2025-07-08 10:43:24,496 [5cc861f4-0f9d-456f-8238-326634426ded] [http-nio-8889-exec-9]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 5559600 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
005559600  100%  
 ===> 0.005
2025-07-08 10:43:29,834 [3e42118e-ca16-44fe-88e6-55c14f834d00] [http-nio-8889-exec-2]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3965700 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003965700  100%  
 ===> 0.003
2025-07-08 10:43:34,875 [ad29f4f6-e127-460e-9867-ba6db8308925] [http-nio-8889-exec-8]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3273800 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003273800  100%  
 ===> 0.003
2025-07-08 10:43:40,210 [66d81864-625f-4021-9d3e-54c5ea435d54] [http-nio-8889-exec-6]  INFO  [c.s.i.b.y.b.controller.ResearchTagController] ResearchTagController.java:161 - wordCloud该接口查询时间为===>StopWatch '': running time = 3120200 ns
---------------------------------------------
ns         %     Task name
---------------------------------------------
003120200  100%  
 ===> 0.003
2025-07-08 10:43:44,741 [3b177e82-b642-41ee-8736-4e04cb3188d7] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:69 - 高亮后的文件路径-->D:\data\upload\/upload/pdf/8751939d7bb3429582ab1e728af54bd8.pdf
2025-07-08 10:43:44,741 [3b177e82-b642-41ee-8736-4e04cb3188d7] [http-nio-8889-exec-1]  INFO  [c.s.i.b.y.b.service.impl.ElectronicReadServiceImpl] ElectronicReadServiceImpl.java:85 - python   D:\01_2109A\DXYY-JSDJ\Highlight.py  D:\data\upload\/upload/pdf/c89bbc7c911841a99b8890c8d394102f_11_86.pdf D:\data\upload\/upload/pdf/8751939d7bb3429582ab1e728af54bd8.pdf 数字孪生城市试点 null_tag
2025-07-08 10:46:44,836 [] [Thread-26]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskScheduler'
2025-07-08 10:46:44,839 [] [Thread-26]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskExecutor'
2025-07-08 10:46:44,857 [] [Thread-26]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:350 - HikariPool-1 - Shutdown initiated...
2025-07-08 10:46:44,865 [] [Thread-26]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:352 - HikariPool-1 - Shutdown completed.
2025-07-08 10:46:45,593 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 34716 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-08 10:46:45,596 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-08 10:46:47,928 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-08 10:46:47,930 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-08 10:46:47,930 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-08 10:46:47,930 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-08 10:46:48,062 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-08 10:46:48,063 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 2449 ms
2025-07-08 10:46:49,111 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:46:49,112 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-08 10:46:50,904 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-08 10:46:50,995 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-08 10:46:50,995 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-08 10:46:50,995 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-08 10:46:51,021 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:46:51,030 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-08 10:46:52,551 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-08 10:46:52,552 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-08 10:46:52,564 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-08 10:46:52,679 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-08 10:46:52,804 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-08 10:46:52,889 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8889"]
2025-07-08 10:46:52,892 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-08 10:46:52,893 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-08 10:46:52,895 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-08 10:46:53,089 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-08 10:46:53,154 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-08 10:46:53,169 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-08 10:46:53,175 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-08 10:46:53,179 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-08 10:46:53,180 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-08 10:46:53,184 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-08 10:46:53,202 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-08 10:46:53,208 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-08 10:46:53,212 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-08 10:46:53,214 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-08 10:46:53,217 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-08 10:46:53,231 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-08 10:46:53,234 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-08 10:46:53,236 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-08 10:46:53,237 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-08 10:46:53,240 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-08 10:46:53,246 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-08 10:46:53,249 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-08 10:46:53,255 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-08 10:46:53,259 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-08 10:46:53,265 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-08 10:46:53,285 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-08 10:46:53,293 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-08 10:46:53,297 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-08 10:46:53,298 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-08 10:46:53,307 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-08 10:46:53,356 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-08 10:46:53,369 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-08 10:46:53,377 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-08 10:46:53,381 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-08 10:46:53,391 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-08 10:46:53,407 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-08 10:46:53,411 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-08 10:46:53,414 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-08 10:46:53,416 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-08 10:46:53,420 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-08 10:46:53,443 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-08 10:46:53,452 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-08 10:46:53,457 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-08 10:46:53,458 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-08 10:46:53,461 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-08 10:46:53,484 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-08 10:46:53,493 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-08 10:46:53,495 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-08 10:46:53,496 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-08 10:46:53,500 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-08 10:46:53,507 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-08 10:46:53,510 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-08 10:46:53,522 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-08 10:46:53,527 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-08 10:46:53,535 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-08 10:46:53,545 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-08 10:46:53,548 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-08 10:46:53,558 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-08 10:46:53,563 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-08 10:46:53,570 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-08 10:46:53,577 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-08 10:46:53,604 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-08 10:46:53,611 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-08 10:46:53,615 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-08 10:46:53,618 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-08 10:46:53,627 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-08 10:46:53,639 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 8.168 seconds (JVM running for 1960.298)
2025-07-08 10:46:53,649 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-2 - Starting...
2025-07-08 10:46:53,658 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-2 - Start completed.
2025-07-08 10:46:53,720 [] [restartedMain]  INFO  [o.s.b.d.a.ConditionEvaluationDeltaLoggingListener] ConditionEvaluationDeltaLoggingListener.java:63 - Condition evaluation unchanged
