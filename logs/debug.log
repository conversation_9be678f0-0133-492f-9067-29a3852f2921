2025-07-04 09:00:53,003 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:55 - Starting DemoApplication using Java 1.8.0_391 on tao with PID 13008 (D:\01_2109A\DXYY-JSDJ\target\classes started by 86132 in D:\01_2109A\DXYY-JSDJ)
2025-07-04 09:00:53,008 [] [background-preinit]  INFO  [org.hibernate.validator.internal.util.Version] Version.java:21 - HV000001: Hibernate Validator 6.1.7.Final
2025-07-04 09:00:53,014 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] SpringApplication.java:664 - The following profiles are active: dm
2025-07-04 09:00:53,133 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-04 09:00:53,134 [] [restartedMain]  INFO  [o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor] DeferredLog.java:255 - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-04 09:00:56,112 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:108 - Tomcat initialized with port(s): 8889 (http)
2025-07-04 09:00:56,128 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Initializing ProtocolHandler ["http-nio-8889"]
2025-07-04 09:00:56,129 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardService] DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-04 09:00:56,130 [] [restartedMain]  INFO  [org.apache.catalina.core.StandardEngine] DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-07-04 09:00:56,302 [] [restartedMain]  INFO  [o.a.c.c.C.[Tomcat].[localhost].[/yearbook]] DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-04 09:00:56,302 [] [restartedMain]  INFO  [o.s.b.w.s.c.ServletWebServerApplicationContext] ServletWebServerApplicationContext.java:289 - Root WebApplicationContext: initialization completed in 3167 ms
2025-07-04 09:00:57,639 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-04 09:00:57,641 [] [restartedMain]  ERROR [com.baomidou.mybatisplus.core.MybatisConfiguration] MybatisConfiguration.java:110 - mapper[com.sinosoft.ie.booster.yearbook.baselib.mapper.MetaOntoRelationMapper.ontolist] is ignored, because it exists, maybe from xml file
2025-07-04 09:01:00,955 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch jar version:7.14.0
2025-07-04 09:01:01,305 [] [restartedMain]  INFO  [easy-es] LogUtils.java:40 - Elasticsearch client version:8.12.2
2025-07-04 09:01:01,306 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Easy-Es supported elasticsearch client version is:7.xx
2025-07-04 09:01:01,306 [] [restartedMain]  WARN  [easy-es] LogUtils.java:59 - Elasticsearch clientVersion:8.12.2 not equals jarVersion:7.14.0, It does not affect your use, but we still recommend keeping it consistent!
2025-07-04 09:01:01,439 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-04 09:01:01,444 [] [restartedMain]  INFO  [easy-es] LogUtils.java:30 - ===> manual index mode activated
2025-07-04 09:01:02,912 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService
2025-07-04 09:01:02,915 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskExecutor'
2025-07-04 09:01:02,938 [] [restartedMain]  INFO  [s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping] WebMvcPropertySourcedRequestMappingHandlerMapping.java:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-04 09:01:03,123 [] [restartedMain]  INFO  [o.s.b.d.autoconfigure.OptionalLiveReloadServer] OptionalLiveReloadServer.java:58 - LiveReload server is running on port 35729
2025-07-04 09:01:03,424 [] [restartedMain]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:181 - Initializing ExecutorService 'taskScheduler'
2025-07-04 09:01:03,598 [] [restartedMain]  INFO  [org.apache.coyote.http11.Http11NioProtocol] DirectJDKLog.java:173 - Starting ProtocolHandler ["http-nio-8889"]
2025-07-04 09:01:03,615 [] [restartedMain]  INFO  [o.s.boot.web.embedded.tomcat.TomcatWebServer] TomcatWebServer.java:220 - Tomcat started on port(s): 8889 (http) with context path '/yearbook'
2025-07-04 09:01:03,617 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] DocumentationPluginsBootstrapper.java:93 - Documentation plugins bootstrapped
2025-07-04 09:01:03,620 [] [restartedMain]  INFO  [s.d.s.web.plugins.DocumentationPluginsBootstrapper] AbstractDocumentationPluginsBootstrapper.java:79 - Found 1 custom documentation plugin(s)
2025-07-04 09:01:03,801 [] [restartedMain]  INFO  [s.d.spring.web.scanners.ApiListingReferenceScanner] ApiListingReferenceScanner.java:44 - Scanning for api listing references
2025-07-04 09:01:04,002 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: downloadUsingGET_1
2025-07-04 09:01:04,030 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_1
2025-07-04 09:01:04,036 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_1
2025-07-04 09:01:04,039 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_1
2025-07-04 09:01:04,040 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-04 09:01:04,044 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_1
2025-07-04 09:01:04,055 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_2
2025-07-04 09:01:04,059 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_2
2025-07-04 09:01:04,064 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_2
2025-07-04 09:01:04,065 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-04 09:01:04,068 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_2
2025-07-04 09:01:04,075 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_3
2025-07-04 09:01:04,080 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_3
2025-07-04 09:01:04,082 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_3
2025-07-04 09:01:04,084 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-04 09:01:04,086 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_3
2025-07-04 09:01:04,090 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_4
2025-07-04 09:01:04,092 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-04 09:01:04,098 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_4
2025-07-04 09:01:04,102 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_4
2025-07-04 09:01:04,105 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_4
2025-07-04 09:01:04,122 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_5
2025-07-04 09:01:04,126 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_5
2025-07-04 09:01:04,133 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_5
2025-07-04 09:01:04,135 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-04 09:01:04,138 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_5
2025-07-04 09:01:04,162 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_6
2025-07-04 09:01:04,167 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_6
2025-07-04 09:01:04,171 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_6
2025-07-04 09:01:04,174 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-04 09:01:04,177 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_6
2025-07-04 09:01:04,186 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_7
2025-07-04 09:01:04,188 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_7
2025-07-04 09:01:04,193 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_7
2025-07-04 09:01:04,194 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-04 09:01:04,197 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_7
2025-07-04 09:01:04,207 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_8
2025-07-04 09:01:04,212 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_8
2025-07-04 09:01:04,215 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_8
2025-07-04 09:01:04,216 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-04 09:01:04,219 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_8
2025-07-04 09:01:04,226 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_9
2025-07-04 09:01:04,229 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_9
2025-07-04 09:01:04,231 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_9
2025-07-04 09:01:04,232 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-04 09:01:04,234 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_9
2025-07-04 09:01:04,237 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_10
2025-07-04 09:01:04,239 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-04 09:01:04,249 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_10
2025-07-04 09:01:04,254 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_10
2025-07-04 09:01:04,258 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_10
2025-07-04 09:01:04,262 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_11
2025-07-04 09:01:04,264 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-04 09:01:04,269 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: getListUsingGET_1
2025-07-04 09:01:04,272 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_11
2025-07-04 09:01:04,274 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_11
2025-07-04 09:01:04,277 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_11
2025-07-04 09:01:04,294 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: listUsingGET_12
2025-07-04 09:01:04,298 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: infoUsingGET_12
2025-07-04 09:01:04,302 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: createUsingPOST_12
2025-07-04 09:01:04,303 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-04 09:01:04,308 [] [restartedMain]  INFO  [s.d.s.w.r.operation.CachingOperationNameGenerator] CachingOperationNameGenerator.java:41 - Generating unique operation named: updateUsingPUT_12
2025-07-04 09:01:04,348 [] [restartedMain]  INFO  [com.sinosoft.ie.booster.yearbook.DemoApplication] StartupInfoLogger.java:61 - Started DemoApplication in 12.106 seconds (JVM running for 15.423)
2025-07-04 09:01:04,518 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:110 - HikariPool-1 - Starting...
2025-07-04 09:01:04,839 [] [restartedMain]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:123 - HikariPool-1 - Start completed.
2025-07-04 09:01:19,187 [] [SpringContextShutdownHook]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskScheduler] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskScheduler'
2025-07-04 09:01:19,188 [] [SpringContextShutdownHook]  INFO  [o.s.scheduling.concurrent.ThreadPoolTaskExecutor] ExecutorConfigurationSupport.java:218 - Shutting down ExecutorService 'taskExecutor'
2025-07-04 09:01:19,194 [] [SpringContextShutdownHook]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:350 - HikariPool-1 - Shutdown initiated...
2025-07-04 09:01:19,200 [] [SpringContextShutdownHook]  INFO  [com.zaxxer.hikari.HikariDataSource] HikariDataSource.java:352 - HikariPool-1 - Shutdown completed.
