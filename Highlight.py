# -*- coding: utf-8 -*
import fitz
import sys

inpath = sys.argv[1]
outpath = sys.argv[2]

title = sys.argv[3]
tag = sys.argv[4]


LINE_WORD_COUNT = 20
MAX_PIXEL = 99999

color_list = [1,0.5,0.5]


doc = fitz.open(inpath)
page = doc[0]

def add_annot(text, color):      
    found = False
    for txt in page.search_for(text):
        hig = page.add_highlight_annot(txt)
        if color == "red":
            hig.set_colors(stroke = color_list)
            hig.update()
        found = True
    if found:
        return
    # 两行的情况,文字在一栏中
    for i in range(0,len(text)):
        afound = False
        bfound = False
        if text[:i] == "" or text[i:] == "":
            continue
        a_txt = []
        b_txt = ""
        search_a = text[:i]
        search_b = text[i:]
        a_max_right = 0
        a_min_left = MAX_PIXEL
        for txt in page.search_for(search_a):
            afound = True
            a_txt.append(txt)
            if a_max_right < txt.bottom_right.x:
                a_max_right = txt.bottom_right.x
            if a_min_left > txt.bottom_left.x:
                a_min_left = txt.bottom_left.x
        if not afound :
            continue
        b_bottom_pos = MAX_PIXEL
        b_top_pos = MAX_PIXEL
        word_high = 0
        for txt in page.search_for(search_b):
            b_txt = txt
            if afound :
                word_high = a_txt[0].bottom_left.y - a_txt[0].top_left.y
                # 判断b是否在a的相邻下方
                if  a_txt[0].bottom_left.y + 2*(word_high) > b_txt.bottom_left.y and a_txt[0].bottom_left.y < b_txt.top_left.y :
                    # 如果b的左边大于a的最右边，不符合
                    if b_txt.bottom_left.x > a_max_right:
                        continue

                    bfound = True
                    hig = page.add_highlight_annot(b_txt)
                    b_top_pos = b_txt.bottom_left.y
                    b_bottom_pos = b_txt.top_left.y
                    if color == "red":
                        hig.set_colors(stroke = color_list)
                        hig.update()
                    found = True
        if bfound:
            #如果bfound，a高亮着色
            same_word_count = 0
            for txt in a_txt:
                if  txt.bottom_left.y + 2*(word_high) > b_bottom_pos and txt.bottom_left.y < b_top_pos :
                    same_word_count += 1
            count = 0
            for txt in a_txt:
                if  txt.bottom_left.y + 2*(word_high) > b_bottom_pos and txt.bottom_left.y < b_top_pos :
                    count += 1
                    if " " not in search_a:
                        if count < same_word_count:
                            continue
                    hig = page.add_highlight_annot(txt)
                    if color == "red":
                        hig.set_colors(stroke = color_list)
                        hig.update()

    # 两行的情况,文字在两栏中
    if True:#not found:
        for i in range(0,len(text)):
            afound = False
            bfound = False
            if text[:i] == "" or text[i:] == "":
                continue
            a_txt = []
            b_txt = ""
            search_a = text[:i]
            search_b = text[i:]
            a_max_right = 0
            a_min_left = MAX_PIXEL
            a_max_bottom = 0
            for txt in page.search_for(search_a):
                afound = True
                a_txt.append(txt)
                if a_max_right < txt.bottom_right.x:
                    a_max_right = txt.bottom_right.x
                if a_min_left > txt.bottom_left.x:
                    a_min_left = txt.bottom_left.x
                if a_max_bottom < txt.bottom_left.y:
                    a_max_bottom = txt.bottom_left.y
            b_txt_top = []
            b_txt_top_pos = MAX_PIXEL
            b_txt_top_pos_list = []
            b_txt_max_left = MAX_PIXEL
            if not afound :
                continue
            word_high = 0
            for txt in page.search_for(search_b):
                b_txt = txt
                if afound :
                    word_high = a_txt[0].bottom_left.y - a_txt[0].top_left.y
                    distance = a_txt[0].bottom_left.y - b_txt.top_left.y
                    badjacent = False
                    if len(a_txt) > 1:
                        for aid in range(0, len(a_txt)):
                            distance = a_txt[aid].bottom_left.y - b_txt.top_left.y
                            if distance + 20 * word_high < page.artbox.bottom_left.y:
                                continue
                            badjacent = True
                            a_max_right = a_txt[aid].bottom_left.x
                            break
                        if not badjacent:
                            continue
                    else:
                        if distance + 20 * word_high < page.artbox.bottom_left.y:
                            continue
                    #b的左边大于a的最右边,查找最上面的匹配b的数据
                    if b_txt.bottom_left.x > a_max_right:
                        if b_txt_top_pos >= b_txt.top_left.y:
                            if b_txt_top_pos > b_txt.top_left.y:
                                b_txt_top = []
                            b_txt_top_pos = b_txt.top_left.y
                            b_txt_top.append(txt)
                            b_txt_top_pos_list.append(b_txt.top_left.y)
                            b_txt_max_left = b_txt.top_left.x
                    
                            bfound = True
            if bfound:
                count = 0
                for txt in a_txt:
                    if len(a_txt) > 1:
                        badjacent = False
                        for aid in range(0, len(b_txt_top_pos_list)):
                            distance = txt.bottom_left.y - b_txt_top_pos_list[aid]
                            if distance + 20 * word_high < page.artbox.bottom_left.y:
                                continue
                            if txt.bottom_right.x > b_txt_max_left:
                                continue
                            badjacent = True
                        if badjacent:
                            hig = page.add_highlight_annot(txt)
                            if color == "red":
                                hig.set_colors(stroke = color_list)
                                hig.update()

                    else:
                        hig = page.add_highlight_annot(txt)
                        if color == "red":
                            hig.set_colors(stroke = color_list)
                            hig.update()
                for txt in b_txt_top:
                    hig = page.add_highlight_annot(txt)
                    if color == "red":
                        hig.set_colors(stroke = color_list)
                        hig.update()

            if afound and bfound:
                break




if title!="null_tag":
    title = title.strip('"')
    title=title.replace("@"," ").replace("=","　")
    titlearr = title.split(";")

    found = False
    for ci in titlearr:
        if ci == "":
            continue
        found = False    
        for txt in page.search_for(ci):
            found = True
            hig = page.add_highlight_annot(txt)
        if not found:
            if len(ci)  < 2 * LINE_WORD_COUNT-4:
                add_annot(ci, "yellow")
            else:
                pos = int(len(ci) / 2 )
                add_annot(ci[:pos], "yellow")
                add_annot(ci[pos:], "yellow")


if tag!="null_tag":
    tag = tag.strip('"')
    tag=tag.replace("@"," ").replace("=","　")
    tagarr = tag.split(";")
    found = False    
    for ci in tagarr:
        if ci == "":
            continue
        found = False    
        for txt in page.search_for(ci):
            found = True
            hig = page.add_highlight_annot(txt)
            hig.set_colors(stroke = color_list)
            hig.update()
        if not found:
            if len(ci)  < 2 * LINE_WORD_COUNT-4:
                add_annot(ci, "red")
            else:
                pos = int(len(ci) / 2)                
                add_annot(ci[:pos], "red")
                add_annot(ci[pos:], "red")


doc.save(outpath)


#python Highlight.py  C:\Users\<USER>\Desktop\全球视野下的大国首都研究-经济篇.pdf  C:\Users\<USER>\Desktop\a.pdf     经济;伦敦   null_tag
#python Highlight.py  C:\Users\<USER>\Desktop\全球视野下的大国首都研究-经济篇.pdf  C:\Users\<USER>\Desktop\a.pdf     白吕;白@吕;白@@吕;白=吕 null_tag
