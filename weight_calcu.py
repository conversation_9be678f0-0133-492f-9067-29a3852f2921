import os,sys,csv,json
import pymysql
from flask import Flask,jsonify,request
app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False

#
# connect = pymysql.connect(host = 'localhost',
#                               port = 3306,
#                               user = 'root',
#                               password = '123456',
#                               database = 'yearbook12_19',
#                               charset = 'utf8')
#
# cursor = connect.cursor()
# sql = 'select name,zonelevel,legend_type,organization,year from research_tag;'
#
# cursor.execute(sql)
# tuple_content_detail = cursor.fetchall()



@app.route('/load_database_label',methods = ['POST'])
def load_database_label():
    json_content = request.form.get('json_list')
    content_dict = eval(json_content)

    results = {}
    results['result'] = []

    ldjc = {'领导决策': 0}
    kyll = {'科研力量': 0}
    kyhj = {'科研环境': 0}
    kyhd = {'科研活动': 0}
    kycg = {'科研成功': 0}
    zzgz = {'政治工作': 0}
    glbz = {'管理保障': 0}
    zhxt = {'综合协调': 0}
    kygl = {'科研管理': 0}
    kyjf = {'科研经费': 0}
    data_all = {}

    database_content = content_dict['data']
    sentence = content_dict['newdata']
    year = content_dict['newdata'][0]['year']
    try:
        for i in sentence:
            sentence_detail = i['tag'][0]
            label_list = sentence_detail.strip().split('###')[0].split('、')
            if sentence_detail.strip().split('###')[1] == '':
                continue
            keyword_list = sentence_detail.strip().split('###')[1].split(';')
            for label in label_list:
                for keyword in keyword_list:
                    if keyword == '':
                        continue
                    data_all[keyword] = data_all.get(keyword,0) + 1
                    if label in ldjc:
                        ldjc[keyword] = ldjc.get(keyword,0) + 1
                    elif label in kyll:
                        kyll[keyword] = kyll.get(keyword,0) + 1
                    elif label in kyhj:
                        kyhj[keyword] = kyhj.get(keyword, 0) + 1
                    elif label in kyhd:
                        kyhd[keyword] = kyhd.get(keyword, 0) + 1
                    elif label in kycg:
                        kycg[keyword] = kycg.get(keyword, 0) + 1
                    elif label in zzgz:
                        zzgz[keyword] = zzgz.get(keyword, 0) + 1
                    elif label in glbz:
                        glbz[keyword] = glbz.get(keyword, 0) + 1
                    elif label in zhxt:
                        zhxt[keyword] = zhxt.get(keyword, 0) + 1
                    elif label in kygl:
                        kygl[keyword] = kygl.get(keyword, 0) + 1
                    elif label in kyjf:
                        kyjf[keyword] = kyjf.get(keyword, 0) + 1
                    else:
                        continue
    except Exception as e:
        import traceback
        traceback.print_exc()

    data_all = sorted(data_all.items(), key=lambda x: x[1], reverse=True)
    ldjc = sorted(ldjc.items(), key=lambda x: x[1], reverse=True)[:30]
    kyll = sorted(kyll.items(), key=lambda x: x[1], reverse=True)[:30]
    kyhj = sorted(kyhj.items(), key=lambda x: x[1], reverse=True)[:30]
    kyhd = sorted(kyhd.items(), key=lambda x: x[1], reverse=True)[:30]
    kycg = sorted(kycg.items(), key=lambda x: x[1], reverse=True)[:30]
    zzgz = sorted(zzgz.items(), key=lambda x: x[1], reverse=True)[:30]
    glbz = sorted(glbz.items(), key=lambda x: x[1], reverse=True)[:30]
    zhxt = sorted(zhxt.items(), key=lambda x: x[1], reverse=True)[:30]
    kygl = sorted(kygl.items(), key=lambda x: x[1], reverse=True)[:30]
    kyjf = sorted(kyjf.items(), key=lambda x: x[1], reverse=True)[:30]

    #权重
    weight = 150 / data_all[0][1]

    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'领导决策',"year":year}) for i in ldjc if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'科研力量',"year":year}) for i in kyll if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'科研环境',"year":year}) for i in kyhj if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'科研活动',"year":year}) for i in kyhd if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'科研成果',"year":year}) for i in kycg if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'政治工作',"year":year}) for i in zzgz if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'管理保障',"year":year}) for i in glbz if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'综合协调',"year":year}) for i in zhxt if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'科研管理',"year":year}) for i in kygl if i[1] != 0]
    [results['result'].append({'name':i[0],"zonelevel":int(i[1] * weight),"legendType":'科研经费',"year":year}) for i in kyjf if i[1] != 0]

    results['result'] += database_content

    return jsonify(results)




if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5081)
