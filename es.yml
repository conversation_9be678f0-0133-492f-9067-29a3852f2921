version: "2.2"
services:

  yearbook-es:
    image: elasticsearch:7.12.0
    container_name: yearbook-es
    hostname: yearbook-es
    restart: always
    networks:
      - yearbook-net    
    ports:
      - 19201:9200
      - 19301:9300
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    environment:
      - TAKE_FILE_OWNERSHIP=true
      #单机模式启动
      - discovery.type=single-node
      - TZ=Asia/Shanghai
      # 设置jvm内存大小
      - "ES_JAVA_OPTS=-Xms4096m -Xmx4096m"
    volumes:
      - ./docker-es/es_logs:/usr/share/elasticsearch/logs
      - ./docker-es/es_data:/usr/share/elasticsearch/data
      



networks:
  yearbook-net:
    external: true